"""DICOM Modules - Find and Use All 40+ DICOM Module Implementations

This module provides access to all DICOM module implementations in PyRT-DICOM.
Each module represents a specific section of the DICOM standard with type-safe
builders, full IntelliSense support, and comprehensive validation integration.

Module Discovery Guide - Find What You Need:

    Looking for Patient Information?
        >>> from pyrt_dicom.core import modules, enums
        >>> 
        >>> # Basic patient demographics
        >>> patient = modules.PatientModule.from_required_elements(
        >>>     patient_name="<PERSON><PERSON><PERSON>John",
        >>>     patient_id="12345",
        >>>     patient_birth_date="19800101"
        >>> ).with_optional_elements(
        >>>     patient_sex=enums.PatientSex.MALE
        >>> )
        >>> 
        >>> # Clinical trial participation
        >>> trial_subject = modules.ClinicalTrialSubjectModule.from_required_elements(...)
        >>> 
        >>> # Study-specific patient data  
        >>> patient_study = modules.PatientStudyModule.from_required_elements(...)

    Looking for Study & Series Information?
        >>> # General study metadata
        >>> study = modules.GeneralStudyModule.from_required_elements(
        >>>     study_instance_uid="*******.5",
        >>>     study_date="20240101",
        >>>     study_time="120000"
        >>> )
        >>> 
        >>> # RT-specific series information
        >>> rt_series = modules.RTSeriesModule.from_required_elements(...)
        >>> 
        >>> # General series metadata
        >>> series = modules.GeneralSeriesModule.from_required_elements(...)

    Looking for Dose & Radiotherapy Data?
        >>> # RT Dose distribution
        >>> rt_dose = modules.RTDoseModule.from_required_elements(
        >>>     dose_units=enums.DoseUnits.GY,
        >>>     dose_type=enums.DoseType.PHYSICAL,
        >>>     dose_summation_type=enums.DoseSummationType.PLAN
        >>> )
        >>> 
        >>> # RT Plan information
        >>> rt_plan = modules.RTGeneralPlanModule.from_required_elements(...)
        >>> 
        >>> # Structure sets and ROIs
        >>> structure_set = modules.StructureSetModule.from_required_elements(...)
        >>> roi_contour = modules.ROIContourModule.from_required_elements(...)

    Looking for Image & Spatial Data?
        >>> # General image metadata
        >>> image = modules.GeneralImageModule.from_required_elements(...)
        >>> 
        >>> # Image geometry and orientation
        >>> image_plane = modules.ImagePlaneModule.from_required_elements(...)
        >>> 
        >>> # Pixel data attributes
        >>> image_pixel = modules.ImagePixelModule.from_required_elements(...)
        >>> 
        >>> # Spatial coordinate system
        >>> frame_ref = modules.FrameOfReferenceModule.from_required_elements(...)

Module Categories (40+ Available):

    Patient Information Modules:
        - PatientModule: Basic patient demographics (name, ID, birth date, sex)
        - ClinicalTrialSubjectModule: Clinical trial participation and subject data
        - PatientStudyModule: Study-specific patient information and medical history

    Study & Series Organization:
        - GeneralStudyModule: Study identification, dates, and referring physician
        - ClinicalTrialStudyModule: Clinical trial study metadata and protocols
        - GeneralSeriesModule: Series identification, dates, and modality information
        - RTSeriesModule: Radiotherapy-specific series data and planning references
        - ClinicalTrialSeriesModule: Trial-specific series data and protocol references

    Image & Spatial Information:
        - GeneralImageModule: Image identification, dates, and acquisition details
        - ImagePlaneModule: Image geometry, orientation, and spatial positioning
        - ImagePixelModule: Pixel data attributes, samples, and photometric interpretation
        - MultiFrameModule: Multi-frame image organization and frame relationships
        - FrameOfReferenceModule: 3D coordinate system definition and spatial references
        - CineModule: Cine (movie) sequence parameters and timing information
        - OverlayPlaneModule: Image overlay definitions and display parameters

    Equipment & Technical:
        - GeneralEquipmentModule: Equipment identification, manufacturer, and software
        - SOPCommonModule: SOP instance management and DICOM metadata
        - CommonInstanceReferenceModule: Cross-reference relationships between objects
        - DeviceModule: Device and software identification for treatment systems
        - GeneralReferenceModule: General reference relationships and dependencies

    Radiotherapy Treatment Modules:
        - RTGeneralPlanModule: General radiotherapy plan information and approval
        - StructureSetModule: Structure set definition and ROI organization
        - ROIContourModule: Region of interest contour geometry and spatial data
        - RTROIObservationsModule: ROI observation data and clinical interpretations
        - RTPrescriptionModule: Prescription information and dose specifications
        - RTToleranceTablesModule: Tolerance table definitions for plan validation
        - RTPatientSetupModule: Patient positioning and setup information
        - RTFractionSchemeModule: Fractionation scheme and delivery scheduling
        - RTBeamsModule: Beam definitions, geometries, and delivery parameters
        - RTBrachyApplicationSetupsModule: Brachytherapy application setup data
        - RTDVHModule: Dose volume histogram data and statistical information
        - RTDoseModule: Dose distribution grids and calibration information
        - RTImageModule: Radiotherapy portal and verification images

    Specialized Medical Imaging:
        - CTImageModule: CT-specific image parameters and reconstruction data
        - MultiEnergyCTImageModule: Multi-energy CT acquisition and processing
        - ContrastBolusModule: Contrast agent administration and timing
        - GeneralAcquisitionModule: Acquisition parameters and protocol information
        - ModalityLutModule: Modality lookup table for pixel value transformation
        - VoiLutModule: Value of interest lookup table for display optimization

    Workflow & Quality Assurance:
        - ApprovalModule: Treatment plan approval workflow and authorization
        - FrameExtractionModule: Frame extraction parameters for multi-frame data
        - EnhancedPatientOrientationModule: Enhanced patient orientation and positioning
        - SynchronizationModule: Temporal synchronization for dynamic studies
        - SpecimenModule: Biological specimen identification and handling

Module Architecture & Features:

    Type-Safe Builder Pattern:
        All modules use consistent builder methods:
        - from_required_elements(): Creates module with all required (Type 1 & 2) elements
        - with_optional_elements(): Adds optional (Type 3) elements with method chaining
        - Full type hints and IntelliSense support throughout

    DICOM Compliance:
        - Strict adherence to DICOM PS3.3 specifications
        - Proper Value Representation (VR) and Value Multiplicity (VM) handling
        - Type 1, 1C, 2, 2C, and 3 element requirement enforcement
        - Enumerated value validation using PyRT-DICOM enums

    Validation Integration:
        - Built-in validate() method on every module
        - Returns structured ValidationResult objects
        - Comprehensive error and warning reporting
        - Integration with module validators for detailed checking

Usage Patterns:

    Basic Module Creation:
        # Start with required elements
        module = modules.PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345", 
            patient_birth_date="19800101"
        )
        
        # Add optional elements with chaining
        module = module.with_optional_elements(
            patient_sex=enums.PatientSex.MALE,
            patient_comments="Test patient"
        )

    Module Composition for IODs:
        # Create individual modules
        patient = modules.PatientModule.from_required_elements(...)
        study = modules.GeneralStudyModule.from_required_elements(...)
        rt_dose = modules.RTDoseModule.from_required_elements(...)
        
        # Compose into complete IOD
        from pyrt_dicom.core.iods import RTDoseIOD
        dose_iod = RTDoseIOD(patient=patient, general_study=study, rt_dose=rt_dose, ...)

    Module Validation:
        from pyrt_dicom.core import module_validators
        
        # Validate individual module
        result = module_validators.PatientValidator.validate(patient)
        if not result.is_valid:
            for error in result.errors:
                print(f"Error: {error.message}")

    Module Data Access:
        # Direct attribute access with IntelliSense
        patient_name = patient.patient_name
        patient_id = patient.patient_id
        
        # Modify data elements
        patient.patient_comments = "Updated comment"

Finding the Right Module:
    1. Use the category list above to identify the domain you need
    2. Check the module's docstring for detailed element descriptions  
    3. Use your IDE's IntelliSense: modules.<Tab> to see all available modules
    4. Reference DICOM PS3.3 for official module specifications
    5. Look at the builder method signatures for required vs. optional elements

Module Development Guidelines:
    - All modules inherit from BaseModule for consistent behavior
    - Use enums for all coded values (never string literals)
    - Follow Google-style docstrings with complete parameter documentation
    - Implement proper DICOM element validation in module constructors
    - Provide comprehensive examples in module docstrings

Integration Benefits:
    - Seamless integration with IOD composition patterns
    - Full validation framework support
    - Type-safe development with IntelliSense
    - DICOM-compliant file generation
    - Extensible architecture for custom modules
"""

from ..modules import *  # noqa: F403
