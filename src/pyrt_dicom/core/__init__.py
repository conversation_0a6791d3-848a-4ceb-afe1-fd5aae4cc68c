

"""PyRT-DICOM Core Module - Streamlined Access to DICOM Components

This core module provides convenient access to all PyRT-DICOM components through a 
unified interface, making it easier to discover and import the modules, validators,
IODs, enums, and utilities you need for your radiotherapy DICOM workflows.

Quick Start Guide:
    >>> from pyrt_dicom.core import modules, enums, iods
    >>> 
    >>> # Access DICOM modules for building IODs
    >>> patient = modules.PatientModule.from_required_elements(...)
    >>> dose = modules.RTDoseModule.from_required_elements(...)
    >>> 
    >>> # Use DICOM-compliant enumerated values
    >>> dose_type = enums.DoseType.PHYSICAL
    >>> dose_summation = enums.DoseSummationType.PLAN
    >>> 
    >>> # Create complete DICOM IODs
    >>> rt_dose_iod = iods.RTDoseIOD(patient=patient, rt_dose=dose, ...)

Available Sub-modules:

    modules: Access to 40+ DICOM modules organized by category
        - Patient modules (PatientModule, ClinicalTrialSubjectModule, ...)
        - Study/Series modules (GeneralStudyModule, RTSeriesModule, ...)
        - Image modules (GeneralImageModule, ImagePlaneModule, ...)
        - RT modules (RTDoseModule, RTBeamsModule, StructureSetModule, ...)
        - Equipment modules (GeneralEquipmentModule, DeviceModule, ...)
        - Specialized modules (CTImageModule, ContrastBolusModule, ...)

    enums: DICOM-compliant enumerated values for all coded fields
        - Dose enums (DoseType, DoseSummationType, DoseUnits, ...)
        - Clinical trial enums (approval status, randomization, ...)
        - Patient/study enums (sex, age, study status, ...)
        - Equipment enums (manufacturer, device types, ...)
        - RT-specific enums (beam types, delivery types, ...)

    iods: Complete DICOM Information Object Definitions
        - RTDoseIOD: For dose distribution files
        - BaseIOD: Foundation for custom IOD implementations
        - (More IODs planned: RTStructureSetIOD, RTPlanIOD, RTImageIOD)

    module_validators: Validation framework for DICOM modules
        - Individual validators for each module type
        - ValidationResult objects for structured error handling
        - DICOM compliance checking and validation rules

    iod_validators: Validation framework for complete IODs
        - End-to-end IOD validation
        - Cross-module dependency checking
        - File generation validation

    utils: Utility functions for DICOM operations
        - Data formatting helpers
        - File I/O utilities
        - Common DICOM operations

Usage Patterns:
    # Building a complete RT Dose file
    from pyrt_dicom.core import modules, enums, iods
    
    # Create required modules with type-safe builders
    patient = modules.PatientModule.from_required_elements(
        patient_name="Doe^John",
        patient_id="12345",
        patient_birth_date="19800101"
    ).with_optional_elements(
        patient_sex=enums.PatientSex.MALE
    )
    
    rt_dose = modules.RTDoseModule.from_required_elements(
        dose_units=enums.DoseUnits.GY,
        dose_type=enums.DoseType.PHYSICAL,
        dose_summation_type=enums.DoseSummationType.PLAN
    )
    
    # Compose into complete IOD
    dose_iod = iods.RTDoseIOD(patient=patient, rt_dose=rt_dose, ...)
    
    # Generate DICOM dataset for file writing
    dataset = dose_iod.to_dataset()

For detailed documentation on specific components, see the individual module docstrings.
"""

from . import modules
from . import module_validators
from . import iods
from . import iod_validators
from . import enums
from . import utils

__all__ = [
    "modules",
    "module_validators",
    "iods",
    "iod_validators",
    "enums",
    "utils",
]