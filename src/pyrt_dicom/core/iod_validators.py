"""DICOM IOD Validators - Complete Information Object Definition Validation

This module provides comprehensive validation for complete DICOM Information Object
Definitions (IODs). IOD validators ensure that entire DICOM objects meet the 
specification requirements, including cross-module dependencies and file generation
readiness.

Quick Usage Guide:
    >>> from pyrt_dicom.core import iod_validators
    >>> from pyrt_dicom.core.iods import RTDoseIOD
    >>> 
    >>> # Create an RT Dose IOD
    >>> dose_iod = RTDoseIOD(patient=patient, rt_dose=rt_dose, ...)
    >>> 
    >>> # Validate the complete IOD
    >>> result = iod_validators.RTDoseIODValidator.validate(dose_iod)
    >>> if result.is_valid:
    >>>     print("IOD is ready for DICOM file generation")
    >>> else:
    >>>     print(f"Validation errors: {result.errors}")

What IOD Validators Check:

    Cross-Module Dependencies:
        - Required module presence validation
        - Inter-module relationship consistency
        - Module data element dependencies
        - IOD-specific business rules

    DICOM Compliance:
        - IOD definition conformance (DICOM PS3.3)
        - SOP Class UID consistency  
        - File meta information requirements
        - Transfer syntax compatibility

    Data Integrity:
        - Patient/study/series ID consistency
        - Spatial coordinate system alignment
        - Dose grid and geometry validation
        - Reference consistency across modules

    File Generation Readiness:
        - All required Type 1 elements present
        - Type 1C elements present when conditions met
        - Type 2 elements have valid null handling
        - Dataset structure completeness

Available IOD Validators:

    RTDoseIODValidator:
        - RT Dose IOD complete validation
        - Dose grid spatial consistency
        - DVH data validation (if present)
        - Patient setup and frame of reference alignment
        - Dose summation type compliance

    BaseIODValidator:
        - Common IOD validation patterns
        - SOP instance management
        - Patient/study/series hierarchy
        - File meta information validation

    (Future validators planned):
        - RTStructureSetIODValidator
        - RTPlanIODValidator  
        - RTImageIODValidator

Validation Workflow:
    1. Individual module validation (see module_validators)
    2. Cross-module dependency checking
    3. IOD-specific business rule validation
    4. DICOM file generation preparation
    5. Final compliance verification

Usage in Your Workflow:
    # Step 1: Build your IOD from modules
    dose_iod = RTDoseIOD(
        patient=patient_module,
        general_study=study_module,
        rt_series=series_module,
        rt_dose=dose_module,
        # ... other required modules
    )
    
    # Step 2: Validate before file generation
    validation_result = iod_validators.RTDoseIODValidator.validate(dose_iod)
    
    # Step 3: Handle validation results
    if validation_result.is_valid:
        # Safe to generate DICOM file
        dataset = dose_iod.to_dataset()
        file_dataset = dose_iod.generate_file_dataset()
    else:
        # Fix validation errors before proceeding
        for error in validation_result.errors:
            print(f"Error: {error.message} in {error.element_name}")

ValidationResult Objects:
    IOD validators return ValidationResult objects with:
    - is_valid: Boolean validation status
    - errors: List of validation errors with details
    - warnings: List of non-blocking issues
    - element_path: Specific location of issues
    - suggested_fixes: Automated fix recommendations

Best Practices:
    - Always validate IODs before DICOM file generation
    - Address all validation errors (warnings are optional)
    - Use validation results to guide IOD construction
    - Validate after any significant IOD modifications
    - Keep validation results for debugging and audit trails

Integration with PyRT-DICOM:
    IOD validators work seamlessly with:
    - Module validators for individual module checks
    - IOD composition patterns for building complete objects
    - DICOM file generation for final output
    - Enum validation for coded value compliance
"""

from ..validators.iods import *  # noqa: F403
