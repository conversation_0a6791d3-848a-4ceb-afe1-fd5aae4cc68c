"""DICOM Information Object Definitions (IODs) - Complete DICOM Objects

This module provides access to complete DICOM Information Object Definitions (IODs)
that represent entire DICOM objects ready for file generation. IODs are composed
from individual DICOM modules using a composition-based architecture for maximum
flexibility and IntelliSense support.

Quick Start Guide:
    >>> from pyrt_dicom.core import iods, modules, enums
    >>> 
    >>> # Create individual modules first
    >>> patient = modules.PatientModule.from_required_elements(...)
    >>> rt_dose = modules.RTDoseModule.from_required_elements(...)
    >>> 
    >>> # Compose into complete IOD
    >>> dose_iod = iods.RTDoseIOD(
    >>>     patient=patient,
    >>>     rt_dose=rt_dose,
    >>>     # ... other required modules
    >>> )
    >>> 
    >>> # Generate DICOM dataset for file writing
    >>> dataset = dose_iod.to_dataset()

Available IOD Classes:

    RTDoseIOD - Radiotherapy Dose Information Object
        Purpose: Creates DICOM-compliant RT Dose files for dose distributions
        Required Modules:
            - PatientModule: Patient demographics and identification
            - GeneralStudyModule: Study-level information
            - RTSeriesModule: RT-specific series information  
            - GeneralEquipmentModule: Equipment and software details
            - RTDoseModule: Dose distribution data and metadata
            - SOPCommonModule: SOP instance management
            - FrameOfReferenceModule: Spatial coordinate system
        
        Optional Modules:
            - PatientStudyModule: Study-specific patient data
            - ClinicalTrialSubjectModule: Clinical trial participation
            - GeneralImageModule: Image-level metadata (if applicable)
            - ImagePlaneModule: Image plane geometry (if applicable)
            - ImagePixelModule: Pixel data attributes (if applicable)
            - MultiFrameModule: Multi-frame image data (if applicable)
            - RTDVHModule: Dose volume histogram data
            - ApprovalModule: Treatment approval information

        Key Features:
            - Composition-based architecture for flexible module management
            - Type-safe module access with full IntelliSense support
            - On-demand dataset generation with proper DICOM structure
            - Built-in validation integration with comprehensive error checking
            - Direct file generation with FileDataset support

    BaseIOD - Foundation Class for Custom IODs
        Purpose: Base class providing common IOD functionality
        Features:
            - Module composition management
            - Dataset generation infrastructure
            - Validation framework integration
            - SOP instance UID management
            - File meta information handling

        Use Cases:
            - Building custom IOD types
            - Extending existing IOD functionality
            - Research and development applications

IOD Architecture Benefits:

    Composition Over Inheritance:
        - IODs store live module references instead of inheriting module data
        - Modules can be modified after IOD creation
        - Clear separation between data (modules) and orchestration (IODs)
        - No complex multiple inheritance hierarchies

    Type-Safe Module Access:
        - get_module() methods provide type-safe module retrieval
        - Full IntelliSense support for module properties and methods
        - Compile-time validation of module presence and types

    On-Demand Dataset Generation:
        - to_dataset() creates fresh pydicom Dataset objects
        - generate_file_dataset() creates FileDataset for direct file I/O
        - No duplicate data storage - modules are single source of truth

Usage Patterns:

    Creating a Complete RT Dose File:
        # Step 1: Create all required modules
        patient = modules.PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345", 
            patient_birth_date="19800101"
        )
        
        general_study = modules.GeneralStudyModule.from_required_elements(
            study_instance_uid="*******.5",
            study_date="20240101",
            study_time="120000"
        )
        
        rt_dose = modules.RTDoseModule.from_required_elements(
            dose_units=enums.DoseUnits.GY,
            dose_type=enums.DoseType.PHYSICAL,
            dose_summation_type=enums.DoseSummationType.PLAN
        )
        
        # Step 2: Compose into IOD
        dose_iod = iods.RTDoseIOD(
            patient=patient,
            general_study=general_study, 
            rt_dose=rt_dose,
            # ... other required modules
        )
        
        # Step 3: Generate DICOM file
        file_dataset = dose_iod.generate_file_dataset()
        file_dataset.save_as("dose_distribution.dcm")

    Accessing and Modifying Modules:
        # Get module with type safety
        patient_module = dose_iod.get_module('patient')  # Returns PatientModule
        
        # Modify module data (updates IOD automatically)
        patient_module.patient_comments = "Updated comment"
        
        # Re-generate dataset with changes
        updated_dataset = dose_iod.to_dataset()

    Validation Integration:
        from pyrt_dicom.core import iod_validators
        
        # Validate complete IOD before file generation
        validation_result = iod_validators.RTDoseIODValidator.validate(dose_iod)
        
        if validation_result.is_valid:
            dataset = dose_iod.to_dataset()
        else:
            print(f"Validation errors: {validation_result.errors}")

Future IOD Classes (Planned):
    - RTStructureSetIOD: For structure sets and ROI definitions
    - RTPlanIOD: For treatment plans and beam definitions
    - RTImageIOD: For portal and verification images
    - CTImageIOD: For CT image series
    - MRImageIOD: For MR image series

Integration with PyRT-DICOM:
    IODs work seamlessly with:
    - Module composition for flexible data modeling
    - Enum validation for DICOM compliance
    - Validation framework for error checking
    - File generation utilities for DICOM output
    - IntelliSense support for developer productivity
"""

from ..iods import *  # noqa: F403
