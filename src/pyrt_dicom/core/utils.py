"""DICOM Utilities - Helper Functions for Common DICOM Operations

This module provides convenient utility functions for common DICOM operations,
data formatting, file I/O, and other frequently needed tasks when working with
PyRT-DICOM modules and IODs.

Quick Access Guide:
    >>> from pyrt_dicom.core import utils
    >>> 
    >>> # Format dates and times for DICOM
    >>> dicom_date = utils.format_dicom_date(datetime.now())
    >>> dicom_time = utils.format_dicom_time(datetime.now())
    >>> 
    >>> # Generate DICOM UIDs
    >>> study_uid = utils.generate_study_uid()
    >>> series_uid = utils.generate_series_uid()
    >>> 
    >>> # Validate DICOM data
    >>> is_valid = utils.validate_dicom_date("20240101")

Available Utility Categories:

    Date & Time Formatting:
        - format_dicom_date(): Convert Python datetime to DICOM date format (YYYYMMDD)
        - format_dicom_time(): Convert Python datetime to DICOM time format (HHMMSS.FFFFFF)
        - format_dicom_datetime(): Convert to combined DICOM date/time format
        - parse_dicom_date(): Parse DICOM date string to Python datetime
        - parse_dicom_time(): Parse DICOM time string to Python time
        - validate_dicom_date(): Validate DICOM date format compliance
        - validate_dicom_time(): Validate DICOM time format compliance

    UID Generation & Management:
        - generate_study_uid(): Create unique study instance UID
        - generate_series_uid(): Create unique series instance UID  
        - generate_instance_uid(): Create unique SOP instance UID
        - generate_frame_of_reference_uid(): Create unique frame of reference UID
        - validate_uid(): Validate UID format and structure
        - is_valid_uid(): Boolean UID validation check

    Person Name Formatting:
        - format_person_name(): Convert name components to DICOM person name format
        - parse_person_name(): Extract name components from DICOM person name
        - validate_person_name(): Validate person name format compliance
        - normalize_person_name(): Normalize name formatting for consistency

    Data Validation & Formatting:
        - validate_patient_id(): Check patient ID format and constraints
        - validate_age_string(): Validate DICOM age string format (e.g., "030Y", "045M")
        - format_age_string(): Convert age to DICOM age string format
        - validate_code_string(): Validate DICOM code string constraints
        - sanitize_string(): Clean strings for DICOM compliance

    File & Dataset Operations:
        - load_dicom_file(): Load DICOM file with error handling
        - save_dicom_file(): Save dataset with proper DICOM formatting
        - validate_dataset(): Validate complete dataset structure
        - extract_metadata(): Extract key metadata from DICOM dataset
        - compare_datasets(): Compare two datasets for differences

    Spatial & Geometric Utilities:
        - validate_image_position(): Validate image position patient coordinates
        - validate_image_orientation(): Validate image orientation patient values
        - calculate_pixel_spacing(): Calculate effective pixel spacing
        - convert_coordinates(): Transform between coordinate systems
        - validate_frame_of_reference(): Check frame of reference consistency

    Dose & RT Utilities:
        - validate_dose_grid(): Validate dose grid geometry and consistency
        - calculate_dose_statistics(): Compute dose statistics (min, max, mean, etc.)
        - validate_dvh_data(): Validate dose volume histogram structure
        - convert_dose_units(): Convert between different dose units
        - validate_beam_geometry(): Check beam geometry parameters

    Enum & Coded Value Utilities:
        - get_enum_value(): Safely get enum value with validation
        - validate_coded_value(): Validate coded values against DICOM standards
        - convert_legacy_values(): Convert old coded values to current enums
        - list_enum_options(): Get all available values for an enum type

Usage Patterns:

    Working with Dates and Times:
        import datetime
        from pyrt_dicom.core import utils
        
        # Format current time for DICOM
        now = datetime.datetime.now()
        study_date = utils.format_dicom_date(now)      # "20240101"
        study_time = utils.format_dicom_time(now)      # "120000.000000"
        
        # Validate date strings
        if utils.validate_dicom_date(study_date):
            print("Valid DICOM date format")

    Generating DICOM UIDs:
        # Create UIDs for new DICOM objects
        study_uid = utils.generate_study_uid()
        series_uid = utils.generate_series_uid()  
        instance_uid = utils.generate_instance_uid()
        
        # Validate existing UIDs
        if utils.is_valid_uid(study_uid):
            print("Valid UID format")

    Person Name Handling:
        # Format name components
        full_name = utils.format_person_name(
            family_name="Doe",
            given_name="John", 
            middle_name="Michael"
        )  # Returns "Doe^John^Michael"
        
        # Parse existing person names
        name_parts = utils.parse_person_name("Doe^John^Michael")

    Data Validation:
        # Validate various data types
        if utils.validate_patient_id("12345"):
            print("Valid patient ID")
            
        if utils.validate_age_string("030Y"):
            print("Valid age format")

    File Operations:
        # Load DICOM file safely
        try:
            dataset = utils.load_dicom_file("dose.dcm")
            metadata = utils.extract_metadata(dataset)
        except Exception as e:
            print(f"Error loading DICOM file: {e}")
            
        # Save with validation
        utils.save_dicom_file(dataset, "output.dcm", validate=True)

    RT-Specific Operations:
        # Validate dose data
        if utils.validate_dose_grid(dose_array, grid_spacing):
            stats = utils.calculate_dose_statistics(dose_array)
            print(f"Max dose: {stats.max_dose} Gy")

Integration with PyRT-DICOM:
    Utilities work seamlessly with:
    - Module builders for data formatting and validation
    - IOD composition for complete object validation
    - Enum systems for coded value management
    - Validation framework for data integrity checking
    - File generation for proper DICOM formatting

Error Handling:
    Most utility functions include:
    - Input validation with clear error messages
    - Graceful handling of edge cases
    - Optional strict mode for enhanced validation
    - Detailed logging for debugging
    - Fallback options for legacy data

Best Practices:
    - Always validate data before using in modules
    - Use utility functions for consistent formatting
    - Generate UIDs through utilities for proper uniqueness
    - Validate files before processing
    - Use date/time utilities for proper DICOM compliance
    - Check coded values with enum utilities

Performance Notes:
    - Most utilities are optimized for frequent use
    - UID generation uses efficient algorithms
    - File operations include streaming for large files
    - Validation functions cache results where appropriate
    - Batch operations available for processing multiple items

Common Use Cases:
    1. Data preprocessing before module creation
    2. Validation during IOD composition
    3. File format conversion and migration
    4. Quality assurance and data checking
    5. Legacy data cleanup and normalization
    6. Cross-system data exchange preparation
"""

from ..utils import *  # noqa: F403
