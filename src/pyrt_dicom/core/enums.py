"""DICOM Enumerations - Find All DICOM-Compliant Coded Values

This module provides access to all DICOM enumerated values used throughout the
PyRT-DICOM library. These enums ensure DICOM compliance and provide IntelliSense
support for finding the correct coded values for your DICOM files.

Quick Discovery Guide:
    >>> from pyrt_dicom.core import enums
    >>> 
    >>> # Dose-related enums
    >>> dose_type = enums.DoseType.PHYSICAL  # vs EFFECTIVE
    >>> dose_units = enums.DoseUnits.GY      # vs RELATIVE
    >>> summation = enums.DoseSummationType.PLAN  # vs BEAM, BRACHY, etc.
    >>> 
    >>> # Patient demographics
    >>> sex = enums.PatientSex.MALE          # vs FEMALE, OTHER
    >>> 
    >>> # Clinical trial status
    >>> approval = enums.ApprovalStatus.APPROVED  # vs PENDING, DENIED

Available Enum Categories:

    Dose & Radiotherapy Enums:
        - DoseType: PHYSICAL, EFFECTIVE  
        - DoseUnits: GY (Gray), RELATIVE
        - DoseSummationType: PLAN, BEAM, BRACHY, FRACTION, etc.
        - RadiationType: PHOTON, ELECTRON, ION, etc.
        - BeamType: STATIC, DYNAMIC, TREATMENT, SETUP
        - TreatmentDeliveryType: TREATMENT, VERIFICATION, etc.

    Patient & Study Enums:
        - PatientSex: MALE, FEMALE, OTHER
        - PatientAge: Age-related coded values
        - StudyStatus: Various study completion states
        - SeriesStatus: Series completion indicators

    Clinical Trial Enums:
        - ApprovalStatus: APPROVED, PENDING, DENIED, etc.
        - RandomizationStatus: Randomization indicators
        - ClinicalTrialTimePoint: Study timeline markers
        - ProtocolDeviationStatus: Deviation tracking

    Equipment & Acquisition Enums:
        - ManufacturerModelName: Common equipment models
        - DeviceType: Equipment classification values
        - AcquisitionType: Image acquisition methods
        - ContrastBolusAgent: Contrast agent types

    Image & Spatial Enums:
        - ImageType: Primary/secondary image indicators
        - PixelPresentation: Monochrome, color presentations
        - PatientOrientation: Anatomical orientation codes
        - FrameOfReferenceType: Spatial reference frameworks

    Specimen & Synchronization Enums:
        - SpecimenType: Biological specimen classifications  
        - SynchronizationType: Temporal sync methods
        - SynchronizationChannel: Sync channel indicators

Usage Patterns:
    # Always use enums instead of string literals for DICOM compliance
    
    # ❌ Wrong - using string literals
    module.dose_type = "PHYSICAL"
    module.patient_sex = "M"
    
    # ✅ Correct - using enums  
    from pyrt_dicom.core import enums
    module.dose_type = enums.DoseType.PHYSICAL
    module.patient_sex = enums.PatientSex.MALE

Benefits of Using Enums:
    - DICOM standard compliance guaranteed
    - IntelliSense autocomplete for valid values
    - Compile-time validation of coded values
    - Protection against typos and invalid codes
    - Clear documentation of available options

Finding the Right Enum:
    1. Look at the enum category names above
    2. Use your IDE's autocomplete: enums.<Tab>
    3. Check individual enum files in pyrt_dicom.enums package
    4. Reference DICOM standard PS3.3 for official coded values

Note: All enums follow strict DICOM PS3.3 specifications. Using these enums
ensures your DICOM files will pass validation and be accepted by DICOM viewers
and treatment planning systems.
"""

from ..enums import *  # noqa: F403
