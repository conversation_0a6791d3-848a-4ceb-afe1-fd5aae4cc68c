"""DICOM Module Validators - Individual Module Validation Framework

This module provides comprehensive validation for individual DICOM modules.
Module validators ensure that each module meets DICOM specification requirements
and contains all necessary data elements with proper values and relationships.

Quick Usage Guide:
    >>> from pyrt_dicom.core import module_validators, modules
    >>> 
    >>> # Create a module with some data
    >>> patient = modules.PatientModule.from_required_elements(
    >>>     patient_name="Doe^John",
    >>>     patient_id="12345",
    >>>     patient_birth_date="19800101"
    >>> )
    >>> 
    >>> # Validate the module
    >>> result = module_validators.PatientValidator.validate(patient)
    >>> if result.is_valid:
    >>>     print("Module is DICOM compliant")
    >>> else:
    >>>     print(f"Validation errors: {result.errors}")

What Module Validators Check:

    DICOM Element Requirements:
        - Type 1 elements: Must be present with valid values
        - Type 1C elements: Present when condition is met  
        - Type 2 elements: Must be present (can be empty)
        - Type 2C elements: Present when condition is met
        - Type 3 elements: Optional (validated if present)

    Data Element Validation:
        - Value Representation (VR) compliance
        - Value Multiplicity (VM) requirements
        - Data element value constraints
        - Enumerated value validation (when applicable)
        - Cross-element consistency checking

    Module-Specific Business Rules:
        - DICOM specification requirements
        - Real-world value constraints
        - Inter-element dependencies
        - Clinical workflow requirements

Available Module Validators:

    Patient Information Validators:
        - PatientValidator: Patient demographics and identification
        - ClinicalTrialSubjectValidator: Clinical trial participation  
        - PatientStudyValidator: Study-specific patient information

    Study & Series Validators:
        - GeneralStudyValidator: Study-level metadata
        - ClinicalTrialStudyValidator: Trial-specific study data
        - GeneralSeriesValidator: Series-level information
        - RTSeriesValidator: RT-specific series metadata
        - ClinicalTrialSeriesValidator: Trial-specific series data

    Image & Spatial Validators:
        - GeneralImageValidator: Image-level metadata
        - ImagePlaneValidator: Image geometry and orientation
        - ImagePixelValidator: Pixel data attributes
        - MultiFrameValidator: Multi-frame image data
        - FrameOfReferenceValidator: Spatial coordinate systems
        - CineValidator: Cine sequence parameters
        - OverlayPlaneValidator: Image overlay data

    Equipment & Common Validators:
        - GeneralEquipmentValidator: Equipment identification
        - SOPCommonValidator: SOP instance management
        - CommonInstanceReferenceValidator: Cross-reference validation
        - DeviceValidator: Device and software information
        - GeneralReferenceValidator: Reference relationships

    Radiotherapy Validators:
        - RTGeneralPlanValidator: General RT plan information
        - StructureSetValidator: Structure set and ROI data
        - ROIContourValidator: ROI contour geometry
        - RTROIObservationsValidator: ROI observation data
        - RTPrescriptionValidator: Prescription information
        - RTToleranceTablesValidator: Tolerance table data
        - RTPatientSetupValidator: Patient setup information
        - RTFractionSchemeValidator: Fraction scheme data
        - RTBeamsValidator: Beam definition validation
        - RTBrachyApplicationSetupsValidator: Brachytherapy setup
        - RTDVHValidator: Dose volume histogram data
        - RTDoseValidator: Dose distribution validation
        - RTImageValidator: RT image validation

    Specialized Validators:
        - CTImageValidator: CT-specific image validation
        - MultiEnergyCTImageValidator: Multi-energy CT data
        - ContrastBolusValidator: Contrast agent information
        - GeneralAcquisitionValidator: Acquisition parameters
        - ModalityLutValidator: Modality LUT validation
        - VoiLutValidator: VOI LUT validation
        - ApprovalValidator: Treatment approval data
        - FrameExtractionValidator: Frame extraction parameters
        - EnhancedPatientOrientationValidator: Enhanced orientation
        - SynchronizationValidator: Temporal synchronization
        - SpecimenValidator: Biological specimen data

Validation Workflow:
    1. Data element presence checking
    2. Value Representation (VR) validation  
    3. Value Multiplicity (VM) verification
    4. Enumerated value compliance (if applicable)
    5. Cross-element consistency checking
    6. Module-specific business rule validation
    7. Warning generation for best practices

ValidationResult Objects:
    All validators return ValidationResult objects containing:
    - is_valid: Boolean indicating overall validation status
    - errors: List of validation errors that must be fixed
    - warnings: List of best practice recommendations
    - element_path: Specific location of validation issues
    - suggested_fixes: Automated recommendations for fixes

Usage Patterns:

    Individual Module Validation:
        # Create module
        rt_dose = modules.RTDoseModule.from_required_elements(
            dose_units=enums.DoseUnits.GY,
            dose_type=enums.DoseType.PHYSICAL
        )
        
        # Validate module
        result = module_validators.RTDoseValidator.validate(rt_dose)
        
        if not result.is_valid:
            for error in result.errors:
                print(f"Error in {error.element_name}: {error.message}")

    Batch Module Validation:
        modules_to_validate = [patient, study, series, rt_dose]
        validators = [
            module_validators.PatientValidator,
            module_validators.GeneralStudyValidator,
            module_validators.RTSeriesValidator,
            module_validators.RTDoseValidator
        ]
        
        for module, validator in zip(modules_to_validate, validators):
            result = validator.validate(module)
            if not result.is_valid:
                print(f"Validation failed for {module.__class__.__name__}")

    Validation Configuration:
        # Some validators support configuration for specific validation rules
        config = ValidationConfig(strict_mode=True, check_warnings=True)
        result = module_validators.PatientValidator.validate(patient, config)

Base Validator Features:
    All module validators inherit from BaseValidator providing:
    - Consistent validation interface
    - Standard error and warning handling
    - ValidationResult object generation
    - Configuration parameter support
    - Extensible validation rules

Integration with PyRT-DICOM:
    Module validators integrate seamlessly with:
    - Module builder patterns for validation during construction
    - IOD validators for complete object validation
    - Enum validation for coded value compliance
    - DICOM file generation for pre-export validation
    - Development tools for real-time validation feedback

Best Practices:
    - Validate modules after construction and before IOD composition
    - Address all validation errors before creating IODs
    - Use warnings to improve DICOM file quality
    - Validate after modifying module data elements
    - Keep validation results for audit trails and debugging
"""

from ..validators.modules import *  # noqa: F403