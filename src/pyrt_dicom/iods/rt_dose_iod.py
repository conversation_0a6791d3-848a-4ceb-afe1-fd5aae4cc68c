"""RT Dose IOD implementation for DICOM PS3.3 A.18.3.

The RT Dose IOD combines radiotherapy dose distribution modules into a complete
DICOM RT Dose object containing 3D dose matrices, scaling factors, and dose
calculation parameters. This implementation follows the new IOD design pattern
with explicit module constructors and on-demand dataset generation.
"""

from typing import Optional, Dict, Any
from datetime import datetime, date
import uuid
from pydicom import uid, Dataset

from pyrt_dicom.enums import PatientSex, Modality, DoseUnits, DoseType, DoseSummationType, PhotometricInterpretation, PixelRepresentation

from .base_iod import BaseIOD, IODValidationError
from ..validators import ValidationResult
from ..validators.iods.rt_dose_iod_validator import RTDoseIODValidator
from ..validators.modules.base_validator import ValidationConfig
from ..modules import (
    PatientModule,
    GeneralStudyModule,
    RTSeriesModule,
    FrameOfReferenceModule,
    GeneralEquipmentModule,
    RTDoseModule,
    GeneralImageModule,
    ImagePlaneModule,
    ImagePixelModule,
    MultiFrameModule,
    RTDVHModule,
    SOPCommonModule,
    OverlayPlaneModule,
    ApprovalModule,
    FrameExtractionModule,
    ClinicalTrialSubjectModule,
    ClinicalTrialStudyModule,
    ClinicalTrialSeriesModule
)


class RTDoseIOD(BaseIOD):
    """RT Dose Information Object Definition for DICOM PS3.3 A.18.3.

    Combines radiotherapy dose distribution modules into a complete DICOM RT Dose object
    containing 3D dose matrices, scaling factors, and dose calculation parameters.

    The RT Dose IOD is designed to address the requirements for transfer of dose
    distributions calculated by radiotherapy treatment planning systems. These
    distributions may be represented as 2D or 3D grids and may also contain
    dose-volume histogram data.

    Required Modules (per DICOM PS3.3 A.18.3):
    - Patient Module (C.7.1.1) - M
    - General Study Module (C.7.2.1) - M
    - RT Series Module (C.8.8.1) - M
    - Frame of Reference Module (C.7.4.1) - M
    - General Equipment Module (C.7.5.1) - M
    - RT Dose Module (C.8.8.3) - M
    - SOP Common Module (C.12.1) - M

    Conditional Modules:
    - General Image Module (C.7.6.1) - C (Required if dose data contains grid-based doses)
    - Image Plane Module (C.7.6.2) - C (Required if dose data contains grid-based doses)
    - Image Pixel Module (C.7.6.3) - C (Required if dose data contains grid-based doses)
    - Multi-frame Module (C.7.6.6) - C (Required if dose data contains grid-based doses and pixel data is multi-frame)

    Optional Modules:
    - RT DVH Module (C.8.8.4) - U
    - Frame Extraction Module (C.12.3) - C (Required if SOP Instance created in response to Frame-Level retrieve)
    - Overlay Plane Module - U (Previously included, now optional)
    - Approval Module - U
    - Clinical Trial Subject Module - U
    - Clinical Trial Study Module - U
    - Clinical Trial Series Module - U

    Usage:
        # Create modules using new composition-based builder patterns
        patient_module = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex="M"
        )
        
        rt_dose_module = RTDoseModule.from_required_elements(
            dose_units="GY",
            dose_type="PHYSICAL",
            dose_summation_type="PLAN"
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        
        # Create RT Dose IOD with 3D dose distribution
        rt_dose = RTDoseIOD(
            patient_module=patient_module,
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid="*******.5"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid="*******.6"
            ),
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid="*******.7"  # Must match planning CT
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Eclipse Treatment Planning System"
            ),
            rt_dose_module=rt_dose_module,
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",  # RT Dose Storage (pydicom.uid.RTDoseStorage)
                sop_instance_uid="*******.8"
            ),
            # Optional: Add image plane for spatial registration
            image_plane_module=ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.5, 2.5],  # Dose grid resolution
                image_orientation_patient=[1,0,0,0,1,0],
                image_position_patient=[-200, -200, -100]  # Dose grid origin
            )
        )

        # Access dose statistics using new composition architecture
        dose_data = rt_dose.get_module('rt_dose')
        max_dose = rt_dose.get_dose_summary().get('max_dose', 0)
        print(f"Maximum dose: {max_dose} Gy")

        # Generate dataset for export
        dataset = rt_dose.to_dataset()
        dataset.save_as("dose.dcm")
    """
    SOP_CLASS_UID = uid.RTDoseStorage  #: RT Dose Storage SOP Class UID per DICOM PS3.6

    def __init__(self,
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 rt_series_module: RTSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 rt_dose_module: RTDoseModule,
                 sop_common_module: SOPCommonModule,
                 general_image_module: Optional[GeneralImageModule] = None,
                 image_plane_module: Optional[ImagePlaneModule] = None,
                 image_pixel_module: Optional[ImagePixelModule] = None,
                 multi_frame_module: Optional[MultiFrameModule] = None,
                 rt_dvh_module: Optional[RTDVHModule] = None,
                 overlay_plane_module: Optional[OverlayPlaneModule] = None,
                 approval_module: Optional[ApprovalModule] = None,
                 frame_extraction_module: Optional[FrameExtractionModule] = None,
                 clinical_trial_subject_module: Optional[ClinicalTrialSubjectModule] = None,
                 clinical_trial_study_module: Optional[ClinicalTrialStudyModule] = None,
                 clinical_trial_series_module: Optional[ClinicalTrialSeriesModule] = None):
        """Create RT Dose IOD from constituent modules.

        Module Requirements:
            M - Required
            C - Conditional
            U - Optional

        Args:
            patient_module (PatientModule): Patient demographic and identification information (M, C.7.1.1)
            general_study_module (GeneralStudyModule): Study-level metadata including study UID and dates (M, C.7.2.1)
            rt_series_module (RTSeriesModule): RT series metadata (must have modality='RTDOSE') (M, C.8.8.1)
            frame_of_reference_module (FrameOfReferenceModule): Spatial coordinate system reference matching planning CT (M, C.7.4.1)
            general_equipment_module (GeneralEquipmentModule): Dose calculation system equipment information (M, C.7.5.1)
            rt_dose_module (RTDoseModule): Dose distribution data, scaling factors, and calculation parameters (M, C.8.8.3)
            sop_common_module (SOPCommonModule): SOP Class and Instance UIDs and creation metadata (M, C.12.1)
            
            general_image_module (GeneralImageModule, optional): Image attributes for dose visualization and display (C, C.7.6.1). 
                Required if dose data contains grid-based doses.
            image_plane_module (ImagePlaneModule, optional): Spatial characteristics for dose grid (C, C.7.6.2). 
                Required if dose data contains grid-based doses. Requires general_image_module.
            image_pixel_module (ImagePixelModule, optional): Pixel data attributes for dose grid (C, C.7.6.3). 
                Required if dose data contains grid-based doses. Requires general_image_module.
            multi_frame_module (MultiFrameModule, optional): Multi-frame attributes for dose slices (C, C.7.6.6). 
                Required if dose data contains multi-frame pixel data. Requires general_image_module.
            
            rt_dvh_module (RTDVHModule, optional): Dose volume histogram data for structures (U, C.8.8.4)
            overlay_plane_module (OverlayPlaneModule, optional): Overlay graphics for isodose lines (U, C.7.6.5)
            approval_module (ApprovalModule, optional): Dose calculation approval information (U, C.12.2)
            frame_extraction_module (FrameExtractionModule, optional): Frame extraction metadata (C, C.12.3). 
                Required if SOP Instance created from frame-level retrieve.

            clinical_trial_subject_module (ClinicalTrialSubjectModule, optional): Clinical trial subject information (U, C.12.4)
            clinical_trial_study_module (ClinicalTrialStudyModule, optional): Clinical trial study information (U, C.12.5)
            clinical_trial_series_module (ClinicalTrialSeriesModule, optional): Clinical trial series information (U, C.12.6)

        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect

        Example:
            # Create RT Dose IOD with 3D dose distribution using new composition architecture
            rt_dose = RTDoseIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John",
                    patient_id="12345",
                    patient_birth_date="19900101",
                    patient_sex="M"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality="RTDOSE",
                    series_instance_uid="*******.6"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Eclipse Treatment Planning System"
                ),
                rt_dose_module=RTDoseModule.from_required_elements(
                    dose_units="GY",
                    dose_type="PHYSICAL",
                    dose_summation_type="PLAN"
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.2",
                    sop_instance_uid="*******.8"
                )
            )
        """
        super().__init__()

        # Validate RT Dose-specific requirements
        if rt_series_module['Modality'] != Modality.RTDOSE.value:
            raise IODValidationError(f"rt_series_module must have modality={Modality.RTDOSE.value}")

        # Validate conditional module dependencies
        self._validate_conditional_dependencies(image_plane_module, image_pixel_module,
                                              multi_frame_module, general_image_module)

        # Store required modules as live references
        self._required_modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'rt_series': rt_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'rt_dose': rt_dose_module,
            'sop_common': sop_common_module
        }

        # Add optional and conditional modules if provided
        optional_modules = {
            'general_image': general_image_module,
            'image_plane': image_plane_module,
            'image_pixel': image_pixel_module,
            'multi_frame': multi_frame_module,
            'rt_dvh': rt_dvh_module,
            'overlay_plane': overlay_plane_module,
            'approval': approval_module,
            'frame_extraction': frame_extraction_module,
            'clinical_trial_subject': clinical_trial_subject_module,
            'clinical_trial_study': clinical_trial_study_module,
            'clinical_trial_series': clinical_trial_series_module
        }

        for name, module in optional_modules.items():
            if module is not None:
                self._required_modules[name] = module

    # =====================================================================
    # IOD-Specific Factory Methods for RT Dose Workflows
    # =====================================================================

    @classmethod
    def for_treatment_planning(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        study_description: str = "Radiotherapy Treatment Planning",
        manufacturer: str = "Treatment Planning System",
        dose_summation_type: DoseSummationType = DoseSummationType.PLAN,
        dose_units: DoseUnits = DoseUnits.GY,
        referring_physician: str = "",
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTDoseIOD':
        """Create RT Dose IOD optimized for clinical treatment planning workflow.
        
        This factory method creates an RT Dose IOD with sensible defaults for
        treatment planning systems. It auto-generates required UIDs and sets up
        the minimal required modules for a complete treatment plan dose distribution.
        
        Args:
            patient_name (str): Patient's name in DICOM format (Family^Given^Middle^Prefix^Suffix)
            patient_id (str): Primary patient identifier
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            study_description (str): Description of the planning study
            manufacturer (str): Name of treatment planning system
            dose_summation_type (DoseSummationType): Type of dose summation (default: PLAN)
            dose_units (DoseUnits): Dose units (default: GY)
            referring_physician (str): Referring physician name
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTDoseIOD: Configured RT Dose IOD for treatment planning
            
        Example:
            # Create dose IOD for treatment planning
            rt_dose = RTDoseIOD.for_treatment_planning(
                patient_name="Doe^John",
                patient_id="12345",
                patient_birth_date="19900101",
                patient_sex=PatientSex.MALE,
                study_description="Prostate IMRT Treatment Plan",
                manufacturer="Eclipse Treatment Planning System v15.6"
            )
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create required modules with treatment planning defaults
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid,
            referring_physicians_name=referring_physician
        ).with_optional_elements(
            study_description=study_description
        )
        
        series_module = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE,
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="Treatment Planning Dose Distribution"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        dose_module = RTDoseModule.from_required_elements(
            dose_units=dose_units,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=dose_summation_type
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            rt_series_module=series_module,
            frame_of_reference_module=frame_ref_module,
            general_equipment_module=equipment_module,
            rt_dose_module=dose_module,
            sop_common_module=sop_module
        )

    @classmethod 
    def for_dose_verification(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        verification_description: str = "Dose Verification",
        manufacturer: str = "Dose Verification System",
        dose_type: str | DoseType = DoseType.PHYSICAL,
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTDoseIOD':
        """Create RT Dose IOD optimized for dose verification and QA workflows.
        
        This factory method creates an RT Dose IOD configured for dose verification,
        quality assurance, and dose measurement workflows. Sets up appropriate
        defaults for verification scenarios.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier  
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            verification_description (str): Description of verification procedure
            manufacturer (str): Name of verification system
            dose_type (str | DoseType): Type of dose (default: PHYSICAL)
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTDoseIOD: Configured RT Dose IOD for dose verification
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create modules for verification workflow
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid
        ).with_optional_elements(
            study_description=f"QA Verification - {verification_description}"
        )
        
        series_module = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE, 
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="Dose Verification"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        dose_module = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=dose_type,
            dose_summation_type=DoseSummationType.FRACTION  # Verification often per fraction
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            rt_series_module=series_module,
            frame_of_reference_module=frame_ref_module,
            general_equipment_module=equipment_module,
            rt_dose_module=dose_module,
            sop_common_module=sop_module
        )

    @classmethod
    def for_research_study(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        protocol_name: str,
        study_description: str = "Research Dose Study",
        manufacturer: str = "Research System",
        dose_summation_type: DoseSummationType = DoseSummationType.PLAN,
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTDoseIOD':
        """Create RT Dose IOD optimized for research studies with clinical trial modules.
        
        This factory method creates an RT Dose IOD configured for research workflows,
        including clinical trial modules for protocol compliance and data management.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier
            patient_birth_date (str | datetime | date): Patient birth date  
            patient_sex (PatientSex): Patient sex (M/F/O)
            protocol_name (str): Clinical trial protocol name
            study_description (str): Research study description
            manufacturer (str): Name of research system
            dose_summation_type (DoseSummationType): Type of dose summation
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTDoseIOD: Configured RT Dose IOD for research studies
        """
        # Create basic RT Dose IOD
        rt_dose = cls.for_treatment_planning(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex,
            study_description=study_description,
            manufacturer=manufacturer,
            dose_summation_type=dose_summation_type,
            study_instance_uid=study_instance_uid,
            series_instance_uid=series_instance_uid,
            frame_of_reference_uid=frame_of_reference_uid,
            sop_instance_uid=sop_instance_uid
        )
        
        # Add clinical trial context
        return rt_dose.with_clinical_trial_context(
            protocol_name=protocol_name
        )

    @classmethod
    def for_quality_assurance(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex = PatientSex.OTHER,
        qa_description: str = "Quality Assurance Dose Measurement",
        manufacturer: str = "QA Measurement System",
        dose_type: DoseType = DoseType.PHYSICAL,
        dose_summation_type: DoseSummationType = DoseSummationType.BEAM,
        qa_procedure_type: str = "Machine QA",
        phantom_name: str = "",
        physicist_name: str = "",
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTDoseIOD':
        """Create RT Dose IOD optimized for medical physics quality assurance workflows.
        
        This factory method creates an RT Dose IOD configured for quality assurance
        and commissioning workflows typically used by medical physicists. Sets up
        appropriate defaults for QA measurements, phantom studies, and machine
        commissioning activities.
        
        Args:
            patient_name (str): Patient/phantom name in DICOM format
            patient_id (str): QA phantom or patient identifier
            patient_birth_date (str | datetime | date): Birth date or phantom commissioning date
            patient_sex (PatientSex): Patient sex or "O" for phantoms (default: PatientSex.OTHER.value)
            qa_description (str): Description of QA procedure
            manufacturer (str): Name of QA measurement system
            dose_type (DoseType): Type of dose measurement (default: PHYSICAL)
            dose_summation_type (DoseSummationType): QA dose summation type (default: BEAM)
            qa_procedure_type (str): Type of QA procedure (Machine QA, Phantom QA, Commissioning, etc.)
            phantom_name (str): Name of QA phantom if applicable
            physicist_name (str): Name of responsible medical physicist
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTDoseIOD: Configured RT Dose IOD for quality assurance
            
        Example:
            # Create QA dose IOD for daily machine QA
            qa_dose = RTDoseIOD.for_quality_assurance(
                patient_name="Daily QA Phantom",
                patient_id="QA-PHANTOM-001",
                patient_birth_date="20240101",
                patient_sex=PatientSex.OTHER,
                qa_description="Daily Machine QA - Output Check",
                manufacturer="PTW BEAMSCAN 3D",
                qa_procedure_type="Daily Machine QA",
                phantom_name="PTW Octavius 4D",
                physicist_name="Dr. Medical Physicist"
            )
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create modules for QA workflow
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        # Add QA-specific patient comments if phantom is used
        if phantom_name:
            patient_module.with_optional_elements(
                patient_comments=f"QA Phantom: {phantom_name}"
            )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid,
            referring_physicians_name=physicist_name
        ).with_optional_elements(
            study_description=f"QA Study - {qa_description}"
        )
        
        series_module = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE,
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description=f"QA Dose Measurement - {qa_procedure_type}"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        dose_module = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=dose_type,
            dose_summation_type=dose_summation_type
        ).with_optional_elements(
            dose_comment=f"QA Measurement: {qa_procedure_type}"
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            rt_series_module=series_module,
            frame_of_reference_module=frame_ref_module,
            general_equipment_module=equipment_module,
            rt_dose_module=dose_module,
            sop_common_module=sop_module
        )

    # =====================================================================
    # IOD-Specific Feature Addition Methods  
    # =====================================================================

    def with_dose_volume_histogram(
        self,
        structure_set_sop_instance_uid: str,
        dvh_data: list[Dataset] | None = None,
        structure_set_sop_class_uid: str = uid.RTStructureSetStorage
    ) -> 'RTDoseIOD':
        """Add dose-volume histogram data to the RT Dose IOD.
        
        Configures the RT DVH Module with dose-volume histogram data for
        structure-based dose analysis. DVH data enables analysis of dose
        distributions to anatomical structures and organs at risk.
        
        Args:
            structure_set_sop_instance_uid (str): SOP Instance UID of referenced structure set
            dvh_data (list[Dataset] | None): DVH data for structures with roi_number, 
                dvh_type, dose_values, volume_values, etc.
            structure_set_sop_class_uid (str): SOP Class UID of structure set (default: RT Structure Set Storage)
            
        Returns:
            RTDoseIOD: Self with DVH data added
            
        Example:
            rt_dose.with_dose_volume_histogram(
                structure_set_sop_instance_uid="*******.5.6.7.8.9",
                dvh_data=[{
                    'roi_number': 1,
                    'dvh_type': 'CUMULATIVE',
                    'dose_scaling': 1.0,
                    'volume_units': 'CM3',
                    'number_of_bins': 100,
                    'dvh_data': [0.1, 10.5, 0.2, 9.8]  # dose1, volume1, dose2, volume2, ...
                }]
            )
        """
        # Create referenced structure set sequence
        structure_set_ref = RTDVHModule.create_referenced_structure_set_item(
            referenced_sop_class_uid=structure_set_sop_class_uid,
            referenced_sop_instance_uid=structure_set_sop_instance_uid
        )
        
        # Create DVH sequence items
        dvh_sequence = []
        if dvh_data:
            for dvh_item in dvh_data:
                # Create DVH referenced ROI sequence
                dvh_roi_ref = RTDVHModule.create_dvh_referenced_roi_item(
                    referenced_roi_number=dvh_item.get('roi_number', 1),
                    dvh_roi_contribution_type=dvh_item.get('roi_contribution_type', 'INCLUDED')
                )
                
                # Create DVH item
                dvh_sequence_item = RTDVHModule.create_dvh_item(
                    dvh_referenced_roi_sequence=[dvh_roi_ref],
                    dvh_type=dvh_item.get('dvh_type', 'CUMULATIVE'),
                    dose_units=dvh_item.get('dose_units', DoseUnits.GY),
                    dose_type=dvh_item.get('dose_type', DoseType.PHYSICAL),
                    dvh_dose_scaling=dvh_item.get('dose_scaling', 1.0),
                    dvh_volume_units=dvh_item.get('volume_units', 'CM3'),
                    dvh_number_of_bins=dvh_item.get('number_of_bins', 100),
                    dvh_data=dvh_item.get('dvh_data', [])
                )
                dvh_sequence.append(dvh_sequence_item)
        
        # Create RT DVH Module
        dvh_module = RTDVHModule.from_required_elements(
            referenced_structure_set_sequence=[structure_set_ref],
            dvh_sequence=dvh_sequence
        )
        
        self._required_modules['rt_dvh'] = dvh_module
        return self

    def with_3d_dose_grid(
        self,
        pixel_spacing: list[float] = [2.5, 2.5],
        image_orientation_patient: list[float] = [1, 0, 0, 0, 1, 0], 
        image_position_patient: list[float] = [0.0, 0.0, 0.0],
        rows: int = 256,
        columns: int = 256,
        number_of_frames: int = 100,
        bits_allocated: int = 16,
        dose_grid_scaling: float = 0.001,
        slice_thickness: float | None = None
    ) -> 'RTDoseIOD':
        """Configure RT Dose IOD for 3D dose grid with required conditional modules.
        
        Sets up the conditional image modules required for grid-based dose data
        per DICOM PS3.3 A.18.3. Configures spatial parameters, pixel data
        characteristics, and dose scaling for 3D dose distributions.
        
        Args:
            pixel_spacing (list[float]): Physical spacing between pixels [row, col] in mm
            image_orientation_patient (list[float]): Direction cosines for image orientation 
            image_position_patient (list[float]): Position of first pixel [x, y, z] in mm
            rows (int): Number of pixel rows in dose grid
            columns (int): Number of pixel columns in dose grid  
            number_of_frames (int): Number of dose planes/slices
            bits_allocated (int): Bits per pixel (16 or 32 for RT Dose)
            dose_grid_scaling (float): Scaling factor from pixel values to dose units
            slice_thickness (float | None): Thickness of dose slices in mm
            
        Returns:
            RTDoseIOD: Self configured for 3D dose grid
            
        Example:
            rt_dose.with_3d_dose_grid(
                pixel_spacing=[2.5, 2.5],  # 2.5mm resolution
                rows=256, columns=256, number_of_frames=120,
                dose_grid_scaling=0.001  # 1 pixel value = 0.001 Gy
            )
        """
        # Configure RT Dose module with pixel data elements
        dose_module = self._required_modules.get('rt_dose')
        if dose_module:
            dose_module.with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                bits_allocated=bits_allocated,
                bits_stored=bits_allocated,
                high_bit=bits_allocated - 1,
                pixel_representation=PixelRepresentation.UNSIGNED,
                dose_grid_scaling=dose_grid_scaling
            )
        
        # Add required conditional modules for grid-based doses
        general_image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        image_plane_module = ImagePlaneModule.from_required_elements(
            pixel_spacing=pixel_spacing,
            image_orientation_patient=image_orientation_patient,
            image_position_patient=image_position_patient,
            slice_thickness=slice_thickness
        )
        
        image_pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=rows,
            columns=columns,
            bits_allocated=bits_allocated,
            bits_stored=bits_allocated,
            high_bit=bits_allocated - 1,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        # Add multi-frame module if multiple frames
        if number_of_frames > 1:
            multi_frame_module = MultiFrameModule.from_required_elements(
                number_of_frames=number_of_frames,
                frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_functional_groups()
            )
            self._required_modules['multi_frame'] = multi_frame_module
        
        # Store the conditional modules
        self._required_modules.update({
            'general_image': general_image_module,
            'image_plane': image_plane_module, 
            'image_pixel': image_pixel_module
        })
        
        return self

    def with_clinical_trial_context(
        self,
        protocol_name: str,
        site_id: str | None = None,
        sponsor_name: str | None = None,
        subject_reading_id: str | None = None,
        subject_id: str | None = None
    ) -> 'RTDoseIOD':
        """Add clinical trial modules for research study compliance.
        
        Configures clinical trial modules for protocol compliance, subject
        management, and research data tracking. Essential for clinical
        research and regulatory submission workflows.
        
        Args:
            protocol_name (str): Clinical trial protocol name/identifier
            site_id (str | None): Clinical trial site identifier
            sponsor_name (str | None): Research sponsor organization
            subject_reading_id (str | None): Subject reading identifier
            subject_id (str | None): Clinical trial subject identifier
            
        Returns:
            RTDoseIOD: Self with clinical trial modules added
            
        Example:
            rt_dose.with_clinical_trial_context(
                protocol_name="RTOG-1234",
                site_id="Site-001",
                sponsor_name="NCI Clinical Trials Network"
            )
        """
        # Add Clinical Trial Subject Module
        trial_subject_module = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name=sponsor_name or "Research Sponsor",
            clinical_trial_protocol_id=f"PROTO-{uuid.uuid4().hex[:8].upper()}",
            clinical_trial_protocol_name=protocol_name,
            clinical_trial_site_id=site_id or "",
            clinical_trial_site_name=""
        )
        
        # Add subject identification (Type 1C requirement)
        trial_subject_module.with_subject_identification(
            clinical_trial_subject_id=subject_id or f"SUBJ-{uuid.uuid4().hex[:8].upper()}"
        )
        
        if subject_reading_id:
            trial_subject_module.with_optional_elements(
                issuer_of_clinical_trial_subject_reading_id=subject_reading_id
            )
        
        # Add Clinical Trial Study Module  
        trial_study_module = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id=""
        )
        
        # Add Clinical Trial Series Module
        trial_series_module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name=""
        )
        
        # Store the clinical trial modules
        self._required_modules.update({
            'clinical_trial_subject': trial_subject_module,
            'clinical_trial_study': trial_study_module,
            'clinical_trial_series': trial_series_module
        })
        
        return self

    def with_dose_approval(
        self,
        approval_status: str = "APPROVED",
        approval_date: str | datetime | date | None = None,
        reviewer_name: str | None = None,
        approval_code_sequence: list[Dataset] | None = None
    ) -> 'RTDoseIOD':
        """Add dose calculation approval information for clinical workflow.
        
        Configures the Approval Module with dose calculation approval data
        for clinical workflow management and regulatory compliance.
        
        Args:
            approval_status (str): Approval status (APPROVED, REJECTED, PENDING)
            approval_date (str | datetime | date | None): Date of approval
            reviewer_name (str | None): Name of approving physician/physicist
            approval_code_sequence (list[Dataset] | None): Coded approval information
            
        Returns:
            RTDoseIOD: Self with approval data added
            
        Example:
            rt_dose.with_dose_approval(
                approval_status="APPROVED",
                approval_date="20240101",
                reviewer_name="Dr. Medical Physicist"
            )
        """
        approval_module = ApprovalModule.from_required_elements(
            approval_status=approval_status
        )
        
        # Add optional approval elements
        optional_elements = {}
        if approval_date is not None:
            optional_elements['approval_date'] = approval_date
        if reviewer_name is not None:
            optional_elements['reviewer_name'] = reviewer_name
        if approval_code_sequence is not None:
            optional_elements['approval_code_sequence'] = approval_code_sequence
            
        if optional_elements:
            approval_module.with_optional_elements(**optional_elements)
        
        self._required_modules['approval'] = approval_module
        return self

    # =====================================================================
    # IOD-Specific Properties and Methods
    # =====================================================================

    @property
    def has_dvh_data(self) -> bool:
        """Check if dose volume histogram data is included.

        Returns:
            bool: True if RT DVH module is present
        """
        return 'rt_dvh' in self._required_modules

    @property
    def has_image_representation(self) -> bool:
        """Check if dose can be displayed as images.

        Returns:
            bool: True if general image module is present for dose visualization
        """
        return 'general_image' in self._required_modules

    @property
    def is_3d_dose(self) -> bool:
        """Check if this contains 3D dose distribution data.

        Returns:
            bool: True if pixel data represents 3D dose array
        """
        dose_module = self._required_modules.get('rt_dose')
        if not dose_module:
            return False

        pixel_data = dose_module.get('PixelData', None)
        if pixel_data is None:
            return False

        # Check if we have image pixel module with dimensions
        image_pixel_module = self._required_modules.get('image_pixel')
        if image_pixel_module:
            rows = image_pixel_module.get('Rows', 0)
            columns = image_pixel_module.get('Columns', 0)
            frames = image_pixel_module.get('NumberOfFrames', 1)
            return frames > 1 or (rows > 0 and columns > 0)

        return False

    @property
    def is_multi_frame(self) -> bool:
        """Check if this is a multi-frame dose distribution.

        Returns:
            bool: True if multi-frame module is present
        """
        return 'multi_frame' in self._required_modules

    @property
    def has_spatial_information(self) -> bool:
        """Check if spatial positioning data is available.

        Returns:
            bool: True if image plane module is present for spatial calculations
        """
        return 'image_plane' in self._required_modules

    @property
    def has_approval_data(self) -> bool:
        """Check if dose calculation approval information is included.

        Returns:
            bool: True if approval module is present
        """
        return 'approval' in self._required_modules

    def get_dose_summary(self) -> Dict[str, Any]:
        """Get dose distribution summary information.

        Returns:
            Dict[str, Any]: Dictionary with key dose parameters and statistics
        """
        dose_module = self._required_modules.get('rt_dose')
        if not dose_module:
            return {}

        summary = {
            'dose_units': dose_module.get('DoseUnits', None),
            'dose_type': dose_module.get('DoseType', None),
            'dose_summation_type': dose_module.get('DoseSummationType', None),
            'dose_grid_scaling': dose_module.get('DoseGridScaling', None),
            'dose_comment': dose_module.get('DoseComment', None)
        }

        # Add pixel data statistics if available
        pixel_data = dose_module.get('PixelData', None)
        scaling = dose_module.get('DoseGridScaling', 1.0)

        if pixel_data is not None and scaling is not None:
            try:
                # Convert pixel data to numpy array for statistics
                import numpy as np
                if isinstance(pixel_data, bytes):
                    # Determine data type and shape from image pixel module
                    image_pixel_module = self._required_modules.get('image_pixel')
                    if image_pixel_module:
                        rows = image_pixel_module.get('Rows', 0)
                        columns = image_pixel_module.get('Columns', 0)
                        frames = image_pixel_module.get('NumberOfFrames', 1)
                        bits_allocated = image_pixel_module.get('BitsAllocated', 16)

                        # Determine numpy dtype based on bits allocated
                        if bits_allocated == 8:
                            dtype = np.uint8
                        elif bits_allocated == 16:
                            dtype = np.uint16
                        elif bits_allocated == 32:
                            dtype = np.uint32
                        else:
                            dtype = np.uint16  # Default fallback

                        # Reshape pixel data into appropriate array structure
                        pixel_array = np.frombuffer(pixel_data, dtype=dtype)
                        if frames > 1:
                            pixel_array = pixel_array.reshape((frames, rows, columns))
                        else:
                            pixel_array = pixel_array.reshape((rows, columns))

                        # Calculate dose statistics using scaling factor
                        dose_array = pixel_array.astype(np.float64) * scaling
                        summary.update({
                            'grid_dimensions': pixel_array.shape,
                            'max_dose': float(np.max(dose_array)) if dose_array.size > 0 else 0.0,
                            'mean_dose': float(np.mean(dose_array)) if dose_array.size > 0 else 0.0,
                            'min_dose': float(np.min(dose_array)) if dose_array.size > 0 else 0.0,
                            'dose_std': float(np.std(dose_array)) if dose_array.size > 0 else 0.0
                        })
                elif isinstance(pixel_data, np.ndarray):
                    # Handle direct numpy array input
                    dose_array = pixel_data.astype(np.float64) * scaling
                    summary.update({
                        'grid_dimensions': pixel_data.shape,
                        'max_dose': float(np.max(dose_array)) if dose_array.size > 0 else 0.0,
                        'mean_dose': float(np.mean(dose_array)) if dose_array.size > 0 else 0.0,
                        'min_dose': float(np.min(dose_array)) if dose_array.size > 0 else 0.0,
                        'dose_std': float(np.std(dose_array)) if dose_array.size > 0 else 0.0
                    })
            except Exception:
                # If pixel data processing fails, return basic info only
                pass

        return summary

    def get_spatial_information(self) -> Dict[str, Any]:
        """Get spatial positioning and orientation information.

        Returns:
            Dict[str, Any]: Dictionary with spatial parameters for dose grid positioning
        """
        spatial_info = {}

        # Frame of reference information
        frame_ref_module = self._required_modules.get('frame_of_reference')
        if frame_ref_module:
            spatial_info['frame_of_reference_uid'] = frame_ref_module.get('FrameOfReferenceUID', None)
            spatial_info['position_reference_indicator'] = frame_ref_module.get('PositionReferenceIndicator', None)

        # Image plane information  
        image_plane_module = self._required_modules.get('image_plane')
        if image_plane_module:
            spatial_info.update({
                'pixel_spacing': image_plane_module.get('PixelSpacing', None),
                'image_orientation_patient': image_plane_module.get('ImageOrientationPatient', None),
                'image_position_patient': image_plane_module.get('ImagePositionPatient', None),
                'slice_thickness': image_plane_module.get('SliceThickness', None),
                'slice_location': image_plane_module.get('SliceLocation', None)
            })

        # Image pixel information
        image_pixel_module = self._required_modules.get('image_pixel')
        if image_pixel_module:
            spatial_info.update({
                'rows': image_pixel_module.get('Rows', None),
                'columns': image_pixel_module.get('Columns', None),
                'number_of_frames': image_pixel_module.get('NumberOfFrames', None)
            })

        return spatial_info

    # =====================================================================
    # IOD-Specific Validation
    # =====================================================================

    def to_validated_dataset(self, config: ValidationConfig | None = None) -> tuple[Dataset, ValidationResult]:
        """Generate validated DICOM dataset from this RT Dose IOD instance.
        
        This method combines dataset generation and validation into a single operation,
        ensuring the user doesn't need to call to_dataset() separately for validation.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            tuple[Dataset, ValidationResult]: Generated dataset and validation results
        """
        # Generate the dataset once
        dataset = self.to_dataset()
        
        # Validate the generated dataset
        validation_result = RTDoseIODValidator.validate(dataset, config)
        
        return dataset, validation_result
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Dose IOD instance (convenience method).
        
        This is a convenience method that generates a dataset and validates it,
        returning only the validation result. For efficiency, use to_validated_dataset()
        when you need both the dataset and validation results.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with errors and warnings
        """
        dataset = self.to_dataset()
        return RTDoseIODValidator.validate(dataset, config)

    def _validate_dependencies(self, *args, **kwargs) -> None:
        """Validate IOD-specific module dependencies.

        This method implements the abstract method from BaseIOD.
        RT Dose IOD dependencies are validated during construction.
        
        Note:
            Dependencies are validated during __init__, so this is a no-op.
        """
        pass

    def _validate_conditional_dependencies(self, image_plane_module, image_pixel_module,
                                         multi_frame_module, general_image_module) -> None:
        """Validate RT Dose IOD conditional module dependencies.

        Per DICOM PS3.3 A.18.3, certain modules are conditionally required
        based on the presence of grid-based dose data.
        
        Args:
            image_plane_module: Image Plane Module instance or None
            image_pixel_module: Image Pixel Module instance or None  
            multi_frame_module: Multi-frame Module instance or None
            general_image_module: General Image Module instance or None
            
        Raises:
            IODValidationError: If conditional dependencies are not satisfied
        """
        # Image-related modules require general_image_module
        if image_plane_module and not general_image_module:
            raise IODValidationError("image_plane_module requires general_image_module")
        if image_pixel_module and not general_image_module:
            raise IODValidationError("image_pixel_module requires general_image_module")
        if multi_frame_module and not general_image_module:
            raise IODValidationError("multi_frame_module requires general_image_module")

        # Multi-frame module requires pixel data to be multi-frame
        if multi_frame_module and image_pixel_module:
            # NOTE: This validation would need to check the actual pixel data structure
            # For now, we assume the caller has provided appropriate data
            pass
