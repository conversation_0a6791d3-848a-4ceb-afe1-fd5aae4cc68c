"""DICOM Information Object Definitions (IODs).

IODs are composed from DICOM modules using explicit module constructors
and on-demand dataset generation. This provides clear composition patterns
with comprehensive validation and type safety.
"""

from .base_iod import IODValidationError
from .ct_image_iod import CTImageIOD
from .rt_dose_iod import RTDoseIOD
from .rt_image_iod import RTImageIOD

__all__ = [
    "IODValidationError",
    "CTImageIOD",
    "RTDoseIOD",
    "RTImageIOD",
]