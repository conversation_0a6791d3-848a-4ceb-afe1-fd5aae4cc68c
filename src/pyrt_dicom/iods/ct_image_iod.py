"""CT IOD implementation for DICOM PS3.3 A.3.

The CT IOD specifies an image that has been created by a Computed Tomography 
imaging device. This implementation follows the new IOD design pattern
with explicit module constructors and on-demand dataset generation.
"""

from typing import Optional, Dict, Any
from datetime import datetime, date
import uuid
from pydicom import uid, Dataset

from pyrt_dicom.enums import (
    PatientSex, Modality, PhotometricInterpretation, PixelRepresentation,
    ImageType, MultiEnergyCTAcquisition, ContrastBolusAgent
)

from .base_iod import BaseIOD, IODValidationError
from ..validators import ValidationResult
from ..validators.modules.base_validator import ValidationConfig
from ..modules import (
    PatientModule,
    GeneralStudyModule,
    GeneralSeriesModule,
    FrameOfReferenceModule,
    GeneralEquipmentModule,
    GeneralAcquisitionModule,
    GeneralImageModule,
    ImagePlaneModule,
    ImagePixelModule,
    CTImageModule,
    SOPCommonModule,
    # Conditional modules
    SynchronizationModule,
    ContrastBolusModule,
    MultiEnergyCTImageModule,
    # Optional modules
    ClinicalTrialSubjectModule,
    PatientStudyModule,
    ClinicalTrialStudyModule,
    ClinicalTrialSeriesModule,
    GeneralReferenceModule,
    EnhancedPatientOrientationModule,
    DeviceModule,
    SpecimenModule,
    OverlayPlaneModule,
    VoiLutModule,
    CommonInstanceReferenceModule
)


class CTImageIOD(BaseIOD):
    """CT Image Information Object Definition for DICOM PS3.3 A.3.

    Specifies an image that has been created by a Computed Tomography imaging device.
    The CT Image IOD is designed to address the requirements for transfer of CT images
    used in radiotherapy treatment planning and medical imaging workflows.

    Required Modules (per DICOM PS3.3 A.3):
    - Patient Module (C.7.1.1) - M
    - General Study Module (C.7.2.1) - M
    - General Series Module (C.7.3.1) - M
    - Frame of Reference Module (C.7.4.1) - M
    - General Equipment Module (C.7.5.1) - M
    - General Acquisition Module (C.7.10.1) - M
    - General Image Module (C.7.6.1) - M
    - Image Plane Module (C.7.6.2) - M
    - Image Pixel Module (C.7.6.3) - M
    - CT Image Module (C.8.2.1) - M
    - SOP Common Module (C.12.1) - M

    Conditional Modules:
    - Synchronization Module (C.7.4.2) - C (Required if time synchronization was applied)
    - Contrast/Bolus Module (C.7.6.4) - C (Required if contrast media was used)
    - Multi-energy CT Image Module (C.8.2.2) - C (Required if Multi-energy CT Acquisition is YES)

    Optional Modules:
    - Clinical Trial Subject Module (C.7.1.3) - U
    - Patient Study Module (C.7.2.2) - U
    - Clinical Trial Study Module (C.7.2.3) - U
    - Clinical Trial Series Module (C.7.3.2) - U
    - General Reference Module (C.12.4) - U
    - Enhanced Patient Orientation Module (C.7.6.30) - U
    - Device Module (C.7.6.12) - U
    - Specimen Module (C.7.6.22) - U
    - Overlay Plane Module (C.9.2) - U
    - VOI LUT Module (C.11.2) - U
    - Common Instance Reference Module (C.12.2) - U

    Usage:
        # Create modules using new composition-based builder patterns
        patient_module = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex="M"
        )
        
        ct_image_module = CTImageModule.from_required_elements(
            kvp=120.0,
            exposure_time=1000
        )
        
        # Create CT IOD with imaging parameters
        ct_image_iod = CTImageIOD(
            patient_module=patient_module,
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid="*******.5"
            ),
            general_series_module=GeneralSeriesModule.from_required_elements(
                modality="CT",
                series_instance_uid="*******.6"
            ),
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid="*******.7"
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Siemens"
            ),
            general_acquisition_module=GeneralAcquisitionModule.from_required_elements(
                acquisition_date="20240101",
                acquisition_time="120000"
            ),
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            image_plane_module=ImagePlaneModule.from_required_elements(
                pixel_spacing=[0.97656, 0.97656],
                image_orientation_patient=[1,0,0,0,1,0],
                image_position_patient=[-250, -250, 100]
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=512,
                columns=512,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=0
            ),
            ct_image_module=ct_image_module,
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.5.1.4.1.1.2",  # CT Image Storage
                sop_instance_uid="*******.8"
            )
        )

        # Access CT parameters using new composition architecture
        ct_params = ct_image_iod.get_ct_parameters()
        print(f"kVp: {ct_params.get('kvp')} kV")

        # Generate dataset for export
        dataset = ct_image_iod.to_dataset()
        dataset.save_as("ct.dcm")
    """
    SOP_CLASS_UID = uid.CTImageStorage  #: CT Image Storage SOP Class UID per DICOM PS3.6

    def __init__(self,
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 general_series_module: GeneralSeriesModule,
                 frame_of_reference_module: FrameOfReferenceModule,
                 general_equipment_module: GeneralEquipmentModule,
                 general_acquisition_module: GeneralAcquisitionModule,
                 general_image_module: GeneralImageModule,
                 image_plane_module: ImagePlaneModule,
                 image_pixel_module: ImagePixelModule,
                 ct_image_module: CTImageModule,
                 sop_common_module: SOPCommonModule,
                 # Conditional modules
                 synchronization_module: Optional[SynchronizationModule] = None,
                 contrast_bolus_module: Optional[ContrastBolusModule] = None,
                 multi_energy_ct_image_module: Optional[MultiEnergyCTImageModule] = None,
                 # Optional modules
                 clinical_trial_subject_module: Optional[ClinicalTrialSubjectModule] = None,
                 patient_study_module: Optional[PatientStudyModule] = None,
                 clinical_trial_study_module: Optional[ClinicalTrialStudyModule] = None,
                 clinical_trial_series_module: Optional[ClinicalTrialSeriesModule] = None,
                 general_reference_module: Optional[GeneralReferenceModule] = None,
                 enhanced_patient_orientation_module: Optional[EnhancedPatientOrientationModule] = None,
                 device_module: Optional[DeviceModule] = None,
                 specimen_module: Optional[SpecimenModule] = None,
                 overlay_plane_module: Optional[OverlayPlaneModule] = None,
                 voi_lut_module: Optional[VoiLutModule] = None,
                 common_instance_reference_module: Optional[CommonInstanceReferenceModule] = None):
        """Create CT IOD from constituent modules.

        Module Requirements:
            M - Required
            C - Conditional
            U - Optional

        Args:
            patient_module (PatientModule): Patient demographic and identification information (M, C.7.1.1)
            general_study_module (GeneralStudyModule): Study-level metadata including study UID and dates (M, C.7.2.1)
            general_series_module (GeneralSeriesModule): Series-level metadata (must have modality='CT') (M, C.7.3.1)
            frame_of_reference_module (FrameOfReferenceModule): Spatial coordinate system reference for treatment planning (M, C.7.4.1)
            general_equipment_module (GeneralEquipmentModule): CT scanner equipment information and settings (M, C.7.5.1)
            general_acquisition_module (GeneralAcquisitionModule): Acquisition timing and parameters (M, C.7.10.1)
            general_image_module (GeneralImageModule): General image attributes including image type and acquisition info (M, C.7.6.1)
            image_plane_module (ImagePlaneModule): Spatial characteristics for image positioning (M, C.7.6.2)
            image_pixel_module (ImagePixelModule): Pixel data and image matrix information for CT slices (M, C.7.6.3)
            ct_image_module (CTImageModule): CT-specific parameters (kVp, mAs, reconstruction kernel, etc.) (M, C.8.2.1)
            sop_common_module (SOPCommonModule): SOP Class and Instance UIDs and creation metadata (M, C.12.1)

            synchronization_module (SynchronizationModule, optional): Time synchronization information (C, C.7.4.2).
                Required if time synchronization was applied.
            contrast_bolus_module (ContrastBolusModule, optional): Contrast agent administration details (C, C.7.6.4).
                Required if contrast media was used in this image.
            multi_energy_ct_image_module (MultiEnergyCTImageModule, optional): Multi-energy CT specific attributes (C, C.8.2.2).
                Required if Multi-energy CT Acquisition is YES.

            clinical_trial_subject_module (ClinicalTrialSubjectModule, optional): Clinical trial subject information (U, C.7.1.3)
            patient_study_module (PatientStudyModule, optional): Patient study specific information (U, C.7.2.2)
            clinical_trial_study_module (ClinicalTrialStudyModule, optional): Clinical trial study information (U, C.7.2.3)
            clinical_trial_series_module (ClinicalTrialSeriesModule, optional): Clinical trial series information (U, C.7.3.2)
            general_reference_module (GeneralReferenceModule, optional): Referenced objects information (U, C.12.4)
            enhanced_patient_orientation_module (EnhancedPatientOrientationModule, optional): Enhanced patient orientation (U, C.7.6.30)
            device_module (DeviceModule, optional): Additional device information beyond general equipment (U, C.7.6.12)
            specimen_module (SpecimenModule, optional): Specimen information if applicable (U, C.7.6.22)
            overlay_plane_module (OverlayPlaneModule, optional): Overlay graphics and annotations (U, C.9.2)
            voi_lut_module (VoiLutModule, optional): VOI LUT transformation for display (U, C.11.2)
            common_instance_reference_module (CommonInstanceReferenceModule, optional): Common instance references (U, C.12.2)

        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect

        Example:
            # Create CT IOD for treatment planning using new composition architecture
            ct_image_iod = CTImageIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John",
                    patient_id="12345",
                    patient_birth_date="19900101",
                    patient_sex="M"
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                general_series_module=GeneralSeriesModule.from_required_elements(
                    modality="CT",
                    series_instance_uid="*******.6"
                ),
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Siemens"
                ),
                general_acquisition_module=GeneralAcquisitionModule.from_required_elements(
                    acquisition_date="20240101",
                    acquisition_time="120000"
                ),
                general_image_module=GeneralImageModule.from_required_elements(
                    instance_number="1"
                ),
                image_plane_module=ImagePlaneModule.from_required_elements(
                    pixel_spacing=[0.97656, 0.97656],
                    image_orientation_patient=[1,0,0,0,1,0],
                    image_position_patient=[-250, -250, 100]
                ),
                image_pixel_module=ImagePixelModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation="MONOCHROME2",
                    rows=512,
                    columns=512,
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=0
                ),
                ct_image_module=CTImageModule.from_required_elements(
                    kvp=120.0,
                    exposure_time=1000
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
                    sop_instance_uid="*******.8"
                )
            )
        """
        super().__init__()

        # Validate CT-specific requirements
        if general_series_module['Modality'] != Modality.CT.value:
            raise IODValidationError(f"general_series_module must have modality={Modality.CT.value}")

        # Validate conditional module dependencies
        self._validate_conditional_dependencies(
            synchronization_module, contrast_bolus_module, multi_energy_ct_image_module,
            ct_image_module
        )

        # Store required modules as live references
        self._required_modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'general_series': general_series_module,
            'frame_of_reference': frame_of_reference_module,
            'general_equipment': general_equipment_module,
            'general_acquisition': general_acquisition_module,
            'general_image': general_image_module,
            'image_plane': image_plane_module,
            'image_pixel': image_pixel_module,
            'ct_image': ct_image_module,
            'sop_common': sop_common_module
        }

        # Add conditional and optional modules if provided
        optional_modules = {
            'synchronization': synchronization_module,
            'contrast_bolus': contrast_bolus_module,
            'multi_energy_ct_image': multi_energy_ct_image_module,
            'clinical_trial_subject': clinical_trial_subject_module,
            'patient_study': patient_study_module,
            'clinical_trial_study': clinical_trial_study_module,
            'clinical_trial_series': clinical_trial_series_module,
            'general_reference': general_reference_module,
            'enhanced_patient_orientation': enhanced_patient_orientation_module,
            'device': device_module,
            'specimen': specimen_module,
            'overlay_plane': overlay_plane_module,
            'voi_lut': voi_lut_module,
            'common_instance_reference': common_instance_reference_module
        }

        for name, module in optional_modules.items():
            if module is not None:
                self._required_modules[name] = module

    # =====================================================================
    # IOD-Specific Factory Methods for CT Workflows
    # =====================================================================

    @classmethod
    def for_diagnostic_imaging(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        study_description: str = "CT Diagnostic Imaging",
        manufacturer: str = "CT Scanner",
        kvp: float = 120.0,
        exposure_time: int = 1000,
        referring_physician: str = "",
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'CTImageIOD':
        """Create CT IOD optimized for diagnostic imaging workflow.
        
        This factory method creates a CT IOD with sensible defaults for
        diagnostic imaging. It auto-generates required UIDs and sets up
        the minimal required modules for a complete CT image.
        
        Args:
            patient_name (str): Patient's name in DICOM format (Family^Given^Middle^Prefix^Suffix)
            patient_id (str): Primary patient identifier
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            study_description (str): Description of the imaging study
            manufacturer (str): Name of CT scanner manufacturer
            kvp (float): X-ray tube voltage in kV (default: 120.0)
            exposure_time (int): Exposure time in ms (default: 1000)
            referring_physician (str): Referring physician name
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            CTImageIOD: Configured CT IOD for diagnostic imaging
            
        Example:
            # Create CT IOD for diagnostic imaging
            ct_image_iod = CTImageIOD.for_diagnostic_imaging(
                patient_name="Doe^John",
                patient_id="12345",
                patient_birth_date="19900101",
                patient_sex=PatientSex.MALE,
                study_description="Chest CT",
                manufacturer="Siemens Healthineers"
            )
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create required modules with diagnostic imaging defaults
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid,
            referring_physicians_name=referring_physician
        ).with_optional_elements(
            study_description=study_description
        )
        
        series_module = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="CT Images"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        acquisition_module = GeneralAcquisitionModule().with_optional_elements(
            acquisition_date=datetime.now().strftime("%Y%m%d"),
            acquisition_time=datetime.now().strftime("%H%M%S")
        )
        
        image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        image_plane_module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1,0,0,0,1,0],
            image_position_patient=[0.0, 0.0, 0.0]
        )
        
        image_pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        ct_image_module = CTImageModule.from_required_elements(
            image_type=ImageType.from_ct_characteristics().value,
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=0,
            rescale_slope=1,
            kvp=kvp
        ).with_optional_elements(
            exposure_time=float(exposure_time)
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            general_series_module=series_module,
            frame_of_reference_module=frame_ref_module,
            general_equipment_module=equipment_module,
            general_acquisition_module=acquisition_module,
            general_image_module=image_module,
            image_plane_module=image_plane_module,
            image_pixel_module=image_pixel_module,
            ct_image_module=ct_image_module,
            sop_common_module=sop_module
        )

    @classmethod
    def for_treatment_planning(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        study_description: str = "CT for Radiotherapy Treatment Planning",
        manufacturer: str = "CT Simulator",
        kvp: float = 120.0,
        exposure_time: int = 1000,
        referring_physician: str = "",
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'CTImageIOD':
        """Create CT IOD optimized for radiotherapy treatment planning workflow.
        
        This factory method creates a CT IOD configured for treatment planning,
        with appropriate defaults for radiation therapy workflows.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier  
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            study_description (str): Description of the planning study
            manufacturer (str): Name of CT simulator/scanner
            kvp (float): X-ray tube voltage in kV (default: 120.0)
            exposure_time (int): Exposure time in ms (default: 1000)
            referring_physician (str): Referring physician name
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            CTImageIOD: Configured CT IOD for treatment planning
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create modules for treatment planning workflow
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid,
            referring_physicians_name=referring_physician
        ).with_optional_elements(
            study_description=study_description
        )
        
        series_module = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT, 
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="Treatment Planning CT"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        acquisition_module = GeneralAcquisitionModule().with_optional_elements(
            acquisition_date=datetime.now().strftime("%Y%m%d"),
            acquisition_time=datetime.now().strftime("%H%M%S")
        )
        
        image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        # Use higher resolution for treatment planning
        image_plane_module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[0.97656, 0.97656],  # High resolution for planning
            image_orientation_patient=[1,0,0,0,1,0],
            image_position_patient=[0.0, 0.0, 0.0]
        )
        
        image_pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        ct_image_module = CTImageModule.from_required_elements(
            image_type=ImageType.from_ct_characteristics().value,
            kvp=kvp,
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=0,
            rescale_slope=1
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            general_series_module=series_module,
            frame_of_reference_module=frame_ref_module,
            general_equipment_module=equipment_module,
            general_acquisition_module=acquisition_module,
            general_image_module=image_module,
            image_plane_module=image_plane_module,
            image_pixel_module=image_pixel_module,
            ct_image_module=ct_image_module,
            sop_common_module=sop_module
        )

    @classmethod
    def for_research_study(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        protocol_name: str,
        study_description: str = "Research CT Study",
        manufacturer: str = "Research CT Scanner",
        kvp: float = 120.0,
        exposure_time: int = 1000,
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'CTImageIOD':
        """Create CT IOD optimized for research studies with clinical trial modules.
        
        This factory method creates a CT IOD configured for research workflows,
        including clinical trial modules for protocol compliance and data management.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier
            patient_birth_date (str | datetime | date): Patient birth date  
            patient_sex (PatientSex): Patient sex (M/F/O)
            protocol_name (str): Clinical trial protocol name
            study_description (str): Research study description
            manufacturer (str): Name of research CT scanner
            kvp (float): X-ray tube voltage in kV (default: 120.0)
            exposure_time (int): Exposure time in ms (default: 1000)
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            CTImageIOD: Configured CT IOD for research studies
        """
        # Create basic CT IOD
        ct_image_iod = cls.for_diagnostic_imaging(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex,
            study_description=study_description,
            manufacturer=manufacturer,
            kvp=kvp,
            exposure_time=exposure_time,
            study_instance_uid=study_instance_uid,
            series_instance_uid=series_instance_uid,
            frame_of_reference_uid=frame_of_reference_uid,
            sop_instance_uid=sop_instance_uid
        )
        
        # Add clinical trial context
        return ct_image_iod.with_clinical_trial_context(
            protocol_name=protocol_name
        )

    @classmethod
    def for_contrast_enhanced_imaging(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        contrast_agent: ContrastBolusAgent,
        study_description: str = "Contrast Enhanced CT",
        manufacturer: str = "CT Scanner",
        kvp: float = 120.0,
        exposure_time: int = 1000,
        referring_physician: str = "",
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'CTImageIOD':
        """Create CT IOD optimized for contrast-enhanced imaging workflows.
        
        This factory method creates a CT IOD configured for contrast-enhanced
        imaging with appropriate contrast/bolus module inclusion.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier  
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            contrast_agent (ContrastBolusAgent): Type of contrast agent used
            study_description (str): Description of contrast study
            manufacturer (str): Name of CT scanner
            kvp (float): X-ray tube voltage in kV (default: 120.0)
            exposure_time (int): Exposure time in ms (default: 1000)
            referring_physician (str): Referring physician name
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            CTImageIOD: Configured CT IOD for contrast-enhanced imaging
        """
        # Create base CT IOD
        ct_image_iod = cls.for_diagnostic_imaging(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex,
            study_description=study_description,
            manufacturer=manufacturer,
            kvp=kvp,
            exposure_time=exposure_time,
            referring_physician=referring_physician,
            study_instance_uid=study_instance_uid,
            series_instance_uid=series_instance_uid,
            frame_of_reference_uid=frame_of_reference_uid,
            sop_instance_uid=sop_instance_uid
        )
        
        # Add contrast agent information
        return ct_image_iod.with_contrast_enhancement(
            contrast_agent=contrast_agent
        )

    # =====================================================================
    # IOD-Specific Fluent API Methods for CT Feature Addition
    # =====================================================================

    def with_contrast_enhancement(
        self,
        contrast_agent: ContrastBolusAgent,
        contrast_bolus_volume: float | None = None,
        contrast_flow_rate: list[float] | None = None,
        contrast_bolus_start_time: str | None = None,
        contrast_bolus_stop_time: str | None = None
    ) -> 'CTImageIOD':
        """Add contrast/bolus module for contrast-enhanced imaging.
        
        Configures the Contrast/Bolus Module with contrast agent information
        for contrast-enhanced CT imaging workflows.
        
        Args:
            contrast_agent (ContrastBolusAgent): Type of contrast agent
            contrast_bolus_volume (float | None): Volume of contrast in ml
            contrast_flow_rate (list[float] | None): Flow rates in ml/s
            contrast_bolus_start_time (str | None): Start time of contrast injection
            contrast_bolus_stop_time (str | None): Stop time of contrast injection
            
        Returns:
            CTImageIOD: Self with contrast information added
            
        Example:
            ct_image_iod.with_contrast_enhancement(
                contrast_agent=ContrastBolusAgent.IODINE,
                contrast_bolus_volume=100.0,
                contrast_flow_rate=[3.0, 2.0],
                contrast_bolus_start_time="120000",
                contrast_bolus_stop_time="120030"
            )
        """
        contrast_module = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent=contrast_agent
        )
        
        # Add optional contrast elements
        optional_elements = {}
        if contrast_bolus_volume is not None:
            optional_elements['contrast_bolus_volume'] = contrast_bolus_volume
        if contrast_flow_rate is not None:
            optional_elements['contrast_flow_rate'] = contrast_flow_rate
        if contrast_bolus_start_time is not None:
            optional_elements['contrast_bolus_start_time'] = contrast_bolus_start_time
        if contrast_bolus_stop_time is not None:
            optional_elements['contrast_bolus_stop_time'] = contrast_bolus_stop_time
            
        if optional_elements:
            contrast_module.with_optional_elements(**optional_elements)
        
        self._required_modules['contrast_bolus'] = contrast_module
        return self

    def with_multi_energy_ct(
        self,
        multi_energy_acquisition: MultiEnergyCTAcquisition = MultiEnergyCTAcquisition.YES,
        energy_weighting_factor: float | None = None,
        photon_energy_lower_bound: float | None = None,
        photon_energy_upper_bound: float | None = None
    ) -> 'CTImageIOD':
        """Add multi-energy CT module for advanced CT imaging.
        
        Configures the Multi-energy CT Image Module for dual-energy or
        multi-energy CT imaging capabilities.
        
        Args:
            multi_energy_acquisition (MultiEnergyCTAcquisition): Multi-energy CT acquisition flag
            energy_weighting_factor (float | None): Energy weighting factor
            photon_energy_lower_bound (float | None): Lower photon energy bound in keV
            photon_energy_upper_bound (float | None): Upper photon energy bound in keV
            
        Returns:
            CTImageIOD: Self with multi-energy CT capabilities added
            
        Example:
            ct_image_iod.with_multi_energy_ct(
                multi_energy_acquisition=MultiEnergyCTAcquisition.YES,
                energy_weighting_factor=0.5,
                photon_energy_lower_bound=40.0,
                photon_energy_upper_bound=140.0
            )
        """
        # Create a basic multi-energy CT acquisition sequence item
        acquisition_item = MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
            x_ray_source_sequence=[
                MultiEnergyCTImageModule.create_x_ray_source_item(
                    x_ray_source_index=1,
                    x_ray_source_id="SOURCE_001",
                    multi_energy_source_technique="SWITCHING_SOURCE",
                    source_start_datetime=datetime.now().strftime("%Y%m%d%H%M%S.%f"),
                    source_end_datetime=datetime.now().strftime("%Y%m%d%H%M%S.%f"),
                    switching_phase_number=1
                )
            ],
            x_ray_detector_sequence=[
                MultiEnergyCTImageModule.create_x_ray_detector_item(
                    x_ray_detector_index=1,
                    x_ray_detector_id="DETECTOR_001",
                    multi_energy_detector_type="PHOTON_COUNTING",
                    nominal_max_energy=120.0,
                    nominal_min_energy=20.0
                )
            ],
            path_sequence=[
                MultiEnergyCTImageModule.create_path_item(
                    path_index=1,
                    referenced_x_ray_source_index=1,
                    referenced_x_ray_detector_index=1
                )
            ]
        )
        
        multi_energy_module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=[acquisition_item]
        )
        
        # Note: The energy_weighting_factor, photon_energy_lower_bound, and photon_energy_upper_bound
        # are not direct optional elements of the Multi-energy CT Image Module in DICOM PS3.3 C.8.2.2
        # These would typically be encoded within the acquisition sequence items or other related modules
        # For now, we store the module without these optional parameters
        
        self._required_modules['multi_energy_ct_image'] = multi_energy_module
        return self

    def with_clinical_trial_context(
        self,
        protocol_name: str,
        site_id: str | None = None,
        sponsor_name: str | None = None,
        subject_reading_id: str | None = None,
        subject_id: str | None = None
    ) -> 'CTImageIOD':
        """Add clinical trial modules for research study compliance.
        
        Configures clinical trial modules for protocol compliance, subject
        management, and research data tracking.
        
        Args:
            protocol_name (str): Clinical trial protocol name/identifier
            site_id (str | None): Clinical trial site identifier
            sponsor_name (str | None): Research sponsor organization
            subject_reading_id (str | None): Subject reading identifier
            subject_id (str | None): Clinical trial subject identifier
            
        Returns:
            CTImageIOD: Self with clinical trial modules added
            
        Example:
            ct_image_iod.with_clinical_trial_context(
                protocol_name="RTOG-1234",
                site_id="Site-001",
                sponsor_name="NCI Clinical Trials Network"
            )
        """
        # Add Clinical Trial Subject Module
        trial_subject_module = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name=sponsor_name or "Research Sponsor",
            clinical_trial_protocol_id=f"PROTO-{uuid.uuid4().hex[:8].upper()}",
            clinical_trial_protocol_name=protocol_name,
            clinical_trial_site_id=site_id or "",
            clinical_trial_site_name=""
        )
        
        # Add subject identification (Type 1C requirement)
        trial_subject_module.with_subject_identification(
            clinical_trial_subject_id=subject_id or f"SUBJ-{uuid.uuid4().hex[:8].upper()}"
        )
        
        if subject_reading_id:
            trial_subject_module.with_optional_elements(
                issuer_of_clinical_trial_subject_reading_id=subject_reading_id
            )
        
        # Add Clinical Trial Study Module  
        trial_study_module = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id=""
        )
        
        # Add Clinical Trial Series Module
        trial_series_module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name=""
        )
        
        # Store the clinical trial modules
        self._required_modules.update({
            'clinical_trial_subject': trial_subject_module,
            'clinical_trial_study': trial_study_module,
            'clinical_trial_series': trial_series_module
        })
        
        return self

    def with_time_synchronization(
        self,
        synchronization_trigger: str,
        acquisition_time_synchronized: str = "Y"
    ) -> 'CTImageIOD':
        """Add time synchronization module for temporal imaging studies.
        
        Configures the Synchronization Module for time-synchronized imaging
        studies requiring temporal correlation.
        
        Args:
            synchronization_trigger (str): Trigger for synchronization
            acquisition_time_synchronized (str): Whether acquisition was time synchronized
            
        Returns:
            CTImageIOD: Self with time synchronization added
            
        Example:
            ct_image_iod.with_time_synchronization(
                synchronization_trigger="ECG",
                acquisition_time_synchronized="Y"
            )
        """
        sync_module = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid=uid.generate_uid(),
            synchronization_trigger=synchronization_trigger,
            acquisition_time_synchronized=acquisition_time_synchronized
        )
        
        self._required_modules['synchronization'] = sync_module
        return self

    # =====================================================================
    # IOD-Specific Properties and Methods
    # =====================================================================

    @property
    def has_contrast_enhancement(self) -> bool:
        """Check if contrast agent was used.

        Returns:
            bool: True if contrast bolus module is present
        """
        return 'contrast_bolus' in self._required_modules

    @property
    def is_multi_energy_ct(self) -> bool:
        """Check if this is a multi-energy CT image.

        Returns:
            bool: True if multi-energy CT module is present
        """
        return 'multi_energy_ct_image' in self._required_modules

    @property
    def has_time_synchronization(self) -> bool:
        """Check if time synchronization was applied.

        Returns:
            bool: True if synchronization module is present
        """
        return 'synchronization' in self._required_modules

    @property
    def has_clinical_trial_data(self) -> bool:
        """Check if clinical trial modules are included.

        Returns:
            bool: True if clinical trial modules are present
        """
        return any(key.startswith('clinical_trial') for key in self._required_modules.keys())

    @property
    def image_dimensions(self) -> tuple[int, int]:
        """Get image matrix dimensions.

        Returns:
            tuple[int, int]: (rows, columns) from image pixel module
        """
        image_pixel_module = self._required_modules.get('image_pixel')
        if image_pixel_module:
            rows = image_pixel_module.get('Rows', 0)
            columns = image_pixel_module.get('Columns', 0)
            return (rows, columns)
        return (0, 0)

    @property
    def pixel_spacing(self) -> list[float]:
        """Get physical pixel spacing.

        Returns:
            list[float]: [row_spacing, column_spacing] in mm from image plane module
        """
        image_plane_module = self._required_modules.get('image_plane')
        if image_plane_module:
            return image_plane_module.get('PixelSpacing', [0.0, 0.0])
        return [0.0, 0.0]

    @property
    def slice_thickness(self) -> float:
        """Get slice thickness.

        Returns:
            float: Slice thickness in mm from image plane module
        """
        image_plane_module = self._required_modules.get('image_plane')
        if image_plane_module:
            thickness = image_plane_module.get('SliceThickness', 0.0)
            # Handle cases where SliceThickness might be an empty string or None
            if thickness == '' or thickness is None:
                return 0.0
            try:
                return float(thickness)
            except (ValueError, TypeError):
                return 0.0
        return 0.0

    def get_ct_parameters(self) -> Dict[str, Any]:
        """Get CT acquisition parameters summary.

        Returns:
            Dict[str, Any]: Dictionary with key CT imaging parameters
        """
        ct_module = self._required_modules.get('ct_image')
        if not ct_module:
            return {}

        summary = {
            'kvp': ct_module.get('KVP', None),
            'exposure_time': ct_module.get('ExposureTime', None),
            'x_ray_tube_current': ct_module.get('XRayTubeCurrent', None),
            'exposure': ct_module.get('Exposure', None),
            'filter_type': ct_module.get('FilterType', None),
            'generator_power': ct_module.get('GeneratorPower', None),
            'focal_spots': ct_module.get('FocalSpots', None),
            'convolution_kernel': ct_module.get('ConvolutionKernel', None),
            'patient_position': ct_module.get('PatientPosition', None),
            'acquisition_type': ct_module.get('AcquisitionType', None),
            'slice_thickness': ct_module.get('SliceThickness', None),
            'pitch': ct_module.get('SpiralPitchFactor', None)
        }

        return {k: v for k, v in summary.items() if v is not None}

    def get_spatial_information(self) -> Dict[str, Any]:
        """Get spatial positioning and orientation information.

        Returns:
            Dict[str, Any]: Dictionary with spatial parameters for image positioning
        """
        spatial_info = {}

        # Frame of reference information
        frame_ref_module = self._required_modules.get('frame_of_reference')
        if frame_ref_module:
            spatial_info['frame_of_reference_uid'] = frame_ref_module.get('FrameOfReferenceUID', None)
            spatial_info['position_reference_indicator'] = frame_ref_module.get('PositionReferenceIndicator', None)

        # Image plane information  
        image_plane_module = self._required_modules.get('image_plane')
        if image_plane_module:
            spatial_info.update({
                'pixel_spacing': image_plane_module.get('PixelSpacing', None),
                'image_orientation_patient': image_plane_module.get('ImageOrientationPatient', None),
                'image_position_patient': image_plane_module.get('ImagePositionPatient', None),
                'slice_thickness': image_plane_module.get('SliceThickness', None),
                'slice_location': image_plane_module.get('SliceLocation', None)
            })

        # Image pixel information
        image_pixel_module = self._required_modules.get('image_pixel')
        if image_pixel_module:
            spatial_info.update({
                'rows': image_pixel_module.get('Rows', None),
                'columns': image_pixel_module.get('Columns', None),
                'samples_per_pixel': image_pixel_module.get('SamplesPerPixel', None),
                'bits_allocated': image_pixel_module.get('BitsAllocated', None),
                'photometric_interpretation': image_pixel_module.get('PhotometricInterpretation', None)
            })

        return spatial_info

    def get_contrast_information(self) -> Dict[str, Any]:
        """Get contrast enhancement information.

        Returns:
            Dict[str, Any]: Dictionary with contrast parameters if available
        """
        if not self.has_contrast_enhancement:
            return {}

        contrast_module = self._required_modules.get('contrast_bolus')
        if not contrast_module:
            return {}

        return {
            'contrast_bolus_agent': contrast_module.get('ContrastBolusAgent', None),
            'contrast_bolus_volume': contrast_module.get('ContrastBolusVolume', None),
            'contrast_flow_rate': contrast_module.get('ContrastFlowRate', None),
            'contrast_bolus_start_time': contrast_module.get('ContrastBolusStartTime', None),
            'contrast_bolus_stop_time': contrast_module.get('ContrastBolusStopTime', None)
        }

    # =====================================================================
    # IOD-Specific Validation
    # =====================================================================

    def to_validated_dataset(self, config: ValidationConfig | None = None) -> tuple[Dataset, ValidationResult]:
        """Generate validated DICOM dataset from this CT IOD instance.
        
        This method combines dataset generation and validation into a single operation,
        ensuring the user doesn't need to call to_dataset() separately for validation.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            tuple[Dataset, ValidationResult]: Generated dataset and validation results
        """
        # Generate the dataset once
        dataset = self.to_dataset()
        
        # For now, create a basic validation result - would use CTImageIODValidator when implemented
        validation_result = ValidationResult()
        
        return dataset, validation_result
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this CT IOD instance (convenience method).
        
        This is a convenience method that generates a dataset and validates it,
        returning only the validation result.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with errors and warnings
        """
        # dataset = self.to_dataset()
        # For now, create a basic validation result - would use CTImageIODValidator when implemented
        return ValidationResult()

    def _validate_dependencies(self, *args, **kwargs) -> None:
        """Validate IOD-specific module dependencies.

        This method implements the abstract method from BaseIOD.
        CT IOD dependencies are validated during construction.
        
        Note:
            Dependencies are validated during __init__, so this is a no-op.
        """
        pass

    def _validate_conditional_dependencies(self, synchronization_module, contrast_bolus_module,
                                         multi_energy_ct_image_module, ct_image_module) -> None:
        """Validate CT IOD conditional module dependencies.

        Per DICOM PS3.3 A.3, certain modules are conditionally required
        based on CT acquisition parameters and imaging techniques.
        
        Args:
            synchronization_module: Synchronization Module instance or None
            contrast_bolus_module: Contrast/Bolus Module instance or None  
            multi_energy_ct_image_module: Multi-energy CT Image Module instance or None
            ct_image_module: CT Image Module instance for checking conditions
            
        Raises:
            IODValidationError: If conditional dependencies are not satisfied
        """
        # Multi-energy CT specific validation
        if multi_energy_ct_image_module:
            # If Multi-energy CT Image Module is present, verify proper setup
            multi_energy_acquisition = multi_energy_ct_image_module.get('MultiEnergyCTAcquisition', None)
            
            if multi_energy_acquisition == MultiEnergyCTAcquisition.YES.value:
                # Per A.3.3.1: Contrast/Bolus Module shall be present if contrast was administered
                # Note: This would require additional context about contrast administration
                # For now, we'll just validate the module structure
                pass