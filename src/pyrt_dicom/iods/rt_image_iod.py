"""RT Image IOD implementation for DICOM PS3.3 A.17.3.

The RT Image IOD combines radiotherapy imaging modules into a complete 
DICOM RT Image object for portal imaging, verification imaging, and 
treatment guidance. This implementation follows the IOD design pattern
with explicit module constructors and on-demand dataset generation.
"""

from typing import Optional, Any
from datetime import datetime, date
import uuid
from pydicom import uid, Dataset

from pyrt_dicom.enums import (
    PatientSex, Modality, PhotometricInterpretation, PixelRepresentation,
    RTImagePlane, ConversionType
)

from .base_iod import BaseIOD, IODValidationError
from ..validators import ValidationResult
from ..validators.modules.base_validator import ValidationConfig
from ..modules import (
    PatientModule,
    GeneralStudyModule,
    RTSeriesModule,
    GeneralEquipmentModule,
    GeneralAcquisitionModule,
    GeneralImageModule,
    ImagePixelModule,
    RTImageModule,
    SOPCommonModule,
    # Optional modules
    PatientStudyModule,
    FrameOfReferenceModule,
    DeviceModule,
    GeneralReferenceModule,
    ContrastBolusModule,
    CineModule,
    MultiFrameModule,
    ModalityLutModule,
    VoiLutModule,
    ApprovalModule,
    FrameExtractionModule,
    CommonInstanceReferenceModule,
    ClinicalTrialSubjectModule,
    ClinicalTrialStudyModule,
    ClinicalTrialSeriesModule
)


class RTImageIOD(BaseIOD):
    """RT Image Information Object Definition for DICOM PS3.3 A.17.3.

    Combines radiotherapy imaging modules into a complete DICOM RT Image object
    for portal imaging, verification imaging, and treatment guidance. Such images
    have conical imaging geometry and may be acquired directly from devices or
    digitized using film digitizers.

    The RT Image IOD addresses requirements for image transfer found in general
    radiotherapy applications performed on conventional simulators, virtual
    simulators, and portal imaging devices. Numeric beam data parameters may
    also be recorded with the image.

    Required Modules (per DICOM PS3.3 A.17.3):
    - Patient Module (C.7.1.1) - M
    - General Study Module (C.7.2.1) - M
    - RT Series Module (C.8.8.1) - M
    - General Equipment Module (C.7.5.1) - M
    - General Acquisition Module (C.7.10.1) - M
    - General Image Module (C.7.6.1) - M
    - Image Pixel Module (C.7.6.3) - M
    - RT Image Module (C.8.8.2) - M
    - SOP Common Module (C.12.1) - M

    Conditional Modules:
    - Contrast/Bolus Module (C.7.6.4) - C (Required if contrast media was used)
    - Cine Module (C.7.6.5) - C (Required if multi-frame image is a cine image)
    - Multi-frame Module (C.7.6.6) - C (Required if pixel data is multi-frame)
    - Frame Extraction Module (C.12.3) - C (Required if SOP Instance created from frame-level retrieve)

    Optional Modules:
    - Clinical Trial Subject Module (C.7.1.3) - U
    - Patient Study Module (C.7.2.2) - U
    - Clinical Trial Study Module (C.7.2.3) - U
    - Clinical Trial Series Module (C.7.3.2) - U
    - Frame of Reference Module (C.7.4.1) - U
    - General Reference Module (C.12.4) - U
    - Device Module (C.7.6.12) - U
    - Modality LUT Module (C.11.1) - U
    - VOI LUT Module (C.11.2) - U
    - Approval Module (C.8.8.16) - U
    - Common Instance Reference Module (C.12.2) - U

    Usage:
        # Create modules using composition-based builder patterns
        patient_module = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        rt_image_module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image 1",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            conversion_type=ConversionType.DI,
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        # Create RT Image IOD with portal imaging
        rt_image = RTImageIOD(
            patient_module=patient_module,
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid="*******.5"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality=Modality.RTIMAGE,
                series_instance_uid="*******.6"
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Varian Medical Systems"
            ),
            general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                rows=1024,
                columns=768,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED
            ),
            rt_image_module=rt_image_module,
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.1",
                sop_instance_uid="*******.8"
            )
        )

        # Access RT imaging parameters
        image_params = rt_image.get_rt_imaging_parameters()
        print(f"RT Image: {image_params['image_label']}, SID: {image_params.get('rt_image_sid')} mm")

        # Generate dataset for export
        dataset = rt_image.to_dataset()
        dataset.save_as("rt_image.dcm")
    """
    SOP_CLASS_UID = uid.RTImageStorage  #: RT Image Storage SOP Class UID per DICOM PS3.6

    def __init__(self,
                 patient_module: PatientModule,
                 general_study_module: GeneralStudyModule,
                 rt_series_module: RTSeriesModule,
                 general_equipment_module: GeneralEquipmentModule,
                 general_acquisition_module: GeneralAcquisitionModule,
                 general_image_module: GeneralImageModule,
                 image_pixel_module: ImagePixelModule,
                 rt_image_module: RTImageModule,
                 sop_common_module: SOPCommonModule,
                 # Optional modules
                 patient_study_module: Optional[PatientStudyModule] = None,
                 frame_of_reference_module: Optional[FrameOfReferenceModule] = None,
                 device_module: Optional[DeviceModule] = None,
                 general_reference_module: Optional[GeneralReferenceModule] = None,
                 # Conditional modules
                 contrast_bolus_module: Optional[ContrastBolusModule] = None,
                 cine_module: Optional[CineModule] = None,
                 multi_frame_module: Optional[MultiFrameModule] = None,
                 frame_extraction_module: Optional[FrameExtractionModule] = None,
                 # Enhancement modules
                 modality_lut_module: Optional[ModalityLutModule] = None,
                 voi_lut_module: Optional[VoiLutModule] = None,
                 approval_module: Optional[ApprovalModule] = None,
                 common_instance_reference_module: Optional[CommonInstanceReferenceModule] = None,
                 # Clinical trial modules
                 clinical_trial_subject_module: Optional[ClinicalTrialSubjectModule] = None,
                 clinical_trial_study_module: Optional[ClinicalTrialStudyModule] = None,
                 clinical_trial_series_module: Optional[ClinicalTrialSeriesModule] = None):
        """Create RT Image IOD from constituent modules.

        Module Requirements:
            M - Required
            C - Conditional
            U - Optional

        Args:
            patient_module (PatientModule): Patient demographic and identification information (M, C.7.1.1)
            general_study_module (GeneralStudyModule): Study-level metadata including study UID and dates (M, C.7.2.1)
            rt_series_module (RTSeriesModule): RT series metadata (must have modality='RTIMAGE') (M, C.8.8.1)
            general_equipment_module (GeneralEquipmentModule): Imaging equipment information (LINAC, portal imager, etc.) (M, C.7.5.1)
            general_acquisition_module (GeneralAcquisitionModule): Acquisition timing and sequence information (M, C.7.10.1)
            general_image_module (GeneralImageModule): General image attributes including image type and acquisition info (M, C.7.6.1)
            image_pixel_module (ImagePixelModule): Pixel data and image matrix information for RT images (M, C.7.6.3)
            rt_image_module (RTImageModule): RT-specific imaging parameters (beam angles, SID, field size, etc.) (M, C.8.8.2)
            sop_common_module (SOPCommonModule): SOP Class and Instance UIDs and creation metadata (M, C.12.1)
            
            patient_study_module (PatientStudyModule, optional): Additional patient study information (U, C.7.2.2)
            frame_of_reference_module (FrameOfReferenceModule, optional): Spatial coordinate system reference for treatment geometry (U, C.7.4.1)
            device_module (DeviceModule, optional): Detailed device information for imaging hardware (U, C.7.6.12)
            general_reference_module (GeneralReferenceModule, optional): General reference information (U, C.12.4)
            
            contrast_bolus_module (ContrastBolusModule, optional): Contrast agent administration details (C, C.7.6.4).
                Required if contrast media was used in this image.
            cine_module (CineModule, optional): Cine playback attributes for dynamic imaging (C, C.7.6.5).
                Required if multi-frame image is a cine image.
            multi_frame_module (MultiFrameModule, optional): Multi-frame attributes for cine or time-series imaging (C, C.7.6.6).
                Required if pixel data is multi-frame data.
            frame_extraction_module (FrameExtractionModule, optional): Frame extraction metadata (C, C.12.3).
                Required if SOP Instance created in response to frame-level retrieve request.
            
            modality_lut_module (ModalityLutModule, optional): Modality LUT for pixel value interpretation (U, C.11.1)
            voi_lut_module (VoiLutModule, optional): VOI LUT for display level mapping (U, C.11.2)
            approval_module (ApprovalModule, optional): Image approval and quality assurance information (U, C.8.8.16)
            common_instance_reference_module (CommonInstanceReferenceModule, optional): Instance reference information (U, C.12.2)
            
            clinical_trial_subject_module (ClinicalTrialSubjectModule, optional): Clinical trial subject information (U, C.7.1.3)
            clinical_trial_study_module (ClinicalTrialStudyModule, optional): Clinical trial study information (U, C.7.2.3)
            clinical_trial_series_module (ClinicalTrialSeriesModule, optional): Clinical trial series information (U, C.7.3.2)

        Raises:
            IODValidationError: If module dependencies are not satisfied or modality is incorrect

        Example:
            # Create RT Image IOD for portal verification using composition architecture
            rt_image = RTImageIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Doe^John",
                    patient_id="12345",
                    patient_birth_date="19900101",
                    patient_sex=PatientSex.MALE
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid="*******.5"
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality=Modality.RTIMAGE,
                    series_instance_uid="*******.6",
                    series_description="Portal Images"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Varian Medical Systems",
                    station_name="TrueBeam STx"
                ),
                general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
                general_image_module=GeneralImageModule.from_required_elements(
                    instance_number="1"
                ),
                image_pixel_module=ImagePixelModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                    rows=1024,
                    columns=768,
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=PixelRepresentation.UNSIGNED
                ),
                rt_image_module=RTImageModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=PixelRepresentation.UNSIGNED,
                    rt_image_label="Portal Image 1",
                    image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
                    conversion_type=ConversionType.DI,
                    rt_image_plane=RTImagePlane.NORMAL
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.1",
                    sop_instance_uid="*******.8"
                ),
                # Optional: Add frame of reference for spatial alignment
                frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                    frame_of_reference_uid="*******.7"
                )
            )
        """
        super().__init__()

        # Validate RT Image-specific requirements
        if rt_series_module['Modality'] != Modality.RTIMAGE.value:
            raise IODValidationError(f"rt_series_module must have modality={Modality.RTIMAGE.value}")

        # Validate conditional module dependencies
        self._validate_conditional_dependencies(contrast_bolus_module, cine_module,
                                              multi_frame_module, frame_extraction_module)

        # Store all modules as live references in _modules for BaseIOD compatibility
        self._required_modules = {
            'patient': patient_module,
            'general_study': general_study_module,
            'rt_series': rt_series_module,
            'general_equipment': general_equipment_module,
            'general_acquisition': general_acquisition_module,
            'general_image': general_image_module,
            'image_pixel': image_pixel_module,
            'rt_image': rt_image_module,
            'sop_common': sop_common_module
        }

        # Add optional and conditional modules if provided
        optional_modules = {
            'patient_study': patient_study_module,
            'frame_of_reference': frame_of_reference_module,
            'device': device_module,
            'general_reference': general_reference_module,
            'contrast_bolus': contrast_bolus_module,
            'cine': cine_module,
            'multi_frame': multi_frame_module,
            'frame_extraction': frame_extraction_module,
            'modality_lut': modality_lut_module,
            'voi_lut': voi_lut_module,
            'approval': approval_module,
            'common_instance_reference': common_instance_reference_module,
            'clinical_trial_subject': clinical_trial_subject_module,
            'clinical_trial_study': clinical_trial_study_module,
            'clinical_trial_series': clinical_trial_series_module
        }

        for name, module in optional_modules.items():
            if module is not None:
                self._required_modules[name] = module

    # =====================================================================
    # IOD-Specific Factory Methods for RT Image Workflows
    # =====================================================================

    @classmethod
    def for_portal_imaging(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        study_description: str = "Portal Imaging",
        manufacturer: str = "Portal Imaging Device",
        rt_image_label: str = "Portal Image",
        image_plane: RTImagePlane = RTImagePlane.NORMAL,
        referring_physician: str = "",
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTImageIOD':
        """Create RT Image IOD optimized for portal imaging workflow.
        
        This factory method creates an RT Image IOD configured for portal imaging
        and verification workflows typically used in radiation therapy. Sets up
        appropriate defaults for portal images acquired during treatment.
        
        Args:
            patient_name (str): Patient's name in DICOM format (Family^Given^Middle^Prefix^Suffix)
            patient_id (str): Primary patient identifier
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            study_description (str): Description of the imaging study
            manufacturer (str): Name of portal imaging system
            rt_image_label (str): Label for the RT image
            image_plane (RTImagePlane): Image plane orientation (default: NORMAL)
            referring_physician (str): Referring physician name
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTImageIOD: Configured RT Image IOD for portal imaging
            
        Example:
            # Create portal image IOD
            rt_image = RTImageIOD.for_portal_imaging(
                patient_name="Doe^John",
                patient_id="12345",
                patient_birth_date="19900101",
                patient_sex=PatientSex.MALE,
                study_description="Portal Image Verification",
                manufacturer="Varian Portal Vision aS1000"
            )
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create required modules with portal imaging defaults
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid,
            referring_physicians_name=referring_physician
        ).with_optional_elements(
            study_description=study_description
        )
        
        series_module = RTSeriesModule.from_required_elements(
            modality=Modality.RTIMAGE,
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="Portal Verification Images"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        acquisition_module = GeneralAcquisitionModule.from_required_elements()
        
        image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        # Default pixel module for portal imaging (typical portal detector)
        pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=1024,
            columns=768,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        rt_image_module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label=rt_image_label,
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            conversion_type=ConversionType.DI,
            rt_image_plane=image_plane
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            rt_series_module=series_module,
            general_equipment_module=equipment_module,
            general_acquisition_module=acquisition_module,
            general_image_module=image_module,
            image_pixel_module=pixel_module,
            rt_image_module=rt_image_module,
            sop_common_module=sop_module,
            frame_of_reference_module=frame_ref_module
        )

    @classmethod
    def for_verification_imaging(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        verification_description: str = "Treatment Verification",
        manufacturer: str = "Verification Imaging System",
        rt_image_label: str = "Verification Image",
        image_plane: RTImagePlane = RTImagePlane.NORMAL,
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTImageIOD':
        """Create RT Image IOD optimized for treatment verification workflow.
        
        This factory method creates an RT Image IOD configured for treatment
        verification and quality assurance workflows. Sets up appropriate
        defaults for verification images acquired to validate treatment delivery.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier  
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            verification_description (str): Description of verification procedure
            manufacturer (str): Name of verification imaging system
            rt_image_label (str): Label for the verification image
            image_plane (RTImagePlane): Image plane orientation (default: NORMAL)
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTImageIOD: Configured RT Image IOD for verification imaging
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create modules for verification workflow
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid
        ).with_optional_elements(
            study_description=f"Treatment Verification - {verification_description}"
        )
        
        series_module = RTSeriesModule.from_required_elements(
            modality=Modality.RTIMAGE,
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="Treatment Verification Images"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        acquisition_module = GeneralAcquisitionModule.from_required_elements()
        
        image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        # Default pixel module for verification imaging
        pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        rt_image_module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label=rt_image_label,
            image_type=["ORIGINAL", "PRIMARY", "VERIFICATION"],
            conversion_type=ConversionType.DI,
            rt_image_plane=image_plane
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            rt_series_module=series_module,
            general_equipment_module=equipment_module,
            general_acquisition_module=acquisition_module,
            general_image_module=image_module,
            image_pixel_module=pixel_module,
            rt_image_module=rt_image_module,
            sop_common_module=sop_module,
            frame_of_reference_module=frame_ref_module
        )

    @classmethod
    def for_simulator_imaging(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        simulator_description: str = "Simulator Imaging",
        manufacturer: str = "Conventional Simulator",
        rt_image_label: str = "Simulator Image",
        image_plane: RTImagePlane = RTImagePlane.NORMAL,
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTImageIOD':
        """Create RT Image IOD optimized for conventional or virtual simulator workflow.
        
        This factory method creates an RT Image IOD configured for simulator
        imaging workflows used in treatment planning and setup verification.
        Sets up appropriate defaults for images from conventional simulators
        or virtual simulators.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            simulator_description (str): Description of simulator imaging
            manufacturer (str): Name of simulator system
            rt_image_label (str): Label for the simulator image
            image_plane (RTImagePlane): Image plane orientation (default: NORMAL)
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTImageIOD: Configured RT Image IOD for simulator imaging
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create modules for simulator workflow
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid
        ).with_optional_elements(
            study_description=f"Simulator Imaging - {simulator_description}"
        )
        
        series_module = RTSeriesModule.from_required_elements(
            modality=Modality.RTIMAGE,
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="Simulator Images"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        acquisition_module = GeneralAcquisitionModule.from_required_elements()
        
        image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        # Default pixel module for simulator imaging
        pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        rt_image_module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label=rt_image_label,
            image_type=["ORIGINAL", "PRIMARY", "SIMULATOR"],
            conversion_type=ConversionType.DI,
            rt_image_plane=image_plane
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            rt_series_module=series_module,
            general_equipment_module=equipment_module,
            general_acquisition_module=acquisition_module,
            general_image_module=image_module,
            image_pixel_module=pixel_module,
            rt_image_module=rt_image_module,
            sop_common_module=sop_module,
            frame_of_reference_module=frame_ref_module
        )

    @classmethod
    def for_drr_imaging(
        cls,
        patient_name: str,
        patient_id: str,
        patient_birth_date: str | datetime | date,
        patient_sex: PatientSex,
        drr_description: str = "Digitally Reconstructed Radiograph",
        manufacturer: str = "Treatment Planning System",
        rt_image_label: str = "DRR",
        image_plane: RTImagePlane = RTImagePlane.NORMAL,
        study_instance_uid: str | None = None,
        series_instance_uid: str | None = None,
        frame_of_reference_uid: str | None = None,
        sop_instance_uid: str | None = None
    ) -> 'RTImageIOD':
        """Create RT Image IOD optimized for Digitally Reconstructed Radiograph workflow.
        
        This factory method creates an RT Image IOD configured for DRR
        generation and comparison workflows used in image guidance and
        treatment planning. Sets up appropriate defaults for calculated
        projection images from 3D CT data.
        
        Args:
            patient_name (str): Patient's name
            patient_id (str): Primary patient identifier
            patient_birth_date (str | datetime | date): Patient birth date
            patient_sex (PatientSex): Patient sex (M/F/O)
            drr_description (str): Description of DRR generation
            manufacturer (str): Name of treatment planning system
            rt_image_label (str): Label for the DRR image
            image_plane (RTImagePlane): Image plane orientation (default: NORMAL)
            study_instance_uid (str | None): Study UID (auto-generated if None)
            series_instance_uid (str | None): Series UID (auto-generated if None)
            frame_of_reference_uid (str | None): Frame of reference UID (auto-generated if None)
            sop_instance_uid (str | None): SOP instance UID (auto-generated if None)
            
        Returns:
            RTImageIOD: Configured RT Image IOD for DRR imaging
        """
        # Auto-generate UIDs if not provided
        study_uid = study_instance_uid or uid.generate_uid()
        series_uid = series_instance_uid or uid.generate_uid()
        frame_ref_uid = frame_of_reference_uid or uid.generate_uid()
        sop_uid = sop_instance_uid or uid.generate_uid()
        
        # Create modules for DRR workflow
        patient_module = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id,
            patient_birth_date=patient_birth_date,
            patient_sex=patient_sex
        )
        
        study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid
        ).with_optional_elements(
            study_description=f"DRR Generation - {drr_description}"
        )
        
        series_module = RTSeriesModule.from_required_elements(
            modality=Modality.RTIMAGE,
            series_instance_uid=series_uid
        ).with_optional_elements(
            series_description="Digitally Reconstructed Radiographs"
        )
        
        frame_ref_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_ref_uid
        )
        
        equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer=manufacturer
        )
        
        acquisition_module = GeneralAcquisitionModule.from_required_elements()
        
        image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        # Default pixel module for DRR imaging
        pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        rt_image_module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label=rt_image_label,
            image_type=["DERIVED", "SECONDARY", "DRR"],
            conversion_type=ConversionType.WSD,  # Workstation
            rt_image_plane=image_plane
        )
        
        sop_module = SOPCommonModule.from_required_elements(
            sop_class_uid=cls.SOP_CLASS_UID,
            sop_instance_uid=sop_uid
        )
        
        return cls(
            patient_module=patient_module,
            general_study_module=study_module,
            rt_series_module=series_module,
            general_equipment_module=equipment_module,
            general_acquisition_module=acquisition_module,
            general_image_module=image_module,
            image_pixel_module=pixel_module,
            rt_image_module=rt_image_module,
            sop_common_module=sop_module,
            frame_of_reference_module=frame_ref_module
        )

    # =====================================================================
    # IOD-Specific Feature Addition Methods  
    # =====================================================================

    def with_multi_frame_capabilities(
        self,
        number_of_frames: int,
        frame_increment_pointer: list[str] | None = None,
        cine_rate: float | None = None,
        frame_time: float | None = None
    ) -> 'RTImageIOD':
        """Add multi-frame imaging capabilities to the RT Image IOD.
        
        Configures the Multi-frame and optionally Cine modules for time-dependent
        image series or multiple exposures. Enables time-series analysis and
        cine playback functionality.
        
        Args:
            number_of_frames (int): Number of frames in the multi-frame image
            frame_increment_pointer (list[str] | None): Data element tags that change between frames
            cine_rate (float | None): Cine playback rate in frames per second
            frame_time (float | None): Time per frame in milliseconds
            
        Returns:
            RTImageIOD: Self with multi-frame capabilities added
            
        Example:
            rt_image.with_multi_frame_capabilities(
                number_of_frames=30,
                cine_rate=10.0,  # 10 fps
                frame_time=100.0  # 100ms per frame
            )
        """
        # Create Multi-frame Module
        multi_frame_module = MultiFrameModule.from_required_elements(
            number_of_frames=number_of_frames,
            frame_increment_pointer=frame_increment_pointer or []
        )
        
        self._required_modules['multi_frame'] = multi_frame_module
        
        # Add Cine Module if cine parameters provided
        if cine_rate is not None or frame_time is not None:
            cine_module = CineModule.from_required_elements()
            if cine_rate is not None:
                cine_module.with_optional_elements(cine_rate=cine_rate)
            if frame_time is not None:
                cine_module.with_frame_time(frame_time=frame_time)
            
            self._required_modules['cine'] = cine_module
            
        return self

    def with_contrast_enhancement(
        self,
        contrast_bolus_agent: str,
        contrast_bolus_route: str | None = None,
        contrast_bolus_volume: float | None = None,
        contrast_bolus_start_time: str | None = None,
        contrast_bolus_stop_time: str | None = None
    ) -> 'RTImageIOD':
        """Add contrast media information to the RT Image IOD.
        
        Configures the Contrast/Bolus module with contrast agent administration
        details for enhanced imaging procedures.
        
        Args:
            contrast_bolus_agent (str): Contrast agent used
            contrast_bolus_route (str | None): Route of administration
            contrast_bolus_volume (float | None): Volume of contrast agent in ml
            contrast_bolus_start_time (str | None): Start time of contrast administration
            contrast_bolus_stop_time (str | None): Stop time of contrast administration
            
        Returns:
            RTImageIOD: Self with contrast information added
            
        Example:
            rt_image.with_contrast_enhancement(
                contrast_bolus_agent="Iodine",
                contrast_bolus_route="IV",
                contrast_bolus_volume=100.0,
                contrast_bolus_start_time="120000"
            )
        """
        contrast_module = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent=contrast_bolus_agent
        )
        
        # Add optional contrast elements
        optional_elements = {}
        if contrast_bolus_route is not None:
            optional_elements['contrast_bolus_route'] = contrast_bolus_route
        if contrast_bolus_volume is not None:
            optional_elements['contrast_bolus_volume'] = contrast_bolus_volume
        if contrast_bolus_start_time is not None:
            optional_elements['contrast_bolus_start_time'] = contrast_bolus_start_time
        if contrast_bolus_stop_time is not None:
            optional_elements['contrast_bolus_stop_time'] = contrast_bolus_stop_time
            
        if optional_elements:
            contrast_module.with_optional_elements(**optional_elements)
        
        self._required_modules['contrast_bolus'] = contrast_module
        return self

    def with_clinical_trial_context(
        self,
        protocol_name: str,
        site_id: str | None = None,
        sponsor_name: str | None = None,
        subject_reading_id: str | None = None,
        subject_id: str | None = None
    ) -> 'RTImageIOD':
        """Add clinical trial modules for research study compliance.
        
        Configures clinical trial modules for protocol compliance, subject
        management, and research data tracking. Essential for clinical
        research and regulatory submission workflows.
        
        Args:
            protocol_name (str): Clinical trial protocol name/identifier
            site_id (str | None): Clinical trial site identifier
            sponsor_name (str | None): Research sponsor organization
            subject_reading_id (str | None): Subject reading identifier
            subject_id (str | None): Clinical trial subject identifier
            
        Returns:
            RTImageIOD: Self with clinical trial modules added
            
        Example:
            rt_image.with_clinical_trial_context(
                protocol_name="RTOG-1234",
                site_id="Site-001",
                sponsor_name="NCI Clinical Trials Network"
            )
        """
        # Add Clinical Trial Subject Module
        trial_subject_module = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name=sponsor_name or "Research Sponsor",
            clinical_trial_protocol_id=f"PROTO-{uuid.uuid4().hex[:8].upper()}",
            clinical_trial_protocol_name=protocol_name,
            clinical_trial_site_id=site_id or "",
            clinical_trial_site_name=""
        )
        
        # Add subject identification (Type 1C requirement)
        trial_subject_module.with_subject_identification(
            clinical_trial_subject_id=subject_id or f"SUBJ-{uuid.uuid4().hex[:8].upper()}"
        )
        
        if subject_reading_id:
            trial_subject_module.with_optional_elements(
                issuer_of_clinical_trial_subject_reading_id=subject_reading_id
            )
        
        # Add Clinical Trial Study Module  
        trial_study_module = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id=""
        )
        
        # Add Clinical Trial Series Module
        trial_series_module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name=""
        )
        
        # Store the clinical trial modules
        self._required_modules.update({
            'clinical_trial_subject': trial_subject_module,
            'clinical_trial_study': trial_study_module,
            'clinical_trial_series': trial_series_module
        })
        
        return self

    def with_image_approval(
        self,
        approval_status: str = "APPROVED",
        approval_date: str | datetime | date | None = None,
        reviewer_name: str | None = None,
        approval_code_sequence: list[Dataset] | None = None  # noqa: ARG002
    ) -> 'RTImageIOD':
        """Add image approval information for clinical workflow.
        
        Configures the Approval Module with image quality approval data
        for clinical workflow management and regulatory compliance.
        
        Args:
            approval_status (str): Approval status (APPROVED, REJECTED, PENDING)
            approval_date (str | datetime | date | None): Date of approval
            reviewer_name (str | None): Name of approving physician/physicist
            approval_code_sequence (list[Dataset] | None): Coded approval information
            
        Returns:
            RTImageIOD: Self with approval data added
            
        Example:
            rt_image.with_image_approval(
                approval_status="APPROVED",
                approval_date="20240101",
                reviewer_name="Dr. Medical Physicist"
            )
        """
        approval_module = ApprovalModule.from_required_elements(
            approval_status=approval_status
        )
        
        # Add review information if provided and status requires it
        if (approval_date is not None and reviewer_name is not None and 
            approval_status in ["APPROVED", "REJECTED"]):
            # Convert date to string format if needed
            date_str = approval_date
            if isinstance(approval_date, (datetime, date)):
                date_str = approval_date.strftime("%Y%m%d")
            # Use current time if not provided
            time_str = datetime.now().strftime("%H%M%S")
            
            approval_module.with_review_information(
                review_date=date_str,
                review_time=time_str,
                reviewer_name=reviewer_name
            )
        
        self._required_modules['approval'] = approval_module
        return self

    def with_lut_enhancement(
        self,
        modality_lut_sequence: list[Dataset] | None = None,
        voi_lut_sequence: list[Dataset] | None = None,
        window_center: float | None = None,
        window_width: float | None = None,
        window_center_width_explanation: str | None = None
    ) -> 'RTImageIOD':
        """Add Lookup Table enhancements for image display optimization.
        
        Configures Modality LUT and VOI LUT modules for pixel value interpretation
        and display optimization. The Modality LUT allows conversion between
        portal image pixel values and dose transmitted through patient.
        
        Args:
            modality_lut_sequence (list[Dataset] | None): Modality LUT data
            voi_lut_sequence (list[Dataset] | None): VOI LUT data
            window_center (float | None): Window center for display
            window_width (float | None): Window width for display
            window_center_width_explanation (str | None): Explanation of windowing
            
        Returns:
            RTImageIOD: Self with LUT enhancements added
            
        Example:
            rt_image.with_lut_enhancement(
                window_center=1024.0,
                window_width=2048.0,
                window_center_width_explanation="Portal image display"
            )
        """
        # Add Modality LUT Module if provided
        if modality_lut_sequence is not None:
            modality_lut_module = ModalityLutModule.from_required_elements().with_modality_lut_sequence(
                modality_lut_sequence=modality_lut_sequence
            )
            self._required_modules['modality_lut'] = modality_lut_module
        
        # Add VOI LUT Module if any VOI parameters provided
        if any(param is not None for param in [voi_lut_sequence, window_center, window_width]):
            voi_lut_module = VoiLutModule.from_required_elements()
            
            optional_elements = {}
            if voi_lut_sequence is not None:
                optional_elements['voi_lut_sequence'] = voi_lut_sequence
            if window_center is not None:
                optional_elements['window_center'] = window_center
            if window_width is not None:
                optional_elements['window_width'] = window_width
            if window_center_width_explanation is not None:
                optional_elements['window_center_width_explanation'] = window_center_width_explanation
                
            if optional_elements:
                voi_lut_module.with_optional_elements(**optional_elements)
            
            self._required_modules['voi_lut'] = voi_lut_module
            
        return self

    # =====================================================================
    # IOD-Specific Properties and Methods
    # =====================================================================

    @property
    def is_portal_image(self) -> bool:
        """Check if this is a portal/verification image.

        Returns:
            bool: True if image type indicates portal imaging
        """
        rt_image_module = self._required_modules.get('rt_image')
        if not rt_image_module:
            return False

        image_type = rt_image_module.get('ImageType', [])
        return 'PORTAL' in image_type or 'VERIFICATION' in image_type

    @property
    def is_drr_image(self) -> bool:
        """Check if this is a digitally reconstructed radiograph.

        Returns:
            bool: True if image type indicates DRR
        """
        rt_image_module = self._required_modules.get('rt_image')
        if not rt_image_module:
            return False

        image_type = rt_image_module.get('ImageType', [])
        return 'DRR' in image_type

    @property
    def is_simulator_image(self) -> bool:
        """Check if this is a simulator image.

        Returns:
            bool: True if image type indicates simulator imaging
        """
        rt_image_module = self._required_modules.get('rt_image')
        if not rt_image_module:
            return False

        image_type = rt_image_module.get('ImageType', [])
        return 'SIMULATOR' in image_type

    @property
    def is_multi_frame(self) -> bool:
        """Check if this is a multi-frame RT image.

        Returns:
            bool: True if multi-frame module is present
        """
        return 'multi_frame' in self._required_modules

    @property
    def has_cine_capability(self) -> bool:
        """Check if this image supports cine playback.

        Returns:
            bool: True if cine module is present
        """
        return 'cine' in self._required_modules

    @property
    def has_contrast_enhancement(self) -> bool:
        """Check if contrast media was used.

        Returns:
            bool: True if contrast bolus module is present
        """
        return 'contrast_bolus' in self._required_modules

    @property
    def has_spatial_information(self) -> bool:
        """Check if spatial positioning data is available.

        Returns:
            bool: True if frame of reference module is present
        """
        return 'frame_of_reference' in self._required_modules

    @property
    def has_approval_data(self) -> bool:
        """Check if image approval information is included.

        Returns:
            bool: True if approval module is present
        """
        return 'approval' in self._required_modules

    @property
    def has_lut_enhancement(self) -> bool:
        """Check if lookup table enhancements are present.

        Returns:
            bool: True if modality LUT or VOI LUT modules are present
        """
        return 'modality_lut' in self._required_modules or 'voi_lut' in self._required_modules

    def get_rt_imaging_parameters(self) -> dict[str, Any]:
        """Get RT imaging parameters summary.

        Returns:
            dict[str, Any]: Dictionary with key RT imaging parameters
        """
        rt_image_module = self._required_modules.get('rt_image')
        if not rt_image_module:
            return {}

        return {
            'image_label': rt_image_module.get('RTImageLabel', None),
            'image_plane': rt_image_module.get('RTImagePlane', None),
            'rt_image_sid': rt_image_module.get('RTImageSID', None),
            'radiation_machine_name': rt_image_module.get('RadiationMachineName', None),
            'primary_dosimeter_unit': rt_image_module.get('PrimaryDosimeterUnit', None),
            'gantry_angle': rt_image_module.get('GantryAngle', None),
            'beam_limiting_device_angle': rt_image_module.get('BeamLimitingDeviceAngle', None),
            'patient_support_angle': rt_image_module.get('PatientSupportAngle', None),
            'image_plane_pixel_spacing': rt_image_module.get('ImagePlanePixelSpacing', None),
            'conversion_type': rt_image_module.get('ConversionType', None),
            'image_type': rt_image_module.get('ImageType', None)
        }

    def get_image_dimensions(self) -> dict[str, Any]:
        """Get image pixel dimensions and characteristics.

        Returns:
            dict[str, Any]: Dictionary with image dimension parameters
        """
        image_pixel_module = self._required_modules.get('image_pixel')
        multi_frame_module = self._required_modules.get('multi_frame')
        
        if not image_pixel_module:
            return {}

        dimensions = {
            'rows': image_pixel_module.get('Rows', None),
            'columns': image_pixel_module.get('Columns', None),
            'samples_per_pixel': image_pixel_module.get('SamplesPerPixel', None),
            'bits_allocated': image_pixel_module.get('BitsAllocated', None),
            'bits_stored': image_pixel_module.get('BitsStored', None),
            'high_bit': image_pixel_module.get('HighBit', None),
            'pixel_representation': image_pixel_module.get('PixelRepresentation', None),
            'photometric_interpretation': image_pixel_module.get('PhotometricInterpretation', None)
        }

        # Add number of frames if multi-frame
        if multi_frame_module:
            dimensions['number_of_frames'] = multi_frame_module.get('NumberOfFrames', None)

        return dimensions

    def get_spatial_information(self) -> dict[str, Any]:
        """Get spatial positioning and orientation information.

        Returns:
            dict[str, Any]: Dictionary with spatial parameters for image positioning
        """
        spatial_info = {}

        # Frame of reference information
        frame_ref_module = self._required_modules.get('frame_of_reference')
        if frame_ref_module:
            spatial_info['frame_of_reference_uid'] = frame_ref_module.get('FrameOfReferenceUID', None)
            spatial_info['position_reference_indicator'] = frame_ref_module.get('PositionReferenceIndicator', None)

        # RT Image spatial information  
        rt_image_module = self._required_modules.get('rt_image')
        if rt_image_module:
            spatial_info.update({
                'rt_image_position': rt_image_module.get('RTImagePosition', None),
                'rt_image_sid': rt_image_module.get('RTImageSID', None),
                'image_plane_pixel_spacing': rt_image_module.get('ImagePlanePixelSpacing', None),
                'gantry_angle': rt_image_module.get('GantryAngle', None),
                'beam_limiting_device_angle': rt_image_module.get('BeamLimitingDeviceAngle', None),
                'patient_support_angle': rt_image_module.get('PatientSupportAngle', None),
                'table_top_eccentric_angle': rt_image_module.get('TableTopEccentricAngle', None),
                'table_top_pitch_angle': rt_image_module.get('TableTopPitchAngle', None),
                'table_top_roll_angle': rt_image_module.get('TableTopRollAngle', None)
            })

        return spatial_info

    def get_acquisition_summary(self) -> dict[str, Any]:
        """Get image acquisition summary information.

        Returns:
            dict[str, Any]: Dictionary with key acquisition parameters
        """
        summary = {}

        # General acquisition information
        acquisition_module = self._required_modules.get('general_acquisition')
        if acquisition_module is not None:
            summary.update({
                'acquisition_date': acquisition_module.get('AcquisitionDate', None),
                'acquisition_time': acquisition_module.get('AcquisitionTime', None),
                'acquisition_number': acquisition_module.get('AcquisitionNumber', None)
            })

        # Equipment information
        equipment_module = self._required_modules.get('general_equipment')
        if equipment_module is not None:
            summary.update({
                'manufacturer': equipment_module.get('Manufacturer', None),
                'manufacturer_model_name': equipment_module.get('ManufacturerModelName', None),
                'device_serial_number': equipment_module.get('DeviceSerialNumber', None),
                'software_versions': equipment_module.get('SoftwareVersions', None),
                'station_name': equipment_module.get('StationName', None)
            })

        # RT-specific parameters
        rt_image_module = self._required_modules.get('rt_image')
        if rt_image_module is not None:
            summary.update({
                'radiation_machine_name': rt_image_module.get('RadiationMachineName', None),
                'radiation_machine_sad': rt_image_module.get('RadiationMachineSAD', None),
                'primary_dosimeter_unit': rt_image_module.get('PrimaryDosimeterUnit', None)
            })

        return summary

    # =====================================================================
    # IOD-Specific Validation
    # =====================================================================

    def to_validated_dataset(self, config: ValidationConfig | None = None) -> tuple[Dataset, ValidationResult]:  # noqa: ARG002
        """Generate validated DICOM dataset from this RT Image IOD instance.
        
        This method combines dataset generation and validation into a single operation,
        ensuring the user doesn't need to call to_dataset() separately for validation.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            tuple[Dataset, ValidationResult]: Generated dataset and validation results
        """
        # Generate the dataset once
        dataset = self.to_dataset()
        
        # Validate the generated dataset (would need RTImageIODValidator)
        # For now, return basic validation
        validation_result = ValidationResult()
        validation_result.add_error("RTImageIODValidator not yet implemented")
        
        return dataset, validation_result
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:  # noqa: ARG002
        """Validate this RT Image IOD instance (convenience method).
        
        This is a convenience method that generates a dataset and validates it,
        returning only the validation result. For efficiency, use to_validated_dataset()
        when you need both the dataset and validation results.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with errors and warnings
        """
        # Would need RTImageIODValidator implementation
        validation_result = ValidationResult()
        validation_result.add_error("RTImageIODValidator not yet implemented")
        return validation_result

    def _validate_dependencies(self, *args, **kwargs) -> None:
        """Validate IOD-specific module dependencies.

        This method implements the abstract method from BaseIOD.
        RT Image IOD dependencies are validated during construction.
        
        Note:
            Dependencies are validated during __init__, so this is a no-op.
        """
        pass

    def _validate_conditional_dependencies(self, contrast_bolus_module, cine_module,  
                                         multi_frame_module, frame_extraction_module) -> None:  # noqa: ARG002
        """Validate RT Image IOD conditional module dependencies.

        Per DICOM PS3.3 A.17.3, certain modules are conditionally required
        based on the presence of specific conditions.
        
        Args:
            contrast_bolus_module: Contrast/Bolus Module instance or None
            cine_module: Cine Module instance or None
            multi_frame_module: Multi-frame Module instance or None  
            frame_extraction_module: Frame Extraction Module instance or None
            
        Raises:
            IODValidationError: If conditional dependencies are not satisfied
        """
        # Cine module requires multi-frame module
        if cine_module and not multi_frame_module:
            raise IODValidationError("cine_module requires multi_frame_module")

        # Frame extraction module validation would require additional context
        # about how the SOP instance was created, which is not available here
        # This would typically be validated at a higher level
        
        # Contrast bolus module is required if contrast media was used
        # This cannot be validated without additional information about
        # the actual imaging procedure, so we accept it if provided