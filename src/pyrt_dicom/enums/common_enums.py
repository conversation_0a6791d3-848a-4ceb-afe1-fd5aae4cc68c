"""Common DICOM enumerations for SOP and reference modules."""

from enum import Enum


class SyntheticData(Enum):
    """Synthetic Data (0008,001C) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - YES = Some or all content was made artificially rather than acquired
    - NO = Content is a faithful representation of acquired data
    """
    YES = "YES"
    NO = "NO"


class SOPInstanceStatus(Enum):
    """SOP Instance Status (0100,0410) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - NS = Not Specified; no special storage status
    - AO = Authorized Original; primary SOP Instance authorized for diagnostic use
    - AC = Authorized Copy; copy of an Authorized Original SOP Instance
    """
    NS = "NS"
    AO = "AO"
    AC = "AC"


class QueryRetrieveView(Enum):
    """Query/Retrieve View (0008,0053) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - CLASSIC = Classic view requested during C-MOVE operation
    - ENHANCED = Enhanced view requested during C-MOVE operation
    """
    CLASSIC = "CLASSIC"
    ENHANCED = "ENHANCED"


class ContentQualification(Enum):
    """Content Qualification (0018,9004) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - PRODUCT = Product content qualification
    - RESEARCH = Research content qualification
    - SERVICE = Service content qualification
    """
    PRODUCT = "PRODUCT"
    RESEARCH = "RESEARCH"
    SERVICE = "SERVICE"


class LongitudinalTemporalInformationModified(Enum):
    """Longitudinal Temporal Information Modified (0028,0303) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - UNMODIFIED = Date and time Attributes have not been modified during de-identification
    - MODIFIED = Date and time Attributes have been modified during de-identification
    - REMOVED = Date and time Attributes have been removed during de-identification
    """
    UNMODIFIED = "UNMODIFIED"
    MODIFIED = "MODIFIED"
    REMOVED = "REMOVED"


class OriginalSpecializedEquipment(Enum):
    """Original Specialized Equipment (0008,0103) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.12.1:
    - YES = Medical device specification flag indicating specialized equipment
    - NO = Standard equipment used
    """
    YES = "YES"
    NO = "NO"


class SpatialLocationsPreserved(Enum):
    """Spatial Locations Preserved (0028,135A) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.12.4:
    - YES = Spatial locations are preserved
    - NO = Spatial locations are not preserved
    - REORIENTED_ONLY = Image has been reoriented only
    """
    YES = "YES"
    NO = "NO"
    REORIENTED_ONLY = "REORIENTED_ONLY"


class PositionReferenceIndicator(Enum):
    """Position Reference Indicator (0020,1040) - DICOM VR: LO

    Defined Terms per DICOM PS3.3 C.*******.2:
    Part of imaging target used as reference point for Frame of Reference.

    Anatomical Reference Points (Patient-based coordinate systems):
    - ILIAC_CREST = Iliac crest anatomical landmark
    - ORBITAL_MEDIAL = Orbital-medial anatomical landmark
    - STERNAL_NOTCH = Sternal notch anatomical landmark
    - SYMPHYSIS_PUBIS = Symphysis pubis anatomical landmark
    - XIPHOID = Xiphoid process anatomical landmark
    - LOWER_COSTAL_MARGIN = Lower costal margin anatomical landmark
    - EXTERNAL_AUDITORY_MEATUS = External auditory meatus anatomical landmark

    Slide-based coordinate systems:
    - SLIDE_CORNER = Slide corner reference point

    Corneal coordinate systems:
    - CORNEAL_VERTEX_R = Corneal vertex for right eye
    - CORNEAL_VERTEX_L = Corneal vertex for left eye

    Note: May be zero length when no meaningful reference point exists
    (e.g., mammographic images without releasing breast compression).
    """
    # Anatomical Reference Points
    ILIAC_CREST = "ILIAC_CREST"
    ORBITAL_MEDIAL = "ORBITAL_MEDIAL"
    STERNAL_NOTCH = "STERNAL_NOTCH"
    SYMPHYSIS_PUBIS = "SYMPHYSIS_PUBIS"
    XIPHOID = "XIPHOID"
    LOWER_COSTAL_MARGIN = "LOWER_COSTAL_MARGIN"
    EXTERNAL_AUDITORY_MEATUS = "EXTERNAL_AUDITORY_MEATUS"

    # Slide-based Reference
    SLIDE_CORNER = "SLIDE_CORNER"

    # Corneal Reference Points
    CORNEAL_VERTEX_R = "CORNEAL_VERTEX_R"
    CORNEAL_VERTEX_L = "CORNEAL_VERTEX_L"
