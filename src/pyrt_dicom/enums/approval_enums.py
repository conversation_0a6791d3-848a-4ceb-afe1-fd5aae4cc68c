"""Approval-related DICOM enumerations."""

from enum import Enum


class ApprovalStatus(Enum):
    """Approval Status (300E,0002) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.16:
    - APPROVED = Reviewer recorded that object met an implied criterion
    - UNAPPROVED = No review of object has been recorded
    - REJECTED = Reviewer recorded that object failed to meet an implied criterion
    """
    APPROVED = "APPROVED"
    UNAPPROVED = "UNAPPROVED"
    REJECTED = "REJECTED"
