"""Patient Study-related DICOM enumerations."""

from enum import Enum


class SmokingStatus(Enum):
    """Smoking Status (0010,21A0) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.2.2:
    - YES = Patient smokes
    - NO = Patient does not smoke
    - UNKNOWN = Smoking status unknown
    """
    YES = "YES"
    NO = "NO"
    UNKNOWN = "UNKNOWN"


class PregnancyStatus(Enum):
    """Pregnancy Status (0010,21C0) - DICOM VR: US
    
    Defined Terms per DICOM PS3.3 C.7.2.2:
    - 1 = Not pregnant
    - 2 = Possibly pregnant
    - 3 = Definitely pregnant
    - 4 = Unknown
    """
    NOT_PREGNANT = 1
    POSSIBLY_PREGNANT = 2
    DEFINITELY_PREGNANT = 3
    UNKNOWN = 4


class PatientSexNeutered(Enum):
    """Patient's Sex Neutered (0010,2203) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.2.2:
    - ALTERED = Altered/Neutered
    - UNALTERED = Unaltered/intact
    """
    ALTERED = "ALTERED"
    UNALTERED = "UNALTERED"
