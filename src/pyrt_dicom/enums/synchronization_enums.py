"""Synchronization Module DICOM enumerations - PS3.3 C.7.4.2."""

from enum import Enum


class SynchronizationTrigger(Enum):
    """Synchronization Trigger (0018,106A) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.4.2:
    - SOURCE = This equipment provides synchronization channel or trigger to other equipment
    - EXTERNAL = This equipment receives synchronization channel or trigger from other equipment
    - PASSTHRU = This equipment receives synchronization channel or trigger and forwards it
    - NO TRIGGER = Data acquisition not synchronized by common channel or trigger
    """
    SOURCE = "SOURCE"
    EXTERNAL = "EXTERNAL"
    PASSTHRU = "PASSTHRU"
    NO_TRIGGER = "NO TRIGGER"


class AcquisitionTimeSynchronized(Enum):
    """Acquisition Time Synchronized (0018,1800) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.4.2:
    - Y = Acquisition DateTime is synchronized with external time reference
    - N = Acquisition DateTime is not synchronized with external time reference
    """
    YES = "Y"
    NO = "N"


class TimeDistributionProtocol(Enum):
    """Time Distribution Protocol (0018,1802) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.4.2:
    - NTP = Network Time Protocol
    - IRIG = Inter Range Instrumentation Group
    - GPS = Global Positioning System
    - SNTP = Simple Network Time Protocol
    - PTP = IEEE 1588 Precision Time Protocol
    """
    NTP = "NTP"
    IRIG = "IRIG"
    GPS = "GPS"
    SNTP = "SNTP"
    PTP = "PTP"
