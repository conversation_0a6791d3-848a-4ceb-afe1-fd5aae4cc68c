"""Series-related DICOM enumerations."""

from enum import Enum


class Modality(Enum):
    """Modality (0008,0060) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.3.1.1.1:
    - CT = Computed Tomography
    - MR = Magnetic Resonance
    - US = Ultrasound
    - XA = X-Ray Angiography
    - RF = Radio Fluoroscopy
    - DX = Digital Radiography
    - CR = Computed Radiography
    - MG = Mammography
    - NM = Nuclear Medicine
    - PT = Positron emission tomography (PET)
    - RTIMAGE = Radiotherapy Image
    - RTDOSE = Radiotherapy Dose
    - RTSTRUCT = Radiotherapy Structure Set
    - RTPLAN = Radiotherapy Plan
    - RTRECORD = RT Treatment Record
    - SR = SR Document
    - DOC = Document
    - PR = Presentation State
    - KO = Key Object Selection
    - SEG = Segmentation
    - REG = Registration
    - PLAN = Plan
    - OT = Other
    """
    CT = "CT"
    MR = "MR"
    US = "US"
    XA = "XA"
    RF = "RF"
    DX = "DX"
    CR = "CR"
    MG = "MG"
    NM = "NM"
    PT = "PT"
    RTIMAGE = "RTIMAGE"
    RTDOSE = "RTDOSE"
    RTSTRUCT = "RTSTRUCT"
    RTPLAN = "RTPLAN"
    RTRECORD = "RTRECORD"
    SR = "SR"
    DOC = "DOC"
    PR = "PR"
    KO = "KO"
    SEG = "SEG"
    REG = "REG"
    PLAN = "PLAN"
    OT = "OT"


class Laterality(Enum):
    """Laterality (0020,0060) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.3.1:
    - R = Right
    - L = Left
    """
    RIGHT = "R"
    LEFT = "L"


class PatientPosition(Enum):
    """Patient Position (0018,5100) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.3.1.1.2:
    - HFP = Head First-Prone
    - HFS = Head First-Supine
    - HFDR = Head First-Decubitus Right
    - HFDL = Head First-Decubitus Left
    - HFV = Head First-Vertical
    - HFI = Head First-Inverted
    - FFP = Feet First-Prone
    - FFS = Feet First-Supine
    - FFDR = Feet First-Decubitus Right
    - FFDL = Feet First-Decubitus Left
    - FFV = Feet First-Vertical
    - FFI = Feet First-Inverted
    - LFP = Left First-Prone
    - LFS = Left First-Supine
    - LFDR = Left First-Decubitus Right
    - LFDL = Left First-Decubitus Left
    - RFP = Right First-Prone
    - RFS = Right First-Supine
    - RFDR = Right First-Decubitus Right
    - RFDL = Right First-Decubitus Left
    - AFP = Anterior First-Prone
    - AFS = Anterior First-Supine
    - AFDR = Anterior First-Decubitus Right
    - AFDL = Anterior First-Decubitus Left
    - PFP = Posterior First-Prone
    - PFS = Posterior First-Supine
    - PFDR = Posterior First-Decubitus Right
    - PFDL = Posterior First-Decubitus Left
    """
    HFP = "HFP"
    HFS = "HFS"
    HFDR = "HFDR"
    HFDL = "HFDL"
    HFV = "HFV"
    HFI = "HFI"
    FFP = "FFP"
    FFS = "FFS"
    FFDR = "FFDR"
    FFDL = "FFDL"
    FFV = "FFV"
    FFI = "FFI"
    LFP = "LFP"
    LFS = "LFS"
    LFDR = "LFDR"
    LFDL = "LFDL"
    RFP = "RFP"
    RFS = "RFS"
    RFDR = "RFDR"
    RFDL = "RFDL"
    AFP = "AFP"
    AFS = "AFS"
    AFDR = "AFDR"
    AFDL = "AFDL"
    PFP = "PFP"
    PFS = "PFS"
    PFDR = "PFDR"
    PFDL = "PFDL"


class AnatomicalOrientationType(Enum):
    """Anatomical Orientation Type (0010,2210) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.3.1:
    - BIPED = Bipedal anatomical orientation
    - QUADRUPED = Quadrupedal anatomical orientation
    """
    BIPED = "BIPED"
    QUADRUPED = "QUADRUPED"
