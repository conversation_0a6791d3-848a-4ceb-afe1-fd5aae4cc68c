"""DICOM Enumerations - Strict DICOM value sets.

Following the user's requirement for strict enum enforcement per DICOM standard.
"""

from .approval_enums import ApprovalStatus

from .clinical_trial_enums import (
    ConsentForDistribution,
    DistributionType,
    LongitudinalTemporalEventType,
)

from .common_enums import (
    SyntheticData,
    SOPInstanceStatus,
    QueryRetrieveView,
    ContentQualification,
    LongitudinalTemporalInformationModified,
    OriginalSpecializedEquipment,
    SpatialLocationsPreserved,
    PositionReferenceIndicator,
)

from .dose_enums import DoseUnits, DoseType

from .equipment_enums import DeviceDiameterUnits, Manufacturer, ProcedureCode

from .image_enums import (
    ContrastBolusAgent,
    ContrastBolusIngredient,
    MultiEnergyCTAcquisition,
    RotationDirection,
    ExposureModulationType,
    CTImageTypeValue1,
    CTImageTypeValue2,
    CTImageTypeValue3,
    CTImageTypeValue4,
    CTSamplesPerPixel,
    CTBitsAllocated,
    CTBitsStored,
    MultiEnergySourceTechnique,
    MultiEnergyDetectorType,
    RescaleType,
    FilterMaterial,
    ScanOptions,
    FilterType,
    QualityControlImage,
    BurnedInAnnotation,
    RecognizableVisualFeatures,
    LossyImageCompression,
    PresentationLUTShape,
    ImageLaterality,
    PhotometricInterpretation,
    PlanarConfiguration,
    PixelRepresentation,
    StereoPairsPresent,
    PreferredPlaybackSequencing,
    ChannelMode,
    OverlayType,
    OverlaySubtype,
    LossyImageCompressionMethod,
    ImageType,
    ModalityLutType,
    VoiLutFunction,
    FrameType,
)

from .patient_enums import (
    PatientSex,
    ResponsiblePersonRole,
    TypeOfPatientID,
    PatientOrientationCode,
    PatientOrientationModifierCode,
    PatientEquipmentRelationshipCode,
)

from .rt_enums import (
    ConversionType,
    BeamType,
    RadiationType,
    ReportedValuesOrigin,
    RTImagePlane,
    TreatmentDeliveryType,
    PrimaryDosimeterUnit,
    PixelIntensityRelationshipSign,
    FluenceMode,
    RTBeamLimitingDeviceType,
    ApplicatorType,
    ApplicatorApertureShape,
    GeneralAccessoryType,
    BlockType,
    BlockDivergence,
    BlockMountingPosition,
    FluenceDataSource,
    SpatialTransformOfDose,
    DoseSummationType,
    TissueHeterogeneityCorrection,
    DVHType,
    DVHROIContributionType,
    DVHVolumeUnits,
    ROIGenerationAlgorithm,
    RTPlanStatus,
    PlanIntent,
    RTPlanGeometry,
    RTPlanRelationship,
    RTImageTypeValue3,
    EnhancedRTBeamLimitingDeviceDefinitionFlag,
    ContourGeometricType,
    RTROIInterpretedType,
    RTROIRelationship,
    ROIPhysicalProperty,
    DoseReferenceStructureType,
    DoseReferenceType,
    DoseValuePurpose,
    DoseValueInterpretation,
    FixationDeviceType,
    ShieldingDeviceType,
    SetupTechnique,
    SetupDeviceType,
    RespiratoryMotionCompensationTechnique,
    RespiratorySignalSource,
    BeamDoseMeaning,
    DoseCalibrationConditionsVerifiedFlag,
    BrachyTreatmentTechnique,
    BrachyTreatmentType,
    ApplicationSetupType,
    SourceType,
    SourceMovementType,
    BrachyAccessoryDeviceType,
    SourceApplicatorType,
    SourceStrengthUnits,
    RTPrescriptionStatus,
)

from .series_enums import (
    Modality,
    Laterality,
    PatientPosition,
    AnatomicalOrientationType,
)

from .specimen_enums import (
    ContainerComponentMaterial,
)

from .study_enums import SmokingStatus, PregnancyStatus, PatientSexNeutered

from .synchronization_enums import (
    SynchronizationTrigger,
    AcquisitionTimeSynchronized,
    TimeDistributionProtocol,
)

__all__ = [
    "PatientSex",
    "TypeOfPatientID",
    "ResponsiblePersonRole",
    "PatientOrientationCode",
    "PatientOrientationModifierCode",
    "PatientEquipmentRelationshipCode",
    "DoseUnits",
    "DoseType",
    "ConsentForDistribution",
    "DistributionType",
    "LongitudinalTemporalEventType",
    "SmokingStatus",
    "PregnancyStatus",
    "PatientSexNeutered",
    "Modality",
    "Laterality",
    "PatientPosition",
    "AnatomicalOrientationType",
    "QualityControlImage",
    "BurnedInAnnotation",
    "RecognizableVisualFeatures",
    "LossyImageCompression",
    "PresentationLUTShape",
    "ImageLaterality",
    "PhotometricInterpretation",
    "PlanarConfiguration",
    "PixelRepresentation",
    "StereoPairsPresent",
    "PreferredPlaybackSequencing",
    "ChannelMode",
    "OverlayType",
    "OverlaySubtype",
    "LossyImageCompressionMethod",
    "ImageType",
    "FrameType",
    "ModalityLutType",
    "RescaleType",
    "VoiLutFunction",
    "DeviceDiameterUnits",
    "Manufacturer",
    "ProcedureCode",
    "ApprovalStatus",
    "SyntheticData",
    "SOPInstanceStatus",
    "QueryRetrieveView",
    "ContentQualification",
    "LongitudinalTemporalInformationModified",
    "OriginalSpecializedEquipment",
    "SpatialLocationsPreserved",
    "PositionReferenceIndicator",
    "ConversionType",
    "BeamType",
    "RadiationType",
    "ReportedValuesOrigin",
    "RTImagePlane",
    "TreatmentDeliveryType",
    "PrimaryDosimeterUnit",
    "PixelIntensityRelationshipSign",
    "FluenceMode",
    "RTBeamLimitingDeviceType",
    "ApplicatorType",
    "ApplicatorApertureShape",
    "GeneralAccessoryType",
    "BlockType",
    "BlockDivergence",
    "BlockMountingPosition",
    "FluenceDataSource",
    "SpatialTransformOfDose",
    "DoseSummationType",
    "TissueHeterogeneityCorrection",
    "DVHType",
    "DVHROIContributionType",
    "DVHVolumeUnits",
    "ROIGenerationAlgorithm",
    "RTPlanStatus",
    "PlanIntent",
    "RTPlanGeometry",
    "RTPlanRelationship",
    "RTImageTypeValue3",
    "EnhancedRTBeamLimitingDeviceDefinitionFlag",
    "ContourGeometricType",
    "RTROIInterpretedType",
    "RTROIRelationship",
    "ROIPhysicalProperty",
    "DoseReferenceStructureType",
    "DoseReferenceType",
    "DoseValuePurpose",
    "DoseValueInterpretation",
    "FixationDeviceType",
    "ShieldingDeviceType",
    "SetupTechnique",
    "SetupDeviceType",
    "RespiratoryMotionCompensationTechnique",
    "RespiratorySignalSource",
    "BeamDoseMeaning",
    "DoseCalibrationConditionsVerifiedFlag",
    "BrachyTreatmentTechnique",
    "BrachyTreatmentType",
    "ApplicationSetupType",
    "SourceType",
    "SourceMovementType",
    "BrachyAccessoryDeviceType",
    "SourceApplicatorType",
    "SourceStrengthUnits",
    "RTPrescriptionStatus",
    "ContrastBolusAgent",
    "ContrastBolusIngredient",
    "MultiEnergyCTAcquisition",
    "RotationDirection",
    "ExposureModulationType",
    "CTImageTypeValue1",
    "CTImageTypeValue2",
    "CTImageTypeValue3",
    "CTImageTypeValue4",
    "CTSamplesPerPixel",
    "CTBitsAllocated",
    "CTBitsStored",
    "MultiEnergySourceTechnique",
    "MultiEnergyDetectorType",
    "RescaleType",
    "FilterMaterial",
    "ScanOptions",
    "FilterType",
    "SynchronizationTrigger",
    "AcquisitionTimeSynchronized",
    "TimeDistributionProtocol",
    "ContainerComponentMaterial",
]
