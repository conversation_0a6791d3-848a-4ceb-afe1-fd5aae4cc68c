"""
Image Module Enums - Enumerated values for image-related DICOM modules.

This module contains enumerated values used across various image modules
including General Image, Image Pixel, Multi-frame, Cine, and Overlay Plane modules.
"""

from enum import Enum


class QualityControlImage(Enum):
    """Quality Control Image (0028,0300) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - YES = Image contains only quality control material
    - NO = Image does not contain quality control material
    - BOTH = Image contains both subject and quality control information
    """

    YES = "YES"
    NO = "NO"
    BOTH = "BOTH"


class BurnedInAnnotation(Enum):
    """Burned In Annotation (0028,0301) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - YES = Image contains sufficient burned in annotation to identify patient and date
    - NO = Image does not contain burned in annotation
    """

    YES = "YES"
    NO = "NO"


class RecognizableVisualFeatures(Enum):
    """Recognizable Visual Features (0028,0302) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - YES = Image contains sufficiently recognizable visual features to identify patient
    - NO = Image does not contain recognizable visual features
    """

    YES = "YES"
    NO = "NO"


class LossyImageCompression(Enum):
    """Lossy Image Compression (0028,2110) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - 00 = Image has not been subjected to lossy compression
    - 01 = Image has been subjected to lossy compression
    """

    NOT_COMPRESSED = "00"
    COMPRESSED = "01"


class PresentationLUTShape(Enum):
    """Presentation LUT Shape (2050,0020) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - IDENTITY = Output is in P-Values (for MONOCHROME2 or color interpretations)
    - INVERSE = Output after inversion is in P-Values (for MONOCHROME1)
    """

    IDENTITY = "IDENTITY"
    INVERSE = "INVERSE"


class ImageLaterality(Enum):
    """Image Laterality (0020,0062) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - R = Right
    - L = Left
    - U = Unpaired
    - B = Both left and right
    """

    RIGHT = "R"
    LEFT = "L"
    UNPAIRED = "U"
    BOTH = "B"


class PhotometricInterpretation(Enum):
    """Photometric Interpretation (0028,0004) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.3.1.2:
    - MONOCHROME1 = Single monochrome image, minimum sample value displayed as white
    - MONOCHROME2 = Single monochrome image, minimum sample value displayed as black
    - PALETTE_COLOR = Color image with single sample per pixel using palette lookup
    - RGB = Color image described by red, green, and blue image planes
    - YBR_FULL = Color image described by luminance (Y) and chrominance (CB, CR) planes
    - YBR_FULL_422 = YBR with horizontal chrominance subsampling
    - YBR_PARTIAL_420 = YBR with horizontal and vertical chrominance subsampling
    - YBR_ICT = YBR with Irreversible Color Transformation
    - YBR_RCT = YBR with Reversible Color Transformation
    - XYB = XYB color model
    """

    MONOCHROME1 = "MONOCHROME1"
    MONOCHROME2 = "MONOCHROME2"
    PALETTE_COLOR = "PALETTE COLOR"
    RGB = "RGB"
    YBR_FULL = "YBR_FULL"
    YBR_FULL_422 = "YBR_FULL_422"
    YBR_PARTIAL_420 = "YBR_PARTIAL_420"
    YBR_ICT = "YBR_ICT"
    YBR_RCT = "YBR_RCT"
    XYB = "XYB"


class PlanarConfiguration(Enum):
    """Planar Configuration (0028,0006) - DICOM VR: US

    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 0 = Color-by-pixel format (R1,G1,B1,R2,G2,B2,...)
    - 1 = Color-by-plane format (R1,R2,R3,...,G1,G2,G3,...,B1,B2,B3,...)
    """

    COLOR_BY_PIXEL = 0
    COLOR_BY_PLANE = 1


class PixelRepresentation(Enum):
    """Pixel Representation (0028,0103) - DICOM VR: US

    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 0 = Unsigned integer
    - 1 = 2's complement (signed integer)
    """

    UNSIGNED = 0
    SIGNED = 1


class StereoPairsPresent(Enum):
    """Stereo Pairs Present (0022,0028) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.6:
    - YES = Multi-frame image consists of stereoscopic pairs
    - NO = Multi-frame image does not consist of stereoscopic pairs
    """

    YES = "YES"
    NO = "NO"


class PreferredPlaybackSequencing(Enum):
    """Preferred Playback Sequencing (0018,1244) - DICOM VR: US

    Defined Terms per DICOM PS3.3 C.7.6.5:
    - 0 = Looping (1,2,...,n,1,2,...,n,1,2,...,n,...)
    - 1 = Sweeping (1,2,...,n,n-1,...,2,1,2,...,n,...)
    """

    LOOPING = 0
    SWEEPING = 1


class ChannelMode(Enum):
    """Channel Mode (003A,0302) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.5:
    - MONO = 1 signal
    - STEREO = 2 simultaneously acquired (left and right) signals
    """

    MONO = "MONO"
    STEREO = "STEREO"


class OverlayType(Enum):
    """Overlay Type (60xx,0040) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.9.2:
    - G = Graphics overlay
    - R = Region of Interest overlay
    """

    GRAPHICS = "G"
    ROI = "R"


class OverlaySubtype(Enum):
    """Overlay Subtype (60xx,0045) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.9.2:
    - USER = User created graphic annotation
    - AUTOMATED = Machine or algorithm generated annotation
    - ACTIVE_IMAGE_AREA = Identification of active area
    """

    USER = "USER"
    AUTOMATED = "AUTOMATED"
    ACTIVE_IMAGE_AREA = "ACTIVE IMAGE AREA"


class LossyImageCompressionMethod(Enum):
    """Lossy Image Compression Method (0028,2114) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - ISO_10918_1 = JPEG Lossy Compression
    - ISO_14495_1 = JPEG-LS Near-lossless Compression
    - ISO_15444_1 = JPEG 2000 Irreversible Compression
    - ISO_15444_15 = High-Throughput JPEG 2000 Irreversible
    - ISO_18181_1 = JPEG XL Image Coding System
    - ISO_13818_2 = MPEG2 Compression
    - ISO_14496_10 = MPEG-4 AVC/H.264 Compression
    - ISO_23008_2 = HEVC/H.265 Lossy Compression
    """

    ISO_10918_1 = "ISO_10918_1"
    ISO_14495_1 = "ISO_14495_1"
    ISO_15444_1 = "ISO_15444_1"
    ISO_15444_15 = "ISO_15444_15"
    ISO_18181_1 = "ISO_18181_1"
    ISO_13818_2 = "ISO_13818_2"
    ISO_14496_10 = "ISO_14496_10"
    ISO_23008_2 = "ISO_23008_2"


class CTImageTypeValue1(Enum):
    """CT Image Type Value 1 (0008,0008) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - ORIGINAL = Original Image
    - DERIVED = Derived Image
    """

    ORIGINAL = "ORIGINAL"
    DERIVED = "DERIVED"


class CTImageTypeValue2(Enum):
    """CT Image Type Value 2 (0008,0008) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - PRIMARY = Primary Image
    - SECONDARY = Secondary Image
    """

    PRIMARY = "PRIMARY"
    SECONDARY = "SECONDARY"


class CTImageTypeValue3(Enum):
    """CT Image Type Value 3 (0008,0008) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - AXIAL = CT Cross-sectional Image
    - LOCALIZER = CT Localizer Image
    """

    AXIAL = "AXIAL"
    LOCALIZER = "LOCALIZER"


class CTImageTypeValue4(Enum):
    """CT Image Type Value 4 for Multi-energy CT (0008,0008) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - VMI = Virtual Monoenergetic Image
    - MAT_SPECIFIC = Material-Specific Image
    - MAT_REMOVED = Material-Removed Image
    - MAT_FRACTIONAL = Material-Fractional Image
    - EFF_ATOMIC_NUM = Effective Atomic Number Image
    - ELECTRON_DENSITY = Electron Density Image
    - MAT_MODIFIED = Material-Modified Image
    - MAT_VALUE_BASED = Value-Based Image

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    VMI = "VMI"
    MAT_SPECIFIC = "MAT_SPECIFIC"
    MAT_REMOVED = "MAT_REMOVED"
    MAT_FRACTIONAL = "MAT_FRACTIONAL"
    EFF_ATOMIC_NUM = "EFF_ATOMIC_NUM"
    ELECTRON_DENSITY = "ELECTRON_DENSITY"
    MAT_MODIFIED = "MAT_MODIFIED"
    MAT_VALUE_BASED = "MAT_VALUE_BASED"


class ImageType:
    """Image Type (0008,0008) - DICOM VR: CS

    Specified in pydicom as a list of strings:
        ds.ImageType = ['ORIGINAL', 'PRIMARY', 'AXIAL']

    Defined Terms per DICOM PS3.3 C.7.6.1.1.2:
    Value 1 - Pixel Data Characteristics:
    - ORIGINAL = Original Image
    - DERIVED = Derived Image

    Value 2 - Patient Examination Characteristics:
    - PRIMARY = Primary Image
    - SECONDARY = Secondary Image

    Value 3 - CT Image Characteristics:
    - AXIAL = CT Cross-sectional Image
    - LOCALIZER = CT Localizer Image

    Value 4 - Multi-energy CT Image Characteristics:
    - VMI = Virtual Monoenergetic Image
    - MAT_SPECIFIC = Material-Specific Image
    - MAT_REMOVED = Material-Removed Image
    - MAT_FRACTIONAL = Material-Fractional Image
    - EFF_ATOMIC_NUM = Effective Atomic Number Image
    - ELECTRON_DENSITY = Electron Density Image
    - MAT_MODIFIED = Material-Modified Image
    - MAT_VALUE_BASED = Value-Based Image

    Note: PS3.3 reference to be added - see missing_dicom_references.md

    Usage:
        # Using enumerated values
        image_type = ImageType.from_enumerations(
            original_or_derived: CTImageTypeValue1,
            primary_or_secondary: CTImageTypeValue2,
            axial_or_localizer: CTImageTypeValue3,
            multi_energy_ct_type: CTImageTypeValue4 | None = None
        )

        # Using CT characteristics
        image_type = ImageType.from_ct_characteristics(
            is_original: bool = True,
            is_primary: bool = True,
            is_axial: bool = True
        ).with_multi_energy_ct(
            multi_energy_ct_type: CTImageTypeValue4
        )

        # Set the ImageType attribute in a dataset
        dataset.ImageType = image_type.value
    """
    DEFAULT = [CTImageTypeValue1.ORIGINAL.value, CTImageTypeValue2.PRIMARY.value, CTImageTypeValue3.AXIAL.value]

    def __init__(
        self,
        original_or_derived: CTImageTypeValue1 = CTImageTypeValue1.ORIGINAL,
        primary_or_secondary: CTImageTypeValue2 = CTImageTypeValue2.PRIMARY,
        axial_or_localizer: CTImageTypeValue3 = CTImageTypeValue3.AXIAL,
        multi_energy_ct_type: CTImageTypeValue4 | None = None
    ):
        """Initialize ImageType with required and optional components.
        
        Args:
            original_or_derived: Pixel data characteristics (Value 1)
            primary_or_secondary: Patient examination characteristics (Value 2)
            axial_or_localizer: CT image characteristics (Value 3)
            multi_energy_ct_type: Multi-energy CT characteristics (Value 4), optional
            
        Raises:
            TypeError: If any required parameter is not of the expected enum type
            
        Example:
            >>> image_type = ImageType(
            ...     CTImageTypeValue1.ORIGINAL,
            ...     CTImageTypeValue2.PRIMARY,
            ...     CTImageTypeValue3.AXIAL,
            ...     CTImageTypeValue4.VMI
            ... )
        """
        # Validate required parameters
        if not isinstance(original_or_derived, CTImageTypeValue1):
            raise TypeError(f"original_or_derived must be CTImageTypeValue1, got {type(original_or_derived)}")
        if not isinstance(primary_or_secondary, CTImageTypeValue2):
            raise TypeError(f"primary_or_secondary must be CTImageTypeValue2, got {type(primary_or_secondary)}")
        if not isinstance(axial_or_localizer, CTImageTypeValue3):
            raise TypeError(f"axial_or_localizer must be CTImageTypeValue3, got {type(axial_or_localizer)}")
        if multi_energy_ct_type is not None and not isinstance(multi_energy_ct_type, CTImageTypeValue4):
            raise TypeError(f"multi_energy_ct_type must be CTImageTypeValue4 or None, got {type(multi_energy_ct_type)}")
        
        self.original_or_derived = original_or_derived
        self.primary_or_secondary = primary_or_secondary
        self.axial_or_localizer = axial_or_localizer
        self.multi_energy_ct_type = multi_energy_ct_type

    @classmethod
    def from_enumerations(
        cls,
        original_or_derived: CTImageTypeValue1 = CTImageTypeValue1.ORIGINAL,
        primary_or_secondary: CTImageTypeValue2 = CTImageTypeValue2.PRIMARY,
        axial_or_localizer: CTImageTypeValue3 = CTImageTypeValue3.AXIAL,
        multi_energy_ct_type: CTImageTypeValue4 | None = None
    ) -> 'ImageType':
        """Create ImageType instance using specific enum values.
        
        Args:
            original_or_derived: Pixel data characteristics (Value 1)
            primary_or_secondary: Patient examination characteristics (Value 2)
            axial_or_localizer: CT image characteristics (Value 3)
            multi_energy_ct_type: Multi-energy CT characteristics (Value 4), optional
            
        Returns:
            New ImageType instance
            
        Example:
            >>> image_type = ImageType.from_enumerations(
            ...     CTImageTypeValue1.ORIGINAL,
            ...     CTImageTypeValue2.PRIMARY,
            ...     CTImageTypeValue3.AXIAL,
            ...     CTImageTypeValue4.VMI
            ... )
        """
        return cls(original_or_derived, primary_or_secondary, axial_or_localizer, multi_energy_ct_type)

    @classmethod
    def from_ct_characteristics(
        cls,
        is_original: bool = True,
        is_primary: bool = True,
        is_axial: bool = True
    ) -> 'ImageType':
        """Create ImageType instance using boolean characteristics.
        
        Convenient factory method for common CT image type scenarios using boolean flags
        instead of explicit enum values.
        
        Args:
            is_original: True for ORIGINAL, False for DERIVED (default: True)
            is_primary: True for PRIMARY, False for SECONDARY (default: True)
            is_axial: True for AXIAL, False for LOCALIZER (default: True)
            
        Returns:
            New ImageType instance
            
        Example:
            >>> # Create standard original primary axial image
            >>> image_type = ImageType.from_ct_characteristics()
            >>> 
            >>> # Create derived secondary localizer image
            >>> localizer = ImageType.from_ct_characteristics(
            ...     is_original=False,
            ...     is_primary=False,
            ...     is_axial=False
            ... )
        """
        return cls(
            CTImageTypeValue1.ORIGINAL if is_original else CTImageTypeValue1.DERIVED,
            CTImageTypeValue2.PRIMARY if is_primary else CTImageTypeValue2.SECONDARY,
            CTImageTypeValue3.AXIAL if is_axial else CTImageTypeValue3.LOCALIZER
        )

    def with_multi_energy_ct(
        self,
        multi_energy_ct_type: CTImageTypeValue4
    ) -> 'ImageType':
        """Add multi-energy CT characteristics (Value 4) to the image type.
        
        Args:
            multi_energy_ct_type: Multi-energy CT image characteristics
            
        Returns:
            Self for method chaining
            
        Raises:
            TypeError: If multi_energy_ct_type is not a CTImageTypeValue4
            
        Example:
            >>> image_type = ImageType.from_ct_characteristics().with_multi_energy_ct(
            ...     CTImageTypeValue4.VMI
            ... )
        """
        if not isinstance(multi_energy_ct_type, CTImageTypeValue4):
            raise TypeError(f"multi_energy_ct_type must be CTImageTypeValue4, got {type(multi_energy_ct_type)}")
        self.multi_energy_ct_type = multi_energy_ct_type
        return self
    
    @property
    def value(self) -> list[str]:
        """Get the DICOM ImageType value as a list of strings.
        
        Returns:
            List of strings suitable for assignment to pydicom Dataset.ImageType
            
        Example:
            >>> image_type = ImageType.from_ct_characteristics()
            >>> dataset.ImageType = image_type.value
            >>> print(image_type.value)
            ['ORIGINAL', 'PRIMARY', 'AXIAL']
        """
        image_type = [self.original_or_derived.value, self.primary_or_secondary.value, self.axial_or_localizer.value]
        if self.multi_energy_ct_type is not None:
            image_type.append(self.multi_energy_ct_type.value)
        return image_type

    def __str__(self) -> str:
        """Return human-readable string representation.
        
        Returns:
            String representation in list format
        """
        return f"[{', '.join(self.value)}]"

    def __repr__(self) -> str:
        """Return detailed string representation for debugging.
        
        Returns:
            String representation showing class and value
        """
        return f"ImageType({self.value})"
    
    def __eq__(self, other: object) -> bool:
        """Check equality with another ImageType instance.
        
        Args:
            other: Object to compare with
            
        Returns:
            True if both instances have the same image type values
        """
        if not isinstance(other, ImageType):
            return NotImplemented
        return self.value == other.value
    
    def __hash__(self) -> int:
        """Return hash value for use in sets and dictionaries.
        
        Returns:
            Hash value based on the image type values
        """
        return hash(tuple(self.value))
    
    def __contains__(self, item: str) -> bool:
        """Check if a specific value is present in the image type.
        
        Args:
            item: String value to check for
            
        Returns:
            True if the item is present in any position
            
        Example:
            >>> image_type = ImageType.from_ct_characteristics()
            >>> 'ORIGINAL' in image_type
            True
            >>> 'DERIVED' in image_type
            False
        """
        return item in self.value
    
    def is_original(self) -> bool:
        """Check if this is an original (not derived) image.
        
        Returns:
            True if Value 1 is ORIGINAL
        """
        return self.original_or_derived == CTImageTypeValue1.ORIGINAL
    
    def is_derived(self) -> bool:
        """Check if this is a derived (not original) image.
        
        Returns:
            True if Value 1 is DERIVED
        """
        return self.original_or_derived == CTImageTypeValue1.DERIVED
    
    def is_primary(self) -> bool:
        """Check if this is a primary (not secondary) image.
        
        Returns:
            True if Value 2 is PRIMARY
        """
        return self.primary_or_secondary == CTImageTypeValue2.PRIMARY
    
    def is_secondary(self) -> bool:
        """Check if this is a secondary (not primary) image.
        
        Returns:
            True if Value 2 is SECONDARY
        """
        return self.primary_or_secondary == CTImageTypeValue2.SECONDARY
    
    def is_axial(self) -> bool:
        """Check if this is an axial (cross-sectional) image.
        
        Returns:
            True if Value 3 is AXIAL
        """
        return self.axial_or_localizer == CTImageTypeValue3.AXIAL
    
    def is_localizer(self) -> bool:
        """Check if this is a localizer image.
        
        Returns:
            True if Value 3 is LOCALIZER
        """
        return self.axial_or_localizer == CTImageTypeValue3.LOCALIZER
    
    def has_multi_energy_ct(self) -> bool:
        """Check if multi-energy CT characteristics are specified.
        
        Returns:
            True if Value 4 is present
        """
        return self.multi_energy_ct_type is not None
    
    def matches(self, other: 'ImageType') -> bool:
        """Check if this ImageType matches another ImageType exactly.
        
        Args:
            other: Another ImageType instance to compare with
            
        Returns:
            True if all values match exactly
            
        Raises:
            TypeError: If other is not an ImageType instance
        """
        if not isinstance(other, ImageType):
            raise TypeError(f"Can only match against ImageType, got {type(other)}")
        return (self.original_or_derived == other.original_or_derived and
                self.primary_or_secondary == other.primary_or_secondary and
                self.axial_or_localizer == other.axial_or_localizer and
                self.multi_energy_ct_type == other.multi_energy_ct_type)


class ModalityLutType(Enum):
    """Modality LUT Type (0028,3004) - DICOM VR: LO

    Defined Terms per DICOM PS3.3 C.11.1:
    - OD = Optical density (thousands)
    - HU = Hounsfield Units (CT)
    - US = Unspecified
    - MGML = mg/ml
    - Z_EFF = Effective Atomic Number
    - ED = Electron density in 10^23 electrons/ml
    - EDW = Electron density normalized to water
    - HU_MOD = Modified Hounsfield Unit
    - PCT = Percentage (%)
    """

    OD = "OD"
    HU = "HU"
    US = "US"
    MGML = "MGML"
    Z_EFF = "Z_EFF"
    ED = "ED"
    EDW = "EDW"
    HU_MOD = "HU_MOD"
    PCT = "PCT"


class VoiLutFunction(Enum):
    """VOI LUT Function (0028,1056) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.11.2:
    - LINEAR = Default linear function
    - LINEAR_EXACT = Exact linear function
    - SIGMOID = Sigmoid function
    """

    LINEAR = "LINEAR"
    LINEAR_EXACT = "LINEAR_EXACT"
    SIGMOID = "SIGMOID"


class FrameType(Enum):
    """Frame Type (0008,9007) - DICOM VR: CS

    Multi-valued enumeration for Enhanced Multi-frame images per DICOM PS3.3.
    Frame Type provides frame-specific classification for multi-dimensional image organization.

    Value 1 - Frame Data Characteristics:
    - ORIGINAL = Original frame data
    - DERIVED = Derived frame data

    Value 2 - Frame Acquisition Characteristics:
    - PRIMARY = Primary acquisition frame
    - SECONDARY = Secondary/processed frame

    Value 3+ - Frame-Specific Classifications:
    - STATIC = Static frame (no temporal variation)
    - DYNAMIC = Dynamic frame (temporal variation)
    - GATED = Cardiac/respiratory gated frame
    - UNGATED = Non-gated frame
    - PERFUSION = Perfusion imaging frame
    - ANGIO = Angiographic frame
    - CARDIAC = Cardiac imaging frame
    - RESPIRATORY = Respiratory imaging frame
    - CONTRAST = Contrast-enhanced frame
    - PRE_CONTRAST = Pre-contrast frame
    - POST_CONTRAST = Post-contrast frame
    - ARTERIAL = Arterial phase frame
    - VENOUS = Venous phase frame
    - DELAYED = Delayed phase frame
    - EQUILIBRIUM = Equilibrium phase frame
    - WASH_IN = Wash-in phase frame
    - WASH_OUT = Wash-out phase frame
    - BASELINE = Baseline frame
    - STRESS = Stress frame
    - REST = Rest frame
    - SYSTOLE = Systolic frame
    - DIASTOLE = Diastolic frame
    - INSPIRATION = Inspiration frame
    - EXPIRATION = Expiration frame
    """

    # Value 1 - Frame Data Characteristics
    ORIGINAL = "ORIGINAL"
    DERIVED = "DERIVED"

    # Value 2 - Frame Acquisition Characteristics
    PRIMARY = "PRIMARY"
    SECONDARY = "SECONDARY"

    # Value 3+ - Frame-Specific Classifications
    STATIC = "STATIC"
    DYNAMIC = "DYNAMIC"
    GATED = "GATED"
    UNGATED = "UNGATED"
    PERFUSION = "PERFUSION"
    ANGIO = "ANGIO"
    CARDIAC = "CARDIAC"
    RESPIRATORY = "RESPIRATORY"
    CONTRAST = "CONTRAST"
    PRE_CONTRAST = "PRE_CONTRAST"
    POST_CONTRAST = "POST_CONTRAST"
    ARTERIAL = "ARTERIAL"
    VENOUS = "VENOUS"
    DELAYED = "DELAYED"
    EQUILIBRIUM = "EQUILIBRIUM"
    WASH_IN = "WASH_IN"
    WASH_OUT = "WASH_OUT"
    BASELINE = "BASELINE"
    STRESS = "STRESS"
    REST = "REST"
    SYSTOLE = "SYSTOLE"
    DIASTOLE = "DIASTOLE"
    INSPIRATION = "INSPIRATION"
    EXPIRATION = "EXPIRATION"


class ContrastBolusAgent(Enum):
    """Contrast/Bolus Agent (0018,0010) - DICOM VR: LO

    Standardized contrast agent names for imaging protocols.
    These values represent commonly used contrast agents in medical imaging.

    Iodinated Contrast Agents (CT, Angiography):
    - IOHEXOL = Iohexol (Omnipaque)
    - IOPAMIDOL = Iopamidol (Isovue)
    - IOVERSOL = Ioversol (Optiray)
    - IODIXANOL = Iodixanol (Visipaque)
    - IOPROMIDE = Iopromide (Ultravist)

    Gadolinium-based Contrast Agents (MRI):
    - GADOPENTETATE = Gadopentetate dimeglumine (Magnevist)
    - GADOBUTROL = Gadobutrol (Gadavist)
    - GADOTERATE = Gadoterate meglumine (Dotarem)
    - GADOTERIDOL = Gadoteridol (ProHance)
    - GADOBENATE = Gadobenate dimeglumine (MultiHance)

    Barium-based Agents (GI Studies):
    - BARIUM_SULFATE = Barium sulfate suspension

    Other Agents:
    - CARBON_DIOXIDE = Carbon dioxide gas
    - SALINE = Saline solution
    """

    # Iodinated Contrast Agents
    IOHEXOL = "Iohexol"
    IOPAMIDOL = "Iopamidol"
    IOVERSOL = "Ioversol"
    IODIXANOL = "Iodixanol"
    IOPROMIDE = "Iopromide"

    # Gadolinium-based Contrast Agents
    GADOPENTETATE = "Gadopentetate dimeglumine"
    GADOBUTROL = "Gadobutrol"
    GADOTERATE = "Gadoterate meglumine"
    GADOTERIDOL = "Gadoteridol"
    GADOBENATE = "Gadobenate dimeglumine"

    # Barium-based Agents
    BARIUM_SULFATE = "Barium sulfate"

    # Other Agents
    CARBON_DIOXIDE = "Carbon dioxide"
    SALINE = "Saline"


class ContrastBolusIngredient(Enum):
    """Contrast/Bolus Ingredient (0018,1048) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.7.6.4:
    - IODINE = Iodine-based contrast agent
    - GADOLINIUM = Gadolinium-based contrast agent
    - CARBON_DIOXIDE = Carbon dioxide contrast agent
    - BARIUM = Barium-based contrast agent
    """

    IODINE = "IODINE"
    GADOLINIUM = "GADOLINIUM"
    CARBON_DIOXIDE = "CARBON DIOXIDE"
    BARIUM = "BARIUM"


class MultiEnergyCTAcquisition(Enum):
    """Multi-energy CT Acquisition (0018,9361) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - YES = Image is created by means of Multi-energy technique
    - NO = Image is not created by means of Multi-energy technique

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    YES = "YES"
    NO = "NO"


class RotationDirection(Enum):
    """Rotation Direction (0018,1140) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - CW = Clockwise rotation
    - CC = Counter clockwise rotation

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    CW = "CW"
    CC = "CC"


class ExposureModulationType(Enum):
    """Exposure Modulation Type (0018,9323) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - NONE = No exposure modulation used

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    NONE = "NONE"


class CTSamplesPerPixel(Enum):
    """CT Samples per Pixel (0028,0002) - DICOM VR: US

    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 1 = Single sample per pixel for CT images
    """

    ONE = 1


class CTBitsAllocated(Enum):
    """CT Bits Allocated (0028,0100) - DICOM VR: US

    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 16 = 16 bits allocated for CT images
    """

    SIXTEEN = 16


class CTBitsStored(Enum):
    """CT Bits Stored (0028,0101) - DICOM VR: US

    Defined Terms per DICOM PS3.3 C.7.6.3:
    - 12 = 12 bits stored
    - 13 = 13 bits stored
    - 14 = 14 bits stored
    - 15 = 15 bits stored
    - 16 = 16 bits stored
    """

    TWELVE = 12
    THIRTEEN = 13
    FOURTEEN = 14
    FIFTEEN = 15
    SIXTEEN = 16


class MultiEnergySourceTechnique(Enum):
    """Multi-energy Source Technique (0018,9368) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - SWITCHING_SOURCE = Physical X-Ray source uses beam mode switching
    - CONSTANT_SOURCE = Physical X-Ray source uses beam with constant characteristics

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    SWITCHING_SOURCE = "SWITCHING_SOURCE"
    CONSTANT_SOURCE = "CONSTANT_SOURCE"


class MultiEnergyDetectorType(Enum):
    """Multi-energy Detector Type (0018,9372) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - INTEGRATING = Physical detector integrates the full X-Ray spectrum
    - MULTILAYER = Physical detector layers absorb different parts of X-Ray spectrum
    - PHOTON_COUNTING = Physical detector counts photons with energy discrimination

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    INTEGRATING = "INTEGRATING"
    MULTILAYER = "MULTILAYER"
    PHOTON_COUNTING = "PHOTON_COUNTING"


class RescaleType(Enum):
    """Rescale Type (0028,1054) - DICOM VR: LO

    Defined Terms per DICOM PS3.3 C.7.6.1:
    - HU = Hounsfield Units
    - US = Unspecified
    - MGML = Milligrams per milliliter
    - PCNT = Percent
    - CPS = Counts per second
    - NONE = No rescaling function
    - CM = Centimeters
    - MM = Millimeters
    - PIXVAL = Pixel value
    - COUNTS = Counts
    - PROPCNT = Proportional to counts
    - DISP = Display units
    - UMOL = Micromoles
    - CONC = Concentration
    - RWU = Relative water units
    - DENS = Density
    - TEMP = Temperature
    - FLOW = Flow
    - PERF = Perfusion
    - DIFF = Diffusion
    - RELAX = Relaxation
    - METAB = Metabolic
    - RATIO = Ratio
    - OTHER = Other units
    - DVPX = Device-specific units
    - COEF = Coefficient
    - GRAD = Gradient
    - FRAC = Fraction
    - MASS = Mass
    - MLMIN = Milliliters per minute
    - MLMINM2 = Milliliters per minute per square meter
    - ML100GM = Milliliters per 100 grams
    - MLMIN100G = Milliliters per minute per 100 grams
    - DEGC = Degrees Celsius
    - SEC = Seconds
    - MSEC = Milliseconds
    - USEC = Microseconds
    - HZ = Hertz
    - PPM = Parts per million
    - RAD = Radians
    - DEG = Degrees
    - OD = Optical density (thousands)
    - Z_EFF = Effective Atomic Number
    - ED = Electron density in 10^23 electrons/ml
    - EDW = Electron density normalized to water
    - HU_MOD = Modified Hounsfield Unit
    - PCT = Percentage (%)

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    HU = "HU"
    US = "US"
    MGML = "MGML"
    PCNT = "PCNT"
    CPS = "CPS"
    NONE = "NONE"
    CM = "CM"
    MM = "MM"
    PIXVAL = "PIXVAL"
    COUNTS = "COUNTS"
    PROPCNT = "PROPCNT"
    DISP = "DISP"
    UMOL = "UMOL"
    CONC = "CONC"
    RWU = "RWU"
    DENS = "DENS"
    TEMP = "TEMP"
    FLOW = "FLOW"
    PERF = "PERF"
    DIFF = "DIFF"
    RELAX = "RELAX"
    METAB = "METAB"
    RATIO = "RATIO"
    OTHER = "OTHER"
    DVPX = "DVPX"
    COEF = "COEF"
    GRAD = "GRAD"
    FRAC = "FRAC"
    MASS = "MASS"
    MLMIN = "MLMIN"
    MLMINM2 = "MLMINM2"
    ML100GM = "ML100GM"
    MLMIN100G = "MLMIN100G"
    DEGC = "DEGC"
    SEC = "SEC"
    MSEC = "MSEC"
    USEC = "USEC"
    HZ = "HZ"
    PPM = "PPM"
    RAD = "RAD"
    DEG = "DEG"
    OD = "OD"
    Z_EFF = "Z_EFF"
    ED = "ED"
    EDW = "EDW"
    HU_MOD = "HU_MOD"
    PCT = "PCT"


class FilterMaterial(Enum):
    """Filter Material (0018,7050) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - AL = Aluminum
    - CU = Copper
    - MO = Molybdenum
    - RH = Rhodium
    - AG = Silver
    - SN = Tin
    - GD = Gadolinium
    - HO = Holmium
    - ER = Erbium
    - YB = Ytterbium
    - W = Tungsten
    - PB = Lead
    - TI = Titanium
    - NI = Nickel
    - FE = Iron
    - BE = Beryllium
    - C = Carbon
    - POLYIMIDE = Polyimide
    - LEXAN = Lexan
    - KAPTON = Kapton
    - MYLAR = Mylar

    Note: PS3.3 reference to be added - see missing_dicom_references.md
    """

    ALUMINUM = "AL"
    COPPER = "CU"
    MOLYBDENUM = "MO"
    RHODIUM = "RH"
    SILVER = "AG"
    TIN = "SN"
    GADOLINIUM = "GD"
    HOLMIUM = "HO"
    ERBIUM = "ER"
    YTTERBIUM = "YB"
    TUNGSTEN = "W"
    LEAD = "PB"
    TITANIUM = "TI"
    NICKEL = "NI"
    IRON = "FE"
    BERYLLIUM = "BE"
    CARBON = "C"
    POLYIMIDE = "POLYIMIDE"
    LEXAN = "LEXAN"
    KAPTON = "KAPTON"
    MYLAR = "MYLAR"


class ScanOptions(Enum):
    """Scan Options (0018,0022) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - HELICAL_CT = Helical/spiral CT scan
    - AXIAL_CT = Axial/sequential CT scan
    - CINE_CT = Cine CT scan
    - CARDIAC_GATING = Cardiac gated scan
    - RESPIRATORY_GATING = Respiratory gated scan
    - CONTRAST_ENHANCED = Contrast enhanced scan
    - NON_CONTRAST = Non-contrast scan
    - PERFUSION = Perfusion scan
    - ANGIOGRAPHY = CT angiography
    - CALCIUM_SCORING = Calcium scoring scan

    Note: This is not a DICOM-defined enumeration but provides common values
    for convenience. Custom values are allowed per DICOM standard.
    """

    HELICAL_CT = "HELICAL_CT"
    AXIAL_CT = "AXIAL_CT"
    CINE_CT = "CINE_CT"
    CARDIAC_GATING = "CARDIAC_GATING"
    RESPIRATORY_GATING = "RESPIRATORY_GATING"
    CONTRAST_ENHANCED = "CONTRAST_ENHANCED"
    NON_CONTRAST = "NON_CONTRAST"
    PERFUSION = "PERFUSION"
    ANGIOGRAPHY = "ANGIOGRAPHY"
    CALCIUM_SCORING = "CALCIUM_SCORING"


class FilterType(Enum):
    """Filter Type (0018,1160) - DICOM VR: CS

    Defined Terms per DICOM PS3.3 C.8.2.1:
    - NONE = No filter
    - ALUMINUM = Aluminum filter
    - COPPER = Copper filter
    - MOLYBDENUM = Molybdenum filter
    - RHODIUM = Rhodium filter
    - SILVER = Silver filter
    - TIN = Tin filter
    - GADOLINIUM = Gadolinium filter
    - TUNGSTEN = Tungsten filter
    - LEAD = Lead filter
    - TITANIUM = Titanium filter
    - BERYLLIUM = Beryllium filter
    - CARBON = Carbon filter
    - COMPOSITE = Composite filter
    - WEDGE = Wedge filter
    - BOW_TIE = Bow-tie filter
    - FLAT = Flat filter

    Note: This is not a DICOM-defined enumeration but provides common values
    for convenience. Custom values are allowed per DICOM standard.
    """

    NONE = "NONE"
    ALUMINUM = "ALUMINUM"
    COPPER = "COPPER"
    MOLYBDENUM = "MOLYBDENUM"
    RHODIUM = "RHODIUM"
    SILVER = "SILVER"
    TIN = "TIN"
    GADOLINIUM = "GADOLINIUM"
    TUNGSTEN = "TUNGSTEN"
    LEAD = "LEAD"
    TITANIUM = "TITANIUM"
    BERYLLIUM = "BERYLLIUM"
    CARBON = "CARBON"
    COMPOSITE = "COMPOSITE"
    WEDGE = "WEDGE"
    BOW_TIE = "BOW_TIE"
    FLAT = "FLAT"
