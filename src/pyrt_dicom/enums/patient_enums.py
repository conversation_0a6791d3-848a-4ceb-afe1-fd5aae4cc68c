"""Patient-related DICOM enumerations."""

from enum import Enum


class PatientSex(Enum):
    """Patient Sex (0010,0040) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1:
    - M = Male
    - F = Female  
    - O = Other
    """
    MALE = "M"
    FEMALE = "F" 
    OTHER = "O"


class ResponsiblePersonRole(Enum):
    """Responsible Person Role (0010,2298) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1.1.2:
    - OWNER = Owner of the patient
    - PARENT = Parent of the patient
    - CHILD = Child of the patient
    - SPOUSE = Spouse of the patient
    - SIBLING = Sibling of the patient
    - RELATIVE = Other relative of the patient
    - G<PERSON><PERSON><PERSON><PERSON> = Legal guardian of the patient
    - CUSTO<PERSON>AN = Legal custodian of the patient
    - AGENT = Agent acting on behalf of the patient
    - INVESTIGATOR = Investigator responsible for the patient
    - VETERINARIAN = Veterinarian responsible for the patient
    """
    OWNER = "OWNER"
    PARENT = "PARENT"
    CHILD = "CHILD"
    SPOUSE = "SPOUSE"
    SIBLING = "SIBLING"
    RELATIVE = "RELATIVE"
    GUARDIAN = "GUARDIAN"
    CUSTODIAN = "CUSTODIAN"
    AGENT = "AGENT"
    INVESTIGATOR = "INVESTIGATOR"
    VETERINARIAN = "VETERINARIAN"


class TypeOfPatientID(Enum):
    """Type of Patient ID (0010,0022) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1:
    - TEXT = Text-based patient identifier
    - RFID = Radio Frequency Identification based identifier
    - BARCODE = Barcode-based identifier
    """
    TEXT = "TEXT"
    RFID = "RFID"
    BARCODE = "BARCODE"


class PatientOrientationCode(Enum):
    """Patient Orientation Code Sequence (0054,0410) - SNOMED CT codes
    
    Common SNOMED CT codes for patient orientation with respect to gravity.
    Used in Enhanced Patient Orientation Module per DICOM PS3.3 C.7.6.30.
    
    Basic Orientations:
    - RECUMBENT = Patient lying down (102538003)
    - ERECT = Patient standing upright (10904000)
    - SITTING = Patient in sitting position (33586001)
    - PRONE = Patient lying face down (1240000)
    - SUPINE = Patient lying face up (40199007)
    - LATERAL = Patient lying on side (102536004)
    
    Decubitus Positions:
    - LEFT_LATERAL_DECUBITUS = Patient lying on left side (102535000)
    - RIGHT_LATERAL_DECUBITUS = Patient lying on right side (102537008)
    
    Trendelenburg Positions:
    - TRENDELENBURG = Head lower than feet (34106002)
    - REVERSE_TRENDELENBURG = Head higher than feet (102539006)
    """
    # Basic Orientations
    RECUMBENT = "102538003"
    ERECT = "10904000"
    SITTING = "33586001"
    PRONE = "1240000"
    SUPINE = "40199007"
    LATERAL = "102536004"
    
    # Decubitus Positions
    LEFT_LATERAL_DECUBITUS = "102535000"
    RIGHT_LATERAL_DECUBITUS = "102537008"
    
    # Trendelenburg Positions
    TRENDELENBURG = "34106002"
    REVERSE_TRENDELENBURG = "102539006"


class PatientOrientationModifierCode(Enum):
    """Patient Orientation Modifier Code Sequence (0054,0412) - SNOMED CT codes
    
    Common SNOMED CT codes for patient orientation modifiers.
    Used in Enhanced Patient Orientation Module per DICOM PS3.3 C.7.6.30.
    
    Position Modifiers:
    - SUPINE = Lying face up (40199007)
    - PRONE = Lying face down (1240000)
    - LEFT_LATERAL = Lying on left side (102535000)
    - RIGHT_LATERAL = Lying on right side (102537008)
    - SEMI_ERECT = Partially upright (102540008)
    - JACKKNIFE = Jackknife position (102541007)
    
    Arm Positions:
    - ARMS_UP = Arms raised above head (102542000)
    - ARMS_DOWN = Arms at sides (102543005)
    - ARMS_CROSSED = Arms crossed over chest (102544004)
    
    Leg Positions:
    - LEGS_EXTENDED = Legs extended (102545003)
    - LEGS_FLEXED = Legs flexed (102546002)
    """
    # Position Modifiers
    SUPINE = "40199007"
    PRONE = "1240000"
    LEFT_LATERAL = "102535000"
    RIGHT_LATERAL = "102537008"
    SEMI_ERECT = "102540008"
    JACKKNIFE = "102541007"
    
    # Arm Positions
    ARMS_UP = "102542000"
    ARMS_DOWN = "102543005"
    ARMS_CROSSED = "102544004"
    
    # Leg Positions
    LEGS_EXTENDED = "102545003"
    LEGS_FLEXED = "102546002"


class PatientEquipmentRelationshipCode(Enum):
    """Patient Equipment Relationship Code Sequence (0054,0414) - SNOMED CT codes
    
    Common SNOMED CT codes for patient-equipment relationship.
    Used in Enhanced Patient Orientation Module per DICOM PS3.3 C.7.6.30.
    
    Entry Directions:
    - HEAD_FIRST = Patient enters head first (102540008)
    - FEET_FIRST = Patient enters feet first (102541007)
    - LEFT_FIRST = Patient enters left side first (102542000)
    - RIGHT_FIRST = Patient enters right side first (102543005)
    
    Equipment Orientations:
    - GANTRY_TILTED = Equipment gantry is tilted (102544004)
    - GANTRY_UPRIGHT = Equipment gantry is upright (102545003)
    - TABLE_TILTED = Patient table is tilted (102546002)
    - TABLE_HORIZONTAL = Patient table is horizontal (102547006)
    
    Positioning Aids:
    - IMMOBILIZATION_DEVICE = Immobilization device used (102548001)
    - POSITIONING_AID = Positioning aid used (102549009)
    - CONTRAST_AGENT = Contrast agent administered (102550009)
    """
    # Entry Directions
    HEAD_FIRST = "102540008"
    FEET_FIRST = "102541007"
    LEFT_FIRST = "102542000"
    RIGHT_FIRST = "102543005"
    
    # Equipment Orientations
    GANTRY_TILTED = "102544004"
    GANTRY_UPRIGHT = "102545003"
    TABLE_TILTED = "102546002"
    TABLE_HORIZONTAL = "102547006"
    
    # Positioning Aids
    IMMOBILIZATION_DEVICE = "102548001"
    POSITIONING_AID = "102549009"
    CONTRAST_AGENT = "102550009"
