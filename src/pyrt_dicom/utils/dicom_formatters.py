"""DICOM data formatting utilities."""
from datetime import datetime, date
from typing import Any


def format_date_value(date_value: Any) -> str:
    """Format date values to DICOM DA format (YYYYMMDD).
    
    Args:
        date_value: Date value (str, datetime, or date object)
        
    Returns:
        str: Date formatted as YYYYMMDD string
    """
    if isinstance(date_value, (datetime, date)):
        return date_value.strftime("%Y%m%d")
    return str(date_value)


def format_time_value(time_value: Any) -> str:
    """Format time values to DICOM TM format (HHMMSS).
    
    Args:
        time_value: Time value (str or datetime object)
        
    Returns:
        str: Time formatted as HHMMSS string
    """
    if isinstance(time_value, datetime):
        if time_value.microsecond:
            return f"{time_value.strftime('%H%M%S')}.{time_value.microsecond:06d}"
        return time_value.strftime("%H%M%S")
    return str(time_value)


def format_enum_string(enum_value: Any) -> str:
    """Extract string value from enum objects.
    
    Args:
        enum_value: Enum object or other value
        
    Returns:
        str: Value from enum or original value
    """
    return str(enum_value.value) if hasattr(enum_value, 'value') else str(enum_value)
    
def format_enum_int(enum_value: Any) -> int:
    """Extract int value from enum objects.
    
    Args:
        enum_value: Enum object or other value
        
    Returns:
        int: Value from enum or original value
    """
    return int(enum_value.value) if hasattr(enum_value, 'value') else int(enum_value)
