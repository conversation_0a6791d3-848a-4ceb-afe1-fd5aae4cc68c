"""RT Fraction Scheme Module DICOM validation - PS3.3 C.8.8.13

Validates RT Fraction Scheme Module according to DICOM PS3.3 specification.
The RT Fraction Scheme Module contains Attributes that describe a single or
multiple scheme of dose descriptions with fractionation patterns and beam or
brachytherapy application setup specifications.

Key validation areas:
- Type 1/2/3 element presence validation
- Type 1C/2C conditional requirements
- Mutual exclusivity constraints (beams vs brachy setups)
- Enumerated value validation
- Fraction pattern consistency
- Sequence structure validation
- Cross-field logical constraints
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import BeamDoseMeaning, DoseCalibrationConditionsVerifiedFlag
from ...enums.dose_enums import DoseType


class RTFractionSchemeValidator(BaseValidator):
    """Validator for DICOM RT Fraction Scheme Module (PS3.3 C.8.8.13).

    Validates all aspects of the RT Fraction Scheme Module including:
    - Required element presence (Type 1/2)
    - Conditional requirements (Type 1C/2C)
    - Enumerated value constraints
    - Sequence structure and item requirements
    - Fraction scheme logical consistency
    - Cross-field validation rules

    The validator ensures 100% adherence to DICOM PS3.3 specifications
    and provides clear, actionable error messages for any violations.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Fraction Scheme Module requirements on any pydicom Dataset.

        Performs comprehensive validation of the RT Fraction Scheme Module according to
        DICOM PS3.3 C.8.8.13 specification, including all Type 1/2/3/1C/2C requirements,
        enumerated values, sequence structures, and logical constraints.

        Args:
            dataset: pydicom Dataset containing RT Fraction Scheme Module data to validate.
                Must contain FractionGroupSequence (300A,0070) as the root element.
            config: Optional validation configuration to control validation scope.
                If None, uses default configuration with all validations enabled.

        Returns:
            ValidationResult: Comprehensive validation result containing:
                - errors: List of DICOM standard violations that must be fixed
                - warnings: List of recommendations and potential issues
                - is_valid: Boolean indicating if dataset passes all validations

        Note:
            This validator checks the complete RT Fraction Scheme Module specification
            including mutual exclusivity of beams vs brachy setups, fraction pattern
            consistency, and all conditional requirements.
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 and Type 2 required elements first
        RTFractionSchemeValidator._validate_required_elements(dataset, result)

        # Validate Type 1C and Type 2C conditional requirements
        if config.validate_conditional_requirements:
            RTFractionSchemeValidator._validate_conditional_requirements(dataset, result)

        # Validate enumerated values against DICOM specifications
        if config.check_enumerated_values:
            RTFractionSchemeValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures and item requirements
        if config.validate_sequences:
            RTFractionSchemeValidator._validate_sequence_requirements(dataset, result)

        # Validate fraction scheme logical consistency and constraints
        RTFractionSchemeValidator._validate_fraction_scheme_consistency(dataset, result)

        # Validate cross-field relationships and semantic constraints
        RTFractionSchemeValidator._validate_semantic_constraints(dataset, result)

        return result

    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 and Type 2 required elements.

        Validates presence of all required elements according to DICOM PS3.3 C.8.8.13.
        Type 1 elements must be present with valid values.
        Type 2 elements must be present but may have empty values.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to accumulate errors and warnings
        """
        # Type 1: Fraction Group Sequence (300A,0070)
        if not hasattr(dataset, 'FractionGroupSequence'):
            result.add_error(
                "Fraction Group Sequence (300A,0070) is required (Type 1). "
                "This sequence describes the fractionation scheme and must contain one or more items."
            )
            return  # Cannot continue validation without this sequence

        fraction_group_seq = dataset.FractionGroupSequence
        if not fraction_group_seq:
            result.add_error(
                "Fraction Group Sequence (300A,0070) cannot be empty (Type 1). "
                "One or more Items shall be included in this Sequence."
            )
            return

        # Validate each fraction group item
        for i, fraction_item in enumerate(fraction_group_seq):
            item_prefix = f"Fraction Group Sequence item {i}"

            # Type 1: Fraction Group Number (300A,0071)
            if not fraction_item.get('FractionGroupNumber'):
                result.add_error(
                    f"{item_prefix}: Fraction Group Number (300A,0071) is required (Type 1). "
                    "This number must be unique within the RT Plan."
                )

            # Type 2: Number of Fractions Planned (300A,0078)
            if 'NumberOfFractionsPlanned' not in fraction_item:
                result.add_error(
                    f"{item_prefix}: Number of Fractions Planned (300A,0078) is required (Type 2). "
                    "Total number of treatments prescribed for this fraction group."
                )

            # Type 1: Number of Beams (300A,0080)
            if 'NumberOfBeams' not in fraction_item:
                result.add_error(
                    f"{item_prefix}: Number of Beams (300A,0080) is required (Type 1). "
                    "If greater than zero, Number of Brachy Application Setups shall equal zero."
                )

            # Type 1: Number of Brachy Application Setups (300A,00A0)
            if 'NumberOfBrachyApplicationSetups' not in fraction_item:
                result.add_error(
                    f"{item_prefix}: Number of Brachy Application Setups (300A,00A0) is required (Type 1). "
                    "If greater than zero, Number of Beams shall equal zero."
                )

    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and Type 2C conditional requirements.

        Validates all conditional requirements where presence of one element
        depends on the value or presence of another element.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to accumulate errors and warnings
        """

        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])
        for i, fraction_item in enumerate(fraction_group_seq):
            item_prefix = f"Fraction Group Sequence item {i}"
            number_of_beams = fraction_item.get('NumberOfBeams', 0)
            number_of_brachy_setups = fraction_item.get('NumberOfBrachyApplicationSetups', 0)

            # Type 1C: Referenced Beam Sequence (300C,0004) required if Number of Beams > 0
            if number_of_beams > 0:
                if not fraction_item.get('ReferencedBeamSequence'):
                    result.add_error(
                        f"{item_prefix}: Referenced Beam Sequence (300C,0004) is required when "
                        f"Number of Beams (300A,0080) > 0 (Type 1C). One or more Items shall be included "
                        "to specify treatment beams in this fraction group."
                    )
                else:
                    # Validate sequence has correct number of items
                    ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
                    if len(ref_beam_seq) != number_of_beams:
                        result.add_warning(
                            f"{item_prefix}: Referenced Beam Sequence contains {len(ref_beam_seq)} items "
                            f"but Number of Beams is {number_of_beams}. Consider verifying beam count consistency."
                        )

            # Type 1C: Referenced Brachy Application Setup Sequence (300C,000A) required if Number of Brachy Application Setups > 0
            if number_of_brachy_setups > 0:
                if not fraction_item.get('ReferencedBrachyApplicationSetupSequence'):
                    result.add_error(
                        f"{item_prefix}: Referenced Brachy Application Setup Sequence (300C,000A) is required when "
                        f"Number of Brachy Application Setups (300A,00A0) > 0 (Type 1C). One or more Items shall be "
                        "included to specify brachytherapy setups in this fraction group."
                    )
                else:
                    # Validate sequence has correct number of items
                    ref_brachy_seq = fraction_item.get('ReferencedBrachyApplicationSetupSequence', [])
                    if len(ref_brachy_seq) != number_of_brachy_setups:
                        result.add_warning(
                            f"{item_prefix}: Referenced Brachy Application Setup Sequence contains {len(ref_brachy_seq)} items "
                            f"but Number of Brachy Application Setups is {number_of_brachy_setups}. "
                            "Consider verifying setup count consistency."
                        )

            # Validate Referenced Beam Sequence conditional requirements
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            for j, beam_item in enumerate(ref_beam_seq):
                beam_prefix = f"Referenced Beam Sequence item {j} in Fraction Group {i}"

                # Type 1C: Beam Dose Type (300A,0090) required if Alternate Beam Dose is present
                if 'AlternateBeamDose' in beam_item and 'BeamDoseType' not in beam_item:
                    result.add_error(
                        f"{beam_prefix}: Beam Dose Type (300A,0090) is required when "
                        "Alternate Beam Dose (300A,0091) is present (Type 1C). "
                        "Must be PHYSICAL or EFFECTIVE and different from Alternate Beam Dose Type."
                    )

                # Type 1C: Alternate Beam Dose Type (300A,0092) required if Alternate Beam Dose is present
                if 'AlternateBeamDose' in beam_item and 'AlternateBeamDoseType' not in beam_item:
                    result.add_error(
                        f"{beam_prefix}: Alternate Beam Dose Type (300A,0092) is required when "
                        "Alternate Beam Dose (300A,0091) is present (Type 1C). "
                        "Must be PHYSICAL or EFFECTIVE and different from Beam Dose Type."
                    )

                # Validate dose types are different when both present
                if ('BeamDoseType' in beam_item and 'AlternateBeamDoseType' in beam_item and
                    beam_item.get('BeamDoseType') == beam_item.get('AlternateBeamDoseType')):
                    result.add_error(
                        f"{beam_prefix}: Beam Dose Type (300A,0090) and Alternate Beam Dose Type (300A,0092) "
                        "shall not have the same value. Use different dose types (PHYSICAL vs EFFECTIVE)."
                    )

                # Type 1C: Dose Calibration Conditions Sequence validation
                calibration_flag = beam_item.get('DoseCalibrationConditionsVerifiedFlag')
                has_calibration_seq = 'DoseCalibrationConditionsSequence' in beam_item
                has_device_config_seq = 'RadiationDeviceConfigurationAndCommissioningKeySequence' in beam_item

                if calibration_flag == "YES":
                    if not has_calibration_seq and not has_device_config_seq:
                        result.add_error(
                            f"{beam_prefix}: Either Dose Calibration Conditions Sequence (300C,0120) or "
                            "Radiation Device Configuration and Commissioning Key Sequence (300A,065A) "
                            "is required when Dose Calibration Conditions Verified Flag (300C,0123) = YES (Type 1C)."
                        )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications.

        Validates all enumerated values in the RT Fraction Scheme Module to ensure
        they match the exact values specified in DICOM PS3.3.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to accumulate errors and warnings
        """

        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])
        for i, fraction_item in enumerate(fraction_group_seq):
            item_prefix = f"Fraction Group Sequence item {i}"

            # Beam Dose Meaning (300A,008B) - Type 3
            beam_dose_meaning = fraction_item.get('BeamDoseMeaning', '')
            if beam_dose_meaning:
                valid_meanings = [meaning.value for meaning in BeamDoseMeaning]
                if beam_dose_meaning not in valid_meanings:
                    result.add_error(
                        f"{item_prefix}: Beam Dose Meaning (300A,008B) value '{beam_dose_meaning}' is invalid. "
                        f"Must be one of: {', '.join(valid_meanings)}. "
                        "BEAM_LEVEL = individually calculated for this Beam, "
                        "FRACTION_LEVEL = calculated on fraction level with nominal distribution."
                    )

            # Validate Referenced Beam Sequence enumerated values
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            for j, beam_item in enumerate(ref_beam_seq):
                beam_prefix = f"Referenced Beam Sequence item {j} in Fraction Group {i}"

                # Beam Dose Type (300A,0090) - Type 1C
                beam_dose_type = beam_item.get('BeamDoseType', '')
                if beam_dose_type:
                    valid_dose_types = [dtype.value for dtype in DoseType]
                    if beam_dose_type not in valid_dose_types:
                        result.add_error(
                            f"{beam_prefix}: Beam Dose Type (300A,0090) value '{beam_dose_type}' is invalid. "
                            f"Must be one of: {', '.join(valid_dose_types)}. "
                            "PHYSICAL = physical dose, EFFECTIVE = effective dose."
                        )

                # Alternate Beam Dose Type (300A,0092) - Type 1C
                alt_beam_dose_type = beam_item.get('AlternateBeamDoseType', '')
                if alt_beam_dose_type:
                    valid_dose_types = [dtype.value for dtype in DoseType]
                    if alt_beam_dose_type not in valid_dose_types:
                        result.add_error(
                            f"{beam_prefix}: Alternate Beam Dose Type (300A,0092) value '{alt_beam_dose_type}' is invalid. "
                            f"Must be one of: {', '.join(valid_dose_types)}. "
                            "PHYSICAL = physical dose, EFFECTIVE = effective dose."
                        )

                # Dose Calibration Conditions Verified Flag (300C,0123) - Type 3
                calibration_flag = beam_item.get('DoseCalibrationConditionsVerifiedFlag', '')
                if calibration_flag:
                    valid_flags = [flag.value for flag in DoseCalibrationConditionsVerifiedFlag]
                    if calibration_flag not in valid_flags:
                        result.add_error(
                            f"{beam_prefix}: Dose Calibration Conditions Verified Flag (300C,0123) "
                            f"value '{calibration_flag}' is invalid. Must be one of: {', '.join(valid_flags)}. "
                            "YES = verifiable calibration conditions used, NO = not used."
                        )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure and item requirements.

        Validates that all sequences contain the required sub-attributes and
        that sequence items have proper structure according to DICOM PS3.3.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to accumulate errors and warnings
        """

        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])

        for i, fraction_item in enumerate(fraction_group_seq):
            # Validate Referenced Beam Sequence items
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            for j, beam_item in enumerate(ref_beam_seq):
                beam_prefix = f"Referenced Beam Sequence item {j} in Fraction Group {i}"

                # Type 1: Referenced Beam Number (300C,0006)
                if not beam_item.get('ReferencedBeamNumber'):
                    result.add_error(
                        f"{beam_prefix}: Referenced Beam Number (300C,0006) is required (Type 1). "
                        "Identifies Beam specified by Beam Number within Beam Sequence in RT Beams Module."
                    )

                # Validate Dose Calibration Conditions Sequence items
                calibration_seq = beam_item.get('DoseCalibrationConditionsSequence', [])
                for k, cal_item in enumerate(calibration_seq):
                    cal_prefix = f"Dose Calibration Conditions Sequence item {k} in {beam_prefix}"

                    # Type 1: Absorbed Dose to Meterset Ratio (300C,0121)
                    if 'AbsorbedDoseToMetersetRatio' not in cal_item:
                        result.add_error(
                            f"{cal_prefix}: Absorbed Dose to Meterset Ratio (300C,0121) is required (Type 1). "
                            "Ratio of absorbed dose in Gy to Meterset in reference conditions."
                        )

                    # Type 1: Delineated Radiation Field Size (300C,0122)
                    if 'DelineatedRadiationFieldSize' not in cal_item:
                        result.add_error(
                            f"{cal_prefix}: Delineated Radiation Field Size (300C,0122) is required (Type 1). "
                            "Field size in mm in X and Y directions in IEC BEAM LIMITING DEVICE coordinate system."
                        )

                    # Type 1: Calibration Reference Point Depth (300C,0124)
                    if 'CalibrationReferencePointDepth' not in cal_item:
                        result.add_error(
                            f"{cal_prefix}: Calibration Reference Point Depth (300C,0124) is required (Type 1). "
                            "Calibration reference point depth in mm from phantom surface."
                        )

                    # Type 1: Source to Surface Distance (300A,0130)
                    if 'SourceToSurfaceDistance' not in cal_item:
                        result.add_error(
                            f"{cal_prefix}: Source to Surface Distance (300A,0130) is required (Type 1). "
                            "Distance in mm from radiation source to phantom surface during calibration."
                        )

                    # Type 2: Calibration DateTime (0018,1203)
                    if 'CalibrationDateTime' not in cal_item:
                        result.add_error(
                            f"{cal_prefix}: Calibration DateTime (0018,1203) is required (Type 2). "
                            "Date and time the calibration was performed."
                        )

            # Validate Referenced Dose Reference Sequence items
            ref_dose_ref_seq = fraction_item.get('ReferencedDoseReferenceSequence', [])
            for j, dose_ref_item in enumerate(ref_dose_ref_seq):
                dose_ref_prefix = f"Referenced Dose Reference Sequence item {j} in Fraction Group {i}"

                # Type 1: Referenced Dose Reference Number (300C,0051)
                if not dose_ref_item.get('ReferencedDoseReferenceNumber'):
                    result.add_error(
                        f"{dose_ref_prefix}: Referenced Dose Reference Number (300C,0051) is required (Type 1). "
                        "Identifies Dose Reference specified by Dose Reference Number within "
                        "Dose Reference Sequence in RT Prescription Module."
                    )

            # Validate Referenced Brachy Application Setup Sequence items
            ref_brachy_seq = fraction_item.get('ReferencedBrachyApplicationSetupSequence', [])
            for j, brachy_item in enumerate(ref_brachy_seq):
                brachy_prefix = f"Referenced Brachy Application Setup Sequence item {j} in Fraction Group {i}"

                # Type 1: Referenced Brachy Application Setup Number (300C,000C)
                if not brachy_item.get('ReferencedBrachyApplicationSetupNumber'):
                    result.add_error(
                        f"{brachy_prefix}: Referenced Brachy Application Setup Number (300C,000C) is required (Type 1). "
                        "Identifies Brachy Application Setup specified by Brachy Application Setup Number "
                        "within Brachy Application Setup Sequence in RT Brachy Application Setups Module."
                    )

    @staticmethod
    def _validate_semantic_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate semantic constraints and cross-field relationships.

        Validates logical relationships between different attributes and
        ensures semantic consistency according to DICOM PS3.3 requirements.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to accumulate errors and warnings
        """
        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])

        # Validate uniqueness of fraction group numbers
        group_numbers = []
        for i, fraction_item in enumerate(fraction_group_seq):
            item_prefix = f"Fraction Group Sequence item {i}"
            group_number = fraction_item.get('FractionGroupNumber')

            if group_number is not None:
                if group_number in group_numbers:
                    result.add_error(
                        f"{item_prefix}: Fraction Group Number ({group_number}) must be unique within the RT Plan. "
                        "Each fraction group must have a distinct identification number."
                    )
                else:
                    group_numbers.append(group_number)

            # Validate dose values are non-negative and reasonable
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            for j, beam_item in enumerate(ref_beam_seq):
                beam_prefix = f"Referenced Beam Sequence item {j} in Fraction Group {i}"

                # Validate beam dose values
                beam_dose = beam_item.get('BeamDose')
                if beam_dose is not None:
                    if beam_dose < 0:
                        result.add_error(
                            f"{beam_prefix}: Beam Dose (300A,0084) cannot be negative ({beam_dose} Gy). "
                            "Dose values must be non-negative."
                        )
                    elif beam_dose > 100:  # Reasonable upper limit
                        result.add_warning(
                            f"{beam_prefix}: Beam Dose (300A,0084) is unusually high ({beam_dose} Gy). "
                            "Consider verifying this dose value is correct."
                        )

                # Validate alternate beam dose values
                alt_beam_dose = beam_item.get('AlternateBeamDose')
                if alt_beam_dose is not None:
                    if alt_beam_dose < 0:
                        result.add_error(
                            f"{beam_prefix}: Alternate Beam Dose (300A,0091) cannot be negative ({alt_beam_dose} Gy). "
                            "Dose values must be non-negative."
                        )
                    elif alt_beam_dose > 100:  # Reasonable upper limit
                        result.add_warning(
                            f"{beam_prefix}: Alternate Beam Dose (300A,0091) is unusually high ({alt_beam_dose} Gy). "
                            "Consider verifying this dose value is correct."
                        )

                # Validate beam meterset values
                beam_meterset = beam_item.get('BeamMeterset')
                if beam_meterset is not None and beam_meterset < 0:
                    result.add_error(
                        f"{beam_prefix}: Beam Meterset (300A,0086) cannot be negative ({beam_meterset}). "
                        "Meterset values must be non-negative."
                    )

                # Validate beam delivery duration limit
                duration_limit = beam_item.get('BeamDeliveryDurationLimit')
                if duration_limit is not None:
                    if duration_limit <= 0:
                        result.add_error(
                            f"{beam_prefix}: Beam Delivery Duration Limit (300A,00C5) must be positive ({duration_limit} sec). "
                            "Duration limit represents maximum delivery time."
                        )
                    elif duration_limit > 3600:  # More than 1 hour seems excessive
                        result.add_warning(
                            f"{beam_prefix}: Beam Delivery Duration Limit (300A,00C5) is unusually long ({duration_limit} sec). "
                            "Consider verifying this duration is reasonable."
                        )

            # Validate brachy dose values
            ref_brachy_seq = fraction_item.get('ReferencedBrachyApplicationSetupSequence', [])
            for j, brachy_item in enumerate(ref_brachy_seq):
                brachy_prefix = f"Referenced Brachy Application Setup Sequence item {j} in Fraction Group {i}"

                brachy_dose = brachy_item.get('BrachyApplicationSetupDose')
                if brachy_dose is not None:
                    if brachy_dose < 0:
                        result.add_error(
                            f"{brachy_prefix}: Brachy Application Setup Dose (300A,00A4) cannot be negative ({brachy_dose} Gy). "
                            "Dose values must be non-negative."
                        )
                    elif brachy_dose > 100:  # Reasonable upper limit
                        result.add_warning(
                            f"{brachy_prefix}: Brachy Application Setup Dose (300A,00A4) is unusually high ({brachy_dose} Gy). "
                            "Consider verifying this dose value is correct."
                        )
    
    @staticmethod
    def _validate_fraction_scheme_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate fraction scheme consistency and logical constraints.

        Validates the internal consistency of fraction scheme data including
        mutual exclusivity constraints, sequence count matching, and fraction
        pattern validation according to DICOM PS3.3 requirements.

        Args:
            dataset: Dataset to validate
            result: ValidationResult to accumulate errors and warnings
        """

        fraction_group_seq = getattr(dataset, 'FractionGroupSequence', [])
        for i, fraction_item in enumerate(fraction_group_seq):
            item_prefix = f"Fraction Group Sequence item {i}"
            number_of_beams = fraction_item.get('NumberOfBeams', 0)
            number_of_brachy_setups = fraction_item.get('NumberOfBrachyApplicationSetups', 0)

            # Validate mutual exclusivity of beams and brachy setups
            if number_of_beams > 0 and number_of_brachy_setups > 0:
                result.add_error(
                    f"{item_prefix}: Number of Beams (300A,0080) and Number of Brachy Application Setups (300A,00A0) "
                    "cannot both be greater than zero. A fraction group must use either beam-based or "
                    "brachytherapy-based delivery, not both."
                )

            # Validate that at least one delivery method is specified
            if number_of_beams == 0 and number_of_brachy_setups == 0:
                result.add_warning(
                    f"{item_prefix}: Both Number of Beams (300A,0080) and Number of Brachy Application Setups (300A,00A0) "
                    "are zero. Consider specifying at least one delivery method for this fraction group."
                )

            # Validate beam sequence count matches number of beams
            ref_beam_seq = fraction_item.get('ReferencedBeamSequence', [])
            if number_of_beams > 0 and len(ref_beam_seq) != number_of_beams:
                result.add_warning(
                    f"{item_prefix}: Number of Beams ({number_of_beams}) does not match "
                    f"Referenced Beam Sequence length ({len(ref_beam_seq)}). "
                    "Consider ensuring sequence contains exactly the declared number of beams."
                )

            # Validate brachy sequence count matches number of setups
            ref_brachy_seq = fraction_item.get('ReferencedBrachyApplicationSetupSequence', [])
            if number_of_brachy_setups > 0 and len(ref_brachy_seq) != number_of_brachy_setups:
                result.add_warning(
                    f"{item_prefix}: Number of Brachy Application Setups ({number_of_brachy_setups}) does not match "
                    f"Referenced Brachy Application Setup Sequence length ({len(ref_brachy_seq)}). "
                    "Consider ensuring sequence contains exactly the declared number of setups."
                )

            # Validate fraction pattern consistency
            pattern = fraction_item.get('FractionPattern', '')
            digits_per_day = fraction_item.get('NumberOfFractionPatternDigitsPerDay')
            cycle_length = fraction_item.get('RepeatFractionCycleLength')

            if pattern and digits_per_day and cycle_length:
                expected_length = 7 * digits_per_day * cycle_length
                if len(pattern) != expected_length:
                    result.add_error(
                        f"{item_prefix}: Fraction Pattern (300A,007B) length ({len(pattern)}) must equal "
                        f"7 × Number of Fraction Pattern Digits Per Day ({digits_per_day}) × "
                        f"Repeat Fraction Cycle Length ({cycle_length}) = {expected_length}. "
                        "Pattern represents 7 days × digits per day × cycle weeks."
                    )

                # Validate pattern contains only 0s and 1s
                if not all(c in '01' for c in pattern):
                    result.add_error(
                        f"{item_prefix}: Fraction Pattern (300A,007B) must contain only '0' (no treatment) "
                        "and '1' (treatment) characters. Pattern describes treatment delivery schedule."
                    )

                # Validate pattern has at least one treatment day
                if pattern and '1' not in pattern:
                    result.add_warning(
                        f"{item_prefix}: Fraction Pattern (300A,007B) contains no treatment days (all '0's). "
                        "Consider verifying this pattern is correct for the treatment schedule."
                    )

            # Validate fraction pattern completeness
            pattern_elements = [pattern, digits_per_day, cycle_length]
            pattern_provided = [elem is not None for elem in pattern_elements]
            if any(pattern_provided) and not all(pattern_provided):
                result.add_warning(
                    f"{item_prefix}: Fraction pattern elements are incomplete. "
                    "When specifying fractionation pattern, provide all three elements: "
                    "Fraction Pattern (300A,007B), Number of Fraction Pattern Digits Per Day (300A,0079), "
                    "and Repeat Fraction Cycle Length (300A,007A)."
                )

            # Validate number of fractions planned is reasonable
            fractions_planned = fraction_item.get('NumberOfFractionsPlanned', 0)
            if fractions_planned < 0:
                result.add_error(
                    f"{item_prefix}: Number of Fractions Planned (300A,0078) cannot be negative ({fractions_planned}). "
                    "Must specify non-negative number of treatment fractions."
                )
            elif fractions_planned > 100:  # Reasonable upper limit
                result.add_warning(
                    f"{item_prefix}: Number of Fractions Planned (300A,0078) is unusually high ({fractions_planned}). "
                    "Consider verifying this fraction count is correct."
                )
