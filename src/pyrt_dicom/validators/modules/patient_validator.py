"""Patient Module DICOM validation - PS3.3 C.7.1.1

This module provides comprehensive validation for the Patient Module as defined in
DICOM PS3.3 Section C.7.1.1. The Patient Module contains attributes of the Patient
that are needed for interpretation of Composite Instances and are common for all
Studies performed on the Patient.

The validator ensures compliance with all DICOM Type 1, Type 2, Type 3, Type 1C,
and Type 2C requirements, including complex conditional logic for non-human organisms,
alternative calendars, responsible persons, and deidentification requirements.
"""

from __future__ import annotations
from typing import Union,TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.patient_enums import ResponsiblePersonRole

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class PatientValidator(BaseValidator):
    """Validator for DICOM Patient Module (PS3.3 C.7.1.1).
    
    This validator provides comprehensive validation of all Patient Module requirements
    including Type 1C and Type 2C conditional logic for:
    - Non-human organisms with species, breed, and responsibility requirements
    - Alternative calendar date specifications
    - Responsible person role requirements
    - Patient deidentification method requirements
    - All enumerated value constraints
    - Complex sequence validation
    
    The validator is designed to provide clear, actionable error messages with
    specific DICOM tag references and guidance for resolution.
    """
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Patient Module requirements on any pydicom Dataset.
        
        Performs comprehensive validation of all DICOM Patient Module requirements
        including Type 1, Type 2, Type 3, Type 1C, and Type 2C elements.
        Validates conditional logic, enumerated values, and sequence structures.
        
        Args:
            data: pydicom Dataset OR BaseModule instance to validate against Patient Module requirements
            config: Validation configuration options. If None, default configuration is used.
            
        Returns:
            ValidationResult containing any validation errors and warnings with
            specific DICOM tag references and actionable guidance for resolution.
            
        Raises:
            None - All validation issues are captured in the ValidationResult
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Always validate required elements
        result.merge(PatientValidator.validate_type_2_requirements(data))
        
        if config.validate_conditional_requirements:
            result.merge(PatientValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(PatientValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(PatientValidator.validate_sequence_requirements(data))
        
        return result
    
    @staticmethod
    def validate_type_2_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 2 elements are present (but may be empty).
        
        Per DICOM PS3.3 C.7.1.1, the Patient Module core elements are Type 2:
        - Patient's Name (0010,0010)
        - Patient ID (0010,0020) 
        - Patient's Birth Date (0010,0030)
        - Patient's Sex (0010,0040)
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            
        Returns:
            ValidationResult containing any errors found
        """
        result = ValidationResult()
        type_2_elements = [
            ('PatientName', '(0010,0010)', "Patient's Name"),
            ('PatientID', '(0010,0020)', "Patient ID"),
            ('PatientBirthDate', '(0010,0030)', "Patient's Birth Date"),
            ('PatientSex', '(0010,0040)', "Patient's Sex")
        ]
        
        for attr_name, tag, description in type_2_elements:
            if attr_name not in data:
                result.add_error(
                    f"{description} {tag} is required (Type 2 element) but is missing. "
                    f"Type 2 elements must be present but may be empty. "
                    f"See DICOM PS3.3 C.7.1.1 for details."
                )
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            
        Returns:
            ValidationResult containing any errors found
        """
        result = ValidationResult()
        
        # Type 1C: Alternative Calendar requirement
        # Per DICOM PS3.3 C.7.1.1: Patient's Alternative Calendar (0010,0035) is required if either
        # Patient's Birth Date in Alternative Calendar (0010,0033) or Patient's Death Date 
        # in Alternative Calendar (0010,0034) is present
        has_alt_birth = 'PatientBirthDateInAlternativeCalendar' in data and bool(str(data.PatientBirthDateInAlternativeCalendar).strip()) if 'PatientBirthDateInAlternativeCalendar' in data else False
        has_alt_death = 'PatientDeathDateInAlternativeCalendar' in data and bool(str(data.PatientDeathDateInAlternativeCalendar).strip()) if 'PatientDeathDateInAlternativeCalendar' in data else False
        if has_alt_birth or has_alt_death:
            if 'PatientAlternativeCalendar' not in data or not bool(str(data.PatientAlternativeCalendar).strip()) if 'PatientAlternativeCalendar' in data else True:
                result.add_error(
                    "Patient's Alternative Calendar (0010,0035) is required when "
                    "Patient's Birth Date in Alternative Calendar (0010,0033) or "
                    "Patient's Death Date in Alternative Calendar (0010,0034) is present (Type 1C requirement). "
                    "See DICOM PS3.3 C.7.1.1 for details."
                )
        
        # Type 1C/2C: Non-human organism requirements per DICOM PS3.3 C.7.1.1
        is_non_human = ('PatientSpeciesDescription' in data or 
                       'PatientSpeciesCodeSequence' in data)
        
        if is_non_human:
            # Type 1C: Either species description OR species code sequence required
            has_species_desc = ('PatientSpeciesDescription' in data and 
                              bool(str(data.PatientSpeciesDescription).strip()))
            has_species_code = ('PatientSpeciesCodeSequence' in data and 
                              bool(data.PatientSpeciesCodeSequence))
            if not has_species_desc and not has_species_code:
                result.add_error(
                    "Patient Species Description (0010,2201) OR Patient Species Code Sequence (0010,2202) "
                    "is required for non-human organisms (Type 1C requirement). "
                    "At least one must be provided. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Patient Breed Code Sequence is required (but may be empty)
            if 'PatientBreedCodeSequence' not in data:
                result.add_error(
                    "Patient Breed Code Sequence (0010,2293) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Breed description required if breed code sequence is empty
            breed_desc = str(data.PatientBreedDescription).strip() if 'PatientBreedDescription' in data else ''
            breed_seq = data.PatientBreedCodeSequence if 'PatientBreedCodeSequence' in data else []
            if not breed_seq and not breed_desc:
                result.add_error(
                    "Patient Breed Description (0010,2292) is required for non-human organisms "
                    "when Patient Breed Code Sequence (0010,2293) is empty (Type 2C requirement). "
                    "See DICOM PS3.3 C.7.1.1 for details."
                )
            
            
            # Type 2C: Breed Registration Sequence is required (but may be empty)
            if 'BreedRegistrationSequence' not in data:
                result.add_error(
                    "Breed Registration Sequence (0010,2294) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Responsible Person is required (but may be empty)
            if 'ResponsiblePerson' not in data:
                result.add_error(
                    "Responsible Person (0010,2297) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
            
            # Type 2C: Responsible Organization is required (but may be empty)
            if 'ResponsibleOrganization' not in data:
                result.add_error(
                    "Responsible Organization (0010,2299) is required for non-human organisms "
                    "(Type 2C requirement), but may be empty. See DICOM PS3.3 C.7.1.1 for details."
                )
        
        # Type 1C: Responsible Person Role requirement per DICOM PS3.3 C.7.1.1
        resp_person = str(data.ResponsiblePerson).strip() if 'ResponsiblePerson' in data else ''
        if resp_person and 'ResponsiblePersonRole' not in data:
            result.add_error(
                "Responsible Person Role (0010,2298) is required when "
                "Responsible Person (0010,2297) is present and has a value (Type 1C requirement). "
                "See DICOM PS3.3 C.*******.2 for defined terms and details."
            )
        
        # Type 1C: De-identification method requirement per DICOM PS3.3 C.7.1.1
        identity_removed = str(data.PatientIdentityRemoved).strip() if 'PatientIdentityRemoved' in data else ''
        if identity_removed == "YES":
            # Use hasattr for these non-standard DICOM keywords that pydicom doesn't recognize
            has_method = (hasattr(data, 'DeIdentificationMethod') and 
                         bool(str(getattr(data, 'DeIdentificationMethod', '')).strip()))
            has_method_seq = (hasattr(data, 'DeIdentificationMethodCodeSequence') and 
                            bool(getattr(data, 'DeIdentificationMethodCodeSequence', [])))
            if not has_method and not has_method_seq:
                result.add_error(
                    "Either De-identification Method (0012,0063) OR De-identification Method Code Sequence (0012,0064) "
                    "is required when Patient Identity Removed (0012,0062) is YES (Type 1C requirement). "
                    "At least one must be provided. See DICOM PS3.3 C.7.1.1 for details."
                )
        
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated values against DICOM specifications.
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            
        Returns:
            ValidationResult containing any errors found
        """
        result = ValidationResult()
        
        # Patient's Sex (0010,0040)
        if 'PatientSex' in data:
            patient_sex = str(data.PatientSex).strip()
            if patient_sex:
                BaseValidator.validate_enumerated_value(
                    patient_sex, ["M", "F", "O"], 
                    "Patient's Sex (0010,0040)", result
                )
        
        # Quality Control Subject (0010,0200)
        if 'QualityControlSubject' in data:
            quality_control = str(data.QualityControlSubject).strip()
            if quality_control:
                BaseValidator.validate_enumerated_value(
                    quality_control, ["YES", "NO"],
                    "Quality Control Subject (0010,0200)", result
                )
        
        # Type of Patient ID (0010,0022)
        if 'TypeOfPatientID' in data:
            type_of_id = str(data.TypeOfPatientID).strip()
            if type_of_id:
                BaseValidator.validate_enumerated_value(
                    type_of_id, ["TEXT", "RFID", "BARCODE"],
                    "Type of Patient ID (0010,0022)", result
                )
        
        # Patient Identity Removed (0012,0062)
        if 'PatientIdentityRemoved' in data:
            identity_removed = str(data.PatientIdentityRemoved).strip()
            if identity_removed:
                BaseValidator.validate_enumerated_value(
                    identity_removed, ["YES", "NO"],
                    "Patient Identity Removed (0012,0062)", result
                )
        
        # Responsible Person Role (0010,2298)
        if 'ResponsiblePersonRole' in data:
            resp_role = str(data.ResponsiblePersonRole).strip()
            if resp_role:
                valid_roles = [role.value for role in ResponsiblePersonRole]
                BaseValidator.validate_enumerated_value(
                    resp_role, valid_roles,
                    "Responsible Person Role (0010,2298)", result
                )
        
        # Strain Nomenclature (0010,0213)
        if 'StrainNomenclature' in data:
            strain_nom = str(data.StrainNomenclature).strip()
            if strain_nom:
                BaseValidator.validate_enumerated_value(
                    strain_nom, ["MGI_2013"],
                    "Strain Nomenclature (0010,0213)", result
                )
        
        return result
    
    @staticmethod
    def validate_sequence_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance
            
        Returns:
            ValidationResult containing any errors found
        """
        result = ValidationResult()
        
        # Other Patient IDs Sequence - each item needs Patient ID and Type
        other_ids_seq = data.OtherPatientIDsSequence if 'OtherPatientIDsSequence' in data else []
        for i, item in enumerate(other_ids_seq):
            if not item.get('PatientID'):
                result.add_error(f"Other Patient IDs Sequence item {i}: Patient ID (0010,0020) is required")
            if not item.get('TypeOfPatientID'):
                result.add_error(f"Other Patient IDs Sequence item {i}: Type of Patient ID (0010,0022) is required")
        
        # Strain Stock Sequence - each item needs stock number, source, and registry
        strain_stock_seq = data.StrainStockSequence if 'StrainStockSequence' in data else []
        for i, item in enumerate(strain_stock_seq):
            if not item.get('StrainStockNumber'):
                result.add_error(f"Strain Stock Sequence item {i}: Strain Stock Number (0010,0214) is required")
            if not item.get('StrainSource'):
                result.add_error(f"Strain Stock Sequence item {i}: Strain Source (0010,0217) is required")
            if not item.get('StrainSourceRegistryCodeSequence'):
                result.add_error(f"Strain Stock Sequence item {i}: Strain Source Registry Code Sequence (0010,0215) is required")
        
        # Genetic Modifications Sequence - each item needs description and nomenclature
        genetic_mod_seq = data.GeneticModificationsSequence if 'GeneticModificationsSequence' in data else []
        for i, item in enumerate(genetic_mod_seq):
            if not item.get('GeneticModificationsDescription'):
                result.add_error(f"Genetic Modifications Sequence item {i}: Genetic Modifications Description (0010,0222) is required")
            if not item.get('GeneticModificationsNomenclature'):
                result.add_error(f"Genetic Modifications Sequence item {i}: Genetic Modifications Nomenclature (0010,0223) is required")
        
        return result