"""Clinical Trial Series Module DICOM validation - PS3.3 C.7.3.2"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class ClinicalTrialSeriesValidator(BaseValidator):
    """Validator for DICOM Clinical Trial Series Module (PS3.3 C.7.3.2)."""
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Check Type 2 requirements - must be present (can be empty)
        if 'ClinicalTrialCoordinatingCenterName' not in data:
            result.add_error(
                "Clinical Trial Coordinating Center Name (0012,0060) is required (Type 2). "
                "Must be present but may have zero-length value."
            )
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # No Type 1C or Type 2C elements defined in this module per PS3.3 C.7.3.2
        # All elements are either Type 2 or Type 3
        
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # No enumerated values defined in this module per PS3.3 C.7.3.2
        # All elements are free-form LO (Long String) values
        
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        
        # No sequence structures defined in this module per PS3.3 C.7.3.2
        # All elements are single-value LO (Long String) types
        
        return result
    
    @staticmethod
    def validate_vr_constraints(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate VR (Value Representation) constraints for LO elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for VR constraint violations
        """
        result = ValidationResult()
        
        # Validate DICOM VR constraints for LO (Long String) data type
        # All elements in this module are VR=LO with VM=1
        _validate_lo_element(result, data, 'ClinicalTrialCoordinatingCenterName', '(0012,0060)')
        _validate_lo_element(result, data, 'ClinicalTrialSeriesID', '(0012,0071)')
        _validate_lo_element(result, data, 'IssuerOfClinicalTrialSeriesID', '(0012,0073)')
        _validate_lo_element(result, data, 'ClinicalTrialSeriesDescription', '(0012,0072)')
        
        return result
    
    @staticmethod
    def validate_semantic_relationships(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate semantic relationships between elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with warnings for semantic issues
        """
        result = ValidationResult()
        
        # Semantic validation: If Series ID is provided, recommend issuer for better identification
        if ('ClinicalTrialSeriesID' in data and 
            data.ClinicalTrialSeriesID and 
            str(data.ClinicalTrialSeriesID).strip() and
            ('IssuerOfClinicalTrialSeriesID' not in data or 
             not data.IssuerOfClinicalTrialSeriesID)):
            result.add_warning(
                "Clinical Trial Series ID (0012,0071) is present but Issuer of Clinical Trial Series ID (0012,0073) "
                "is not provided. Consider adding issuer information for better series identification."
            )
        
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(ClinicalTrialSeriesValidator.validate_required_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(ClinicalTrialSeriesValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(ClinicalTrialSeriesValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(ClinicalTrialSeriesValidator.validate_sequence_structures(data))
        
        # Always validate VR constraints
        result.merge(ClinicalTrialSeriesValidator.validate_vr_constraints(data))
        
        # Always validate semantic relationships 
        result.merge(ClinicalTrialSeriesValidator.validate_semantic_relationships(data))
        
        return result


def _validate_lo_element(result: ValidationResult, data: Union[Dataset, 'BaseModule'], element_name: str, tag: str) -> None:
    """Validate Long String (LO) VR constraints for clinical trial series elements.
    
    LO VR constraints:
    - Maximum length: 64 characters
    - Character set: Default Character Set and extensions (typically ISO 8859-1)
    - Leading/trailing spaces should be insignificant
    
    Args:
        result: ValidationResult to add errors/warnings to
        data: Dataset or BaseModule to validate
        element_name: Name of the DICOM element
        tag: DICOM tag in format (gggg,eeee)
    """
    if element_name in data:
        # Use direct attribute access - works with both Dataset and BaseModule via __getattr__
        value = getattr(data, element_name)
        if value is not None:
            # Convert to string for validation
            str_value = str(value) if value is not None else ""
            
            # Check maximum length for LO VR (64 characters)
            if len(str_value) > 64:
                result.add_error(
                    f"{element_name} {tag} exceeds maximum length of 64 characters for LO VR. "
                    f"Current length: {len(str_value)} characters."
                )
            
            # Check for control characters (except space)
            if any(ord(char) < 32 for char in str_value if char != ' '):
                result.add_error(
                    f"{element_name} {tag} contains invalid control characters. "
                    f"LO VR should not contain control characters except space."
                )
