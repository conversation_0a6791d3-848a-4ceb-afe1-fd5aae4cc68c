"""RT Prescription Module DICOM validation - PS3.3 C.8.8.10.

This validator ensures compliance with DICOM standard requirements for the
RT Prescription Module, including conditional requirements, enumerated values,
and semantic consistency of dose-related attributes.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import DoseReferenceStructureType, DoseReferenceType, DoseValuePurpose, DoseValueInterpretation


class RTPrescriptionValidator(BaseValidator):
    """Validator for DICOM RT Prescription Module (PS3.3 C.8.8.10)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Prescription Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTPrescriptionValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTPrescriptionValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTPrescriptionValidator._validate_sequence_requirements(dataset, result)
        
        # Validate dose value consistency
        RTPrescriptionValidator._validate_dose_value_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            structure_type = getattr(dose_ref_item, 'DoseReferenceStructureType', '')

            # Type 1C: Referenced ROI Number required if structure type is POINT or VOLUME
            BaseValidator.validate_conditional_requirement(
                condition=structure_type in ['POINT', 'VOLUME'],
                required_fields=['ReferencedROINumber'],
                dataset=dose_ref_item,
                error_message=(
                    f"Dose Reference Sequence item {i}: "
                    f"Referenced ROI Number (3006,0084) is required when "
                    f"Dose Reference Structure Type (300A,0014) is '{structure_type}'. "
                    "This field identifies the ROI representing the dose reference "
                    "specified by ROI Number in Structure Set ROI Sequence."
                ),
                result=result
            )

            # Type 1C: Dose Reference Point Coordinates required if structure type is COORDINATES  
            BaseValidator.validate_conditional_requirement(
                condition=structure_type == 'COORDINATES',
                required_fields=['DoseReferencePointCoordinates'],
                dataset=dose_ref_item,
                error_message=(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Point Coordinates (300A,0018) is required when "
                    "Dose Reference Structure Type (300A,0014) is 'COORDINATES'. "
                    "This field specifies the (x,y,z) coordinates in mm in the "
                    "Patient-Based Coordinate System described in Section C.7.6.2.1.1."
                ),
                result=result
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            # Dose Reference Structure Type (300A,0014)
            structure_type = getattr(dose_ref_item, 'DoseReferenceStructureType', '')
            if structure_type:
                valid_structure_types = [stype.value for stype in DoseReferenceStructureType]
                BaseValidator.validate_enumerated_value(
                    structure_type, valid_structure_types,
                    f"Dose Reference Structure Type (300A,0014) in item {i}", result
                )
            
            # Dose Reference Type (300A,0020)
            reference_type = getattr(dose_ref_item, 'DoseReferenceType', '')
            if reference_type:
                valid_reference_types = [rtype.value for rtype in DoseReferenceType]
                BaseValidator.validate_enumerated_value(
                    reference_type, valid_reference_types,
                    f"Dose Reference Type (300A,0020) in item {i}", result
                )
            
            # Dose Value Purpose (300A,061D)
            value_purpose = getattr(dose_ref_item, 'DoseValuePurpose', '')
            if value_purpose:
                valid_purposes = [purpose.value for purpose in DoseValuePurpose]
                BaseValidator.validate_enumerated_value(
                    value_purpose, valid_purposes,
                    f"Dose Value Purpose (300A,061D) in item {i}", result
                )
            
            # Dose Value Interpretation (300A,068B)
            value_interpretation = getattr(dose_ref_item, 'DoseValueInterpretation', '')
            if value_interpretation:
                valid_interpretations = [interp.value for interp in DoseValueInterpretation]
                BaseValidator.validate_enumerated_value(
                    value_interpretation, valid_interpretations,
                    f"Dose Value Interpretation (300A,068B) in item {i}", result
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            # Type 1 validation for required elements within sequence items
            if not hasattr(dose_ref_item, 'DoseReferenceNumber') or getattr(dose_ref_item, 'DoseReferenceNumber', None) is None:
                result.add_error(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Number (300A,0012) is required (Type 1). "
                    "This field provides a unique identification number for the dose reference."
                )
            
            if not hasattr(dose_ref_item, 'DoseReferenceStructureType') or not getattr(dose_ref_item, 'DoseReferenceStructureType', None):
                result.add_error(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Structure Type (300A,0014) is required (Type 1). "
                    "This field specifies the structure type of the dose reference "
                    "and must be one of: POINT, VOLUME, COORDINATES, or SITE."
                )
            
            if not hasattr(dose_ref_item, 'DoseReferenceType') or not getattr(dose_ref_item, 'DoseReferenceType', None):
                result.add_error(
                    f"Dose Reference Sequence item {i}: "
                    "Dose Reference Type (300A,0020) is required (Type 1). "
                    "This field specifies the type of dose reference and must be "
                    "one of: TARGET or ORGAN_AT_RISK."
                )
            
            # Validate Dose Reference Point Coordinates format if present
            coordinates = getattr(dose_ref_item, 'DoseReferencePointCoordinates', None)
            if coordinates is not None:
                if not hasattr(coordinates, '__len__') or len(coordinates) != 3:
                    result.add_error(
                        f"Dose Reference Sequence item {i}: "
                        "Dose Reference Point Coordinates (300A,0018) must contain exactly "
                        "3 values representing (x,y,z) coordinates in mm in the "
                        "Patient-Based Coordinate System. "
                        f"Found {len(coordinates) if hasattr(coordinates, '__len__') else 'invalid'} values."
                    )
                else:
                    # Validate that all coordinates are numeric
                    try:
                        _ = [float(coord) for coord in coordinates]
                        # Optionally validate coordinate ranges if needed
                    except (ValueError, TypeError):
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Dose Reference Point Coordinates (300A,0018) should contain "
                            "numeric values representing coordinates in mm."
                        )

            # Validate Dose Reference UID format if present
            dose_ref_uid = getattr(dose_ref_item, 'DoseReferenceUID', '')
            if dose_ref_uid:
                BaseValidator.validate_uid_format(
                    dose_ref_uid, 
                    f"Dose Reference Sequence item {i}: Dose Reference UID (300A,0013)",
                    result
                )

        # Validate uniqueness of dose reference numbers (DICOM standard requirement)
        dose_ref_numbers = []
        for i, dose_ref_item in enumerate(dose_ref_seq):
            dose_ref_number = getattr(dose_ref_item, 'DoseReferenceNumber', None)
            if dose_ref_number is not None:
                if dose_ref_number in dose_ref_numbers:
                    result.add_error(
                        f"Dose Reference Sequence item {i}: "
                        f"Dose Reference Number (300A,0012) value '{dose_ref_number}' must be "
                        "unique within the RT Plan in which it is created. "
                        f"This value is already used by another dose reference item."
                    )
                else:
                    dose_ref_numbers.append(dose_ref_number)
    
    @staticmethod
    def _validate_dose_value_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate dose value consistency and logical constraints."""
        
        dose_ref_seq = getattr(dataset, 'DoseReferenceSequence', [])
        for i, dose_ref_item in enumerate(dose_ref_seq):
            reference_type = getattr(dose_ref_item, 'DoseReferenceType', '')

            # Validate target dose values
            if reference_type == 'TARGET':
                target_min = getattr(dose_ref_item, 'TargetMinimumDose', None)
                target_prescription = getattr(dose_ref_item, 'TargetPrescriptionDose', None)
                target_max = getattr(dose_ref_item, 'TargetMaximumDose', None)
                
                # Check dose ordering: min <= prescription <= max
                if target_min is not None and target_prescription is not None:
                    if target_min > target_prescription:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Target Minimum Dose (300A,0025) should not exceed "
                            "Target Prescription Dose (300A,0026). "
                            f"Current values: minimum={target_min}Gy, prescription={target_prescription}Gy."
                        )
                
                if target_prescription is not None and target_max is not None:
                    if target_prescription > target_max:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Target Prescription Dose (300A,0026) should not exceed "
                            "Target Maximum Dose (300A,0027). "
                            f"Current values: prescription={target_prescription}Gy, maximum={target_max}Gy."
                        )
                
                if target_min is not None and target_max is not None:
                    if target_min > target_max:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Target Minimum Dose (300A,0025) should not exceed "
                            "Target Maximum Dose (300A,0027). "
                            f"Current values: minimum={target_min}Gy, maximum={target_max}Gy."
                        )
                
                # Validate underdose volume fraction
                underdose_fraction = getattr(dose_ref_item, 'TargetUnderdoseVolumeFraction', None)
                if underdose_fraction is not None:
                    if underdose_fraction < 0.0 or underdose_fraction > 100.0:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            f"Target Underdose Volume Fraction (300A,0028) value '{underdose_fraction}' "
                            "should be between 0.0 and 100.0 percent. This field specifies the "
                            "maximum permitted fraction of the target to receive less than the "
                            "Target Prescription Dose."
                        )
                else:
                    # Per DICOM spec: "If the Target Underdose Volume Fraction (300A,0028) is not present, it shall be interpreted as zero."
                    # This is informational, not an error
                    pass

            # Validate organ at risk dose values
            elif reference_type == 'ORGAN_AT_RISK':
                oar_full_volume = getattr(dose_ref_item, 'OrganAtRiskFullvolumeDose', None)
                oar_limit = getattr(dose_ref_item, 'OrganAtRiskLimitDose', None)
                oar_maximum = getattr(dose_ref_item, 'OrganAtRiskMaximumDose', None)
                
                # Check dose ordering for OAR
                if oar_full_volume is not None and oar_limit is not None:
                    if oar_full_volume > oar_limit:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Organ At Risk Full Volume Dose (300A,002A) should not exceed "
                            "Organ At Risk Limit Dose (300A,002B). "
                            f"Current values: full volume={oar_full_volume}Gy, limit={oar_limit}Gy."
                        )
                
                if oar_full_volume is not None and oar_maximum is not None:
                    if oar_full_volume > oar_maximum:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            "Organ At Risk Full Volume Dose (300A,002A) should not exceed "
                            "Organ At Risk Maximum Dose (300A,002C). "
                            f"Current values: full volume={oar_full_volume}Gy, maximum={oar_maximum}Gy."
                        )
                
                # Validate overdose volume fraction
                overdose_fraction = getattr(dose_ref_item, 'OrganAtRiskOverdoseVolumeFraction', None)
                if overdose_fraction is not None:
                    if overdose_fraction < 0.0 or overdose_fraction > 100.0:
                        result.add_warning(
                            f"Dose Reference Sequence item {i}: "
                            f"Organ At Risk Overdose Volume Fraction (300A,002D) value '{overdose_fraction}' "
                            "should be between 0.0 and 100.0 percent. This field specifies the "
                            "maximum permitted fraction of the organ at risk to receive more than "
                            "the Organ At Risk Maximum Dose."
                        )
                else:
                    # Per DICOM spec: "If the Organ at Risk Overdose Volume Fraction (300A,002D) is not present, it shall be interpreted as zero."
                    # This is informational, not an error
                    pass

            # Validate general dose values
            delivery_warning = getattr(dose_ref_item, 'DeliveryWarningDose', None)
            delivery_maximum = getattr(dose_ref_item, 'DeliveryMaximumDose', None)

            if delivery_warning is not None and delivery_maximum is not None:
                if delivery_warning > delivery_maximum:
                    result.add_warning(
                        f"Dose Reference Sequence item {i}: "
                        "Delivery Warning Dose (300A,0022) should not exceed "
                        "Delivery Maximum Dose (300A,0023). "
                        f"Current values: warning={delivery_warning}Gy, maximum={delivery_maximum}Gy."
                    )

            # Validate constraint weight
            constraint_weight = getattr(dose_ref_item, 'ConstraintWeight', None)
            if constraint_weight is not None and constraint_weight < 0:
                result.add_warning(
                    f"Dose Reference Sequence item {i}: "
                    f"Constraint Weight (300A,0021) value '{constraint_weight}' should be "
                    "non-negative. This field represents the relative importance of "
                    "satisfying the constraint, where higher values indicate more important constraints."
                )

            # Validate nominal prior dose
            nominal_prior = getattr(dose_ref_item, 'NominalPriorDose', None)
            if nominal_prior is not None and nominal_prior < 0:
                result.add_warning(
                    f"Dose Reference Sequence item {i}: "
                    f"Nominal Prior Dose (300A,001A) value '{nominal_prior}' should be "
                    "non-negative. This field specifies the dose in Gy from prior "
                    "treatment to this dose reference (e.g., from a previous course of treatment)."
                )
