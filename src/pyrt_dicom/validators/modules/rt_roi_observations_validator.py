"""RT ROI Observations Module DICOM validation - PS3.3 C.8.8.8

This validator ensures complete compliance with DICOM PS3.3 C.8.8.8 RT ROI Observations Module
requirements, including all Type 1C conditional logic, sequence validation, and semantic constraints.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import RTROIInterpretedType, RTROIRelationship, ROIPhysicalProperty


class RTROIObservationsValidator(BaseValidator):
    """Validator for DICOM RT ROI Observations Module (PS3.3 C.8.8.8).

    Validates all requirements from DICOM PS3.3 C.8.8.8 including:
    - Type 1 required elements within RT ROI Observations Sequence
    - Type 2 required elements within RT ROI Observations Sequence
    - Type 1C conditional requirements (ROI Elemental Composition, ROI Interpreter Sequence)
    - Sequence structure validation and nested sequence requirements
    - Enumerated value validation for RT ROI Interpreted Type, ROI Relationship, Physical Properties
    - Physical properties consistency and elemental composition validation
    - Observation number uniqueness requirements
    - DateTime format validation

    This validator provides comprehensive error checking with clear, actionable
    error messages to guide users in creating DICOM-compliant RT structure sets.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT ROI Observations Module requirements on any pydicom Dataset.

        Performs comprehensive validation of DICOM PS3.3 C.8.8.8 requirements including:
        - Type 1 and Type 2 element presence validation within sequences
        - Type 1C conditional requirements (ROI Elemental Composition, ROI Interpreter Sequence)
        - Sequence structure and nested sequence validation
        - Enumerated value validation for all coded fields
        - Physical properties consistency and elemental composition validation
        - Observation number uniqueness requirements
        - DateTime format validation and semantic constraints

        Args:
            dataset (Dataset): pydicom Dataset to validate against RT ROI Observations Module requirements
            config (ValidationConfig | None): Optional validation configuration to control validation behavior.
                If None, uses default configuration with all validations enabled.

        Returns:
            ValidationResult: Validation result containing lists of errors and warnings with specific
                DICOM tag references and actionable guidance for resolving issues.
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 and Type 2 required elements within sequences
        if config.validate_required_elements:
            RTROIObservationsValidator._validate_required_elements(dataset, result)

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTROIObservationsValidator._validate_conditional_requirements(dataset, result)

        # Validate enumerated values
        if config.check_enumerated_values:
            RTROIObservationsValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures
        if config.validate_sequences:
            RTROIObservationsValidator._validate_sequence_requirements(dataset, result)

        # Validate semantic constraints and physical properties consistency
        if config.validate_semantic_constraints:
            RTROIObservationsValidator._validate_semantic_constraints(dataset, result)

        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 and Type 2 required elements within RT ROI Observations Sequence.

        Validates that all required elements are present within sequence items according to
        DICOM PS3.3 C.8.8.8 specifications.
        """
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            # Type 1: Observation Number (3006,0082)
            if not obs_item.get('ObservationNumber'):
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "Observation Number (3006,0082) is required (Type 1). "
                    "This uniquely identifies the observation within the sequence."
                )

            # Type 1: Referenced ROI Number (3006,0084)
            if not obs_item.get('ReferencedROINumber'):
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "Referenced ROI Number (3006,0084) is required (Type 1). "
                    "This must reference a ROI defined in the Structure Set ROI Sequence (3006,0020)."
                )

            # Type 2: RT ROI Interpreted Type (3006,00A4) - required but may be empty
            if 'RTROIInterpretedType' not in obs_item:
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "RT ROI Interpreted Type (3006,00A4) is required (Type 2). "
                    "This describes the class of ROI (e.g., PTV, CTV, OAR). "
                    "It may be empty but must be present."
                )

            # Type 2: ROI Interpreter (3006,00A6) - required but may be empty
            if 'ROIInterpreter' not in obs_item:
                result.add_error(
                    f"RT ROI Observations Sequence item {i}: "
                    "ROI Interpreter (3006,00A6) is required (Type 2). "
                    "This identifies the person performing the interpretation. "
                    "It may be empty but must be present."
                )

    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements.

        Validates conditional requirements that depend on the presence or values of other elements.
        """
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            # Type 1C: ROI Interpreter Sequence (3006,004E)
            # Required if ROI Creator Sequence (3006,004D) is present in Structure Set Module
            # and the person/device performing interpretation differs from ROI Creator Sequence
            # Note: Full validation requires Structure Set Module context at IOD level

            # Validate ROI Physical Properties Sequence conditional requirements
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            for j, props_item in enumerate(roi_physical_props_seq):
                roi_physical_property = props_item.get('ROIPhysicalProperty', '')
                roi_elemental_comp_seq = props_item.get('ROIElementalCompositionSequence', [])

                # Type 1C: ROI Elemental Composition Sequence (3006,00B6)
                # Required if ROI Physical Property (3006,00B2) equals ELEM_FRACTION
                if roi_physical_property == 'ELEM_FRACTION' and not roi_elemental_comp_seq:
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                        "ROI Elemental Composition Sequence (3006,00B6) is required when "
                        "ROI Physical Property (3006,00B2) equals 'ELEM_FRACTION'. "
                        "This sequence must contain the elemental composition and atomic mass fractions."
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications.

        Validates that all enumerated fields contain values from their respective
        DICOM-defined value sets according to PS3.3 C.8.8.8.
        """
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            # RT ROI Interpreted Type (3006,00A4)
            interpreted_type = obs_item.get('RTROIInterpretedType', '')
            if interpreted_type:
                valid_types = [itype.value for itype in RTROIInterpretedType]
                if interpreted_type not in valid_types:
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}: "
                        f"RT ROI Interpreted Type (3006,00A4) value '{interpreted_type}' is not valid. "
                        f"Must be one of: {', '.join(valid_types)}. "
                        "See DICOM PS3.3 C.******* for complete definitions."
                    )

            # RT ROI Relationship in RT Related ROI Sequence
            rt_related_roi_seq = obs_item.get('RTRelatedROISequence', [])
            for j, related_item in enumerate(rt_related_roi_seq):
                roi_relationship = related_item.get('RTROIRelationship', '')
                if roi_relationship:
                    valid_relationships = [rel.value for rel in RTROIRelationship]
                    if roi_relationship not in valid_relationships:
                        result.add_error(
                            f"RT ROI Observations Sequence item {i}, RT Related ROI item {j}: "
                            f"RT ROI Relationship (3006,0033) value '{roi_relationship}' is not valid. "
                            f"Must be one of: {', '.join(valid_relationships)}. "
                            "SAME=same entity, ENCLOSED=referenced ROI encloses referencing ROI, "
                            "ENCLOSING=referencing ROI encloses referenced ROI."
                        )

            # ROI Physical Property in ROI Physical Properties Sequence
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            for j, props_item in enumerate(roi_physical_props_seq):
                physical_property = props_item.get('ROIPhysicalProperty', '')
                if physical_property:
                    valid_properties = [prop.value for prop in ROIPhysicalProperty]
                    if physical_property not in valid_properties:
                        result.add_error(
                            f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                            f"ROI Physical Property (3006,00B2) value '{physical_property}' is not valid. "
                            f"Must be one of: {', '.join(valid_properties)}. "
                            "See DICOM PS3.3 C.8.8.8 for physical property definitions."
                        )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements.

        Validates the structure and required elements within nested sequences
        according to DICOM PS3.3 C.8.8.8 specifications.
        """
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])

        # Validate uniqueness of observation numbers across all observations
        observation_numbers = []
        for i, obs_item in enumerate(rt_roi_obs_seq):
            obs_number = obs_item.get('ObservationNumber')
            if obs_number is not None:
                if obs_number in observation_numbers:
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}: "
                        f"Observation Number ({obs_number}) must be unique within the "
                        "RT ROI Observations Sequence (3006,0080). "
                        f"Duplicate found with previous observation."
                    )
                else:
                    observation_numbers.append(obs_number)

        for i, obs_item in enumerate(rt_roi_obs_seq):
            
            # Validate RT Related ROI Sequence
            rt_related_roi_seq = obs_item.get('RTRelatedROISequence', [])
            for j, related_item in enumerate(rt_related_roi_seq):
                if not related_item.get('ReferencedROINumber'):
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}, RT Related ROI item {j}: "
                        "Referenced ROI Number (3006,0084) is required (Type 1). "
                        "This must reference a ROI defined in the Structure Set ROI Sequence."
                    )

            # Validate ROI Physical Properties Sequence
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            for j, props_item in enumerate(roi_physical_props_seq):
                if not props_item.get('ROIPhysicalProperty'):
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                        "ROI Physical Property (3006,00B2) is required (Type 1). "
                        "This specifies the type of physical property being described."
                    )
                if 'ROIPhysicalPropertyValue' not in props_item:
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                        "ROI Physical Property Value (3006,00B4) is required (Type 1). "
                        "This provides the numerical value for the specified physical property."
                    )

                # Validate ROI Elemental Composition Sequence
                roi_elemental_comp_seq = props_item.get('ROIElementalCompositionSequence', [])
                for k, comp_item in enumerate(roi_elemental_comp_seq):
                    if not comp_item.get('ROIElementalCompositionAtomicNumber'):
                        result.add_error(
                            f"RT ROI Observations Sequence item {i}, ROI Elemental Composition item {k}: "
                            "ROI Elemental Composition Atomic Number (3006,00B7) is required (Type 1). "
                            "This specifies the atomic number of the element (1-118)."
                        )
                    if 'ROIElementalCompositionAtomicMassFraction' not in comp_item:
                        result.add_error(
                            f"RT ROI Observations Sequence item {i}, ROI Elemental Composition item {k}: "
                            "ROI Elemental Composition Atomic Mass Fraction (3006,00B8) is required (Type 1). "
                            "This specifies the fractional weight of the element (0.0-1.0)."
                        )

            # Validate Related RT ROI Observations Sequence
            related_obs_seq = obs_item.get('RelatedRTROIObservationsSequence', [])
            for j, related_obs_item in enumerate(related_obs_seq):
                if not related_obs_item.get('ObservationNumber'):
                    result.add_error(
                        f"RT ROI Observations Sequence item {i}, Related RT ROI Observations item {j}: "
                        "Observation Number (3006,0082) is required (Type 1). "
                        "This must reference another observation in the RT ROI Observations Sequence."
                    )

            # Validate ROI Interpreter Sequence (when present)
            roi_interpreter_seq = obs_item.get('ROIInterpreterSequence', [])
            for j, interpreter_item in enumerate(roi_interpreter_seq):
                # This sequence follows the Identified Person or Device Macro
                # Basic validation for required elements
                if not interpreter_item.get('PersonIdentificationCodeSequence') and not interpreter_item.get('DeviceID'):
                    result.add_warning(
                        f"RT ROI Observations Sequence item {i}, ROI Interpreter Sequence item {j}: "
                        "Either Person Identification Code Sequence or Device ID should be present "
                        "to properly identify the interpreter according to the Identified Person or Device Macro."
                    )
    
    @staticmethod
    def _validate_semantic_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate semantic constraints and logical consistency.

        Validates logical relationships and constraints that ensure the dataset
        makes semantic sense according to DICOM standards and real-world usage.
        """
        
        rt_roi_obs_seq = getattr(dataset, 'RTROIObservationsSequence', [])
        for i, obs_item in enumerate(rt_roi_obs_seq):
            roi_physical_props_seq = obs_item.get('ROIPhysicalPropertiesSequence', [])
            
            for j, props_item in enumerate(roi_physical_props_seq):
                # Validate elemental composition mass fractions sum to 1.0
                roi_elemental_comp_seq = props_item.get('ROIElementalCompositionSequence', [])
                if roi_elemental_comp_seq:
                    total_mass_fraction = 0.0
                    for comp_item in roi_elemental_comp_seq:
                        mass_fraction = comp_item.get('ROIElementalCompositionAtomicMassFraction', 0.0)
                        total_mass_fraction += mass_fraction
                    
                    # Check if sum is approximately 1.0 (within floating point precision)
                    if abs(total_mass_fraction - 1.0) > 0.001:
                        result.add_warning(
                            f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                            f"Sum of ROI Elemental Composition Atomic Mass Fractions ({total_mass_fraction:.3f}) "
                            "should equal 1.0 within acceptable limits of floating point precision. "
                            "This ensures the elemental composition represents a complete material description."
                        )
                
                # Validate atomic numbers are valid (1-118)
                for k, comp_item in enumerate(roi_elemental_comp_seq):
                    atomic_number = comp_item.get('ROIElementalCompositionAtomicNumber', 0)
                    if atomic_number < 1 or atomic_number > 118:
                        result.add_warning(
                            f"RT ROI Observations Sequence item {i}, ROI Elemental Composition item {k}: "
                            f"Atomic Number ({atomic_number}) should be between 1 and 118 (valid elements). "
                            "Values outside this range do not correspond to known chemical elements."
                        )

                    mass_fraction = comp_item.get('ROIElementalCompositionAtomicMassFraction', 0.0)
                    if mass_fraction < 0.0 or mass_fraction > 1.0:
                        result.add_warning(
                            f"RT ROI Observations Sequence item {i}, ROI Elemental Composition item {k}: "
                            f"Atomic Mass Fraction ({mass_fraction}) should be between 0.0 and 1.0. "
                            "Mass fractions represent the fractional weight contribution of each element."
                        )
                
                # Validate physical property values are reasonable
                physical_property = props_item.get('ROIPhysicalProperty', '')
                property_value = props_item.get('ROIPhysicalPropertyValue', 0.0)

                if physical_property in ['REL_MASS_DENSITY', 'REL_ELEC_DENSITY'] and property_value < 0:
                    result.add_warning(
                        f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                        f"Physical property value ({property_value}) should be non-negative for {physical_property}. "
                        "Relative density values are typically positive numbers relative to water."
                    )
                elif physical_property == 'EFFECTIVE_Z' and (property_value < 1 or property_value > 118):
                    result.add_warning(
                        f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                        f"Effective Z value ({property_value}) should be between 1 and 118. "
                        "Effective atomic number should correspond to realistic material properties."
                    )
                elif physical_property == 'MEAN_EXCI_ENERGY' and property_value <= 0:
                    result.add_warning(
                        f"RT ROI Observations Sequence item {i}, ROI Physical Properties item {j}: "
                        f"Mean Excitation Energy ({property_value} eV) should be positive. "
                        "Mean excitation energy is a positive physical quantity measured in electron volts."
                    )

            # Validate ROI Observation DateTime format
            roi_obs_datetime = obs_item.get('ROIObservationDateTime', '')
            if roi_obs_datetime:
                # DICOM DateTime format: YYYYMMDDHHMMSS.FFFFFF&ZZXX
                # Basic validation for minimum length and numeric characters
                if len(roi_obs_datetime) < 8 or not roi_obs_datetime[:8].isdigit():
                    result.add_warning(
                        f"RT ROI Observations Sequence item {i}: "
                        f"ROI Observation DateTime (3006,002E) value '{roi_obs_datetime}' should follow "
                        "DICOM DateTime format (YYYYMMDDHHMMSS.FFFFFF&ZZXX). "
                        "At minimum, YYYYMMDD format is required."
                    )

            # Validate RT ROI Interpreted Type and therapeutic role consistency
            interpreted_type = obs_item.get('RTROIInterpretedType', '')
            therapeutic_category = obs_item.get('TherapeuticRoleCategoryCodeSequence', [])

            if interpreted_type in ['PTV', 'CTV', 'GTV'] and not therapeutic_category:
                result.add_warning(
                    f"RT ROI Observations Sequence item {i}: "
                    f"RT ROI Interpreted Type '{interpreted_type}' typically requires "
                    "Therapeutic Role Category Code Sequence (3010,0064) to be present "
                    "for complete semantic description according to DICOM PS3.3 C.*******."
                )
