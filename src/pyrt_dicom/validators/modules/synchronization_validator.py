"""Synchronization Module DICOM validation - PS3.3 C.7.4.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class SynchronizationValidator(BaseValidator):
    """Validator for DICOM Synchronization Module (PS3.3 C.7.4.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Synchronization Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        SynchronizationValidator._validate_required_elements(dataset, result)
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            SynchronizationValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            SynchronizationValidator._validate_enumerated_values(dataset, result)
        
        # Validate cross-field dependencies
        if config.validate_cross_field_dependencies:
            SynchronizationValidator._validate_cross_field_dependencies(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements per DICOM PS3.3 C.7.4.2."""
        
        # Synchronization Frame of Reference UID (0020,0200) - Type 1
        if not hasattr(dataset, 'SynchronizationFrameOfReferenceUID') or not dataset.SynchronizationFrameOfReferenceUID:
            result.add_error(
                "Synchronization Frame of Reference UID (0020,0200) is required (Type 1). "
                "This UID identifies the common synchronization environment. "
                "Use SynchronizationModule.from_required_elements() with a valid UID string. "
                "See DICOM PS3.3 C.*******.1 for details."
            )
        else:
            # Validate UID format
            BaseValidator.validate_uid_format(
                dataset.SynchronizationFrameOfReferenceUID,
                "Synchronization Frame of Reference UID (0020,0200)",
                result
            )
        
        # Synchronization Trigger (0018,106A) - Type 1
        if not hasattr(dataset, 'SynchronizationTrigger') or not dataset.SynchronizationTrigger:
            result.add_error(
                "Synchronization Trigger (0018,106A) is required (Type 1). "
                "This defines how the equipment handles synchronization. "
                "Valid values: SOURCE, EXTERNAL, PASSTHRU, NO TRIGGER. "
                "Use SynchronizationModule.from_required_elements() with SynchronizationTrigger enum. "
                "See DICOM PS3.3 C.7.4.2 for definitions."
            )
        
        # Acquisition Time Synchronized (0018,1800) - Type 1
        if not hasattr(dataset, 'AcquisitionTimeSynchronized') or not dataset.AcquisitionTimeSynchronized:
            result.add_error(
                "Acquisition Time Synchronized (0018,1800) is required (Type 1). "
                "This indicates whether Acquisition DateTime is synchronized with external time reference. "
                "Valid values: Y (synchronized), N (not synchronized). "
                "Use SynchronizationModule.from_required_elements() with AcquisitionTimeSynchronized enum. "
                "See DICOM PS3.3 C.*******.4 for details."
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements per DICOM PS3.3 C.7.4.2."""
        
        # Synchronization Channel (0018,106C) - Type 1C
        # Required if synchronization channel or trigger is encoded in a waveform in this SOP Instance
        if hasattr(dataset, 'SynchronizationChannel'):
            sync_channel = dataset.SynchronizationChannel
            # Convert to list for easier handling (works with both list and MultiValue)
            sync_values = list(sync_channel) if hasattr(sync_channel, '__iter__') else [sync_channel]
            
            if len(sync_values) != 2:
                result.add_error(
                    "Synchronization Channel (0018,106C) must be specified as [M,C] pair with exactly 2 values. "
                    "M is the ordinal of Sequence Item of Waveform Sequence (5400,0100), "
                    "C is the ordinal of Sequence Item of Channel Definition Sequence (003A,0200). "
                    "Use SynchronizationModule.with_synchronization_channel([M, C]). "
                    "See DICOM PS3.3 C.*******.3 for details."
                )
            else:
                # Check value types and ranges
                if not all(isinstance(x, int) for x in sync_values):
                    result.add_error(
                        "Synchronization Channel (0018,106C) values must be integers representing "
                        f"sequence item ordinals. Current values: {sync_values}. "
                        "Use integer values for multiplex group and channel ordinals."
                    )
                elif not all(x > 0 for x in sync_values):
                    result.add_error(
                        "Synchronization Channel (0018,106C) values must be positive integers "
                        f"(ordinals start from 1). Current values: {sync_values}. "
                        "Use positive integers for sequence item ordinals."
                    )
        
        # Add informational warning about the conditional nature only if there are triggers that might need it
        # Note: We cannot automatically determine if synchronization channel is required
        # without knowledge of waveform sequences in the SOP Instance
        sync_trigger = getattr(dataset, 'SynchronizationTrigger', '')
        if not hasattr(dataset, 'SynchronizationChannel') and sync_trigger != 'NO TRIGGER':
            result.add_warning(
                "Synchronization Channel (0018,106C) is conditionally required (Type 1C) "
                "if synchronization channel or trigger is encoded in a waveform in this SOP Instance. "
                "This validation cannot determine if waveforms with synchronization data are present. "
                "If your SOP Instance contains waveform data that encodes synchronization information, "
                "use SynchronizationModule.with_synchronization_channel([M, C]). "
                "See DICOM PS3.3 C.*******.3 for details."
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM PS3.3 C.7.4.2 specifications."""
        
        # Synchronization Trigger (0018,106A) enumerated values - Type 1 (required)
        sync_trigger = getattr(dataset, 'SynchronizationTrigger', '')
        if sync_trigger:
            allowed_triggers = ["SOURCE", "EXTERNAL", "PASSTHRU", "NO TRIGGER"]
            if str(sync_trigger) not in allowed_triggers:
                result.add_error(
                    f"Synchronization Trigger (0018,106A) value '{sync_trigger}' is invalid. "
                    f"Must be one of: {', '.join(allowed_triggers)}. "
                    "SOURCE = equipment provides sync to others, "
                    "EXTERNAL = equipment receives sync from others, "
                    "PASSTHRU = equipment receives and forwards sync, "
                    "NO TRIGGER = no synchronization. "
                    "Use SynchronizationTrigger enum values. "
                    "See DICOM PS3.3 C.7.4.2 for definitions."
                )
        
        # Acquisition Time Synchronized (0018,1800) enumerated values - Type 1 (required)
        time_sync = getattr(dataset, 'AcquisitionTimeSynchronized', '')
        if time_sync:
            allowed_sync_values = ["Y", "N"]
            if str(time_sync) not in allowed_sync_values:
                result.add_error(
                    f"Acquisition Time Synchronized (0018,1800) value '{time_sync}' is invalid. "
                    f"Must be one of: {', '.join(allowed_sync_values)}. "
                    "Y = Acquisition DateTime synchronized with external time reference, "
                    "N = Acquisition DateTime not synchronized. "
                    "Use AcquisitionTimeSynchronized enum values. "
                    "See DICOM PS3.3 C.*******.4 for details."
                )
        
        # Time Distribution Protocol (0018,1802) enumerated values - Type 3 (optional)
        time_protocol = getattr(dataset, 'TimeDistributionProtocol', '')
        if time_protocol:
            allowed_protocols = ["NTP", "IRIG", "GPS", "SNTP", "PTP"]
            if str(time_protocol) not in allowed_protocols:
                result.add_warning(
                    f"Time Distribution Protocol (0018,1802) value '{time_protocol}' is invalid. "
                    f"Should be one of: {', '.join(allowed_protocols)}. "
                    "NTP = Network Time Protocol, "
                    "IRIG = Inter Range Instrumentation Group, "
                    "GPS = Global Positioning System, "
                    "SNTP = Simple Network Time Protocol, "
                    "PTP = IEEE 1588 Precision Time Protocol. "
                    "Use TimeDistributionProtocol enum values. "
                    "See DICOM PS3.3 C.*******.2 for details."
                )
        
        # NTP Source Address (0018,1803) format validation - Type 3 (optional)
        ntp_address = getattr(dataset, 'NTPSourceAddress', '')
        if ntp_address:
            # Basic validation for IPv4 (dotted decimal) or IPv6 (colon separated hex)
            if not SynchronizationValidator._is_valid_ip_address(ntp_address):
                result.add_warning(
                    f"NTP Source Address (0018,1803) '{ntp_address}' does not appear to be "
                    "a valid IPv4 (dotted decimal, e.g., ***********) or "
                    "IPv6 (colon separated hexadecimal, e.g., 12:34:56:78:9a:bc:de:f0) address. "
                    "This field specifies the IP address of NTP, SNTP, or PTP time source. "
                    "Verify the address format matches network standards."
                )
    
    @staticmethod
    def _is_valid_ip_address(address: str) -> bool:
        """Basic validation for IP address format.
        
        Args:
            address: IP address string to validate
            
        Returns:
            bool: True if address appears to be valid IPv4 or IPv6 format
        """
        # Basic IPv4 validation (dotted decimal)
        if '.' in address:
            parts = address.split('.')
            if len(parts) == 4:
                try:
                    return all(0 <= int(part) <= 255 for part in parts)
                except ValueError:
                    return False
        
        # Basic IPv6 validation (colon separated hex)
        elif ':' in address:
            parts = address.split(':')
            if 2 <= len(parts) <= 8:  # IPv6 can have compressed notation
                try:
                    for part in parts:
                        if part:  # Allow empty parts for compressed notation
                            int(part, 16)  # Validate as hexadecimal
                    return True
                except ValueError:
                    return False
        
        return False
    
    @staticmethod
    def _validate_cross_field_dependencies(dataset: Dataset, result: ValidationResult) -> None:
        """Validate logical consistency between related fields per DICOM PS3.3 C.7.4.2."""
        
        # Validate NTP Source Address usage with Time Distribution Protocol
        ntp_address = getattr(dataset, 'NTPSourceAddress', '')
        time_protocol = getattr(dataset, 'TimeDistributionProtocol', '')
        
        if ntp_address and time_protocol:
            if time_protocol not in ["NTP", "SNTP", "PTP"]:
                result.add_warning(
                    f"NTP Source Address (0018,1803) is present but Time Distribution Protocol "
                    f"(0018,1802) is '{time_protocol}'. NTP Source Address is intended for "
                    "NTP, SNTP, or PTP protocols only. Consider removing NTP Source Address "
                    "or changing the protocol to match your time distribution method."
                )
        elif ntp_address and not time_protocol:
            result.add_warning(
                "NTP Source Address (0018,1803) is present but Time Distribution Protocol "
                "(0018,1802) is not specified. Consider adding the time distribution protocol "
                "to clarify how this address is used (NTP, SNTP, or PTP)."
            )
        
        # Validate UTC Synchronization UID usage
        sync_uid = getattr(dataset, 'SynchronizationFrameOfReferenceUID', '')
        if sync_uid == '1.2.840.10008.15.1.1':  # UTC Synchronization UID
            time_sync = getattr(dataset, 'AcquisitionTimeSynchronized', '')
            if time_sync == 'N':
                result.add_warning(
                    "Synchronization Frame of Reference UID (0020,0200) is set to UTC "
                    "Synchronization UID '1.2.840.10008.15.1.1' but Acquisition Time "
                    "Synchronized (0018,1800) is 'N'. This combination suggests the equipment "
                    "is synchronized to UTC but acquisition times are not synchronized, "
                    "which may be inconsistent. Verify your synchronization configuration."
                )
            
            # Recommend including time distribution info when using UTC sync UID
            if not time_protocol:
                result.add_warning(
                    "Using UTC Synchronization UID '1.2.840.10008.15.1.1'. Consider specifying "
                    "Time Distribution Protocol (0018,1802) and NTP Source Address (0018,1803) "
                    "to document the quality and source of UTC synchronization. "
                    "See DICOM PS3.3 C.*******.1 for recommendations."
                )
        
        # Validate synchronization trigger logic
        sync_trigger = getattr(dataset, 'SynchronizationTrigger', '')
        if sync_trigger == 'NO TRIGGER':
            if hasattr(dataset, 'SynchronizationChannel'):
                result.add_warning(
                    "Synchronization Trigger (0018,106A) is 'NO TRIGGER' but Synchronization "
                    "Channel (0018,106C) is present. If data acquisition is not synchronized "
                    "by common channel or trigger, the synchronization channel specification "
                    "may be unnecessary or inconsistent."
                )
            
            # Check if other sync-related fields are present with NO TRIGGER
            sync_fields = ['TriggerSourceOrType', 'TimeSource', 'TimeDistributionProtocol', 'NTPSourceAddress']
            present_fields = [field for field in sync_fields if hasattr(dataset, field)]
            if present_fields:
                result.add_warning(
                    f"Synchronization Trigger (0018,106A) is 'NO TRIGGER' but synchronization-related "
                    f"fields are present: {', '.join(present_fields)}. If no synchronization is used, "
                    "these fields may be unnecessary. If synchronization is used, verify the trigger value."
                )
