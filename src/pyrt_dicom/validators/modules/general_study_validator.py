"""General Study Module DICOM validation - PS3.3 C.7.2.1

Comprehensive validation for the General Study Module according to DICOM PS3.3 C.7.2.1.
Validates all Type 1, Type 2, Type 3, Type 1C, and Type 2C requirements with proper
conditional logic and sequence structure validation.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule

class GeneralStudyValidator(BaseValidator):
    """Validator for DICOM General Study Module (PS3.3 C.7.2.1).

    Performs comprehensive validation including:
    - Type 1 required element presence (Study Instance UID)
    - Type 2 required element presence with empty value handling
    - Type 3 optional element format validation
    - Type 1C/2C conditional requirements with context-aware validation
    - Sequence structure validation (single vs multiple item constraints)
    - Person Identification Macro validation (Table 10-1)
    - Code Sequence Macro validation (Table 8.8-1)
    - Cross-field consistency (physician name/ID correspondence)
    - DICOM format compliance and enumerated value checking
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements for General Study Module.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Type 1 requirements (required and must be non-empty)
        if 'StudyInstanceUID' not in data:
            result.add_error(
                "Study Instance UID (0020,000D) is required and must be non-empty (Type 1). "
                "Provide a unique identifier for the Study using a valid UID format."
            )
        elif 'StudyInstanceUID' in data and not data.StudyInstanceUID:
            result.add_error(
                "Study Instance UID (0020,000D) is required and must be non-empty (Type 1). "
                "Provide a unique identifier for the Study using a valid UID format."
            )

        # Type 2 requirements (required but can be empty)
        type2_elements = [
            ('StudyDate', '(0008,0020)', 'Study Date', 'the date the Study started in DICOM DA format (YYYYMMDD) or empty string'),
            ('StudyTime', '(0008,0030)', 'Study Time', 'the time the Study started in DICOM TM format (HHMMSS) or empty string'),
            ('ReferringPhysicianName', '(0008,0090)', "Referring Physician's Name", "the name of the Patient's referring physician in DICOM PN format or empty string"),
            ('StudyID', '(0020,0010)', 'Study ID', 'a user or equipment generated Study identifier or empty string'),
            ('AccessionNumber', '(0008,0050)', 'Accession Number', 'a departmental Information System generated number or empty string')
        ]

        for element_name, tag, display_name, description in type2_elements:
            if element_name not in data:
                result.add_error(
                    f"{display_name} {tag} is required (Type 2). "
                    f"Provide {description}."
                )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements for General Study Module.

        Note: The General Study Module has no Type 1C or 2C attributes according to
        DICOM PS3.3 C.7.2.1, so this method returns an empty result for consistency
        with the refactor pattern.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no conditional requirements in this module)
        """
        result = ValidationResult()
        # No Type 1C or 2C requirements in General Study Module
        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints for General Study Module.

        Note: The General Study Module has no enumerated value constraints according to
        DICOM PS3.3 C.7.2.1, so this method returns an empty result for consistency
        with the refactor pattern.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty validation result (no enumerated values in this module)
        """
        result = ValidationResult()
        # No enumerated value constraints in General Study Module
        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements for General Study Module.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for sequence structure violations
        """
        result = ValidationResult()

        # Validate single-item sequence constraints
        single_item_sequences = [
            ('ReferringPhysicianIdentificationSequence', '(0008,0096)', 'Referring Physician Identification Sequence'),
            ('IssuerOfAccessionNumberSequence', '(0008,0051)', 'Issuer of Accession Number Sequence'),
            ('RequestingServiceCodeSequence', '(0032,1034)', 'Requesting Service Code Sequence')
        ]

        for seq_name, tag, display_name in single_item_sequences:
            if seq_name in data:
                sequence = getattr(data, seq_name, [])
                if len(sequence) > 1:
                    result.add_error(
                        f"{display_name} {tag} contains {len(sequence)} items but only a single Item is permitted. "
                        "Per DICOM PS3.3 C.7.2.1, remove extra items to comply with the standard."
                    )

        # Validate Code Sequence Macro requirements for sequences that use it
        GeneralStudyValidator._validate_code_sequences(data, result)

        # Validate Person Identification Macro requirements
        GeneralStudyValidator._validate_person_identification_sequences(data, result)

        # Validate cross-field consistency and correspondence requirements
        GeneralStudyValidator._validate_physician_correspondence(data, result)

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: Union[ValidationConfig, None] = None) -> ValidationResult:
        """Orchestrate all validations for General Study Module.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Comprehensive validation result with structured errors and warnings
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(GeneralStudyValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(GeneralStudyValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(GeneralStudyValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(GeneralStudyValidator.validate_sequence_structures(data))

        return result

    @staticmethod
    def _validate_code_sequences(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Code Sequence Macro requirements for code sequences in the module."""

        # Requesting Service Code Sequence (uses Code Sequence Macro)
        if 'RequestingServiceCodeSequence' in data:
            requesting_service_seq = data.RequestingServiceCodeSequence
            for i, item in enumerate(requesting_service_seq):
                item_prefix = f"Requesting Service Code Sequence (0032,1034) Item {i + 1}"
                GeneralStudyValidator._validate_code_sequence_item(item, item_prefix, result)

        # Procedure Code Sequence (uses Code Sequence Macro)
        if 'ProcedureCodeSequence' in data:
            procedure_code_seq = data.ProcedureCodeSequence
            for i, item in enumerate(procedure_code_seq):
                item_prefix = f"Procedure Code Sequence (0008,1032) Item {i + 1}"
                GeneralStudyValidator._validate_code_sequence_item(item, item_prefix, result)

        # Reason For Performed Procedure Code Sequence (uses Code Sequence Macro)
        if 'ReasonForPerformedProcedureCodeSequence' in data:
            reason_code_seq = data.ReasonForPerformedProcedureCodeSequence
            for i, item in enumerate(reason_code_seq):
                item_prefix = f"Reason For Performed Procedure Code Sequence (0040,1012) Item {i + 1}"
                GeneralStudyValidator._validate_code_sequence_item(item, item_prefix, result)

    @staticmethod
    def _validate_physician_correspondence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate correspondence between physician names and identification sequences."""

        # Consulting Physician correspondence
        consulting_name = data.ConsultingPhysicianName if 'ConsultingPhysicianName' in data else None
        consulting_seq = data.ConsultingPhysicianIdentificationSequence if 'ConsultingPhysicianIdentificationSequence' in data else []

        if consulting_name and consulting_seq:
            # Handle both string and list formats for multi-value fields
            if isinstance(consulting_name, str):
                name_count = len(consulting_name.split('\\')) if consulting_name else 0
            else:
                name_count = len(consulting_name) if consulting_name else 0

            if len(consulting_seq) != name_count:
                result.add_warning(
                    f"Consulting Physician Identification Sequence (0008,009D) has {len(consulting_seq)} items "
                    f"but Consulting Physician Name (0008,009C) has {name_count} values. "
                    "Per DICOM PS3.3 C.7.2.1, the number and order should correspond when both are present."
                )

        # Physicians of Record correspondence
        record_name = data.PhysiciansOfRecord if 'PhysiciansOfRecord' in data else None
        record_seq = data.PhysiciansOfRecordIdentificationSequence if 'PhysiciansOfRecordIdentificationSequence' in data else []

        if record_name and record_seq:
            if isinstance(record_name, str):
                name_count = len(record_name.split('\\')) if record_name else 0
            else:
                name_count = len(record_name) if record_name else 0

            if len(record_seq) != name_count:
                result.add_warning(
                    f"Physician(s) of Record Identification Sequence (0008,1049) has {len(record_seq)} items "
                    f"but Physician(s) of Record (0008,1048) has {name_count} values. "
                    "Per DICOM PS3.3 C.7.2.1, the number and order should correspond when both are present."
                )

        # Physicians Reading Study correspondence
        reading_name = data.NameOfPhysiciansReadingStudy if 'NameOfPhysiciansReadingStudy' in data else None
        reading_seq = data.PhysiciansReadingStudyIdentificationSequence if 'PhysiciansReadingStudyIdentificationSequence' in data else []

        if reading_name and reading_seq:
            if isinstance(reading_name, str):
                name_count = len(reading_name.split('\\')) if reading_name else 0
            else:
                name_count = len(reading_name) if reading_name else 0

            if len(reading_seq) != name_count:
                result.add_warning(
                    f"Physician(s) Reading Study Identification Sequence (0008,1062) has {len(reading_seq)} items "
                    f"but Name of Physician(s) Reading Study (0008,1060) has {name_count} values. "
                    "Per DICOM PS3.3 C.7.2.1, the number and order should correspond when both are present."
                )

    @staticmethod
    def _validate_person_identification_sequences(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Person Identification Macro (Table 10-1) requirements in physician identification sequences.

        Validates all physician identification sequences according to DICOM PS3.3 Table 10-1
        Person Identification Macro Attributes, ensuring proper structure and required elements.
        """

        # List of physician identification sequences to validate
        sequences_to_validate = [
            ('ReferringPhysicianIdentificationSequence', 'Referring Physician Identification Sequence (0008,0096)'),
            ('ConsultingPhysicianIdentificationSequence', 'Consulting Physician Identification Sequence (0008,009D)'),
            ('PhysiciansOfRecordIdentificationSequence', 'Physician(s) of Record Identification Sequence (0008,1049)'),
            ('PhysiciansReadingStudyIdentificationSequence', 'Physician(s) Reading Study Identification Sequence (0008,1062)')
        ]

        for seq_attr, seq_name in sequences_to_validate:
            if seq_attr in data:
                sequence = getattr(data, seq_attr, [])
                for i, item in enumerate(sequence):
                    GeneralStudyValidator._validate_person_identification_item(item, seq_name, i, result)

    @staticmethod
    def _validate_person_identification_item(item: Dataset, seq_name: str, item_index: int, result: ValidationResult) -> None:
        """Validate a single Person Identification Macro item (Table 10-1).

        Validates all requirements from DICOM PS3.3 Table 10-1 Person Identification Macro
        including Type 1 and Type 1C requirements with proper error guidance.
        """

        item_prefix = f"{seq_name} Item {item_index + 1}"

        # Validate Person Identification Code Sequence (0040,1101) Type 1
        if 'PersonIdentificationCodeSequence' not in item:
            result.add_error(
                f"{item_prefix}: Person Identification Code Sequence (0040,1101) is required (Type 1). "
                "Add at least one code sequence item using the Code Sequence Macro (Table 8.8-1) "
                "to identify the person according to DICOM PS3.3 Table 10-1."
            )
        else:
            person_id_code_seq = item.PersonIdentificationCodeSequence
            if not person_id_code_seq:
                result.add_error(
                    f"{item_prefix}: Person Identification Code Sequence (0040,1101) is required (Type 1). "
                    "Add at least one code sequence item using the Code Sequence Macro (Table 8.8-1) "
                    "to identify the person according to DICOM PS3.3 Table 10-1."
                )
            else:
                # Validate each code sequence item (Table 8.8-1)
                for j, code_item in enumerate(person_id_code_seq):
                    code_prefix = f"{item_prefix} Person ID Code {j + 1}"
                    GeneralStudyValidator._validate_code_sequence_item(code_item, code_prefix, result)

        # Validate Type 1C requirement: Institution Name OR Institution Code Sequence must be present
        institution_name = item.InstitutionName if 'InstitutionName' in item else None
        institution_code_seq = item.InstitutionCodeSequence if 'InstitutionCodeSequence' in item else []

        if not institution_name and not institution_code_seq:
            result.add_error(
                f"{item_prefix}: Either Institution Name (0008,0080) or Institution Code Sequence (0008,0082) "
                "must be present (Type 1C requirement). Per DICOM PS3.3 Table 10-1, provide either the "
                "institution name as text or a coded representation of the institution."
            )

        # Validate Institution Code Sequence if present
        if institution_code_seq:
            if len(institution_code_seq) > 1:
                result.add_error(
                    f"{item_prefix}: Institution Code Sequence (0008,0082) contains "
                    f"{len(institution_code_seq)} items but only a single Item shall be included. "
                    "Per DICOM PS3.3 Table 10-1, remove extra items to comply with the standard."
                )
            for j, code_item in enumerate(institution_code_seq):
                code_prefix = f"{item_prefix} Institution Code {j + 1}"
                GeneralStudyValidator._validate_code_sequence_item(code_item, code_prefix, result)

    @staticmethod
    def _validate_code_sequence_item(code_item: Dataset, item_prefix: str, result: ValidationResult) -> None:
        """Validate a Code Sequence Macro item (Table 8.8-1).

        Validates all requirements from DICOM PS3.3 Table 8.8-1 Code Sequence Macro
        including Type 1 and Type 1C requirements with proper error guidance.
        """

        # Check Code Meaning (Type 1)
        if 'CodeMeaning' not in code_item or not code_item.CodeMeaning:
            result.add_error(
                f"{item_prefix}: Code Meaning (0008,0104) is required and must be non-empty (Type 1). "
                "Provide a human-readable meaning of the code according to DICOM PS3.3 Table 8.8-1."
            )

        # Check Type 1C requirements for code values (at least one must be present)
        code_value = code_item.CodeValue if 'CodeValue' in code_item else None
        long_code_value = code_item.LongCodeValue if 'LongCodeValue' in code_item else None
        urn_code_value = code_item.URNCodeValue if 'URNCodeValue' in code_item else None

        if not any([code_value, long_code_value, urn_code_value]):
            result.add_error(
                f"{item_prefix}: At least one of Code Value (0008,0100), Long Code Value (0008,0119), "
                "or URN Code Value (0008,0120) must be present (Type 1C requirement). "
                "Per DICOM PS3.3 Table 8.8-1, provide the actual code identifier."
            )

        # Check Coding Scheme Designator (Type 1C - required if Code Value or Long Code Value is present)
        coding_scheme_designator = code_item.CodingSchemeDesignator if 'CodingSchemeDesignator' in code_item else None
        if (code_value or long_code_value) and not coding_scheme_designator:
            result.add_error(
                f"{item_prefix}: Coding Scheme Designator (0008,0102) is required when "
                "Code Value or Long Code Value is present (Type 1C requirement). "
                "Per DICOM PS3.3 Table 8.8-1, specify the coding scheme that defines the code."
            )


