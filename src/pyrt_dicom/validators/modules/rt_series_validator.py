"""RT Series Module DICOM validation - PS3.3 C.8.8.1

This validator ensures 100% compliance with DICOM PS3.3 C.8.8.1 RT Series Module
specifications, including proper validation of RT-specific requirements, conditional
logic, and sequence structures for radiotherapy objects.

Key Validation Areas:
- Type 1 required elements (Modality, Series Instance UID)
- Type 2 required elements (Operators' Name)
- Type 3 optional elements with proper enumerated value checking
- Conditional sequence requirements and cardinality constraints
- RT-specific modality validation and IOD-specific requirements
- Cross-field consistency validation for operator identification
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class RTSeriesValidator(BaseValidator):
    """Validator for DICOM RT Series Module (PS3.3 C.8.8.1).

    Provides comprehensive validation of RT Series Module requirements for radiotherapy
    objects, ensuring full compliance with DICOM standard requirements for RT series
    identification and selection within the Query/Retrieve model.

    Validation Coverage:
    - Type 1 required elements with RT-specific modality constraints
    - Type 2 required elements with proper empty value handling
    - Type 3 optional elements with enumerated value validation
    - Conditional sequence requirements and operator identification consistency
    - RT-specific temporal and workflow requirements
    - Cross-field validation for operator and identification sequences

    Error Message Standards:
    - Include DICOM tag references (e.g., "(0008,0060)")
    - Reference DICOM PS3.3 sections for complex requirements
    - Provide actionable guidance for resolving issues
    - Explain the RT-specific context and clinical workflow requirements
    - Suggest valid values for RT-specific enumerated attributes
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Series Module requirements on any pydicom Dataset.

        Performs comprehensive validation of DICOM PS3.3 C.8.8.1 RT Series Module
        requirements, including RT-specific conditional logic, sequence structures,
        and radiotherapy workflow requirements.

        This method validates:
        1. Type 1 required elements (Modality, Series Instance UID)
        2. Type 2 required elements (Operators' Name)
        3. Type 3 optional elements with RT-specific enumerated value checking
        4. Conditional sequence requirements and cardinality constraints
        5. Cross-field consistency (operator identification sequences)
        6. RT-specific modality constraints and IOD compliance
        7. Temporal workflow requirements for RT treatment sessions

        Args:
            dataset (Dataset): pydicom Dataset containing RT Series Module data.
                             Must be a valid pydicom Dataset object.
            config (ValidationConfig | None): Optional validation configuration to control
                                             validation scope and behavior. If None, uses
                                             default configuration with all validations enabled.

        Returns:
            ValidationResult: Comprehensive validation result with specific error messages
                            and actionable guidance for resolving any DICOM compliance issues.

        Raises:
            None - All validation issues are captured in the ValidationResult
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 required elements
        if config.validate_required_elements:
            RTSeriesValidator._validate_type_1_requirements(dataset, result)

        # Validate Type 2 required elements
        if config.validate_required_elements:
            RTSeriesValidator._validate_type_2_requirements(dataset, result)

        # Validate enumerated values with RT-specific constraints
        if config.check_enumerated_values:
            RTSeriesValidator._validate_enumerated_values(dataset, result)

        # Validate sequence structures and conditional requirements
        if config.validate_sequences:
            RTSeriesValidator._validate_sequence_requirements(dataset, result)

        # Validate conditional requirements and cross-field consistency
        if config.validate_conditional_requirements:
            RTSeriesValidator._validate_conditional_requirements(dataset, result)

        return result
    
    @staticmethod
    def _validate_type_1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements per DICOM PS3.3 C.8.8.1.

        Type 1 elements must be present and contain valid data.
        """
        # Modality (0008,0060) - Type 1
        if not hasattr(dataset, 'Modality') or not dataset.Modality:
            result.add_error(
                "Modality (0008,0060) is required (Type 1). "
                "The type of equipment that originally acquired the data used to create the "
                "Instances in this Series must be specified. For RT Series, valid values are: "
                "RTIMAGE, RTDOSE, RTSTRUCT, RTPLAN, RTRECORD. "
                "See DICOM PS3.3 C.******* for IOD-specific requirements. "
                "Solution: Set Modality to one of the valid RT enumerated values."
            )

        # Series Instance UID (0020,000E) - Type 1
        if not hasattr(dataset, 'SeriesInstanceUID') or not dataset.SeriesInstanceUID:
            result.add_error(
                "Series Instance UID (0020,000E) is required (Type 1). "
                "A unique identifier for the Series must be provided to enable proper "
                "DICOM Query/Retrieve operations and series identification. "
                "Solution: Set SeriesInstanceUID to a valid DICOM UID."
            )

    @staticmethod
    def _validate_type_2_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 (required but may be empty) elements per DICOM PS3.3 C.8.8.1.

        Type 2 elements must be present but may contain empty values.
        """
        # Operators' Name (0008,1070) - Type 2
        if not hasattr(dataset, 'OperatorsName'):
            result.add_error(
                "Operators' Name (0008,1070) is required (Type 2). "
                "The name(s) of the operator(s) supporting the Series must be present, "
                "even if empty. This attribute is essential for RT workflow tracking "
                "and quality assurance. "
                "Solution: Set OperatorsName to operator name(s) or empty string if unknown."
            )

    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM PS3.3 C.8.8.1 specifications.

        Validates RT-specific enumerated values and their IOD-specific constraints.
        """
        # Modality (0008,0060) - RT-specific enumerated values
        modality = getattr(dataset, 'Modality', '')
        if modality:
            rt_modalities = ["RTIMAGE", "RTDOSE", "RTSTRUCT", "RTPLAN", "RTRECORD"]
            if modality not in rt_modalities:
                result.add_error(
                    f"Modality (0008,0060) has invalid value '{modality}' for RT Series. "
                    f"Valid RT modality values are: {', '.join(rt_modalities)}. "
                    "The enumerated value must be determined by the IOD: "
                    "RTIMAGE (RT Image IOD), RTDOSE (RT Dose IOD), RTSTRUCT (RT Structure Set IOD), "
                    "RTPLAN (RT Plan/Ion Plan IOD), RTRECORD (RT Treatment Record IODs). "
                    "See DICOM PS3.3 C.******* for IOD-specific requirements."
                )

    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements per DICOM PS3.3 C.8.8.1.

        Validates sequence cardinality constraints and required sub-attributes.
        """
        # Series Description Code Sequence (0008,103F) - Type 3, but if present, only single item allowed
        series_desc_seq = getattr(dataset, 'SeriesDescriptionCodeSequence', [])
        if len(series_desc_seq) > 1:
            result.add_error(
                "Series Description Code Sequence (0008,103F) constraint violation: "
                f"Only a single Item is permitted in this Sequence, found {len(series_desc_seq)} items. "
                "This sequence provides a coded description of the Series and must contain "
                "exactly one code sequence item when present. "
                "Solution: Reduce sequence to contain only one code sequence item."
            )

        # Validate Series Description Code Sequence items if present
        for i, item in enumerate(series_desc_seq):
            RTSeriesValidator._validate_code_sequence_item(item, f"Series Description Code Sequence item {i}", result)

        # Operator Identification Sequence (0008,1072) - Type 3
        operator_seq = getattr(dataset, 'OperatorIdentificationSequence', [])
        for i, item in enumerate(operator_seq):
            RTSeriesValidator._validate_operator_identification_item(item, i, result)

        # Referenced Performed Procedure Step Sequence (0008,1111) - Type 3
        ref_pps_seq = getattr(dataset, 'ReferencedPerformedProcedureStepSequence', [])
        for i, item in enumerate(ref_pps_seq):
            RTSeriesValidator._validate_sop_instance_reference_item(
                item, f"Referenced Performed Procedure Step Sequence item {i}", result
            )

        # Request Attributes Sequence (0040,0275) - Type 3
        request_seq = getattr(dataset, 'RequestAttributesSequence', [])
        for i, item in enumerate(request_seq):
            RTSeriesValidator._validate_request_attributes_item(item, i, result)

    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate conditional requirements and cross-field consistency per DICOM PS3.3 C.8.8.1.

        Validates operator identification sequence correspondence and other conditional logic.
        """
        # Operator Identification Sequence correspondence with Operators' Name
        operator_seq = getattr(dataset, 'OperatorIdentificationSequence', [])
        operator_name = getattr(dataset, 'OperatorsName', '')

        if operator_seq and operator_name:
            # Check that number and order correspond if both are present
            # pydicom automatically converts backslash-separated strings to MultiValue (list-like)
            from pydicom.valuerep import PersonName
            from pydicom.multival import MultiValue

            if isinstance(operator_name, MultiValue):
                # Handle MultiValue format (multiple operators)
                operator_names = list(operator_name)
            elif isinstance(operator_name, (str, PersonName)):
                # Handle single operator (string or PersonName)
                operator_names = [operator_name]
            else:
                # Handle other formats
                operator_names = [operator_name]

            if len(operator_seq) != len(operator_names):
                result.add_warning(
                    "Operator Identification Sequence (0008,1072) correspondence issue: "
                    f"Number of sequence items ({len(operator_seq)}) should correspond to "
                    f"number of Operators' Name values ({len(operator_names)}). "
                    "If more than one Item, the number and order shall correspond to the "
                    "Value of Operators' Name (0008,1070), if present. "
                    "See DICOM PS3.3 C.8.8.1 for operator identification requirements."
                )

    @staticmethod
    def _validate_code_sequence_item(item: Dataset, context: str, result: ValidationResult) -> None:
        """Validate Code Sequence Macro attributes in sequence items."""
        # Code Value (0008,0100) - Type 1 in Code Sequence Macro
        if not item.get('CodeValue'):
            result.add_error(f"{context}: Code Value (0008,0100) is required in Code Sequence Macro")

        # Coding Scheme Designator (0008,0102) - Type 1 in Code Sequence Macro
        if not item.get('CodingSchemeDesignator'):
            result.add_error(f"{context}: Coding Scheme Designator (0008,0102) is required in Code Sequence Macro")

        # Code Meaning (0008,0104) - Type 1 in Code Sequence Macro
        if not item.get('CodeMeaning'):
            result.add_error(f"{context}: Code Meaning (0008,0104) is required in Code Sequence Macro")

    @staticmethod
    def _validate_operator_identification_item(item: Dataset, index: int, result: ValidationResult) -> None:
        """Validate Person Identification Macro attributes in Operator Identification Sequence items."""
        context = f"Operator Identification Sequence item {index}"

        # Person Identification Code Sequence (0040,1101) - Type 2 in Person Identification Macro
        if not hasattr(item, 'PersonIdentificationCodeSequence'):
            result.add_warning(
                f"{context}: Person Identification Code Sequence (0040,1101) is recommended "
                "(Type 2 in Person Identification Macro)"
            )

    @staticmethod
    def _validate_sop_instance_reference_item(item: Dataset, context: str, result: ValidationResult) -> None:
        """Validate SOP Instance Reference Macro attributes in sequence items."""
        # Referenced SOP Class UID (0008,1150) - Type 1 in SOP Instance Reference Macro
        if not item.get('ReferencedSOPClassUID'):
            result.add_error(f"{context}: Referenced SOP Class UID (0008,1150) is required in SOP Instance Reference Macro")

        # Referenced SOP Instance UID (0008,1155) - Type 1 in SOP Instance Reference Macro
        if not item.get('ReferencedSOPInstanceUID'):
            result.add_error(f"{context}: Referenced SOP Instance UID (0008,1155) is required in SOP Instance Reference Macro")

    @staticmethod
    def _validate_request_attributes_item(item: Dataset, index: int, result: ValidationResult) -> None:
        """Validate Request Attributes Macro attributes in Request Attributes Sequence items."""
        context = f"Request Attributes Sequence item {index}"

        # Requested Procedure ID (0040,1001) - Type 1 in Request Attributes Macro
        if not item.get('RequestedProcedureID'):
            result.add_error(f"{context}: Requested Procedure ID (0040,1001) is required in Request Attributes Macro")

        # Scheduled Procedure Step ID (0040,0009) - Type 1 in Request Attributes Macro
        if not item.get('ScheduledProcedureStepID'):
            result.add_error(f"{context}: Scheduled Procedure Step ID (0040,0009) is required in Request Attributes Macro")
