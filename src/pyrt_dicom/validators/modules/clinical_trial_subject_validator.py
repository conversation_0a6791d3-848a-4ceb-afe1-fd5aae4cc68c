"""Clinical Trial Subject Module DICOM validation - PS3.3 C.7.1.3

This validator ensures complete compliance with DICOM PS3.3 C.7.1.3 Clinical Trial Subject Module
requirements, including all Type 1, Type 2, Type 1C conditional requirements, and sequence validation.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class ClinicalTrialSubjectValidator(BaseValidator):
    """Independent validator that works on pydicom Dataset OR BaseModule instances.
    
    Validates all requirements for clinical trial subject identification including:
    - Type 1 required elements (sponsor name, protocol ID)
    - Type 2 required but can be empty elements (protocol name, site ID/name)
    - Type 1C conditional requirements (subject identification, ethics committee)
    - Type 3 optional elements validation
    - Sequence structure validation for Other Clinical Trial Protocol IDs

    Provides comprehensive error messages with DICOM tag references and guidance
    for resolving validation issues.
    """
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 required elements per DICOM PS3.3 C.7.1.3.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Clinical Trial Sponsor Name (0012,0010) Type 1
        if 'ClinicalTrialSponsorName' not in data or not getattr(data, 'ClinicalTrialSponsorName', '').strip():
            result.add_error(
                "Clinical Trial Sponsor Name (0012,0010) is required (Type 1) per DICOM PS3.3 C.7.1.3. "
                "This identifies the entity responsible for conducting the clinical trial. "
                "Provide a non-empty string value for the sponsor organization name."
            )

        # Clinical Trial Protocol ID (0012,0020) Type 1
        if 'ClinicalTrialProtocolID' not in data or not getattr(data, 'ClinicalTrialProtocolID', '').strip():
            result.add_error(
                "Clinical Trial Protocol ID (0012,0020) is required (Type 1) per DICOM PS3.3 C.7.1.3. "
                "This uniquely identifies the investigational protocol. "
                "Provide a non-empty string value for the protocol identifier."
            )

        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements per DICOM PS3.3 C.7.1.3.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Type 1C: Clinical Trial Subject ID (0012,0040) or Clinical Trial Subject Reading ID (0012,0042) required
        # DICOM PS3.3 C.7.1.3: "Shall be present if Clinical Trial Subject Reading ID (0012,0042) is absent. May be present otherwise."
        # DICOM PS3.3 C.7.1.3: "Shall be present if Clinical Trial Subject ID (0012,0040) is absent. May be present otherwise."
        has_subject_id = 'ClinicalTrialSubjectID' in data and getattr(data, 'ClinicalTrialSubjectID', '').strip()
        has_reading_id = 'ClinicalTrialSubjectReadingID' in data and getattr(data, 'ClinicalTrialSubjectReadingID', '').strip()

        if not has_subject_id and not has_reading_id:
            result.add_error(
                "Either Clinical Trial Subject ID (0012,0040) or Clinical Trial Subject Reading ID (0012,0042) "
                "is required (Type 1C) per DICOM PS3.3 C.7.1.3. At least one must be present to identify the subject. "
                "Use Clinical Trial Subject ID for standard identification or Clinical Trial Subject Reading ID "
                "for blinded evaluations. Both may be present if needed."
            )

        # Type 1C: Ethics Committee Name (0012,0081) required if Ethics Committee Approval Number (0012,0082) is present
        # DICOM PS3.3 C.7.1.3: "Required if Clinical Trial Protocol Ethics Committee Approval Number (0012,0082) is present."
        has_approval_number = ('ClinicalTrialProtocolEthicsCommitteeApprovalNumber' in data and
                              getattr(data, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber', '').strip())
        has_committee_name = ('ClinicalTrialProtocolEthicsCommitteeName' in data and
                             getattr(data, 'ClinicalTrialProtocolEthicsCommitteeName', '').strip())

        if has_approval_number and not has_committee_name:
            result.add_error(
                "Clinical Trial Protocol Ethics Committee Name (0012,0081) is required (Type 1C) when "
                "Clinical Trial Protocol Ethics Committee Approval Number (0012,0082) is present "
                "per DICOM PS3.3 C.7.1.3. Provide the name of the Ethics Committee, IRB, or IACUC "
                "that issued the approval number."
            )

        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints per DICOM PS3.3 C.7.1.3.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # Validate LO (Long String) VR constraints for all string attributes
        lo_attributes = [
            ('ClinicalTrialSponsorName', '0012,0010'),
            ('ClinicalTrialProtocolID', '0012,0020'),
            ('ClinicalTrialProtocolName', '0012,0021'),
            ('IssuerOfClinicalTrialProtocolID', '0012,0022'),
            ('ClinicalTrialSiteID', '0012,0030'),
            ('ClinicalTrialSiteName', '0012,0031'),
            ('IssuerOfClinicalTrialSiteID', '0012,0032'),
            ('ClinicalTrialSubjectID', '0012,0040'),
            ('IssuerOfClinicalTrialSubjectID', '0012,0041'),
            ('ClinicalTrialSubjectReadingID', '0012,0042'),
            ('IssuerOfClinicalTrialSubjectReadingID', '0012,0043'),
            ('ClinicalTrialProtocolEthicsCommitteeName', '0012,0081'),
            ('ClinicalTrialProtocolEthicsCommitteeApprovalNumber', '0012,0082')
        ]

        for attr_name, tag in lo_attributes:
            if attr_name in data:
                value = getattr(data, attr_name, '')
                if isinstance(value, str) and len(value) > 64:
                    result.add_warning(
                        f"{attr_name} ({tag}) value exceeds 64 characters (LO VR constraint). "
                        f"Current length: {len(value)}. Consider shortening the value for DICOM compliance."
                    )

        # Validate semantic consistency: warn if both subject ID and reading ID are present
        # (allowed but may indicate confusion about intended use)
        if ('ClinicalTrialSubjectID' in data and
            'ClinicalTrialSubjectReadingID' in data and
            getattr(data, 'ClinicalTrialSubjectID', '').strip() and
            getattr(data, 'ClinicalTrialSubjectReadingID', '').strip()):
            result.add_warning(
                "Both Clinical Trial Subject ID (0012,0040) and Clinical Trial Subject Reading ID (0012,0042) "
                "are present. While allowed by DICOM PS3.3 C.7.1.3, ensure this is intentional. "
                "Use Subject ID for standard identification and Reading ID for blinded evaluations."
            )

        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements per DICOM PS3.3 C.7.1.3.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Other Clinical Trial Protocol IDs Sequence (0012,0023) validation
        if 'OtherClinicalTrialProtocolIDsSequence' in data:
            other_protocol_ids_seq = getattr(data, 'OtherClinicalTrialProtocolIDsSequence', [])

            # Check if it's iterable (handles pydicom.Sequence, list, tuple)
            if not hasattr(other_protocol_ids_seq, '__iter__'):
                result.add_error(
                    "Other Clinical Trial Protocol IDs Sequence (0012,0023) must be a sequence "
                    "of Dataset objects per DICOM PS3.3 C.7.1.3"
                )
                return result

            for i, item in enumerate(other_protocol_ids_seq):
                # Check that sequence item is a Dataset
                if not isinstance(item, Dataset):
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Sequence items must be pydicom Dataset objects. "
                        "Use ClinicalTrialSubjectModule.create_other_protocol_id_item() to create valid items."
                    )
                    continue

                # Clinical Trial Protocol ID (0012,0020) Type 1 within sequence
                if 'ClinicalTrialProtocolID' not in item or not getattr(item, 'ClinicalTrialProtocolID', '').strip():
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Clinical Trial Protocol ID (0012,0020) is required (Type 1) within sequence items "
                        "per DICOM PS3.3 C.7.1.3. Provide a non-empty protocol identifier."
                    )

                # Issuer of Clinical Trial Protocol ID (0012,0022) Type 1 within sequence
                if 'IssuerOfClinicalTrialProtocolID' not in item or not getattr(item, 'IssuerOfClinicalTrialProtocolID', '').strip():
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Issuer of Clinical Trial Protocol ID (0012,0022) is required (Type 1) within sequence items "
                        "per DICOM PS3.3 C.7.1.3. Provide the assigning authority for the protocol ID."
                    )

        return result
    
    @staticmethod
    def validate_type_2_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 2 (required but can be empty) elements per DICOM PS3.3 C.7.1.3.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing Type 2 elements
        """
        result = ValidationResult()

        # Clinical Trial Protocol Name (0012,0021) Type 2
        if 'ClinicalTrialProtocolName' not in data:
            result.add_error(
                "Clinical Trial Protocol Name (0012,0021) must be present (Type 2) per DICOM PS3.3 C.7.1.3. "
                "This contains the title of the investigational protocol. "
                "Add this attribute to the dataset (value may be empty string if unknown)."
            )

        # Clinical Trial Site ID (0012,0030) Type 2
        if 'ClinicalTrialSiteID' not in data:
            result.add_error(
                "Clinical Trial Site ID (0012,0030) must be present (Type 2) per DICOM PS3.3 C.7.1.3. "
                "This identifies the site responsible for submitting clinical trial data. "
                "Add this attribute to the dataset (value may be empty string if not applicable)."
            )

        # Clinical Trial Site Name (0012,0031) Type 2
        if 'ClinicalTrialSiteName' not in data:
            result.add_error(
                "Clinical Trial Site Name (0012,0031) must be present (Type 2) per DICOM PS3.3 C.7.1.3. "
                "This is the name of the site responsible for submitting clinical trial data. "
                "Add this attribute to the dataset (value may be empty string if not applicable)."
            )

        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(ClinicalTrialSubjectValidator.validate_required_elements(data))
        
        # Always validate Type 2 elements
        result.merge(ClinicalTrialSubjectValidator.validate_type_2_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(ClinicalTrialSubjectValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(ClinicalTrialSubjectValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(ClinicalTrialSubjectValidator.validate_sequence_structures(data))
        
        return result
