"""RT Image Module DICOM validation - PS3.3 C.8.8.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import (
    ConversionType, ReportedValuesOrigin, RTImagePlane, PrimaryDosimeterUnit,
    PixelIntensityRelationshipSign, RTImageTypeValue3, RTBeamLimitingDeviceType,
    BlockType, BlockDivergence, FluenceDataSource, FluenceMode,
    EnhancedRTBeamLimitingDeviceDefinitionFlag
)
from ...enums.image_enums import PhotometricInterpretation, PixelRepresentation
from ...enums.series_enums import PatientPosition


class RTImageValidator(BaseValidator):
    """Validator for DICOM RT Image Module (PS3.3 C.8.8.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Image Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate required Type 1 elements
        RTImageValidator._validate_required_elements(dataset, result)
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            RTImageValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            RTImageValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            RTImageValidator._validate_sequence_requirements(dataset, result)
        
        # Validate pixel data consistency and RT Image specific rules
        RTImageValidator._validate_pixel_data_consistency(dataset, result)
        
        # Validate RT Image specific semantic rules
        RTImageValidator._validate_rt_image_semantics(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 1C: Pixel Intensity Relationship Sign required if Pixel Intensity Relationship is present
        has_pixel_intensity_relationship = hasattr(dataset, 'PixelIntensityRelationship')
        if has_pixel_intensity_relationship and not hasattr(dataset, 'PixelIntensityRelationshipSign'):
            result.add_error(
                "Pixel Intensity Relationship Sign (0028,1041) is required when "
                "Pixel Intensity Relationship (0028,1040) is present"
            )
        
        # Type 2C: Reported Values Origin required if Image Type Value 3 is SIMULATOR or PORTAL
        image_type = getattr(dataset, 'ImageType', [])
        image_type_value3 = image_type[2] if len(image_type) > 2 else ''
        
        if image_type_value3 in ['SIMULATOR', 'PORTAL']:
            if not hasattr(dataset, 'ReportedValuesOrigin'):
                result.add_error(
                    f"Reported Values Origin (3002,000A) is required when "
                    f"Image Type Value 3 is {image_type_value3}"
                )
        
        # Type 2C: RT Image Orientation required if RT Image Plane is NON_NORMAL
        rt_image_plane = getattr(dataset, 'RTImagePlane', '')
        if rt_image_plane == "NON_NORMAL" and not hasattr(dataset, 'RTImageOrientation'):
            result.add_error(
                "RT Image Orientation (3002,0010) is required when "
                "RT Image Plane (3002,000C) is NON_NORMAL"
            )
        
        # Type 1C: Patient Position required if Isocenter Position is present
        has_isocenter_position = hasattr(dataset, 'IsocenterPosition')
        if has_isocenter_position and not hasattr(dataset, 'PatientPosition'):
            result.add_error(
                "Patient Position (0018,5100) is required when "
                "Isocenter Position (300A,012C) is present"
            )
        
        # Type 1C: Fluence Map Sequence required if Image Type Value 3 is FLUENCE
        if image_type_value3 == 'FLUENCE' and not hasattr(dataset, 'FluenceMapSequence'):
            result.add_error(
                "Fluence Map Sequence (3002,0040) is required when "
                "Image Type Value 3 is FLUENCE"
            )
        
        # Validate exposure sequence conditional requirements
        exposure_seq = getattr(dataset, 'ExposureSequence', [])
        for i, exposure_item in enumerate(exposure_seq):
            # Type 2C: KVP required if Image Type Value 3 is PORTAL, SIMULATOR or RADIOGRAPH
            if image_type_value3 in ['PORTAL', 'SIMULATOR', 'RADIOGRAPH']:
                if not exposure_item.get('KVP'):
                    result.add_error(
                        f"Exposure Sequence item {i}: KVP (0018,0060) is required when "
                        f"Image Type Value 3 is {image_type_value3}"
                    )
            
            # Type 2C: X-Ray Tube Current required if Image Type Value 3 is SIMULATOR or RADIOGRAPH
            if image_type_value3 in ['SIMULATOR', 'RADIOGRAPH']:
                if not exposure_item.get('XRayTubeCurrent'):
                    result.add_error(
                        f"Exposure Sequence item {i}: X-Ray Tube Current (0018,1151) is required when "
                        f"Image Type Value 3 is {image_type_value3}"
                    )
            
            # Type 2C: Exposure Time required if Image Type Value 3 is SIMULATOR or RADIOGRAPH
            if image_type_value3 in ['SIMULATOR', 'RADIOGRAPH']:
                if not exposure_item.get('ExposureTime'):
                    result.add_error(
                        f"Exposure Sequence item {i}: Exposure Time (0018,1150) is required when "
                        f"Image Type Value 3 is {image_type_value3}"
                    )
            
            # Type 2C: Meterset Exposure required if Image Type Value 3 is PORTAL
            if image_type_value3 == 'PORTAL':
                if not exposure_item.get('MetersetExposure'):
                    result.add_error(
                        f"Exposure Sequence item {i}: Meterset Exposure (3002,0032) is required when "
                        f"Image Type Value 3 is PORTAL"
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Note: Photometric Interpretation validation moved to main enum validation section
        
        # Samples per Pixel (0028,0002) - should be 1 for RT Image
        samples_per_pixel = getattr(dataset, 'SamplesPerPixel', None)
        if samples_per_pixel is not None and samples_per_pixel != 1:
            result.add_warning(
                "Samples per Pixel (0028,0002) should be 1 for RT Image"
            )
        
        # Bits Allocated (0028,0100) - should be 8 or 16 for RT Image
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        if bits_allocated is not None and bits_allocated not in [8, 16]:
            result.add_warning(
                "Bits Allocated (0028,0100) should be 8 or 16 for RT Image"
            )
        
        # Conversion Type (0008,0064)
        conversion_type = getattr(dataset, 'ConversionType', '')
        if conversion_type:
            valid_types = [ctype.value for ctype in ConversionType]
            BaseValidator.validate_enumerated_value(
                conversion_type, valid_types,
                "Conversion Type (0008,0064)", result
            )
        
        # Reported Values Origin (3002,000A)
        reported_values_origin = getattr(dataset, 'ReportedValuesOrigin', '')
        if reported_values_origin:
            valid_origins = [origin.value for origin in ReportedValuesOrigin]
            BaseValidator.validate_enumerated_value(
                reported_values_origin, valid_origins,
                "Reported Values Origin (3002,000A)", result
            )
        
        # RT Image Plane (3002,000C)
        rt_image_plane = getattr(dataset, 'RTImagePlane', '')
        if rt_image_plane:
            valid_planes = [plane.value for plane in RTImagePlane]
            BaseValidator.validate_enumerated_value(
                rt_image_plane, valid_planes,
                "RT Image Plane (3002,000C)", result
            )
        
        # Primary Dosimeter Unit (300A,00B3)
        primary_dosimeter_unit = getattr(dataset, 'PrimaryDosimeterUnit', '')
        if primary_dosimeter_unit:
            valid_units = [unit.value for unit in PrimaryDosimeterUnit]
            BaseValidator.validate_enumerated_value(
                primary_dosimeter_unit, valid_units,
                "Primary Dosimeter Unit (300A,00B3)", result
            )
        
        # Enhanced RT Beam Limiting Device Definition Flag (3008,00A3)
        enhanced_flag = getattr(dataset, 'EnhancedRTBeamLimitingDeviceDefinitionFlag', '')
        if enhanced_flag:
            valid_flags = [flag.value for flag in EnhancedRTBeamLimitingDeviceDefinitionFlag]
            BaseValidator.validate_enumerated_value(
                enhanced_flag, valid_flags,
                "Enhanced RT Beam Limiting Device Definition Flag (3008,00A3)", result
            )
        
        # Validate Image Type Value 3
        image_type = getattr(dataset, 'ImageType', [])
        if len(image_type) > 2:
            image_type_value3 = image_type[2]
            valid_image_types = [itype.value for itype in RTImageTypeValue3]
            BaseValidator.validate_enumerated_value(
                image_type_value3, valid_image_types,
                "Image Type Value 3 (0008,0008)", result
            )
        
        # Pixel Intensity Relationship Sign (0028,1041)
        pixel_intensity_sign = getattr(dataset, 'PixelIntensityRelationshipSign', '')
        if pixel_intensity_sign:
            valid_signs = [str(sign.value) for sign in PixelIntensityRelationshipSign]
            BaseValidator.validate_enumerated_value(
                pixel_intensity_sign, valid_signs,
                "Pixel Intensity Relationship Sign (0028,1041)", result
            )
        
        # Photometric Interpretation (0028,0004) - validate against enum instead of hardcoding
        photometric_interp = getattr(dataset, 'PhotometricInterpretation', '')
        if photometric_interp:
            valid_interpretations = [interp.value for interp in PhotometricInterpretation]
            BaseValidator.validate_enumerated_value(
                photometric_interp, valid_interpretations,
                "Photometric Interpretation (0028,0004)", result
            )
        
        # Pixel Representation (0028,0103)
        pixel_representation = getattr(dataset, 'PixelRepresentation', '')
        if pixel_representation:
            valid_representations = [str(rep.value) for rep in PixelRepresentation]
            BaseValidator.validate_enumerated_value(
                pixel_representation, valid_representations,
                "Pixel Representation (0028,0103)", result
            )
        
        # Patient Position (0018,5100)
        patient_position = getattr(dataset, 'PatientPosition', '')
        if patient_position:
            valid_positions = [pos.value for pos in PatientPosition]
            BaseValidator.validate_enumerated_value(
                patient_position, valid_positions,
                "Patient Position (0018,5100)", result
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Referenced RT Plan Sequence validation
        ref_rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
        for i, plan_item in enumerate(ref_rt_plan_seq):
            if not plan_item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not plan_item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Exposure Sequence validation
        exposure_seq = getattr(dataset, 'ExposureSequence', [])
        for i, exposure_item in enumerate(exposure_seq):
            # Validate beam limiting device sequence if present
            beam_limiting_seq = exposure_item.get('BeamLimitingDeviceSequence', [])
            for j, device_item in enumerate(beam_limiting_seq):
                rt_beam_limiting_device_type = device_item.get('RTBeamLimitingDeviceType')
                if not rt_beam_limiting_device_type:
                    result.add_error(
                        f"Beam Limiting Device Sequence item {j} in Exposure {i}: "
                        "RT Beam Limiting Device Type (300A,00B8) is required"
                    )
                else:
                    # Validate enumerated value
                    valid_device_types = [dtype.value for dtype in RTBeamLimitingDeviceType]
                    BaseValidator.validate_enumerated_value(
                        rt_beam_limiting_device_type, valid_device_types,
                        f"RT Beam Limiting Device Type in Beam Limiting Device Sequence item {j} in Exposure {i} (300A,00B8)", result
                    )
                if not device_item.get('NumberOfLeafJawPairs'):
                    result.add_error(
                        f"Beam Limiting Device Sequence item {j} in Exposure {i}: "
                        "Number of Leaf/Jaw Pairs (300A,00BC) is required"
                    )
            
            # Validate block sequence if present
            block_seq = exposure_item.get('BlockSequence', [])
            number_of_blocks = exposure_item.get('NumberOfBlocks', 0)
            
            if number_of_blocks > 0 and not block_seq:
                result.add_error(
                    f"Exposure Sequence item {i}: "
                    "Block Sequence (300A,00F4) is required when Number of Blocks is non-zero"
                )
            
            for j, block_item in enumerate(block_seq):
                required_block_fields = [
                    ('BlockNumber', '300A,00FC'),
                    ('SourceToBlockTrayDistance', '300A,00F6'),
                    ('MaterialID', '300A,00E1'),
                    ('BlockNumberOfPoints', '300A,0104'),
                    ('BlockData', '300A,0106')
                ]
                
                for field_name, tag in required_block_fields:
                    if not block_item.get(field_name):
                        result.add_error(
                            f"Block Sequence item {j} in Exposure {i}: "
                            f"{field_name} ({tag}) is required"
                        )
                
                # Validate Block Type enumerated value
                block_type = block_item.get('BlockType')
                if not block_type:
                    result.add_error(
                        f"Block Sequence item {j} in Exposure {i}: "
                        "Block Type (300A,00F8) is required"
                    )
                else:
                    valid_block_types = [btype.value for btype in BlockType]
                    BaseValidator.validate_enumerated_value(
                        block_type, valid_block_types,
                        f"Block Type in Block Sequence item {j} in Exposure {i} (300A,00F8)", result
                    )
                
                # Validate Block Divergence enumerated value
                block_divergence = block_item.get('BlockDivergence')
                if not block_divergence:
                    result.add_error(
                        f"Block Sequence item {j} in Exposure {i}: "
                        "Block Divergence (300A,00FA) is required"
                    )
                else:
                    valid_block_divergences = [bdiv.value for bdiv in BlockDivergence]
                    BaseValidator.validate_enumerated_value(
                        block_divergence, valid_block_divergences,
                        f"Block Divergence in Block Sequence item {j} in Exposure {i} (300A,00FA)", result
                    )
        
        # Fluence Map Sequence validation
        fluence_seq = getattr(dataset, 'FluenceMapSequence', [])
        for i, fluence_item in enumerate(fluence_seq):
            fluence_data_source = fluence_item.get('FluenceDataSource')
            if not fluence_data_source:
                result.add_error(
                    f"Fluence Map Sequence item {i}: "
                    "Fluence Data Source (3002,0041) is required"
                )
            else:
                # Validate enumerated value
                valid_data_sources = [source.value for source in FluenceDataSource]
                BaseValidator.validate_enumerated_value(
                    fluence_data_source, valid_data_sources,
                    f"Fluence Data Source in Fluence Map Sequence item {i} (3002,0041)", result
                )
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel data consistency and logical constraints."""
        
        # Validate bits stored/high bit consistency for RT Images
        bits_stored = getattr(dataset, 'BitsStored', None)
        high_bit = getattr(dataset, 'HighBit', None)
        
        if bits_stored is not None and high_bit is not None:
            if high_bit != bits_stored - 1:
                result.add_warning(
                    "For RT Images, High Bit (0028,0102) should be one less than Bits Stored (0028,0101)"
                )
        
        # Note: Pixel Representation validation moved to main enum validation section
        # RT Image specific validation for bits allocated and pixel representation relationship
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        pixel_representation = getattr(dataset, 'PixelRepresentation', None)
        
        if bits_allocated == 8 and pixel_representation and pixel_representation != 0:
            result.add_warning(
                "Pixel Representation should be 0 (unsigned) when Bits Allocated is 8 for RT Images"
            )
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate presence of required Type 1 elements."""
        
        # Type 1 elements (REQUIRED)
        required_type1_elements = [
            ('SamplesPerPixel', '0028,0002', 'Samples per Pixel'),
            ('PhotometricInterpretation', '0028,0004', 'Photometric Interpretation'),
            ('BitsAllocated', '0028,0100', 'Bits Allocated'),
            ('BitsStored', '0028,0101', 'Bits Stored'),
            ('HighBit', '0028,0102', 'High Bit'),
            ('PixelRepresentation', '0028,0103', 'Pixel Representation'),
            ('RTImageLabel', '3002,0002', 'RT Image Label'),
            ('ImageType', '0008,0008', 'Image Type'),
            ('RTImagePlane', '3002,000C', 'RT Image Plane')
        ]
        
        for attr_name, tag, description in required_type1_elements:
            if not hasattr(dataset, attr_name):
                result.add_error(
                    f"{description} ({tag}) is required for RT Image Module (Type 1 element)"
                )
    
    @staticmethod
    def _validate_rt_image_semantics(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT Image specific semantic rules and constraints."""
        
        # Validate RT Image SID and SAD relationship
        rt_image_sid = getattr(dataset, 'RTImageSID', None)
        radiation_machine_sad = getattr(dataset, 'RadiationMachineSAD', None)
        x_ray_receptor_translation = getattr(dataset, 'XRayImageReceptorTranslation', None)
        
        if rt_image_sid and radiation_machine_sad and x_ray_receptor_translation:
            # The Z coordinate of X-Ray Image Receptor Translation should equal SAD minus SID
            expected_z = radiation_machine_sad - rt_image_sid
            if len(x_ray_receptor_translation) >= 3:
                actual_z = x_ray_receptor_translation[2]
                if abs(actual_z - expected_z) > 0.1:  # Allow small floating point differences
                    result.add_warning(
                        f"X-Ray Image Receptor Translation Z coordinate ({actual_z}) should equal "
                        f"Radiation Machine SAD ({radiation_machine_sad}) minus RT Image SID ({rt_image_sid}) = {expected_z}. "
                        f"See DICOM PS3.3 C.8.8.2 Note 2."
                    )
        
        # Validate Multi-frame specific requirements
        exposure_seq = getattr(dataset, 'ExposureSequence', [])
        if len(exposure_seq) > 1:
            # Check if any exposure item has Referenced Frame Number (required for multi-frame)
            for i, exposure_item in enumerate(exposure_seq):
                referenced_frame_number = exposure_item.get('ReferencedFrameNumber')
                if referenced_frame_number is None:
                    result.add_error(
                        f"Referenced Frame Number (0008,1160) is required in Exposure Sequence item {i} "
                        f"when there is more than one item in the sequence (Type 1C requirement)"
                    )
        
        # Validate Enhanced RT Beam Limiting Device Definition Flag requirements
        enhanced_flag = getattr(dataset, 'EnhancedRTBeamLimitingDeviceDefinitionFlag', '')
        enhanced_sequence = getattr(dataset, 'EnhancedRTBeamLimitingDeviceSequence', [])
        
        if enhanced_flag == 'YES' and not enhanced_sequence:
            result.add_error(
                "Enhanced RT Beam Limiting Device Sequence (3008,00A1) is required when "
                "Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is YES (Type 1C requirement)"
            )
        
        # Validate Image Type consistency
        image_type = getattr(dataset, 'ImageType', [])
        if len(image_type) >= 3:
            image_type_value3 = image_type[2]
            
            # Check for DRR specific requirements
            if image_type_value3 == 'DRR':
                if not rt_image_sid or not radiation_machine_sad:
                    result.add_warning(
                        "RT Image SID (3002,0026) and Radiation Machine SAD (3002,0022) should be present "
                        "for DRR images to define the image plane distance"
                    )
            
            # Check for FLUENCE specific requirements
            if image_type_value3 == 'FLUENCE':
                fluence_seq = getattr(dataset, 'FluenceMapSequence', [])
                if fluence_seq:
                    fluence_item = fluence_seq[0]
                    fluence_mode_seq = fluence_item.get('PrimaryFluenceModeSequence', [])
                    if fluence_mode_seq:
                        fluence_mode = fluence_mode_seq[0].get('FluenceMode', '')
                        fluence_mode_id = fluence_mode_seq[0].get('FluenceModeID', '')
                        
                        # Validate Fluence Mode enumerated value
                        if fluence_mode:
                            valid_fluence_modes = [mode.value for mode in FluenceMode]
                            BaseValidator.validate_enumerated_value(
                                fluence_mode, valid_fluence_modes,
                                "Fluence Mode (3002,0051)", result
                            )
                        
                        # Type 1C: Fluence Mode ID required if Fluence Mode is NON_STANDARD
                        if fluence_mode == 'NON_STANDARD' and not fluence_mode_id:
                            result.add_error(
                                "Fluence Mode ID (3002,0052) is required when "
                                "Fluence Mode (3002,0051) is NON_STANDARD (Type 1C requirement)"
                            )
        
        # Validate Applicator Sequence requirements
        exposure_seq = getattr(dataset, 'ExposureSequence', [])
        for i, exposure_item in enumerate(exposure_seq):
            applicator_seq = exposure_item.get('ApplicatorSequence', [])
            for applicator_item in applicator_seq:
                applicator_geometry_seq = applicator_item.get('ApplicatorGeometrySequence', [])
                if applicator_geometry_seq:
                    geometry_item = applicator_geometry_seq[0]
                    aperture_shape = geometry_item.get('ApplicatorApertureShape', '')
                    
                    # Type 1C: Applicator Opening required for SYM_SQUARE or SYM_CIRCULAR
                    if aperture_shape in ['SYM_SQUARE', 'SYM_CIRCULAR']:
                        if not geometry_item.get('ApplicatorOpening'):
                            result.add_error(
                                f"Applicator Opening (300A,0433) is required when "
                                f"Applicator Aperture Shape is {aperture_shape} (Type 1C requirement)"
                            )
                    
                    # Type 1C: Applicator Opening X/Y required for SYM_RECTANGLE
                    if aperture_shape == 'SYM_RECTANGLE':
                        if not geometry_item.get('ApplicatorOpeningX'):
                            result.add_error(
                                "Applicator Opening X (300A,0434) is required when "
                                "Applicator Aperture Shape is SYM_RECTANGLE (Type 1C requirement)"
                            )
                        if not geometry_item.get('ApplicatorOpeningY'):
                            result.add_error(
                                "Applicator Opening Y (300A,0435) is required when "
                                "Applicator Aperture Shape is SYM_RECTANGLE (Type 1C requirement)"
                            )
        
        # Validate Block Sequence requirements  
        for i, exposure_item in enumerate(exposure_seq):
            number_of_blocks = exposure_item.get('NumberOfBlocks', 0)
            block_seq = exposure_item.get('BlockSequence', [])
            
            # Validate block count consistency
            if number_of_blocks != len(block_seq):
                result.add_warning(
                    f"Exposure Sequence item {i}: Number of Blocks ({number_of_blocks}) "
                    f"does not match actual Block Sequence length ({len(block_seq)})"
                )
        
        # Validate coordinate system consistency
        patient_position = getattr(dataset, 'PatientPosition', '')
        isocenter_position = getattr(dataset, 'IsocenterPosition', None)
        
        if patient_position and isocenter_position and len(isocenter_position) == 3:
            # Provide guidance on coordinate system understanding
            # Just validation - no informational messages needed for coordinate system
            pass
