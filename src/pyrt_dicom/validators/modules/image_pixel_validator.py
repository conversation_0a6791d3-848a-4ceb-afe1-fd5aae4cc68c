"""Image Pixel Module DICOM validation - PS3.3 C.7.6.3

Validates the Image Pixel Module, Image Pixel Macro, and Image Pixel Description Macro
according to DICOM PS3.3 C.7.6.3. Implements comprehensive validation of all Type 1,
Type 1C, Type 2C conditional requirements, enumerated values, and complex pixel data
consistency rules.

Key Validation Areas:
- Type 1 required elements validation
- Complex Type 1C conditional requirements (pixel data, planar configuration, palette color, etc.)
- Photometric interpretation constraints on samples per pixel
- Bit allocation and representation consistency
- YBR format specific constraints (422, 420, ICT, RCT)
- Palette color lookup table consistency
- Extended offset table conditional requirements
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import PhotometricInterpretation, PlanarConfiguration, PixelRepresentation


class ImagePixelValidator(BaseValidator):
    """Validator for DICOM Image Pixel Module (PS3.3 C.7.6.3).

    Provides comprehensive validation of the Image Pixel Module including:
    - All Type 1 required elements from Image Pixel Description Macro
    - Complex Type 1C conditional requirements with detailed error messages
    - Photometric interpretation constraints and consistency validation
    - Bit allocation and representation validation with cross-field checks
    - YBR format specific validation (422, 420, ICT, RCT constraints)
    - Palette color lookup table validation and consistency checks
    - Extended offset table conditional validation
    - Pixel data consistency and format validation
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Image Pixel Module requirements against DICOM PS3.3 C.7.6.3.

        Performs comprehensive validation of all Image Pixel Module requirements including
        Type 1 elements, complex Type 1C conditional logic, enumerated values, and
        pixel data consistency according to the DICOM standard.

        Args:
            dataset: pydicom Dataset to validate against Image Pixel Module requirements
            config: Optional validation configuration to control validation scope

        Returns:
            ValidationResult: Structured result with specific error and warning messages
                including DICOM tag references and actionable guidance for resolution
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 requirements (always performed)
        ImagePixelValidator._validate_type1_requirements(dataset, result)

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            ImagePixelValidator._validate_conditional_requirements(dataset, result)
            # Additional conditional validations moved from module
            ImagePixelValidator._validate_pixel_data_mutual_exclusivity(dataset, result)
            ImagePixelValidator._validate_planar_configuration_conditional(dataset, result)
            ImagePixelValidator._validate_palette_color_conditional(dataset, result)
            ImagePixelValidator._validate_palette_descriptor_format(dataset, result)

        # Validate enumerated values against DICOM standard
        if config.check_enumerated_values:
            ImagePixelValidator._validate_enumerated_values(dataset, result)

        # Validate pixel data consistency and format constraints
        ImagePixelValidator._validate_pixel_data_consistency(dataset, result)

        # Validate photometric interpretation specific constraints
        ImagePixelValidator._validate_photometric_constraints(dataset, result)

        # Validate YBR format specific requirements
        ImagePixelValidator._validate_ybr_format_constraints(dataset, result)

        return result
    
    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) attributes from Image Pixel Description Macro.

        According to DICOM PS3.3 C.*******, all these attributes are Type 1 (required).
        """

        required_attrs = [
            ('SamplesPerPixel', '0028,0002', 'Number of samples (planes) in this image'),
            ('PhotometricInterpretation', '0028,0004', 'Intended interpretation of pixel data'),
            ('Rows', '0028,0010', 'Number of rows in the image'),
            ('Columns', '0028,0011', 'Number of columns in the image'),
            ('BitsAllocated', '0028,0100', 'Number of bits allocated for each pixel sample'),
            ('BitsStored', '0028,0101', 'Number of bits stored for each pixel sample'),
            ('HighBit', '0028,0102', 'Most significant bit for pixel sample data'),
            ('PixelRepresentation', '0028,0103', 'Data representation of pixel samples')
        ]

        for attr_name, tag, description in required_attrs:
            if not hasattr(dataset, attr_name):
                result.add_error(
                    f"Missing required attribute {attr_name} ({tag}): {description}. "
                    f"This is a Type 1 element according to DICOM PS3.3 C.******* "
                    f"Image Pixel Description Macro."
                )

        # Validate specific Type 1 value constraints with detailed error messages
        ImagePixelValidator._validate_samples_per_pixel(dataset, result)
        ImagePixelValidator._validate_image_dimensions(dataset, result)
        ImagePixelValidator._validate_bit_allocation(dataset, result)
        ImagePixelValidator._validate_bit_relationships(dataset, result)

    @staticmethod
    def _validate_samples_per_pixel(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Samples per Pixel constraints."""
        if hasattr(dataset, 'SamplesPerPixel'):
            if not isinstance(dataset.SamplesPerPixel, int) or dataset.SamplesPerPixel < 1:
                result.add_error(
                    f"Samples per Pixel (0028,0002) must be a positive integer, got "
                    f"'{dataset.SamplesPerPixel}'. Valid values are 1 for monochrome/palette "
                    f"images or 3 for RGB/YBR color images."
                )

    @staticmethod
    def _validate_image_dimensions(dataset: Dataset, result: ValidationResult) -> None:
        """Validate image dimension constraints."""
        if hasattr(dataset, 'Rows'):
            if not isinstance(dataset.Rows, int) or dataset.Rows < 1:
                result.add_error(
                    f"Rows (0028,0010) must be a positive integer, got '{dataset.Rows}'. "
                    f"This represents the number of rows in the image matrix."
                )

        if hasattr(dataset, 'Columns'):
            if not isinstance(dataset.Columns, int) or dataset.Columns < 1:
                result.add_error(
                    f"Columns (0028,0011) must be a positive integer, got '{dataset.Columns}'. "
                    f"This represents the number of columns in the image matrix."
                )

    @staticmethod
    def _validate_bit_allocation(dataset: Dataset, result: ValidationResult) -> None:
        """Validate bit allocation constraints."""
        if hasattr(dataset, 'BitsAllocated'):
            if not isinstance(dataset.BitsAllocated, int) or dataset.BitsAllocated < 1:
                result.add_error(
                    f"Bits Allocated (0028,0100) must be a positive integer, got "
                    f"'{dataset.BitsAllocated}'. This specifies bits allocated for each pixel sample."
                )
            elif dataset.BitsAllocated != 1 and dataset.BitsAllocated % 8 != 0:
                result.add_error(
                    f"Bits Allocated (0028,0100) must be 1 or a multiple of 8, got "
                    f"'{dataset.BitsAllocated}'. According to DICOM PS3.5, valid values "
                    f"are 1, 8, 16, 24, 32, etc."
                )

        if hasattr(dataset, 'BitsStored'):
            if not isinstance(dataset.BitsStored, int) or dataset.BitsStored < 1:
                result.add_error(
                    f"Bits Stored (0028,0101) must be a positive integer, got "
                    f"'{dataset.BitsStored}'. This specifies the number of bits actually "
                    f"used for each pixel sample."
                )

        if hasattr(dataset, 'HighBit'):
            if not isinstance(dataset.HighBit, int) or dataset.HighBit < 0:
                result.add_error(
                    f"High Bit (0028,0102) must be a non-negative integer, got "
                    f"'{dataset.HighBit}'. This specifies the most significant bit position."
                )

    @staticmethod
    def _validate_bit_relationships(dataset: Dataset, result: ValidationResult) -> None:
        """Validate relationships between bit-related attributes."""
        if all(hasattr(dataset, attr) for attr in ['BitsAllocated', 'BitsStored', 'HighBit']):
            if dataset.BitsStored > dataset.BitsAllocated:
                result.add_error(
                    f"Bits Stored (0028,0101) cannot exceed Bits Allocated (0028,0100). "
                    f"Got Bits Stored = {dataset.BitsStored}, Bits Allocated = {dataset.BitsAllocated}. "
                    f"Bits Stored represents the actual bits used, which cannot exceed the allocated space."
                )

            expected_high_bit = dataset.BitsStored - 1
            if dataset.HighBit != expected_high_bit:
                result.add_error(
                    f"High Bit (0028,0102) must equal Bits Stored (0028,0101) - 1. "
                    f"Expected {expected_high_bit}, got {dataset.HighBit}. "
                    f"According to DICOM PS3.3 C.*******, High Bit shall be one less than Bits Stored."
                )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements according to DICOM PS3.3 C.7.6.3."""

        # Validate pixel data requirements (Type 1C - exactly one required)
        ImagePixelValidator._validate_pixel_data_requirements(dataset, result)

        # Validate planar configuration requirements (Type 1C)
        ImagePixelValidator._validate_planar_configuration_requirements(dataset, result)

        # Validate palette color requirements (Type 1C)
        ImagePixelValidator._validate_palette_color_requirements(dataset, result)

        # Validate pixel aspect ratio requirements (Type 1C)
        ImagePixelValidator._validate_pixel_aspect_ratio_requirements(dataset, result)

        # Validate extended offset table requirements (Type 1C)
        ImagePixelValidator._validate_extended_offset_table_requirements(dataset, result)

        # Validate pixel padding range limit requirements (Type 1C)
        ImagePixelValidator._validate_pixel_padding_requirements(dataset, result)

    @staticmethod
    def _validate_pixel_data_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel data Type 1C requirements."""
        has_pixel_data = hasattr(dataset, 'PixelData')
        has_pixel_data_url = hasattr(dataset, 'PixelDataProviderURL')

        if not has_pixel_data and not has_pixel_data_url:
            result.add_error(
                "Missing required pixel data: Either Pixel Data (7FE0,0010) or "
                "Pixel Data Provider URL (0028,7FE0) is required. According to DICOM PS3.3 C.7.6.3, "
                "exactly one of these Type 1C elements must be present. Pixel Data contains the "
                "actual pixel samples, while Pixel Data Provider URL is used for JPIP transfer syntaxes."
            )
        elif has_pixel_data and has_pixel_data_url:
            result.add_error(
                "Both Pixel Data (7FE0,0010) and Pixel Data Provider URL (0028,7FE0) are present. "
                "According to DICOM PS3.3 C.7.6.3, these are mutually exclusive Type 1C elements. "
                "Only one of pixel_data or pixel_data_provider_url should be provided. "
                "DICOM PS3.3 C.7.6.3 requires exactly one of these Type 1C elements."
            )

    @staticmethod
    def _validate_planar_configuration_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate planar configuration Type 1C requirements."""
        if hasattr(dataset, 'SamplesPerPixel'):
            try:
                samples_per_pixel = int(dataset.SamplesPerPixel)
                if samples_per_pixel > 1:
                    if not hasattr(dataset, 'PlanarConfiguration'):
                        result.add_error(
                            f"Missing Planar Configuration (0028,0006): Required when Samples per Pixel "
                            f"(0028,0002) > 1. Current Samples per Pixel = {dataset.SamplesPerPixel}. "
                            f"According to DICOM PS3.3 C.*******, Planar Configuration specifies whether "
                            f"color pixel data are encoded color-by-pixel (0) or color-by-plane (1)."
                        )
                elif samples_per_pixel == 1 and hasattr(dataset, 'PlanarConfiguration'):
                    result.add_warning(
                        f"Planar Configuration (0028,0006) should not be present when Samples per Pixel "
                        f"(0028,0002) = 1. According to DICOM PS3.3 C.*******, this attribute shall be "
                        f"present if Samples per Pixel has a value greater than 1, and shall not be "
                        f"present otherwise."
                    )
            except (ValueError, TypeError):
                # Invalid SamplesPerPixel value - will be caught by Type 1 validation
                pass

    @staticmethod
    def _validate_palette_color_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate palette color lookup table Type 1C requirements."""
        if hasattr(dataset, 'PhotometricInterpretation') and dataset.PhotometricInterpretation == "PALETTE COLOR":
            required_palette_attrs = [
                ('RedPaletteColorLookupTableDescriptor', '0028,1101', 'Red palette format descriptor'),
                ('GreenPaletteColorLookupTableDescriptor', '0028,1102', 'Green palette format descriptor'),
                ('BluePaletteColorLookupTableDescriptor', '0028,1103', 'Blue palette format descriptor'),
                ('RedPaletteColorLookupTableData', '0028,1201', 'Red palette lookup table data'),
                ('GreenPaletteColorLookupTableData', '0028,1202', 'Green palette lookup table data'),
                ('BluePaletteColorLookupTableData', '0028,1203', 'Blue palette lookup table data')
            ]

            missing_attrs = []
            for attr_name, tag, _ in required_palette_attrs:
                if not hasattr(dataset, attr_name):
                    missing_attrs.append(f"{attr_name} ({tag})")

            if missing_attrs:
                result.add_error(
                    f"Missing required palette color attributes: {', '.join(missing_attrs)}. "
                    f"According to DICOM PS3.3 C.*******, all palette color lookup table "
                    f"attributes are Type 1C - required when Photometric Interpretation = PALETTE COLOR. "
                    f"These define the color mapping for single-sample palette color images."
                )

    @staticmethod
    def _validate_pixel_aspect_ratio_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel aspect ratio Type 1C requirements."""
        # Note: This is a complex conditional that depends on physical spacing attributes
        # from other modules. For now, we validate the format if present.
        if hasattr(dataset, 'PixelAspectRatio'):
            if not isinstance(dataset.PixelAspectRatio, (list, tuple)) or len(dataset.PixelAspectRatio) != 2:
                result.add_error(
                    f"Pixel Aspect Ratio (0028,0034) must be a sequence of exactly 2 values "
                    f"[vertical_size, horizontal_size], got '{dataset.PixelAspectRatio}'. "
                    f"According to DICOM PS3.3 C.*******.7, this represents the ratio of "
                    f"vertical to horizontal pixel size as integer values."
                )

    @staticmethod
    def _validate_pixel_data_mutual_exclusivity(dataset: Dataset, result: ValidationResult) -> None:
        """Validate mutual exclusivity of pixel data and provider URL."""
        has_pixel_data = hasattr(dataset, 'PixelData')
        has_pixel_data_url = hasattr(dataset, 'PixelDataProviderURL')

        if has_pixel_data and has_pixel_data_url:
            result.add_error(
                "Only one of pixel_data or pixel_data_provider_url should be provided. "
                "DICOM PS3.3 C.7.6.3 requires exactly one of these Type 1C elements."
            )

        if not has_pixel_data and not has_pixel_data_url:
            result.add_error(
                "Either pixel_data or pixel_data_provider_url must be provided. "
                "DICOM PS3.3 C.7.6.3 requires exactly one of these Type 1C elements."
            )

    @staticmethod
    def _validate_planar_configuration_conditional(dataset: Dataset, result: ValidationResult) -> None:
        """Validate planar configuration conditional requirements."""
        if hasattr(dataset, 'SamplesPerPixel') and hasattr(dataset, 'PlanarConfiguration'):
            if dataset.SamplesPerPixel == 1:
                result.add_error(
                    "Planar Configuration (0028,0006) should not be present when "
                    "Samples per Pixel (0028,0002) = 1. This is a Type 1C conditional requirement."
                )

    @staticmethod
    def _validate_palette_color_conditional(dataset: Dataset, result: ValidationResult) -> None:
        """Validate palette color conditional requirements."""
        if hasattr(dataset, 'PhotometricInterpretation'):
            photometric = dataset.PhotometricInterpretation
            
            # Check palette color descriptors
            palette_attrs = [
                'RedPaletteColorLookupTableDescriptor',
                'GreenPaletteColorLookupTableDescriptor', 
                'BluePaletteColorLookupTableDescriptor'
            ]
            
            has_palette_attrs = any(hasattr(dataset, attr) for attr in palette_attrs)
            
            if photometric != "PALETTE COLOR" and has_palette_attrs:
                result.add_error(
                    f"Palette Color Lookup Tables are only required when "
                    f"Photometric Interpretation = PALETTE COLOR, but got "
                    f"'{photometric}'"
                )

    @staticmethod  
    def _validate_palette_descriptor_format(dataset: Dataset, result: ValidationResult) -> None:
        """Validate palette descriptor format."""
        descriptor_attrs = [
            ('red_palette_descriptor', 'RedPaletteColorLookupTableDescriptor'),
            ('green_palette_descriptor', 'GreenPaletteColorLookupTableDescriptor'),
            ('blue_palette_descriptor', 'BluePaletteColorLookupTableDescriptor')
        ]
        
        for desc_name, attr_name in descriptor_attrs:
            if hasattr(dataset, attr_name):
                desc_value = getattr(dataset, attr_name)
                if not isinstance(desc_value, (list, tuple)) or len(desc_value) != 3:
                    result.add_error(
                        f"{desc_name} must be a list of exactly 3 integers "
                        f"[entries, first_value, bits_per_entry]"
                    )

    @staticmethod
    def _validate_extended_offset_table_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate extended offset table Type 1C requirements."""
        if hasattr(dataset, 'ExtendedOffsetTable') and not hasattr(dataset, 'ExtendedOffsetTableLengths'):
            result.add_error(
                "Missing Extended Offset Table Lengths (7FE0,0002): Required when "
                "Extended Offset Table (7FE0,0001) is present. According to DICOM PS3.3 C.7.6.3, "
                "Extended Offset Table Lengths is Type 1C - required if Extended Offset Table "
                "is present. These provide byte lengths of frames in encapsulated pixel data."
            )

        # Validate Extended Offset Table constraints
        if hasattr(dataset, 'ExtendedOffsetTable'):
            # According to DICOM PS3.3 C.*******.8, Extended Offset Table may only be present when:
            # - Pixel Data is present, and
            # - Transfer Syntax uses Encapsulated Format, and
            # - Transfer Syntax encodes Frames in separate Fragments, and
            # - Basic Offset Table is not present (first Item has zero length), and
            # - Each Frame is entirely contained within one Fragment
            if not hasattr(dataset, 'PixelData'):
                result.add_error(
                    "Extended Offset Table (7FE0,0001) may only be present when Pixel Data "
                    "(7FE0,0010) is present. According to DICOM PS3.3 C.*******.8, Extended "
                    "Offset Table is only used with encapsulated pixel data."
                )

    @staticmethod
    def _validate_pixel_padding_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel padding range limit Type 1C requirements."""
        if hasattr(dataset, 'PixelPaddingRangeLimit'):
            # Note: Pixel Padding Value (0028,0120) is from General Equipment Module
            # We can only warn about this dependency
            result.add_warning(
                "Pixel Padding Range Limit (0028,0121) is present. According to DICOM PS3.3 C.7.6.3, "
                "this Type 1C element requires Pixel Padding Value (0028,0120) from the General "
                "Equipment Module to be present as well. Ensure both attributes are used together "
                "to define a range of padding values."
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard definitions."""

        # Photometric Interpretation - validate against defined terms
        if hasattr(dataset, 'PhotometricInterpretation'):
            valid_photometric_values = [e.value for e in PhotometricInterpretation]
            if dataset.PhotometricInterpretation not in valid_photometric_values:
                result.add_error(
                    f"Invalid Photometric Interpretation (0028,0004): '{dataset.PhotometricInterpretation}'. "
                    f"Valid values according to DICOM PS3.3 C.*******.2 are: {', '.join(valid_photometric_values)}. "
                    f"This attribute specifies the intended interpretation of the pixel data."
                )

        # Planar Configuration - validate enumerated values
        if hasattr(dataset, 'PlanarConfiguration'):
            valid_planar_values = [str(e.value) for e in PlanarConfiguration]
            planar_str = str(dataset.PlanarConfiguration)
            if planar_str not in valid_planar_values:
                result.add_error(
                    f"Invalid Planar Configuration (0028,0006): '{dataset.PlanarConfiguration}'. "
                    f"Valid values are: 0 (color-by-pixel) or 1 (color-by-plane). "
                    f"According to DICOM PS3.3 C.*******.3, this indicates whether color pixel "
                    f"data are encoded color-by-pixel or color-by-plane."
                )

        # Pixel Representation - validate enumerated values
        if hasattr(dataset, 'PixelRepresentation'):
            valid_pixel_rep_values = [str(e.value) for e in PixelRepresentation]
            pixel_rep_str = str(dataset.PixelRepresentation)
            if pixel_rep_str not in valid_pixel_rep_values:
                result.add_error(
                    f"Invalid Pixel Representation (0028,0103): '{dataset.PixelRepresentation}'. "
                    f"Valid values are: 0 (unsigned integer) or 1 (2's complement signed integer). "
                    f"According to DICOM PS3.3 C.*******, this specifies the data representation "
                    f"of the pixel samples."
                )
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel data consistency and format constraints."""

        # Validate palette color descriptor consistency
        ImagePixelValidator._validate_palette_descriptor_consistency(dataset, result)

        # Validate ICC Profile and Color Space constraints
        ImagePixelValidator._validate_color_profile_constraints(dataset, result)

    @staticmethod
    def _validate_photometric_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate photometric interpretation constraints on samples per pixel."""

        if hasattr(dataset, 'PhotometricInterpretation') and hasattr(dataset, 'SamplesPerPixel'):
            photometric = dataset.PhotometricInterpretation
            samples = dataset.SamplesPerPixel

            # Single-sample photometric interpretations
            single_sample_types = ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]
            if photometric in single_sample_types and samples != 1:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 1, "
                    f"got {samples}. According to DICOM PS3.3 C.*******.2, {photometric} images "
                    f"represent single-plane data and must have exactly 1 sample per pixel."
                )

            # Three-sample photometric interpretations
            three_sample_types = ["RGB", "YBR_FULL", "YBR_FULL_422", "YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT", "XYB"]
            if photometric in three_sample_types and samples != 3:
                result.add_error(
                    f"Photometric Interpretation '{photometric}' requires Samples per Pixel = 3, "
                    f"got {samples}. According to DICOM PS3.3 C.*******.2, {photometric} images "
                    f"represent three-component color data and must have exactly 3 samples per pixel."
                )

    @staticmethod
    def _validate_ybr_format_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate YBR format specific constraints."""

        if not hasattr(dataset, 'PhotometricInterpretation'):
            return

        photometric = dataset.PhotometricInterpretation

        # YBR_FULL_422 specific constraints
        if photometric == "YBR_FULL_422":
            # Must have Planar Configuration = 0
            if hasattr(dataset, 'PlanarConfiguration') and dataset.PlanarConfiguration != 0:
                result.add_error(
                    f"YBR_FULL_422 Photometric Interpretation requires Planar Configuration = 0, "
                    f"got {dataset.PlanarConfiguration}. According to DICOM PS3.3 C.*******.2, "
                    f"YBR_FULL_422 shall have Planar Configuration = 0 for proper chrominance subsampling."
                )

            # Must have even number of columns for horizontal subsampling
            if hasattr(dataset, 'Columns') and dataset.Columns % 2 != 0:
                result.add_error(
                    f"YBR_FULL_422 Photometric Interpretation requires even number of Columns "
                    f"for horizontal subsampling, got {dataset.Columns}. According to DICOM PS3.3 C.*******.2, "
                    f"YBR_FULL_422 uses horizontal chrominance subsampling which requires even column count."
                )

        # YBR_PARTIAL_420, YBR_ICT, YBR_RCT constraints (compressed format only)
        compressed_only_types = ["YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT"]
        if photometric in compressed_only_types:
            # These should only be used with compressed transfer syntaxes
            # We can only provide a warning since we don't have transfer syntax info
            result.add_warning(
                f"Photometric Interpretation '{photometric}' should only be used with "
                f"compressed transfer syntaxes. According to DICOM PS3.3 C.*******.2, "
                f"{photometric} shall only be used for pixel data in Encapsulated (compressed) format."
            )

            # Must have Planar Configuration = 0
            if hasattr(dataset, 'PlanarConfiguration') and dataset.PlanarConfiguration != 0:
                result.add_error(
                    f"{photometric} Photometric Interpretation requires Planar Configuration = 0, "
                    f"got {dataset.PlanarConfiguration}. According to DICOM PS3.3 C.*******.2, "
                    f"{photometric} shall have Planar Configuration = 0."
                )

    @staticmethod
    def _validate_palette_descriptor_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate palette color descriptor consistency."""

        if hasattr(dataset, 'PhotometricInterpretation') and dataset.PhotometricInterpretation == "PALETTE COLOR":
            descriptors = []
            descriptor_names = []

            for color in ['Red', 'Green', 'Blue']:
                attr_name = f'{color}PaletteColorLookupTableDescriptor'
                if hasattr(dataset, attr_name):
                    descriptors.append(getattr(dataset, attr_name))
                    descriptor_names.append(attr_name)

            if len(descriptors) == 3:
                # Validate descriptor format (should be 3-element sequences)
                for i, (desc, name) in enumerate(zip(descriptors, descriptor_names)):
                    # Convert to list if it's a pydicom sequence type
                    try:
                        desc_list = list(desc) if hasattr(desc, '__iter__') and not isinstance(desc, str) else desc
                        if not hasattr(desc_list, '__len__') or len(desc_list) != 3:
                            result.add_error(
                                f"{name} must be a sequence of exactly 3 values "
                                f"[entries, first_value, bits_per_entry], got '{desc}'. "
                                f"According to DICOM PS3.3 C.*******.5, palette descriptors specify "
                                f"the format of the lookup table data."
                            )
                            continue
                        # Update the descriptor in the list for consistency checking
                        descriptors[i] = desc_list
                    except (TypeError, ValueError):
                        result.add_error(
                            f"{name} must be a sequence of exactly 3 values "
                            f"[entries, first_value, bits_per_entry], got '{desc}'. "
                            f"According to DICOM PS3.3 C.*******.5, palette descriptors specify "
                            f"the format of the lookup table data."
                        )
                        continue

                # Check consistency between descriptors
                if len(descriptors) == 3 and all(hasattr(desc, '__len__') and len(desc) == 3 for desc in descriptors):
                    # First value (number of entries) must be identical
                    if not all(desc[0] == descriptors[0][0] for desc in descriptors):
                        result.add_error(
                            f"All Palette Color Lookup Table Descriptors must have the same first value "
                            f"(number of entries). Got Red={descriptors[0][0]}, Green={descriptors[1][0]}, "
                            f"Blue={descriptors[2][0]}. According to DICOM PS3.3 C.*******.5, the first "
                            f"value shall be identical for each color component."
                        )

                    # Second value (first input value mapped) must be identical
                    if not all(desc[1] == descriptors[0][1] for desc in descriptors):
                        result.add_error(
                            f"All Palette Color Lookup Table Descriptors must have the same second value "
                            f"(first input value mapped). Got Red={descriptors[0][1]}, Green={descriptors[1][1]}, "
                            f"Blue={descriptors[2][1]}. According to DICOM PS3.3 C.*******.5, the second "
                            f"value shall be identical for each color component."
                        )

                    # Third value (bits per entry) must be identical for RGB
                    if not all(desc[2] == descriptors[0][2] for desc in descriptors):
                        result.add_error(
                            f"All RGB Palette Color Lookup Table Descriptors must have the same third value "
                            f"(bits per entry). Got Red={descriptors[0][2]}, Green={descriptors[1][2]}, "
                            f"Blue={descriptors[2][2]}. According to DICOM PS3.3 C.*******.5, the third "
                            f"value shall be identical for RGB components."
                        )

    @staticmethod
    def _validate_color_profile_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate ICC Profile and Color Space constraints."""

        # ICC Profile constraints
        if hasattr(dataset, 'ICCProfile'):
            # Check for Optical Path Sequence constraint
            if hasattr(dataset, 'OpticalPathSequence'):
                result.add_error(
                    "ICC Profile (0028,2000) shall not be present when Optical Path Sequence "
                    "(0048,0105) is present. According to DICOM PS3.3 C.*******, when the "
                    "Optical Path Module is used, each optical path has its own ICC Profile."
                )

        # Color Space constraints
        if hasattr(dataset, 'ColorSpace'):
            # Should be consistent with ICC Profile if present
            if hasattr(dataset, 'ICCProfile'):
                result.add_warning(
                    "Color Space (0028,2002) is present along with ICC Profile (0028,2000). "
                    "According to DICOM PS3.3 C.*******, Color Space shall be consistent "
                    "with any ICC Profile that is also present. Verify color space compatibility."
                )

            # Should not be present when Optical Path Sequence present
            if hasattr(dataset, 'OpticalPathSequence'):
                result.add_error(
                    "Color Space (0028,2002) shall not be present when Optical Path Sequence "
                    "(0048,0105) is present. According to DICOM PS3.3 C.*******, color space "
                    "information is handled per optical path when the Optical Path Module is used."
                )
