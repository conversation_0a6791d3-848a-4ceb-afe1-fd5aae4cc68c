"""General Equipment Module DICOM validation - PS3.3 C.7.5.1

This validator ensures complete compliance with DICOM PS3.3 C.7.5.1 General Equipment Module
requirements, including all Type 1C conditional logic, sequence validation, and semantic constraints.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class GeneralEquipmentValidator(BaseValidator):
    """Validator for DICOM General Equipment Module (PS3.3 C.7.5.1).

    Validates all requirements from DICOM PS3.3 C.7.5.1 including:
    - Type 2 required elements (Manufacturer)
    - Type 1C conditional requirements (Pixel Padding Value)
    - Sequence structure validation (Institutional Department Type Code, UDI)
    - Calibration date/time pairing requirements
    - Value representation constraints
    - Semantic validation of relationships between attributes

    This validator provides comprehensive error checking with clear, actionable
    error messages to guide users in creating DICOM-compliant datasets.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Equipment Module requirements on any pydicom Dataset.

        Performs comprehensive validation of DICOM PS3.3 C.7.5.1 requirements including:
        - Type 2 element presence validation
        - Type 1C conditional requirements
        - Sequence structure and macro validation
        - Calibration date/time pairing
        - Value representation constraints
        - Semantic validation of attribute relationships

        Args:
            dataset (Dataset): pydicom Dataset to validate against General Equipment Module requirements
            config (ValidationConfig | None): Optional validation configuration to control validation behavior.
                If None, uses default configuration with all validations enabled.

        Returns:
            ValidationResult: Validation result containing lists of errors and warnings with specific
                DICOM tag references and actionable guidance for resolving issues.
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 2 required elements
        GeneralEquipmentValidator._validate_required_elements(dataset, result)

        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            GeneralEquipmentValidator._validate_conditional_requirements(dataset, result)

        # Validate sequence structures and macros
        if config.validate_sequences:
            GeneralEquipmentValidator._validate_sequence_requirements(dataset, result)

        # Validate calibration date/time pairing requirements
        GeneralEquipmentValidator._validate_calibration_pairing(dataset, result)

        # Validate semantic constraints and relationships
        if config.validate_semantic_constraints:
            GeneralEquipmentValidator._validate_semantic_constraints(dataset, result)

        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 required elements.

        Type 2 elements are required to be present but may have empty values.
        """
        # Type 2: Manufacturer (0008,0070) - Required but may be empty
        if not hasattr(dataset, 'Manufacturer'):
            result.add_error(
                "Manufacturer (0008,0070) is required (Type 2). This element identifies the "
                "manufacturer of the equipment that produced the Composite Instances. "
                "It may be empty but must be present."
            )

    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements.

        Type 1C elements are required only when specific conditions are met.
        """
        # Type 1C: Pixel Padding Value (0028,0120)
        # Required if Pixel Padding Range Limit (0028,0121) is present
        # AND either Pixel Data (7FE0,0010) or Pixel Data Provider URL (0028,7FE0) is present
        has_pixel_padding_range_limit = hasattr(dataset, 'PixelPaddingRangeLimit')
        has_pixel_data = hasattr(dataset, 'PixelData') or hasattr(dataset, 'PixelDataProviderURL')

        if has_pixel_padding_range_limit and has_pixel_data:
            if not hasattr(dataset, 'PixelPaddingValue'):
                result.add_error(
                    "Pixel Padding Value (0028,0120) is required (Type 1C) when Pixel Padding Range Limit "
                    "(0028,0121) is present and either Pixel Data (7FE0,0010) or Pixel Data Provider URL "
                    "(0028,7FE0) is present. This value specifies the pixel value used for padding to "
                    "rectangular format. See DICOM PS3.3 C.*******.2 for detailed requirements."
                )

        # Additional validation: Pixel Padding Value should only be present if pixel data exists
        if hasattr(dataset, 'PixelPaddingValue') and not has_pixel_data:
            result.add_warning(
                "Pixel Padding Value (0028,0120) is present but no Pixel Data (7FE0,0010) or "
                "Pixel Data Provider URL (0028,7FE0) is found. Pixel Padding Value may only be "
                "present when pixel data is available."
            )

        # Validate pixel padding setup conditions (moved from module)
        GeneralEquipmentValidator._validate_pixel_padding_setup_conditions(dataset, result)

    @staticmethod
    def _validate_pixel_padding_setup_conditions(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel padding setup conditions that were previously in the module.
        
        This validation ensures that pixel padding value setup follows DICOM requirements
        without raising exceptions in the module itself.
        """
        # Check if attempting to set pixel padding value inappropriately
        if hasattr(dataset, 'PixelPaddingValue'):
            has_pixel_padding_range_limit = hasattr(dataset, 'PixelPaddingRangeLimit')
            has_pixel_data = hasattr(dataset, 'PixelData') or hasattr(dataset, 'PixelDataProviderURL')
            
            # If Pixel Padding Range Limit is present, then Pixel Data must also be present
            if has_pixel_padding_range_limit and not has_pixel_data:
                result.add_error(
                    "Pixel Padding Value (0028,0120) cannot be set when Pixel Padding Range Limit "
                    "(0028,0121) is present but Pixel Data (7FE0,0010) or Pixel Data Provider URL "
                    "(0028,7FE0) is not present. Pixel Padding Value requires pixel data to be meaningful."
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements and macro compliance.

        Validates that all sequences contain required sub-attributes according to
        their respective macro definitions.
        """
        # Institutional Department Type Code Sequence (0008,1041) - Type 3
        # When present, each item must include Code Sequence Macro attributes (Table 8.8-1)
        dept_type_seq = getattr(dataset, 'InstitutionalDepartmentTypeCodeSequence', [])
        if dept_type_seq:
            # Only a single item is permitted in this sequence
            if len(dept_type_seq) > 1:
                result.add_error(
                    "Institutional Department Type Code Sequence (0008,1041) may contain only a single item. "
                    f"Found {len(dept_type_seq)} items. See DICOM PS3.3 C.7.5.1."
                )

            for i, item in enumerate(dept_type_seq):
                # Code Sequence Macro validation (Table 8.8-1)
                if not item.get('CodeValue'):
                    result.add_error(
                        f"Institutional Department Type Code Sequence item {i+1}: Code Value (0008,0100) "
                        "is required as part of Code Sequence Macro. This identifies the specific "
                        "department type code."
                    )
                if not item.get('CodingSchemeDesignator'):
                    result.add_error(
                        f"Institutional Department Type Code Sequence item {i+1}: Coding Scheme Designator "
                        "(0008,0102) is required as part of Code Sequence Macro. This identifies the "
                        "coding scheme used (e.g., 'DCM' for DICOM)."
                    )
                if not item.get('CodeMeaning'):
                    result.add_error(
                        f"Institutional Department Type Code Sequence item {i+1}: Code Meaning (0008,0104) "
                        "is required as part of Code Sequence Macro. This provides human-readable "
                        "description of the code."
                    )

        # UDI Sequence (0018,100A) - Type 3
        # When present, each item must include UDI Macro attributes (Table 10.29-1)
        udi_seq = getattr(dataset, 'UDISequence', [])
        for i, item in enumerate(udi_seq):
            # UDI Macro validation (Table 10.29-1)
            if not item.get('UniqueDeviceIdentifier'):
                result.add_error(
                    f"UDI Sequence item {i+1}: Unique Device Identifier is required as part of "
                    "UDI Macro. This is the complete UDI string that uniquely identifies the device. "
                    "See DICOM PS3.3 Table 10.29-1."
                )

            # Additional UDI Macro attributes validation
            # Note: Other UDI Macro attributes like DeviceIdentifier, IssuerOfUDI are optional
            # but if present should be validated for format compliance
    
    @staticmethod
    def _validate_calibration_pairing(dataset: Dataset, result: ValidationResult) -> None:
        """Validate calibration date/time pairing requirements.

        According to DICOM PS3.3 C.*******.1, Date of Last Calibration may be supported alone,
        but Time of Last Calibration has no meaning unless Date of Last Calibration is also
        supported. When both are supported, they shall be provided as pairs.
        """
        date_calibration = getattr(dataset, 'DateOfLastCalibration', None)
        time_calibration = getattr(dataset, 'TimeOfLastCalibration', None)

        # Convert to lists for consistent processing
        if date_calibration is None:
            date_calibration = []
        elif not hasattr(date_calibration, '__len__') or isinstance(date_calibration, str):
            date_calibration = [date_calibration]
        else:
            date_calibration = list(date_calibration)  # Convert MultiValue to list

        if time_calibration is None:
            time_calibration = []
        elif not hasattr(time_calibration, '__len__') or isinstance(time_calibration, str):
            time_calibration = [time_calibration]
        else:
            time_calibration = list(time_calibration)  # Convert MultiValue to list

        # Time of Last Calibration has no meaning unless Date of Last Calibration is also present
        if time_calibration and not date_calibration:
            result.add_error(
                "Time of Last Calibration (0018,1201) has no meaning unless Date of Last Calibration "
                "(0018,1200) is also present. Per DICOM PS3.3 C.*******.1, Date of Last Calibration "
                "may be supported alone, but Time requires Date to be meaningful. Remove Time of Last "
                "Calibration or add corresponding Date of Last Calibration values."
            )

        # When both are present, they should have the same number of values (paired)
        if date_calibration and time_calibration:
            if len(date_calibration) != len(time_calibration):
                result.add_error(
                    f"Date of Last Calibration (0018,1200) and Time of Last Calibration (0018,1201) "
                    f"must have the same number of values when both are present. Found {len(date_calibration)} "
                    f"date values and {len(time_calibration)} time values. Per DICOM PS3.3 C.*******.1, "
                    "when both attributes are supported they shall be provided as pairs."
                )

        # Validate ordering requirement: values should be from oldest to most recent
        if len(date_calibration) > 1:
            for i in range(1, len(date_calibration)):
                # Compare dates as strings (YYYYMMDD format allows string comparison)
                if str(date_calibration[i]) < str(date_calibration[i-1]):
                    result.add_warning(
                        "Date of Last Calibration (0018,1200) values should be ordered from oldest "
                        "to most recent date/time. Per DICOM PS3.3 C.*******.1, the order for each "
                        "attribute shall be from the oldest date/time to the most recent date/time."
                    )
                    break

    @staticmethod
    def _validate_semantic_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate semantic constraints and relationships between attributes.

        Validates logical relationships and constraints that ensure the dataset
        makes semantic sense according to DICOM standards and real-world usage.
        """
        # Validate Software Versions format and multiplicity
        software_versions = getattr(dataset, 'SoftwareVersions', None)
        if software_versions is not None:
            if isinstance(software_versions, str):
                # Single value is fine
                pass
            elif isinstance(software_versions, list):
                if len(software_versions) == 0:
                    result.add_warning(
                        "Software Versions (0018,1020) is present but empty. If provided, it should "
                        "contain at least one software version identifier."
                    )
            else:
                result.add_warning(
                    "Software Versions (0018,1020) should be a string or list of strings representing "
                    "software version identifiers."
                )

        # Validate Manufacturer Device Class UID format
        device_class_uid = getattr(dataset, 'ManufacturerDeviceClassUID', None)
        if device_class_uid is not None:
            # Handle different pydicom representations (string, UID object, list, MultiValue)
            if isinstance(device_class_uid, str):
                device_class_uid = [device_class_uid]
            elif hasattr(device_class_uid, '__iter__') and not isinstance(device_class_uid, str):
                # Convert any iterable (including MultiValue, UID objects) to list
                device_class_uid = list(device_class_uid)
            else:
                # Single UID object or other type
                device_class_uid = [str(device_class_uid)]

            # Validate UID format for each value
            for i, uid in enumerate(device_class_uid):
                uid_str = str(uid)  # Convert UID objects to string
                if not all(c.isdigit() or c == '.' for c in uid_str):
                    result.add_warning(
                        f"Manufacturer Device Class UID item {i+1}: '{uid_str}' does not appear to be "
                        "a valid UID format. UIDs should contain only digits and periods."
                    )

        # Validate spatial resolution value
        spatial_resolution = getattr(dataset, 'SpatialResolution', None)
        if spatial_resolution is not None:
            try:
                resolution_value = float(spatial_resolution)
                if resolution_value <= 0:
                    result.add_warning(
                        "Spatial Resolution (0018,1050) should be a positive value representing "
                        "the inherent limiting resolution in mm."
                    )
            except (ValueError, TypeError):
                result.add_warning(
                    "Spatial Resolution (0018,1050) should be a numeric value representing "
                    "the inherent limiting resolution in mm."
                )

        # Validate date formats (basic check)
        date_fields = [
            ('DateOfManufacture', '0018,1204'),
            ('DateOfInstallation', '0018,1205')
        ]

        for field_name, tag in date_fields:
            date_value = getattr(dataset, field_name, None)
            if date_value is not None:
                if not isinstance(date_value, str) or len(date_value) != 8:
                    result.add_warning(
                        f"{field_name} ({tag}) should be in DICOM DA format (YYYYMMDD). "
                        f"Current value '{date_value}' may not be properly formatted."
                    )
