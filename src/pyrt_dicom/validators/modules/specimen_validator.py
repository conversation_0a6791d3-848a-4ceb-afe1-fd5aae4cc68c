"""Specimen Module DICOM validation - PS3.3 C.7.6.22"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class SpecimenValidator(BaseValidator):
    """Validator for DICOM Specimen Module (PS3.3 C.7.6.22).
    
    Validates specimen identification and container information according to
    DICOM PS3.3 C.7.6.22 Specimen Module specification. Ensures proper
    conditional logic for specimen localization when multiple specimens are present.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Specimen Module requirements on any pydicom Dataset.
        
        Validates specimen identification, container information, and all conditional
        requirements according to DICOM PS3.3 C.7.6.22 specification.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Always validate Type 1 requirements
        SpecimenValidator._validate_required_elements(dataset, result)
        
        # Validate Type 2 requirements (required but can be empty)
        SpecimenValidator._validate_type2_elements(dataset, result)
        
        # Validate conditional requirements (Type 1C/2C)
        if config.validate_conditional_requirements:
            SpecimenValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            SpecimenValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures and cardinality
        if config.validate_sequences:
            SpecimenValidator._validate_sequence_requirements(dataset, result)
        
        # Validate cross-field relationships
        SpecimenValidator._validate_cross_field_relationships(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements per DICOM PS3.3 C.7.6.22."""
        
        # Container Identifier (0040,0512) - Type 1
        if not hasattr(dataset, 'ContainerIdentifier'):
            result.add_error(
                "Container Identifier (0040,0512) is missing. "
                "This Type 1 element is required to identify the container "
                "that contains the specimen(s) being imaged."
            )
        elif not dataset.ContainerIdentifier:
            result.add_error(
                "Container Identifier (0040,0512) cannot be empty. "
                "Type 1 elements must have a value."
            )
        
        # Specimen Description Sequence (0040,0560) - Type 1
        if not hasattr(dataset, 'SpecimenDescriptionSequence'):
            result.add_error(
                "Specimen Description Sequence (0040,0560) is missing. "
                "This Type 1 element is required to describe the specimen(s) being imaged."
            )
        elif not dataset.SpecimenDescriptionSequence:
            result.add_error(
                "Specimen Description Sequence (0040,0560) cannot be empty. "
                "One or more Items shall be included in this Sequence."
            )
        else:
            # Validate each specimen description item
            SpecimenValidator._validate_specimen_description_items(dataset, result)
    
    @staticmethod
    def _validate_type2_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 elements (required but can be empty)."""
        
        # Issuer of Container Identifier Sequence (0040,0513) - Type 2
        if not hasattr(dataset, 'IssuerOfContainerIdentifierSequence'):
            result.add_error(
                "Issuer of Container Identifier Sequence (0040,0513) is missing. "
                "This Type 2 element is required (can be empty sequence)."
            )
        
        # Container Type Code Sequence (0040,0518) - Type 2  
        if not hasattr(dataset, 'ContainerTypeCodeSequence'):
            result.add_error(
                "Container Type Code Sequence (0040,0518) is missing. "
                "This Type 2 element is required (can be empty sequence)."
            )
    
    @staticmethod
    def _validate_specimen_description_items(dataset: Dataset, result: ValidationResult) -> None:
        """Validate specimen description sequence items."""
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        
        for i, specimen_item in enumerate(specimen_seq):
            context = f"Specimen Description Sequence item {i + 1}"
            
            # Type 1 elements within specimen description
            if not specimen_item.get('SpecimenIdentifier'):
                result.add_error(
                    f"{context}: Specimen Identifier (0040,0551) is required (Type 1). "
                    "A departmental information system identifier for the Specimen is required."
                )
            
            if not specimen_item.get('SpecimenUID'):
                result.add_error(
                    f"{context}: Specimen UID (0040,0554) is required (Type 1). "
                    "A globally unique identifier for the Specimen is required."
                )
            
            # Type 2 elements within specimen description
            if not specimen_item.get('IssuerOfSpecimenIdentifierSequence'):
                result.add_warning(
                    f"{context}: Issuer of Specimen Identifier Sequence (0040,0562) "
                    "should be present (Type 2) to identify the organization that assigned the Specimen Identifier."
                )
            
            if not specimen_item.get('SpecimenPreparationSequence'):
                result.add_warning(
                    f"{context}: Specimen Preparation Sequence (0040,0610) should be present (Type 2) "
                    "to describe the process steps used to prepare the specimen for image acquisition."
                )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements per DICOM PS3.3 C.7.6.22."""
        
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        
        # Type 1C: Specimen Localization Content Item Sequence (0040,0620)
        # Required if multiple specimens are present in the image
        if len(specimen_seq) > 1:
            for i, specimen_item in enumerate(specimen_seq):
                context = f"Specimen Description Sequence item {i + 1}"
                if not specimen_item.get('SpecimenLocalizationContentItemSequence'):
                    result.add_error(
                        f"{context}: Specimen Localization Content Item Sequence (0040,0620) "
                        "is required when multiple specimens are present in the image (Type 1C). "
                        "This sequence is needed to identify the location of each specimen "
                        "in the container and/or in the image."
                    )
                else:
                    # Validate that localization sequence is not empty
                    loc_seq = specimen_item.get('SpecimenLocalizationContentItemSequence', [])
                    if not loc_seq:
                        result.add_error(
                            f"{context}: Specimen Localization Content Item Sequence (0040,0620) "
                            "cannot be empty when multiple specimens are present. "
                            "One or more Items shall be included in this Sequence."
                        )
        
        # Validate hierarchical designator conditional requirements
        SpecimenValidator._validate_hierarchic_designator_conditionals(dataset, result)
    
    @staticmethod
    def _validate_hierarchic_designator_conditionals(dataset: Dataset, result: ValidationResult) -> None:
        """Validate HL7v2 Hierarchic Designator Macro conditional requirements."""
        
        # Check issuer sequences for hierarchical designator conditionals
        issuer_seq = getattr(dataset, 'IssuerOfContainerIdentifierSequence', [])
        for i, issuer_item in enumerate(issuer_seq):
            context = f"Issuer of Container Identifier Sequence item {i + 1}"
            SpecimenValidator._validate_single_hierarchic_designator(issuer_item, context, result)
        
        # Check specimen issuer sequences
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        for i, specimen_item in enumerate(specimen_seq):
            specimen_issuer_seq = specimen_item.get('IssuerOfSpecimenIdentifierSequence', [])
            for j, issuer_item in enumerate(specimen_issuer_seq):
                context = f"Specimen Description Sequence item {i + 1}, Issuer of Specimen Identifier Sequence item {j + 1}"
                SpecimenValidator._validate_single_hierarchic_designator(issuer_item, context, result)
    
    @staticmethod
    def _validate_single_hierarchic_designator(issuer_item: dict, context: str, result: ValidationResult) -> None:
        """Validate a single hierarchic designator item."""
        local_ns = issuer_item.get('LocalNamespaceEntityID')
        universal_id = issuer_item.get('UniversalEntityID')
        universal_type = issuer_item.get('UniversalEntityIDType')
        
        # Type 1C: At least one of Local Namespace Entity ID or Universal Entity ID must be present
        if not local_ns and not universal_id:
            result.add_error(
                f"{context}: Either Local Namespace Entity ID (0040,0031) or "
                "Universal Entity ID (0040,0032) must be present (Type 1C)."
            )
        
        # Type 1C: Universal Entity ID Type required if Universal Entity ID is present
        if universal_id and not universal_type:
            result.add_error(
                f"{context}: Universal Entity ID Type (0040,0033) is required "
                "when Universal Entity ID (0040,0032) is present (Type 1C)."
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications per PS3.3 C.7.6.22."""
        
        # Container Component Material enumerated values (0050,001A)
        component_seq = getattr(dataset, 'ContainerComponentSequence', [])
        for i, component in enumerate(component_seq):
            material = component.get('ContainerComponentMaterial', '')
            if material:
                context = f"Container Component Sequence item {i + 1}: Container Component Material (0050,001A)"
                BaseValidator.validate_enumerated_value(
                    material, 
                    ["GLASS", "PLASTIC", "METAL"],
                    context, 
                    result
                )
        
        # Validate Universal Entity ID Type enumerated values
        SpecimenValidator._validate_universal_entity_id_types(dataset, result)
    
    @staticmethod
    def _validate_universal_entity_id_types(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Universal Entity ID Type enumerated values."""
        valid_types = ["DNS", "EUI64", "ISO", "URI", "UUID", "X400", "X500"]
        
        # Check container issuer sequences
        issuer_seq = getattr(dataset, 'IssuerOfContainerIdentifierSequence', [])
        for i, issuer_item in enumerate(issuer_seq):
            entity_type = issuer_item.get('UniversalEntityIDType')
            if entity_type:
                context = f"Issuer of Container Identifier Sequence item {i + 1}: Universal Entity ID Type (0040,0033)"
                BaseValidator.validate_enumerated_value(entity_type, valid_types, context, result)
        
        # Check specimen issuer sequences
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        for i, specimen_item in enumerate(specimen_seq):
            specimen_issuer_seq = specimen_item.get('IssuerOfSpecimenIdentifierSequence', [])
            for j, issuer_item in enumerate(specimen_issuer_seq):
                entity_type = issuer_item.get('UniversalEntityIDType')
                if entity_type:
                    context = f"Specimen Description Sequence item {i + 1}, Issuer of Specimen Identifier Sequence item {j + 1}: Universal Entity ID Type (0040,0033)"
                    BaseValidator.validate_enumerated_value(entity_type, valid_types, context, result)
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure and cardinality requirements per DICOM PS3.3 C.7.6.22."""
        
        # Validate main sequence cardinalities
        SpecimenValidator._validate_main_sequence_cardinalities(dataset, result)
        
        # Validate Alternate Container Identifier Sequence structure
        SpecimenValidator._validate_alternate_container_sequences(dataset, result)
        
        # Validate Container Component Sequence structure
        SpecimenValidator._validate_container_component_sequences(dataset, result)
        
        # Validate all Code Sequence structures
        SpecimenValidator._validate_code_sequences(dataset, result)
    
    @staticmethod
    def _validate_main_sequence_cardinalities(dataset: Dataset, result: ValidationResult) -> None:
        """Validate main sequence cardinality requirements."""
        
        # Issuer of Container Identifier Sequence (0040,0513) - cardinality 0-1
        issuer_seq = getattr(dataset, 'IssuerOfContainerIdentifierSequence', [])
        if len(issuer_seq) > 1:
            result.add_error(
                "Issuer of Container Identifier Sequence (0040,0513) cardinality violation: "
                "Zero or one Item shall be included in this Sequence. "
                f"Found {len(issuer_seq)} items."
            )
        
        # Container Type Code Sequence (0040,0518) - cardinality 0-1
        container_type_seq = getattr(dataset, 'ContainerTypeCodeSequence', [])
        if len(container_type_seq) > 1:
            result.add_error(
                "Container Type Code Sequence (0040,0518) cardinality violation: "
                "Zero or one Item shall be included in this Sequence. "
                f"Found {len(container_type_seq)} items."
            )
        
        # Specimen Description Sequence (0040,0560) - cardinality 1-n
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        if len(specimen_seq) == 0:
            result.add_error(
                "Specimen Description Sequence (0040,0560) cardinality violation: "
                "One or more Items shall be included in this Sequence."
            )
    
    @staticmethod
    def _validate_alternate_container_sequences(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Alternate Container Identifier Sequence structure."""
        alt_container_seq = getattr(dataset, 'AlternateContainerIdentifierSequence', [])
        
        for i, alt_item in enumerate(alt_container_seq):
            context = f"Alternate Container Identifier Sequence item {i + 1}"
            
            # Type 1: Container Identifier is required within alternate container items
            if not alt_item.get('ContainerIdentifier'):
                result.add_error(
                    f"{context}: Container Identifier (0040,0512) is required (Type 1)."
                )
            
            # Validate issuer sequence cardinality within alternate container items
            alt_issuer_seq = alt_item.get('IssuerOfContainerIdentifierSequence', [])
            if len(alt_issuer_seq) > 1:
                result.add_error(
                    f"{context}: Issuer of Container Identifier Sequence (0040,0513) "
                    "cardinality violation: Zero or one Item shall be included in this Sequence. "
                    f"Found {len(alt_issuer_seq)} items."
                )
    
    @staticmethod
    def _validate_container_component_sequences(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Container Component Sequence structure."""
        component_seq = getattr(dataset, 'ContainerComponentSequence', [])
        
        for i, component in enumerate(component_seq):
            context = f"Container Component Sequence item {i + 1}"
            
            # Type 1: Container Component Type Code Sequence is required
            if not component.get('ContainerComponentTypeCodeSequence'):
                result.add_error(
                    f"{context}: Container Component Type Code Sequence (0050,0012) "
                    "is required (Type 1)."
                )
            else:
                # Validate that only single item is included (cardinality = 1)
                type_code_seq = component.get('ContainerComponentTypeCodeSequence', [])
                if len(type_code_seq) != 1:
                    result.add_error(
                        f"{context}: Container Component Type Code Sequence (0050,0012) "
                        "cardinality violation: Only a single Item shall be included in this Sequence. "
                        f"Found {len(type_code_seq)} items."
                    )
    
    @staticmethod
    def _validate_cross_field_relationships(dataset: Dataset, result: ValidationResult) -> None:
        """Validate relationships and dependencies between different elements."""
        
        # Validate container-specimen relationships
        container_id = getattr(dataset, 'ContainerIdentifier', '')
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        
        # Check for meaningful specimen identifiers
        specimen_ids = []
        for i, specimen_item in enumerate(specimen_seq):
            spec_id = specimen_item.get('SpecimenIdentifier', '')
            if spec_id:
                specimen_ids.append(spec_id)
                
                # Warning: If single specimen and container ID match, that's typical
                if len(specimen_seq) == 1 and spec_id == container_id:
                    result.add_warning(
                        f"Specimen Description Sequence item {i + 1}: "
                        "Specimen Identifier matches Container Identifier. "
                        "This is typical for single-specimen containers but verify intentional."
                    )
        
        # Check for duplicate specimen identifiers within the same container
        if len(specimen_ids) != len(set(specimen_ids)):
            result.add_error(
                "Duplicate Specimen Identifiers detected within the same container. "
                "Each specimen in a container must have a unique identifier for workflow management."
            )
        
        # Validate UID uniqueness
        specimen_uids = [item.get('SpecimenUID', '') for item in specimen_seq if item.get('SpecimenUID')]
        if len(specimen_uids) != len(set(specimen_uids)):
            result.add_error(
                "Duplicate Specimen UIDs detected. "
                "Specimen UID (0040,0554) must be globally unique for each specimen."
            )
    
    @staticmethod
    def _validate_code_sequences(dataset: Dataset, result: ValidationResult) -> None:
        """Validate code sequence structure requirements."""
        
        # Validate Container Type Code Sequence
        container_type_seq = getattr(dataset, 'ContainerTypeCodeSequence', [])
        for i, code_item in enumerate(container_type_seq):
            SpecimenValidator._validate_code_item(
                code_item, f"Container Type Code Sequence item {i + 1}", result
            )
        
        # Validate Specimen Description Sequence code sequences
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        for i, specimen_item in enumerate(specimen_seq):
            # Validate Specimen Type Code Sequence
            specimen_type_seq = specimen_item.get('SpecimenTypeCodeSequence', [])
            for j, code_item in enumerate(specimen_type_seq):
                SpecimenValidator._validate_code_item(
                    code_item, f"Specimen Description Sequence item {i + 1}, Specimen Type Code Sequence item {j + 1}", result
                )
        
        # Validate Container Component Type Code Sequences
        component_seq = getattr(dataset, 'ContainerComponentSequence', [])
        for i, component in enumerate(component_seq):
            type_code_seq = component.get('ContainerComponentTypeCodeSequence', [])
            for j, code_item in enumerate(type_code_seq):
                SpecimenValidator._validate_code_item(
                    code_item, f"Container Component Sequence item {i + 1}, Container Component Type Code Sequence item {j + 1}", result
                )
    
    @staticmethod
    def _validate_code_item(code_item: dict, context: str, result: ValidationResult) -> None:
        """Validate individual code sequence item structure per DICOM PS3.3 Table 8.8-1."""
        
        # Type 1 elements in Code Sequence Macro
        if not code_item.get('CodeValue'):
            result.add_error(
                f"{context}: Code Value (0008,0100) is required (Type 1). "
                "The identifier of the code."
            )
        if not code_item.get('CodingSchemeDesignator'):
            result.add_error(
                f"{context}: Coding Scheme Designator (0008,0102) is required (Type 1). "
                "The coding scheme in which the code is defined."
            )
        if not code_item.get('CodeMeaning'):
            result.add_error(
                f"{context}: Code Meaning (0008,0104) is required (Type 1). "
                "Human-readable meaning of the code."
            )
        
        # Type 1C: Coding Scheme Version may be required
        coding_scheme = code_item.get('CodingSchemeDesignator', '')
        coding_version = code_item.get('CodingSchemeVersion')
        if coding_scheme and not coding_version:
            # Add warning for common schemes that should have versions
            if coding_scheme in ['SCT', 'SNOMED', 'ICD10', 'ICD11']:
                result.add_warning(
                    f"{context}: Coding Scheme Version (0008,0103) should be present "
                    f"for coding scheme '{coding_scheme}' to ensure proper code interpretation."
                )
