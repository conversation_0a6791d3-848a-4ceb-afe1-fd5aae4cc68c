"""Device Module DICOM validation - PS3.3 C.7.6.12

Validates DICOM Device Module according to PS3.3 specification including:
- Type 1 required elements validation
- Type 2C conditional requirements (Device Diameter Units when Device Diameter present)
- Code Sequence Macro attributes validation in device sequence items
- Enumerated values compliance for Device Diameter Units
- Comprehensive error reporting with DICOM tag references and resolution guidance
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.equipment_enums import DeviceDiameterUnits

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class DeviceValidator(BaseValidator):
    """Validator for DICOM Device Module (PS3.3 C.7.6.12).

    Independent validator that works on pydicom Dataset OR BaseModule instances.
    Validates Device Module requirements including:
    - Type 1 Device Sequence presence and structure
    - Code Sequence Macro attributes in each device item
    - Type 2C conditional requirements (Device Diameter Units when Device Diameter present)
    - Enumerated value compliance for Device Diameter Units
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 required elements.

        Device Sequence (0050,0010) is Type 1 - must be present and non-empty.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Type 1: Device Sequence (0050,0010)
        if 'DeviceSequence' not in data:
            result.add_error(
                "Device Sequence (0050,0010) is required (Type 1). "
                "Use DeviceModule.from_required_elements() with at least one device item."
            )
        elif not data.DeviceSequence:
            result.add_error(
                "Device Sequence (0050,0010) cannot be empty (Type 1). "
                "At least one device must be specified using DeviceModule.create_device_item()."
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 2C conditional requirements.

        Device Diameter Units (0050,0017) is Type 2C - required if Device Diameter
        (0050,0016) is present in any device item.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Type 2C: Device Diameter Units required if Device Diameter is present
        if 'DeviceSequence' in data:
            device_seq = data.DeviceSequence
            for i, item in enumerate(device_seq):
                if 'DeviceDiameter' in item and item.DeviceDiameter:
                    if 'DeviceDiameterUnits' not in item or not item.DeviceDiameterUnits:
                        result.add_error(
                            f"Device Sequence item {i}: Device Diameter Units (0050,0017) is required "
                            f"when Device Diameter (0050,0016) is present (Type 2C). "
                            f"Valid units: FR (French), GA (Gauge), IN (Inch), MM (Millimeter)"
                        )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated values against DICOM specifications.

        Device Diameter Units (0050,0017) must be one of the defined terms:
        FR (French), GA (Gauge), IN (Inch), MM (Millimeter)

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Device Diameter Units (0050,0017) enumerated value validation
        if 'DeviceSequence' in data:
            device_seq = data.DeviceSequence
            for i, item in enumerate(device_seq):
                if 'DeviceDiameterUnits' in item and item.DeviceDiameterUnits:
                    diameter_units = item.DeviceDiameterUnits
                    valid_units = [unit.value for unit in DeviceDiameterUnits]
                    if diameter_units not in valid_units:
                        result.add_error(
                            f"Device Sequence item {i}: Device Diameter Units (0050,0017) "
                            f"has invalid value '{diameter_units}'. "
                            f"Valid values are: {', '.join(valid_units)} "
                            f"(FR=French, GA=Gauge, IN=Inch, MM=Millimeter)"
                        )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Device Sequence structure and Code Sequence Macro requirements.

        Each device item must:
        - Be a pydicom Dataset object
        - Include Code Sequence Macro attributes (CodeValue, CodingSchemeDesignator, CodeMeaning)
        - Follow DICOM PS3.3 Table C.7-18 specifications

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Device Sequence - validate sequence structure and Code Sequence Macro
        if 'DeviceSequence' in data:
            device_seq = data.DeviceSequence
            for i, item in enumerate(device_seq):
                # Validate that sequence items are Dataset objects
                if not isinstance(item, Dataset):
                    result.add_error(
                        f"Device Sequence item {i}: Must be a pydicom Dataset object, "
                        f"got {type(item).__name__}. Use DeviceModule.create_device_item() to create items."
                    )
                    continue

                # Code Sequence Macro attributes (from Table 8.8-1)
                if 'CodeValue' not in item or not item.CodeValue:
                    result.add_error(
                        f"Device Sequence item {i}: Code Value (0008,0100) is required "
                        f"for Code Sequence Macro. Specify device type code."
                    )
                if 'CodingSchemeDesignator' not in item or not item.CodingSchemeDesignator:
                    result.add_error(
                        f"Device Sequence item {i}: Coding Scheme Designator (0008,0102) is required "
                        f"for Code Sequence Macro. Use standard like 'SRT' for SNOMED CT."
                    )
                if 'CodeMeaning' not in item or not item.CodeMeaning:
                    result.add_error(
                        f"Device Sequence item {i}: Code Meaning (0008,0104) is required "
                        f"for Code Sequence Macro. Provide human-readable device description."
                    )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(DeviceValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(DeviceValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(DeviceValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(DeviceValidator.validate_sequence_structures(data))

        return result
