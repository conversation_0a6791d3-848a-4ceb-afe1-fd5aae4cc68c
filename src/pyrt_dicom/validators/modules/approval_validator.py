"""Approval Module Validator - DICOM PS3.3 C.8.8.16 validation."""

from __future__ import annotations

from pydicom import Dataset
from typing import Union, TYPE_CHECKING
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.approval_enums import ApprovalStatus

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class ApprovalValidator(BaseValidator):
    """Independent validator that works on pydicom Dataset OR BaseModule instances."""
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 required elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Use 'in' operator instead of hasattr - works with both Dataset and BaseModule
        if 'ApprovalStatus' not in data:
            result.add_error(
                "Missing required Type 1 element: ApprovalStatus (300E,0002). "
                "This element is mandatory for all Approval Module instances per DICOM PS3.3 C.8.8.16."
            )
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # Direct attribute access - works with both types via __getattr__
        if 'ApprovalStatus' not in data:
            return result  # Can't validate conditional requirements without ApprovalStatus
            
        approval_status = data.ApprovalStatus
        requires_review_info = approval_status in [ApprovalStatus.APPROVED.value, ApprovalStatus.REJECTED.value]
        
        if requires_review_info:
            # Review Date, Review Time, and Reviewer Name are required (Type 2C)
            required_review_elements = {
                'ReviewDate': '(300E,0004)',
                'ReviewTime': '(300E,0005)', 
                'ReviewerName': '(300E,0008)'
            }
            
            missing_elements = []
            for field_name, tag in required_review_elements.items():
                if field_name not in data:
                    missing_elements.append(f"{field_name} {tag}")
            
            if missing_elements:
                result.add_error(
                    f"Missing required Type 2C elements for ApprovalStatus '{approval_status}': "
                    f"{', '.join(missing_elements)}. Per DICOM PS3.3 C.8.8.16, review information "
                    f"is required when ApprovalStatus is APPROVED or REJECTED."
                )
        else:
            # For UNAPPROVED status, review information should not be present
            if approval_status == ApprovalStatus.UNAPPROVED.value:
                review_fields = ['ReviewDate', 'ReviewTime', 'ReviewerName']
                present_fields = [field for field in review_fields if field in data]
                
                if present_fields:
                    # Map field names to DICOM tags for clearer messaging
                    field_tags = {
                        'ReviewDate': '(300E,0004)',
                        'ReviewTime': '(300E,0005)',
                        'ReviewerName': '(300E,0008)'
                    }
                    tagged_fields = [f"{field} {field_tags[field]}" for field in present_fields]
                    
                    result.add_error(
                        f"Type 2C elements present for UNAPPROVED status: {', '.join(tagged_fields)}. "
                        f"Per DICOM PS3.3 C.8.8.16, review information should not be present "
                        f"when ApprovalStatus is UNAPPROVED."
                    )
                    
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # Enum validation with direct access
        if 'ApprovalStatus' in data:
            value = data.ApprovalStatus
            allowed_values = [e.value for e in ApprovalStatus]
            if value not in allowed_values:
                result.add_warning(
                    f"ApprovalStatus (300E,0002) value '{value}' is not in the defined enumerated values: "
                    f"{', '.join(allowed_values)}. Per DICOM PS3.3 C.8.8.16."
                )
                
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        # Approval Module does not contain any sequences per DICOM PS3.3 C.8.8.16
        return result
    
    @staticmethod
    def validate_date_time_formats(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate date and time format requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid date/time formats
        """
        result = ValidationResult()
        
        # Validate Review Date format (YYYYMMDD) - DICOM VR: DA
        if 'ReviewDate' in data:
            if not BaseValidator.validate_date_format(data.ReviewDate):
                result.add_error(
                    f"ReviewDate (300E,0004) value '{data.ReviewDate}' is invalid. "
                    f"Must be in DICOM DA format (YYYYMMDD) per DICOM PS3.5."
                )
        
        # Validate Review Time format (HHMMSS or HHMMSS.FFFFFF) - DICOM VR: TM
        if 'ReviewTime' in data:
            if not BaseValidator.validate_time_format(data.ReviewTime):
                result.add_error(
                    f"ReviewTime (300E,0005) value '{data.ReviewTime}' is invalid. "
                    f"Must be in DICOM TM format (HHMMSS or HHMMSS.FFFFFF) per DICOM PS3.5."
                )
                
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(ApprovalValidator.validate_required_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(ApprovalValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(ApprovalValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(ApprovalValidator.validate_sequence_structures(data))
        
        # Always validate date/time formats
        result.merge(ApprovalValidator.validate_date_time_formats(data))
        
        return result
    