"""General Series Module DICOM validation - PS3.3 C.7.3.1

This module implements comprehensive validation for the DICOM General Series Module
according to DICOM PS3.3 Section C.7.3.1, ensuring complete compliance with
Type 1, Type 2, Type 2C, Type 1C, and Type 3 requirements.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class GeneralSeriesValidator(BaseValidator):
    """Validator for DICOM General Series Module (PS3.3 C.7.3.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Series Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 (required) elements
        GeneralSeriesValidator._validate_type_1_elements(dataset, result)
        
        # Validate Type 2 (required but may be empty) elements  
        GeneralSeriesValidator._validate_type_2_elements(dataset, result)
        
        # Validate Type 1C and 2C conditional requirements
        if config.validate_conditional_requirements:
            GeneralSeriesValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values against DICOM defined terms
        if config.check_enumerated_values:
            GeneralSeriesValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures and relationships
        if config.validate_sequences:
            GeneralSeriesValidator._validate_sequence_requirements(dataset, result)
        
        # Validate cross-field dependencies and relationships
        if config.validate_cross_field_dependencies:
            GeneralSeriesValidator._validate_cross_field_dependencies(dataset, result)
        
        # Validate specific module-level conditional requirements
        if config.validate_conditional_requirements:
            GeneralSeriesValidator._validate_module_conditional_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type_1_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements."""
        
        # Modality (0008,0060) - Type 1
        if not hasattr(dataset, 'Modality'):
            result.add_error(
                "Modality (0008,0060) is required (Type 1). "
                "Specify the type of equipment that originally acquired or produced the data. "
                "Use standard DICOM modality values (e.g., 'CT', 'MR', 'RTDOSE'). See DICOM PS3.3 C.*******.1."
            )
        
        # Series Instance UID (0020,000E) - Type 1
        if not hasattr(dataset, 'SeriesInstanceUID'):
            result.add_error(
                "Series Instance UID (0020,000E) is required (Type 1). "
                "Provide a unique identifier for this Series following DICOM UID construction rules. "
                "Format: root.suffix where root is your organization's registered UID. See DICOM PS3.5."
            )
    
    @staticmethod
    def _validate_type_2_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 (required but may be empty) elements."""
        
        # Series Number (0020,0011) - Type 2
        # Note: Type 2 elements must be present but can have empty values
        # We only warn if completely missing, as None/empty values are acceptable
        if not hasattr(dataset, 'SeriesNumber'):
            result.add_warning(
                "Series Number (0020,0011) should be present (Type 2). "
                "While empty values are permitted for Type 2 elements, providing a series number "
                "helps identify and organize the Series within a Study. Use integer values (e.g., 1, 2, 3)."
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 2C: Laterality required for paired body parts if Image/Frame/Measurement Laterality not present
        # This is a complex check that would require knowledge of the body part and other modules
        # For now, we'll just check if Laterality is present when it might be needed
        body_part = getattr(dataset, 'BodyPartExamined', '')
        # Comprehensive list of paired body parts from DICOM PS3.3 and clinical practice
        paired_body_parts = [
            'BREAST', 'EYE', 'KIDNEY', 'LUNG', 'OVARY', 'TESTIS', 
            'ARM', 'LEG', 'HAND', 'FOOT', 'ANKLE', 'KNEE', 'HIP',
            'SHOULDER', 'ELBOW', 'WRIST', 'EAR', 'TEMPORAL', 'ORBIT',
            'MANDIBLE', 'MAXILLA', 'TMJ', 'CLAVICLE', 'SCAPULA', 'RIB'
        ]
        
        if any(part.lower() in body_part.lower() for part in paired_body_parts):
            if not hasattr(dataset, 'Laterality'):
                result.add_warning(
                    f"Laterality (0020,0060) may be required for paired body part '{body_part}' examination "
                    "if Image Laterality (0020,0062), Frame Laterality (0020,0930), or Measurement Laterality "
                    "are not present elsewhere in the dataset. See DICOM PS3.3 C.7.3.1 for complete requirements."
                )
        
        # Type 2C: Patient Position required for specific SOP Classes
        sop_class_uid = getattr(dataset, 'SOPClassUID', '')
        required_sop_classes = [
            "1.2.840.10008.*******.1.2",    # CT Image Storage
            "1.2.840.10008.*******.1.4",    # MR Image Storage
            "1.2.840.10008.*******.1.2.1",  # Enhanced CT Image Storage
            "1.2.840.10008.*******.1.4.1",  # Enhanced MR Image Storage
            "1.2.840.10008.*******.1.4.3",  # Enhanced Color MR Image Storage
            "1.2.840.10008.*******.1.4.2"   # MR Spectroscopy Storage
        ]
        
        if sop_class_uid in required_sop_classes:
            has_patient_position = hasattr(dataset, 'PatientPosition')
            has_patient_orientation = hasattr(dataset, 'PatientOrientationCodeSequence')
            
            if not has_patient_position and not has_patient_orientation:
                result.add_error(
                    f"Patient Position (0018,5100) is required (Type 2C) for SOP Class '{sop_class_uid}' "
                    "when Patient Orientation Code Sequence (0054,0410) is not present. "
                    "Specify patient position relative to imaging equipment. See DICOM PS3.3 C.7.3.1."
                )
        
        # Type 1C: Anatomical Orientation Type required for non-human organisms with non-bipedal frame of reference
        is_non_human = (hasattr(dataset, 'PatientSpeciesDescription') or 
                       hasattr(dataset, 'PatientSpeciesCodeSequence'))
        
        if is_non_human and not hasattr(dataset, 'AnatomicalOrientationType'):
            result.add_warning(
                "Anatomical Orientation Type (0010,2210) is required (Type 1C) for non-human organisms "
                "with non-bipedal anatomical frame of reference. Must be 'BIPED' or 'QUADRUPED'. "
                "See DICOM PS3.3 C.7.3.1 for complete conditional requirements."
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Modality (0008,0060) - extensive validation against DICOM defined terms
        GeneralSeriesValidator._validate_modality_enumeration(dataset, result)
        
        # Laterality (0020,0060)
        laterality = getattr(dataset, 'Laterality', '')
        if laterality:
            BaseValidator.validate_enumerated_value(
                laterality, ["R", "L"],
                "Laterality (0020,0060)", result
            )
        
        # Anatomical Orientation Type (0010,2210)
        orientation_type = getattr(dataset, 'AnatomicalOrientationType', '')
        if orientation_type:
            BaseValidator.validate_enumerated_value(
                orientation_type, ["BIPED", "QUADRUPED"],
                "Anatomical Orientation Type (0010,2210)", result
            )
        
        # Patient Position (0018,5100) - extensive list of valid values
        patient_position = getattr(dataset, 'PatientPosition', '')
        if patient_position:
            valid_positions = [
                "HFP", "HFS", "HFDR", "HFDL", "HFV", "HFI",
                "FFDR", "FFDL", "FFP", "FFS", "FFV", "FFI",
                "LFP", "LFS", "LFDR", "LFDL",
                "RFP", "RFS", "RFDR", "RFDL",
                "AFP", "AFS", "AFDR", "AFDL",
                "PFP", "PFS", "PFDR", "PFDL"
            ]
            BaseValidator.validate_enumerated_value(
                patient_position, valid_positions,
                "Patient Position (0018,5100)", result
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Performing Physician Identification Sequence validation
        performing_phys_seq = getattr(dataset, 'PerformingPhysicianIdentificationSequence', [])
        performing_phys_name = getattr(dataset, 'PerformingPhysiciansName', '')
        if performing_phys_seq and performing_phys_name:
            # Check that number and order correspond if both are present
            performing_names = performing_phys_name.split('\\') if isinstance(performing_phys_name, str) else []
            if len(performing_phys_seq) != len(performing_names):
                result.add_warning(
                    "Performing Physician Identification Sequence (0008,1052): "
                    "Number of items should correspond to Performing Physician's Name (0008,1050)"
                )
        
        # Operator Identification Sequence validation
        operator_seq = getattr(dataset, 'OperatorIdentificationSequence', [])
        operator_name = getattr(dataset, 'OperatorsName', '')
        if operator_seq and operator_name:
            # Check that number and order correspond if both are present
            operator_names = operator_name.split('\\') if isinstance(operator_name, str) else []
            if len(operator_seq) != len(operator_names):
                result.add_warning(
                    "Operator Identification Sequence (0008,1072): "
                    "Number of items should correspond to Operators' Name (0008,1070)"
                )
        
        # Referenced Performed Procedure Step Sequence validation
        ref_pps_seq = getattr(dataset, 'ReferencedPerformedProcedureStepSequence', [])
        if len(ref_pps_seq) > 1:
            result.add_error(
                "Referenced Performed Procedure Step Sequence (0008,1111): "
                "Only a single Item is permitted in this Sequence per DICOM PS3.3 C.7.3.1. "
                f"Found {len(ref_pps_seq)} items. Remove excess items to comply with standard."
            )
        
        # Related Series Sequence validation
        related_series_seq = getattr(dataset, 'RelatedSeriesSequence', [])
        for i, item in enumerate(related_series_seq):
            if not item.get('StudyInstanceUID'):
                result.add_error(
                    f"Related Series Sequence item {i}: "
                    "Study Instance UID (0020,000D) is required"
                )
            if not item.get('SeriesInstanceUID'):
                result.add_error(
                    f"Related Series Sequence item {i}: "
                    "Series Instance UID (0020,000E) is required"
                )
        
        # Series Description Code Sequence validation
        series_desc_seq = getattr(dataset, 'SeriesDescriptionCodeSequence', [])
        if len(series_desc_seq) > 1:
            result.add_error(
                "Series Description Code Sequence (0008,103F): "
                "Only a single Item is permitted in this Sequence. "
                "Remove additional sequence items to comply with DICOM standard."
            )
        
        # Request Attributes Sequence validation (Type 3)
        request_attr_seq = getattr(dataset, 'RequestAttributesSequence', [])
        for i, item in enumerate(request_attr_seq):
            # Validate required sub-attributes within each sequence item
            if not item.get('ScheduledProcedureStepID'):
                result.add_error(
                    f"Request Attributes Sequence item {i}: "
                    "Scheduled Procedure Step ID (0040,0009) is required within sequence item. "
                    "See DICOM PS3.3 C.7.3.1 for Request Attributes Sequence requirements."
                )
            
            # Validate optional sub-attributes are properly formatted if present
            if hasattr(item, 'ScheduledProcedureStepDescription'):
                if not isinstance(item.ScheduledProcedureStepDescription, str):
                    result.add_warning(
                        f"Request Attributes Sequence item {i}: "
                        "Scheduled Procedure Step Description (0040,0007) should be a string value."
                    )
            
            if hasattr(item, 'RequestedProcedureID'):
                if not isinstance(item.RequestedProcedureID, str):
                    result.add_warning(
                        f"Request Attributes Sequence item {i}: "
                        "Requested Procedure ID (0040,1001) should be a string value."
                    )
            
            if hasattr(item, 'RequestedProcedureDescription'):
                if not isinstance(item.RequestedProcedureDescription, str):
                    result.add_warning(
                        f"Request Attributes Sequence item {i}: "
                        "Requested Procedure Description (0040,1002) should be a string value."
                    )
    
    @staticmethod
    def _validate_modality_enumeration(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Modality against comprehensive DICOM defined terms."""
        modality = getattr(dataset, 'Modality', '')
        if not modality:
            return
            
        # Comprehensive list of DICOM modality defined terms from PS3.3 C.*******.1
        valid_modalities = {
            # Current defined terms
            'ANN', 'AR', 'ASMT', 'AU', 'BDUS', 'BI', 'BMD', 'CFM', 'CR', 'CT',
            'CTPROTOCOL', 'DMS', 'DG', 'DOC', 'DX', 'ECG', 'EEG', 'EMG', 'EOG',
            'EPS', 'ES', 'FID', 'GM', 'HC', 'HD', 'IO', 'IOL', 'IVOCT', 'IVUS',
            'KER', 'KO', 'LEN', 'LS', 'MG', 'MR', 'M3D', 'NM', 'OAM', 'OCT',
            'OP', 'OPM', 'OPT', 'OPTBSV', 'OPTENF', 'OPV', 'OSS', 'OT', 'PA',
            'PLAN', 'POS', 'PR', 'PT', 'PX', 'REG', 'RESP', 'RF', 'RG', 'RTDOSE',
            'RTIMAGE', 'RTINTENT', 'RTPLAN', 'RTRAD', 'RTRECORD', 'RTSEGANN',
            'RTSTRUCT', 'RWV', 'SEG', 'SM', 'SMR', 'SR', 'SRF', 'STAIN',
            'TEXTUREMAP', 'TG', 'US', 'VA', 'XA', 'XAPROTOCOL', 'XC'
        }
        
        if modality not in valid_modalities:
            result.add_warning(
                f"Modality (0008,0060) value '{modality}' is not a recognized DICOM defined term. "
                "Refer to DICOM PS3.3 C.*******.1 for valid modality values. Common values include: "
                "'CT', 'MR', 'RTDOSE', 'RTSTRUCT', 'RTPLAN', 'RTIMAGE', 'US', 'XA', 'CR', 'DX'. "
                "Using non-standard values may cause interoperability issues with DICOM viewers and PACS systems."
            )
    
    @staticmethod
    def _validate_cross_field_dependencies(dataset: Dataset, result: ValidationResult) -> None:
        """Validate cross-field dependencies and logical relationships."""
        
        # Validate Treatment Session UID (Type 3) if present
        treatment_session_uid = getattr(dataset, 'TreatmentSessionUID', '')
        if treatment_session_uid:
            # Use base validator for UID format validation
            BaseValidator.validate_uid_format(
                uid=treatment_session_uid,
                field_name='Treatment Session UID (300A,0700)',
                result=result
            )
            
            # Additional context validation for RT modalities
            modality = getattr(dataset, 'Modality', '')
            rt_modalities = ['RTDOSE', 'RTIMAGE', 'RTPLAN', 'RTSTRUCT', 'RTRECORD']
            if modality not in rt_modalities:
                result.add_warning(
                    f"Treatment Session UID (300A,0700) is typically used with RT modalities "
                    f"but current modality is '{modality}'. Verify this is appropriate for your use case."
                )
        
        # Validate pixel value consistency
        smallest_pixel = getattr(dataset, 'SmallestPixelValueInSeries', None)
        largest_pixel = getattr(dataset, 'LargestPixelValueInSeries', None)
        
        if smallest_pixel is not None and largest_pixel is not None:
            try:
                if int(smallest_pixel) > int(largest_pixel):
                    result.add_error(
                        f"Smallest Pixel Value in Series ({smallest_pixel}) cannot be greater than "
                        f"Largest Pixel Value in Series ({largest_pixel})"
                    )
            except (ValueError, TypeError):
                result.add_warning(
                    "Pixel value elements should contain valid numeric values. "
                    "Ensure Smallest/Largest Pixel Value in Series contain integer values."
                )

    @staticmethod
    def _validate_module_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate module-specific conditional requirements that were moved from the module."""
        
        # Validate Patient Position Type 2C requirements
        GeneralSeriesValidator._validate_patient_position_type_2c(dataset, result)
        
        # Validate Laterality Type 2C requirements  
        GeneralSeriesValidator._validate_laterality_type_2c(dataset, result)
        
        # Validate Anatomical Orientation Type 1C requirements
        GeneralSeriesValidator._validate_anatomical_orientation_type_1c(dataset, result)
    
    @staticmethod
    def _validate_patient_position_type_2c(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Patient Position Type 2C conditional requirements."""
        sop_class_uid = getattr(dataset, 'SOPClassUID', '')
        if not sop_class_uid:
            return  # Cannot validate without SOP Class UID
            
        required_sop_classes = [
            "1.2.840.10008.*******.1.2",    # CT Image Storage
            "1.2.840.10008.*******.1.4",    # MR Image Storage
            "1.2.840.10008.*******.1.2.1",  # Enhanced CT Image Storage
            "1.2.840.10008.*******.1.4.1",  # Enhanced MR Image Storage
            "1.2.840.10008.*******.1.4.3",  # Enhanced Color MR Image Storage
            "1.2.840.10008.*******.1.4.2"   # MR Spectroscopy Storage
        ]
        
        if sop_class_uid in required_sop_classes:
            has_patient_orientation = hasattr(dataset, 'PatientOrientationCodeSequence')
            has_patient_position = hasattr(dataset, 'PatientPosition')
            
            if not has_patient_orientation and not has_patient_position:
                result.add_error(
                    "Patient Position (0018,5100) is required (Type 2C) for this SOP Class "
                    "when Patient Orientation Code Sequence (0054,0410) is not present. "
                    f"SOP Class UID: {sop_class_uid}. See DICOM PS3.3 C.7.3.1."
                )
    
    @staticmethod
    def _validate_laterality_type_2c(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Laterality Type 2C conditional requirements."""
        body_part = getattr(dataset, 'BodyPartExamined', '')
        if not body_part:
            return  # Cannot validate without body part
            
        # Comprehensive list of paired body parts as per DICOM standard
        paired_body_parts = {
            'BREAST', 'EYE', 'KIDNEY', 'LUNG', 'OVARY', 'TESTIS', 
            'ARM', 'LEG', 'HAND', 'FOOT', 'SHOULDER', 'ELBOW', 
            'WRIST', 'HIP', 'KNEE', 'ANKLE', 'EAR', 'ADRENAL',
            'UTERINE_TUBE', 'OVARIAN'
        }
        
        is_paired_structure = any(
            part.upper() in body_part.upper() or
            body_part.upper() in part
            for part in paired_body_parts
        )
        
        if is_paired_structure:
            # Check if other laterality attributes are present (from other modules)
            has_image_laterality = hasattr(dataset, 'ImageLaterality')
            has_frame_laterality = hasattr(dataset, 'FrameLaterality') 
            has_measurement_laterality = hasattr(dataset, 'MeasurementLaterality')
            has_laterality = hasattr(dataset, 'Laterality')
            
            if not (has_image_laterality or has_frame_laterality or has_measurement_laterality or has_laterality):
                result.add_error(
                    f"Laterality (0020,0060) is required (Type 2C) for paired body part '{body_part}'. "
                    "DICOM PS3.3 C.7.3.1 requires laterality when examining paired structures "
                    "and Image Laterality, Frame Laterality, or Measurement Laterality are not present."
                )
    
    @staticmethod
    def _validate_anatomical_orientation_type_1c(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Anatomical Orientation Type 1C conditional requirements."""
        # Check if this is for a non-human organism
        # This could be indicated by PatientSpeciesDescription or PatientSpeciesCodeSequence
        # or by a special attribute that might be set during module creation
        is_non_human = (
            hasattr(dataset, 'PatientSpeciesDescription') or 
            hasattr(dataset, 'PatientSpeciesCodeSequence')
        )
        
        # Note: In the original module code, is_non_human was passed as a parameter
        # Since we can't access that here, we infer it from other attributes
        # This is a limitation of moving validation to the validator
        
        if is_non_human and not hasattr(dataset, 'AnatomicalOrientationType'):
            result.add_error(
                "Anatomical Orientation Type (0010,2210) is required (Type 1C) "
                "for non-human organisms with non-bipedal anatomical frame of reference. "
                "DICOM PS3.3 C.7.3.1 requires this attribute for proper anatomical orientation."
            )
