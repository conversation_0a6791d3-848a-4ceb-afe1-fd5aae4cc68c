"""Cine Module DICOM validation - PS3.3 C.7.6.5"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import PreferredPlaybackSequencing, ChannelMode

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class CineValidator(BaseValidator):
    """Validator for DICOM Cine Module (PS3.3 C.7.6.5).

    Validates all aspects of Cine Module including:
    - Type 1C (conditionally required) elements
    - Type 2C (conditionally required but can be empty) elements
    - Type 3 (optional) elements
    - Enumerated value constraints
    - Sequence structure validation
    - Timing consistency validation

    Supports both pydicom Dataset and BaseModule instances for zero-copy validation.
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        The Cine Module has no Type 1 or Type 2 elements - all elements are either
        Type 1C (conditional) or Type 3 (optional).

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Empty result since no required elements exist
        """
        return ValidationResult()

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Frame Time (0018,1063) Type 1C - Required if Frame Increment Pointer points to it
        # Frame Time Vector (0018,1065) Type 1C - Required if Frame Increment Pointer points to it
        #
        # ARCHITECTURAL LIMITATION: We cannot fully validate these Type 1C requirements without
        # access to the Frame Increment Pointer (0028,0009) from the Multi-frame Module.
        # This is a cross-module dependency that requires framework-level validation.

        has_frame_time = 'FrameTime' in data
        has_frame_time_vector = 'FrameTimeVector' in data

        # Basic consistency check - warn if both are present
        if has_frame_time and has_frame_time_vector:
            result.add_warning(
                "Both Frame Time (0018,1063) and Frame Time Vector (0018,1065) are present. "
                "Only one should be used based on Frame Increment Pointer (0028,0009) from Multi-frame Module. "
                "This validation requires cross-module dependency checking."
            )

        # Document the validation limitation for end users only when conditional validation is enabled
        # and there are other attributes present (indicating this is not just an empty test dataset)
        if (not has_frame_time and not has_frame_time_vector and
            CineValidator._has_cine_related_attributes(data)):
            result.add_warning(
                "VALIDATION LIMITATION: Frame Time (0018,1063) or Frame Time Vector (0018,1065) may be required "
                "depending on Frame Increment Pointer (0028,0009) from Multi-frame Module. "
                "Complete Type 1C validation requires access to the Multi-frame Module."
            )

        # Validate Frame Time value
        if has_frame_time:
            try:
                frame_time = float(data.FrameTime)
                if frame_time <= 0:
                    result.add_error("Frame Time (0018,1063) must be positive")
            except (ValueError, TypeError):
                result.add_error("Frame Time (0018,1063) must be numeric")

        # Validate Frame Time Vector
        if has_frame_time_vector:
            # Check if it's a list, tuple, pydicom MultiValue, or single numeric value
            from pydicom.multival import MultiValue
            frame_time_vector = data.FrameTimeVector

            # Handle single value case (pydicom converts single-element lists to single values)
            if isinstance(frame_time_vector, (int, float)):
                frame_time_vector = [frame_time_vector]
            elif not isinstance(frame_time_vector, (list, tuple, MultiValue)):
                result.add_error("Frame Time Vector (0018,1065) must be a list of values")
                frame_time_vector = None

            if frame_time_vector is not None:
                try:
                    time_vector = [float(x) for x in frame_time_vector]
                    if len(time_vector) > 0 and time_vector[0] != 0:
                        result.add_warning(
                            "Frame Time Vector (0018,1065) first value should be 0 "
                            "(first frame always has time increment of 0)"
                        )

                    if any(t < 0 for t in time_vector):
                        result.add_error("Frame Time Vector (0018,1065) values must be non-negative")

                except (ValueError, TypeError):
                    result.add_error("Frame Time Vector (0018,1065) values must be numeric")

        # Type 2C: Multiplexed Audio Channels Description Code Sequence
        # Required if the Transfer Syntax used to encode the Multi-frame Image contains multiplexed audio channels
        # Since we don't have access to transfer syntax here, we can only validate structure if present
        if 'MultiplexedAudioChannelsDescriptionCodeSequence' in data:
            audio_sequence = data.MultiplexedAudioChannelsDescriptionCodeSequence
            # Check if it's a sequence-like object (list, tuple, or pydicom Sequence)
            from pydicom.sequence import Sequence
            if not isinstance(audio_sequence, (list, tuple, Sequence)):
                result.add_error(
                    "Multiplexed Audio Channels Description Code Sequence (003A,0300) must be a sequence"
                )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Preferred Playback Sequencing (0018,1244)
        if 'PreferredPlaybackSequencing' in data:
            valid_values = [str(e.value) for e in PreferredPlaybackSequencing]
            BaseValidator.validate_enumerated_value(
                str(data.PreferredPlaybackSequencing),
                valid_values,
                "Preferred Playback Sequencing (0018,1244)",
                result
            )

        # Channel Mode validation for any standalone instances (beyond sequence validation)
        # This complements the sequence-specific validation in validate_sequence_structures
        if 'ChannelMode' in data:
            valid_modes = [e.value for e in ChannelMode]
            BaseValidator.validate_enumerated_value(
                str(data.ChannelMode),
                valid_modes,
                "Channel Mode (003A,0302)",
                result
            )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Multiplexed Audio Channels Description Code Sequence validation
        if 'MultiplexedAudioChannelsDescriptionCodeSequence' in data:
            audio_sequence = data.MultiplexedAudioChannelsDescriptionCodeSequence

            # Check if it's a sequence-like object (list, tuple, or pydicom Sequence)
            from pydicom.sequence import Sequence
            if not isinstance(audio_sequence, (list, tuple, Sequence)):
                result.add_error(
                    "Multiplexed Audio Channels Description Code Sequence (003A,0300) must be a sequence"
                )
            else:
                CineValidator._validate_audio_channel_structure(audio_sequence, result)

        return result

    @staticmethod
    def validate_timing_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate timing-related attribute consistency.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for timing inconsistencies
        """
        result = ValidationResult()

        # Validate trim values
        if 'StartTrim' in data and 'StopTrim' in data:
            try:
                start_trim = int(data.StartTrim)
                stop_trim = int(data.StopTrim)

                if start_trim < 1:
                    result.add_error("Start Trim (0008,2142) must be >= 1 (frame numbers are 1-based)")

                if stop_trim < 1:
                    result.add_error("Stop Trim (0008,2143) must be >= 1 (frame numbers are 1-based)")

                if start_trim > stop_trim:
                    result.add_error(
                        f"Start Trim ({start_trim}) cannot be greater than Stop Trim ({stop_trim})"
                    )

            except (ValueError, TypeError):
                result.add_error("Start Trim and Stop Trim must be numeric")

        # Validate frame rates
        frame_rate_attrs = [
            ('RecommendedDisplayFrameRate', '0008,2144'),
            ('CineRate', '0018,0040')
        ]

        for attr_name, tag in frame_rate_attrs:
            if attr_name in data:
                try:
                    rate = float(getattr(data, attr_name))
                    if rate <= 0:
                        result.add_error(f"{attr_name} ({tag}) must be positive")
                    elif rate > 1000:
                        result.add_warning(
                            f"{attr_name} ({tag}) value {rate} seems unusually high for frame rate"
                        )
                except (ValueError, TypeError):
                    result.add_error(f"{attr_name} ({tag}) must be numeric")

        # Validate duration values
        duration_attrs = [
            ('FrameDelay', '0018,1066'),
            ('ImageTriggerDelay', '0018,1067'),
            ('EffectiveDuration', '0018,0072'),
            ('ActualFrameDuration', '0018,1242')
        ]

        for attr_name, tag in duration_attrs:
            if attr_name in data:
                try:
                    duration = float(getattr(data, attr_name))
                    if duration < 0:
                        result.add_error(f"{attr_name} ({tag}) must be non-negative")
                except (ValueError, TypeError):
                    result.add_error(f"{attr_name} ({tag}) must be numeric")

        # Cross-validation: Frame Time vs calculated rate
        if 'FrameTime' in data and 'CineRate' in data:
            try:
                frame_time = float(data.FrameTime)
                cine_rate = float(data.CineRate)

                if frame_time > 0 and cine_rate > 0:
                    calculated_rate = 1000.0 / frame_time  # Convert msec to frames/sec
                    rate_diff = abs(calculated_rate - cine_rate)

                    if rate_diff > 0.1:  # Allow small tolerance
                        result.add_warning(
                            f"Frame Time ({frame_time} msec) and Cine Rate ({cine_rate} fps) are inconsistent. "
                            f"Frame Time suggests {calculated_rate:.2f} fps."
                        )
            except (ValueError, TypeError):
                pass  # Numeric validation already handled above

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Comprehensive validation result with structured errors and warnings
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements (empty for Cine Module)
        result.merge(CineValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(CineValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(CineValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(CineValidator.validate_sequence_structures(data))

        # Always validate timing consistency
        result.merge(CineValidator.validate_timing_consistency(data))

        return result

    @staticmethod
    def _validate_audio_channel_structure(audio_sequence, result: ValidationResult) -> None:
        """Validate individual audio channel items in sequence."""
        for i, item in enumerate(audio_sequence):
            item_prefix = f"Audio channel item {i+1}"

            # Validate that item is a Dataset object
            from pydicom import Dataset
            if not isinstance(item, Dataset):
                result.add_error(f"{item_prefix} must be a Dataset object")
                continue

            # Required attributes in each item
            if 'ChannelIdentificationCode' not in item:
                result.add_error(
                    f"{item_prefix}: Channel Identification Code (003A,0301) is required"
                )
            else:
                try:
                    channel_id = int(item.ChannelIdentificationCode)
                    if channel_id < 1 or channel_id > 9:
                        result.add_warning(
                            f"{item_prefix}: Channel Identification Code should be 1-9 "
                            f"(1=main, 2=second, 3-9=complementary)"
                        )
                except (ValueError, TypeError):
                    result.add_error(
                        f"{item_prefix}: Channel Identification Code (003A,0301) must be numeric"
                    )

            if 'ChannelMode' not in item:
                result.add_error(
                    f"{item_prefix}: Channel Mode (003A,0302) is required"
                )
            else:
                valid_modes = [e.value for e in ChannelMode]
                if item.ChannelMode not in valid_modes:
                    result.add_error(
                        f"{item_prefix}: Channel Mode (003A,0302) has invalid value '{item.ChannelMode}'. "
                        f"Valid values: {valid_modes}"
                    )

            if 'ChannelSourceSequence' not in item:
                result.add_error(
                    f"{item_prefix}: Channel Source Sequence (003A,0208) is required"
                )
            else:
                source_seq = item.ChannelSourceSequence
                # Check if it's a sequence-like object and has exactly one item
                from pydicom.sequence import Sequence
                if not isinstance(source_seq, (list, tuple, Sequence)) or len(source_seq) != 1:
                    result.add_error(
                        f"{item_prefix}: Channel Source Sequence (003A,0208) must contain exactly one item"
                    )
                elif len(source_seq) == 1:
                    source_item = source_seq[0]
                    if 'CodeValue' not in source_item:
                        result.add_error(
                            f"{item_prefix}: Channel Source Sequence item missing Code Value"
                        )
                    if 'CodingSchemeDesignator' not in source_item:
                        result.add_error(
                            f"{item_prefix}: Channel Source Sequence item missing Coding Scheme Designator"
                        )

    @staticmethod
    def _has_cine_related_attributes(data: Union[Dataset, 'BaseModule']) -> bool:
        """Check if dataset has any cine-related attributes that would indicate real usage."""
        cine_attributes = [
            'PreferredPlaybackSequencing', 'StartTrim', 'StopTrim', 'RecommendedDisplayFrameRate',
            'CineRate', 'FrameDelay', 'ImageTriggerDelay', 'EffectiveDuration', 'ActualFrameDuration',
            'MultiplexedAudioChannelsDescriptionCodeSequence'
        ]
        return any(attr in data for attr in cine_attributes)

