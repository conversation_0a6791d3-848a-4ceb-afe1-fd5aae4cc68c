"""Multi-energy CT Image Module Validator - DICOM PS3.3 C.8.2.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import MultiEnergySourceTechnique, MultiEnergyDetectorType


class MultiEnergyCTImageValidator(BaseValidator):
    """Validator for Multi-energy CT Image Module (C.8.2.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Multi-energy CT Image Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 required elements
        MultiEnergyCTImageValidator._validate_required_elements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            MultiEnergyCTImageValidator._validate_sequence_structures(dataset, result, config)
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            MultiEnergyCTImageValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            MultiEnergyCTImageValidator._validate_enumerated_values(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate presence of Type 1 required elements."""
        
        # Multi-energy CT Acquisition Sequence (0018,9362) Type 1
        if (0x0018, 0x9362) not in dataset:
            result.add_error(
                "Multi-energy CT Acquisition Sequence (0018,9362) is required (Type 1)"
            )
    
    @staticmethod
    def _validate_sequence_structures(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate nested sequence structures and their required elements."""
        
        # Check if acquisition sequence exists and has content
        if (0x0018, 0x9362) not in dataset:
            return  # Already validated in required elements
        
        acq_seq = dataset[(0x0018, 0x9362)].value if (0x0018, 0x9362) in dataset else []
        if not acq_seq:
            result.add_error(
                "Multi-energy CT Acquisition Sequence (0018,9362) must contain at least one Item"
            )
            return
        
        for i, acq_item in enumerate(acq_seq):
            # Validate X-Ray Source Sequence (Type 1)
            source_seq = acq_item[(0x0018, 0x9365)].value if (0x0018, 0x9365) in acq_item else []
            if not source_seq:
                result.add_error(
                    f"Multi-energy CT X-Ray Source Sequence (0018,9365) is required "
                    f"in acquisition item {i+1} (Type 1)"
                )
            else:
                MultiEnergyCTImageValidator._validate_x_ray_source_sequence(
                    source_seq, i+1, result, config
                )
            
            # Validate X-Ray Detector Sequence (Type 1)
            detector_seq = acq_item[(0x0018, 0x936F)].value if (0x0018, 0x936F) in acq_item else []
            if not detector_seq:
                result.add_error(
                    f"Multi-energy CT X-Ray Detector Sequence (0018,936F) is required "
                    f"in acquisition item {i+1} (Type 1)"
                )
            else:
                MultiEnergyCTImageValidator._validate_x_ray_detector_sequence(
                    detector_seq, i+1, result, config
                )
            
            # Validate Path Sequence (Type 1)
            path_seq = acq_item[(0x0018, 0x9379)].value if (0x0018, 0x9379) in acq_item else []
            if not path_seq:
                result.add_error(
                    f"Multi-energy CT Path Sequence (0018,9379) is required "
                    f"in acquisition item {i+1} (Type 1)"
                )
            elif len(path_seq) < 2:
                result.add_error(
                    f"Multi-energy CT Path Sequence (0018,9379) must contain "
                    f"two or more Items in acquisition item {i+1}"
                )
            else:
                MultiEnergyCTImageValidator._validate_path_sequence(
                    path_seq, source_seq, detector_seq, i+1, result
                )
    
    @staticmethod
    def _validate_x_ray_source_sequence(source_seq: list, acq_index: int, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate X-Ray Source Sequence items."""
        
        for j, source_item in enumerate(source_seq):
            item_desc = f"X-Ray source item {j+1} in acquisition {acq_index}"
            
            # Type 1 required elements using DICOM tags
            required_tags = [
                ((0x0018, 0x9366), 'X-Ray Source Index (0018,9366)'),
                ((0x0018, 0x9367), 'X-Ray Source ID (0018,9367)'),
                ((0x0018, 0x9368), 'Multi-energy Source Technique (0018,9368)'),
                ((0x0018, 0x9369), 'Source Start DateTime (0018,9369)'),
                ((0x0018, 0x936A), 'Source End DateTime (0018,936A)')
            ]
            
            for tag, display_name in required_tags:
                if tag not in source_item:
                    result.add_error(f"{display_name} is required in {item_desc} (Type 1)")
            
            # Type 1C: Switching Phase Number required if technique is SWITCHING_SOURCE
            if config.validate_conditional_requirements:
                technique_tag = (0x0018, 0x9368)
                switching_phase_tag = (0x0018, 0x936B)
                if technique_tag in source_item:
                    technique = source_item[technique_tag].value
                    if technique == "SWITCHING_SOURCE":
                        if switching_phase_tag not in source_item:
                            result.add_error(
                                f"Switching Phase Number (0018,936B) is required in {item_desc} "
                                f"when Multi-energy Source Technique is SWITCHING_SOURCE (Type 1C)"
                            )
    
    @staticmethod
    def _validate_x_ray_detector_sequence(detector_seq: list, acq_index: int, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate X-Ray Detector Sequence items."""
        
        for j, detector_item in enumerate(detector_seq):
            item_desc = f"X-Ray detector item {j+1} in acquisition {acq_index}"
            
            # Type 1 required elements using DICOM tags
            required_tags = [
                ((0x0018, 0x9370), 'X-Ray Detector Index (0018,9370)'),
                ((0x0018, 0x9371), 'X-Ray Detector ID (0018,9371)'),
                ((0x0018, 0x9372), 'Multi-energy Detector Type (0018,9372)')
            ]
            
            for tag, display_name in required_tags:
                if tag not in detector_item:
                    result.add_error(f"{display_name} is required in {item_desc} (Type 1)")
            
            # Type 1C: Energy ranges required if detector type is PHOTON_COUNTING
            if config.validate_conditional_requirements:
                detector_type_tag = (0x0018, 0x9372)
                max_energy_tag = (0x0018, 0x9374)
                min_energy_tag = (0x0018, 0x9375)
                if detector_type_tag in detector_item:
                    detector_type = detector_item[detector_type_tag].value
                    if detector_type == "PHOTON_COUNTING":
                        if max_energy_tag not in detector_item:
                            result.add_error(
                                f"Nominal Max Energy (0018,9374) is required in {item_desc} "
                                f"when Multi-energy Detector Type is PHOTON_COUNTING (Type 1C)"
                            )
                        if min_energy_tag not in detector_item:
                            result.add_error(
                                f"Nominal Min Energy (0018,9375) is required in {item_desc} "
                                f"when Multi-energy Detector Type is PHOTON_COUNTING (Type 1C)"
                            )
    
    @staticmethod
    def _validate_path_sequence(path_seq: list, source_seq: list, detector_seq: list, 
                               acq_index: int, result: ValidationResult) -> None:
        """Validate Path Sequence items and their references."""
        
        # Collect valid source and detector indices using DICOM tags
        source_index_tag = (0x0018, 0x9366)
        detector_index_tag = (0x0018, 0x9370)
        valid_source_indices = {item[source_index_tag].value for item in source_seq if source_index_tag in item}
        valid_detector_indices = {item[detector_index_tag].value for item in detector_seq if detector_index_tag in item}
        
        for j, path_item in enumerate(path_seq):
            item_desc = f"Path item {j+1} in acquisition {acq_index}"
            
            # Type 1 required elements using DICOM tags
            required_tags = [
                ((0x0018, 0x937A), 'Multi-energy CT Path Index (0018,937A)'),
                ((0x0018, 0x9377), 'Referenced X-Ray Source Index (0018,9377)'),
                ((0x0018, 0x9376), 'Referenced X-Ray Detector Index (0018,9376)')
            ]
            
            for tag, display_name in required_tags:
                if tag not in path_item:
                    result.add_error(f"{display_name} is required in {item_desc} (Type 1)")
            
            # Validate references using DICOM tags
            ref_source_tag = (0x0018, 0x9377)
            ref_detector_tag = (0x0018, 0x9376)
            
            if ref_source_tag in path_item:
                ref_source_index = path_item[ref_source_tag].value
                if ref_source_index not in valid_source_indices:
                    result.add_error(
                        f"Referenced X-Ray Source Index {ref_source_index} in {item_desc} "
                        f"does not match any X-Ray Source Index in the source sequence"
                    )
            
            if ref_detector_tag in path_item:
                ref_detector_index = path_item[ref_detector_tag].value
                if ref_detector_index not in valid_detector_indices:
                    result.add_error(
                        f"Referenced X-Ray Detector Index {ref_detector_index} in {item_desc} "
                        f"does not match any X-Ray Detector Index in the detector sequence"
                    )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        # Already validated in sequence structure validation
        # Additional conditional logic can be added here if needed
        pass
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        acq_seq = dataset[(0x0018, 0x9362)].value if (0x0018, 0x9362) in dataset else []
        
        for i, acq_item in enumerate(acq_seq):
            # Validate source technique enumerated values using DICOM tags
            source_seq = acq_item[(0x0018, 0x9365)].value if (0x0018, 0x9365) in acq_item else []
            for j, source_item in enumerate(source_seq):
                technique_tag = (0x0018, 0x9368)
                if technique_tag in source_item:
                    technique = source_item[technique_tag].value
                    valid_techniques = [technique.value for technique in MultiEnergySourceTechnique]
                    BaseValidator.validate_enumerated_value(
                        technique, valid_techniques,
                        f"Multi-energy Source Technique (0018,9368) in source {j+1}, acquisition {i+1}",
                        result
                    )
            
            # Validate detector type enumerated values using DICOM tags
            detector_seq = acq_item[(0x0018, 0x936F)].value if (0x0018, 0x936F) in acq_item else []
            for j, detector_item in enumerate(detector_seq):
                detector_type_tag = (0x0018, 0x9372)
                if detector_type_tag in detector_item:
                    detector_type = detector_item[detector_type_tag].value
                    valid_detector_types = [detector.value for detector in MultiEnergyDetectorType]
                    BaseValidator.validate_enumerated_value(
                        detector_type, valid_detector_types,
                        f"Multi-energy Detector Type (0018,9372) in detector {j+1}, acquisition {i+1}",
                        result
                    )
