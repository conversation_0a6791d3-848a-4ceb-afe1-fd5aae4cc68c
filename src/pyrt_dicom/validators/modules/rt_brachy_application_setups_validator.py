"""
RT Brachy Application Setups Module DICOM validation - PS3.3 C.8.8.15

This validator implements comprehensive validation for the RT Brachy Application Setups Module
according to DICOM PS3.3 C.8.8.15 specification, including:
- Type 1/1C/2/2C/3 element validation
- Enumerated value validation
- Conditional logic validation
- Semantic consistency validation
- Cross-reference validation
"""

from datetime import datetime
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import (
    BrachyTreatmentTechnique, BrachyTreatmentType, ApplicationSetupType,
    SourceType, SourceMovementType, BrachyAccessoryDeviceType,
    SourceApplicatorType, SourceStrengthUnits
)


class RTBrachyApplicationSetupsValidator(BaseValidator):
    """
    Validator for DICOM RT Brachy Application Setups Module (PS3.3 C.8.8.15).

    This validator provides comprehensive validation including:
    - Required element presence validation (Type 1)
    - Conditional element validation (Type 1C/2C)
    - Enumerated value validation
    - Semantic consistency validation
    - Cross-reference validation
    - Value range validation
    """

    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """
        Validate RT Brachy Application Setups Module requirements on any pydicom Dataset.

        Performs comprehensive validation according to DICOM PS3.3 C.8.8.15 including:
        - Type 1 required element validation
        - Type 1C/2C conditional element validation
        - Enumerated value validation
        - Semantic consistency validation
        - Cross-reference validation

        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options

        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1 required elements
        RTBrachyApplicationSetupsValidator._validate_type1_elements(dataset, result)

        # Validate enumerated values
        RTBrachyApplicationSetupsValidator._validate_enumerated_values(dataset, result)

        # Validate conditional elements (Type 1C/2C)
        RTBrachyApplicationSetupsValidator._validate_conditional_elements(dataset, result)

        # Validate sequence structures
        if config.validate_sequences:
            RTBrachyApplicationSetupsValidator._validate_sequence_requirements(dataset, result)

        # Validate semantic consistency
        RTBrachyApplicationSetupsValidator._validate_semantic_consistency(dataset, result)

        # Validate cross-references
        RTBrachyApplicationSetupsValidator._validate_cross_references(dataset, result)

        return result
    
    @staticmethod
    def _validate_type1_elements(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate Type 1 (required) elements according to DICOM PS3.3 C.8.8.15.

        Type 1 elements must be present and have a non-empty value.
        """
        # Brachy Treatment Technique (300A,0200) - Type 1
        if not hasattr(dataset, 'BrachyTreatmentTechnique') or not dataset.BrachyTreatmentTechnique:
            result.add_error(
                "BrachyTreatmentTechnique (300A,0200) is required (Type 1) and must have a value"
            )

        # Brachy Treatment Type (300A,0202) - Type 1
        if not hasattr(dataset, 'BrachyTreatmentType') or not dataset.BrachyTreatmentType:
            result.add_error(
                "BrachyTreatmentType (300A,0202) is required (Type 1) and must have a value"
            )

        # Treatment Machine Sequence (300A,0206) - Type 1
        # Only a single Item shall be included in this Sequence
        treatment_machine_seq = getattr(dataset, 'TreatmentMachineSequence', None)
        if not treatment_machine_seq:
            result.add_error(
                "TreatmentMachineSequence (300A,0206) is required (Type 1)"
            )
        elif len(treatment_machine_seq) != 1:
            result.add_error(
                f"TreatmentMachineSequence (300A,0206) must contain exactly one item, found {len(treatment_machine_seq)}"
            )
        else:
            machine_item = treatment_machine_seq[0]
            # Treatment Machine Name (300A,00B2) - Type 2
            if not hasattr(machine_item, 'TreatmentMachineName'):
                result.add_error(
                    "TreatmentMachineName (300A,00B2) is required (Type 2) in Treatment Machine Sequence"
                )

        # Source Sequence (300A,0210) - Type 1
        # One or more Items shall be included in this Sequence
        source_seq = getattr(dataset, 'SourceSequence', None)
        if not source_seq:
            result.add_error(
                "SourceSequence (300A,0210) is required (Type 1)"
            )
        elif len(source_seq) == 0:
            result.add_error(
                "SourceSequence (300A,0210) must contain at least one item"
            )

        # Application Setup Sequence (300A,0230) - Type 1
        # One or more Items shall be included in this Sequence
        app_setup_seq = getattr(dataset, 'ApplicationSetupSequence', None)
        if not app_setup_seq:
            result.add_error(
                "ApplicationSetupSequence (300A,0230) is required (Type 1)"
            )
        elif len(app_setup_seq) == 0:
            result.add_error(
                "ApplicationSetupSequence (300A,0230) must contain at least one item"
            )

    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate enumerated values according to DICOM PS3.3 C.8.8.15.

        Checks that enumerated values match the defined terms in the standard.
        """
        # Brachy Treatment Technique (300A,0200) - Enumerated Values
        if hasattr(dataset, 'BrachyTreatmentTechnique'):
            technique = dataset.BrachyTreatmentTechnique
            valid_techniques = [e.value for e in BrachyTreatmentTechnique]
            if technique not in valid_techniques:
                result.add_error(
                    f"BrachyTreatmentTechnique (300A,0200) value '{technique}' is not a valid enumerated value. "
                    f"Valid values: {valid_techniques}"
                )

        # Brachy Treatment Type (300A,0202) - Defined Terms
        if hasattr(dataset, 'BrachyTreatmentType'):
            treatment_type = dataset.BrachyTreatmentType
            valid_types = [e.value for e in BrachyTreatmentType]
            if treatment_type not in valid_types:
                result.add_error(
                    f"BrachyTreatmentType (300A,0202) value '{treatment_type}' is not a valid defined term. "
                    f"Valid values: {valid_types}"
                )

        # Validate Source Sequence enumerated values
        source_seq = getattr(dataset, 'SourceSequence', [])
        for i, source_item in enumerate(source_seq):
            # Source Type (300A,0214) - Defined Terms
            if hasattr(source_item, 'SourceType'):
                source_type = source_item.SourceType
                valid_source_types = [e.value for e in SourceType]
                if source_type not in valid_source_types:
                    result.add_error(
                        f"SourceSequence[{i}].SourceType (300A,0214) value '{source_type}' is not valid. "
                        f"Valid values: {valid_source_types}"
                    )

            # Source Strength Units (300A,0229) - Enumerated Values (if present)
            if hasattr(source_item, 'SourceStrengthUnits'):
                units = source_item.SourceStrengthUnits
                valid_units = [e.value for e in SourceStrengthUnits]
                if units not in valid_units:
                    result.add_error(
                        f"SourceSequence[{i}].SourceStrengthUnits (300A,0229) value '{units}' is not valid. "
                        f"Valid values: {valid_units}"
                    )

        # Validate Application Setup Sequence enumerated values
        app_setup_seq = getattr(dataset, 'ApplicationSetupSequence', [])
        for i, setup_item in enumerate(app_setup_seq):
            # Application Setup Type (300A,0232) - Defined Terms
            if hasattr(setup_item, 'ApplicationSetupType'):
                setup_type = setup_item.ApplicationSetupType
                valid_setup_types = [e.value for e in ApplicationSetupType]
                if setup_type not in valid_setup_types:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ApplicationSetupType (300A,0232) value '{setup_type}' is not valid. "
                        f"Valid values: {valid_setup_types}"
                    )

            # Validate Brachy Accessory Device Sequence enumerated values
            accessory_seq = getattr(setup_item, 'BrachyAccessoryDeviceSequence', [])
            for j, accessory_item in enumerate(accessory_seq):
                if hasattr(accessory_item, 'BrachyAccessoryDeviceType'):
                    device_type = accessory_item.BrachyAccessoryDeviceType
                    valid_device_types = [e.value for e in BrachyAccessoryDeviceType]
                    if device_type not in valid_device_types:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].BrachyAccessoryDeviceSequence[{j}]."
                            f"BrachyAccessoryDeviceType (300A,0264) value '{device_type}' is not valid. "
                            f"Valid values: {valid_device_types}"
                        )

            # Validate Channel Sequence enumerated values
            channel_seq = getattr(setup_item, 'ChannelSequence', [])
            for j, channel_item in enumerate(channel_seq):
                # Source Movement Type (300A,0288) - Defined Terms
                if hasattr(channel_item, 'SourceMovementType'):
                    movement_type = channel_item.SourceMovementType
                    valid_movement_types = [e.value for e in SourceMovementType]
                    if movement_type not in valid_movement_types:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}]."
                            f"SourceMovementType (300A,0288) value '{movement_type}' is not valid. "
                            f"Valid values: {valid_movement_types}"
                        )

                # Source Applicator Type (300A,0292) - Defined Terms (if present)
                if hasattr(channel_item, 'SourceApplicatorType'):
                    applicator_type = channel_item.SourceApplicatorType
                    valid_applicator_types = [e.value for e in SourceApplicatorType]
                    if applicator_type not in valid_applicator_types:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}]."
                            f"SourceApplicatorType (300A,0292) value '{applicator_type}' is not valid. "
                            f"Valid values: {valid_applicator_types}"
                        )

    @staticmethod
    def _validate_conditional_elements(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate Type 1C and Type 2C conditional elements according to DICOM PS3.3 C.8.8.15.

        Type 1C: Required if condition is met, must have value
        Type 2C: Required if condition is met, may have empty value
        """
        # Check if this is a PDR treatment for conditional validation
        is_pdr_treatment = (hasattr(dataset, 'BrachyTreatmentType') and
                           dataset.BrachyTreatmentType == BrachyTreatmentType.PDR.value)

        # Validate Source Sequence conditional elements
        source_seq = getattr(dataset, 'SourceSequence', [])
        for i, source_item in enumerate(source_seq):
            # Source Strength Units (300A,0229) - Type 1C
            # Required if the source is not a gamma-emitting (photon) source
            has_source_strength_units = hasattr(source_item, 'SourceStrengthUnits')
            has_source_strength = hasattr(source_item, 'SourceStrength')

            # Source Strength (300A,022B) - Type 1C
            # Required if the source is not a gamma-emitting (photon) source
            if has_source_strength_units and not has_source_strength:
                result.add_error(
                    f"SourceSequence[{i}].SourceStrength (300A,022B) is required (Type 1C) "
                    "when SourceStrengthUnits is present"
                )
            elif has_source_strength and not has_source_strength_units:
                result.add_error(
                    f"SourceSequence[{i}].SourceStrengthUnits (300A,0229) is required (Type 1C) "
                    "when SourceStrength is present"
                )

        # Validate Application Setup Sequence conditional elements
        app_setup_seq = getattr(dataset, 'ApplicationSetupSequence', [])
        for i, setup_item in enumerate(app_setup_seq):
            # Validate Channel Sequence conditional elements
            channel_seq = getattr(setup_item, 'ChannelSequence', [])
            for j, channel_item in enumerate(channel_seq):
                # Number of Pulses (300A,028A) - Type 1C
                # Required if Brachy Treatment Type is PDR
                if is_pdr_treatment and not hasattr(channel_item, 'NumberOfPulses'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfPulses (300A,028A) "
                        "is required (Type 1C) when Brachy Treatment Type is PDR"
                    )

                # Pulse Repetition Interval (300A,028C) - Type 1C
                # Required if Brachy Treatment Type is PDR
                if is_pdr_treatment and not hasattr(channel_item, 'PulseRepetitionInterval'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].PulseRepetitionInterval (300A,028C) "
                        "is required (Type 1C) when Brachy Treatment Type is PDR"
                    )

                # Source Applicator Step Size (300A,02A0) - Type 1C
                # Required if Source Movement Type is STEPWISE
                if (hasattr(channel_item, 'SourceMovementType') and
                    channel_item.SourceMovementType == SourceMovementType.STEPWISE.value and
                    not hasattr(channel_item, 'SourceApplicatorStepSize')):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorStepSize (300A,02A0) "
                        "is required (Type 1C) when Source Movement Type is STEPWISE"
                    )

                # Source Applicator ID (300A,0291) - Type 2C
                # Required if Source Applicator Number is present
                has_applicator_number = hasattr(channel_item, 'SourceApplicatorNumber')
                if has_applicator_number and not hasattr(channel_item, 'SourceApplicatorID'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorID (300A,0291) "
                        "is required (Type 2C) when Source Applicator Number is present"
                    )

                # Source Applicator Type (300A,0292) - Type 1C
                # Required if Source Applicator Number is present
                if has_applicator_number and not hasattr(channel_item, 'SourceApplicatorType'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorType (300A,0292) "
                        "is required (Type 1C) when Source Applicator Number is present"
                    )

                # Source Applicator Length (300A,0296) - Type 1C
                # Required if Source Applicator Number is present
                if has_applicator_number and not hasattr(channel_item, 'SourceApplicatorLength'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorLength (300A,0296) "
                        "is required (Type 1C) when Source Applicator Number is present"
                    )

                # Source Applicator Tip Length (300A,0274) - Type 2C
                # Required if Channel Effective Length is present
                has_effective_length = hasattr(channel_item, 'ChannelEffectiveLength')
                if has_effective_length and not hasattr(channel_item, 'SourceApplicatorTipLength'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceApplicatorTipLength (300A,0274) "
                        "is required (Type 2C) when Channel Effective Length is present"
                    )

                # Channel Inner Length (300A,0272) - Type 2C
                # Required if Channel Effective Length is present
                if has_effective_length and not hasattr(channel_item, 'ChannelInnerLength'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelInnerLength (300A,0272) "
                        "is required (Type 2C) when Channel Effective Length is present"
                    )

                # Referenced ROI Number (3006,0084) - Type 2C
                # Required if Source Applicator Number is present
                if has_applicator_number and not hasattr(channel_item, 'ReferencedROINumber'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ReferencedROINumber (3006,0084) "
                        "is required (Type 2C) when Source Applicator Number is present"
                    )

                # Transfer Tube Length (300A,02A4) - Type 2C
                # Required if Transfer Tube Number is non-null
                has_transfer_tube = (hasattr(channel_item, 'TransferTubeNumber') and
                                   channel_item.TransferTubeNumber is not None)
                if has_transfer_tube and not hasattr(channel_item, 'TransferTubeLength'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].TransferTubeLength (300A,02A4) "
                        "is required (Type 2C) when Transfer Tube Number is present"
                    )

                # Final Cumulative Time Weight (300A,02C8) - Type 1C
                # Required if Cumulative Time Weight is non-null in Control Points
                control_points = getattr(channel_item, 'BrachyControlPointSequence', [])
                has_cumulative_time_weight = any(
                    hasattr(cp, 'CumulativeTimeWeight') for cp in control_points
                )
                if has_cumulative_time_weight and not hasattr(channel_item, 'FinalCumulativeTimeWeight'):
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].FinalCumulativeTimeWeight (300A,02C8) "
                        "is required (Type 1C) when Cumulative Time Weight is present in Control Points"
                    )
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate sequence structure requirements and Type 1 elements within sequences.
        """
        # Validate Source Sequence Type 1 elements
        source_seq = getattr(dataset, 'SourceSequence', [])
        for i, source_item in enumerate(source_seq):
            # Source Number (300A,0212) - Type 1
            if not hasattr(source_item, 'SourceNumber') or source_item.SourceNumber is None:
                result.add_error(
                    f"SourceSequence[{i}].SourceNumber (300A,0212) is required (Type 1)"
                )

            # Source Type (300A,0214) - Type 1
            if not hasattr(source_item, 'SourceType') or not source_item.SourceType:
                result.add_error(
                    f"SourceSequence[{i}].SourceType (300A,0214) is required (Type 1)"
                )

            # Source Isotope Name (300A,0226) - Type 1
            if not hasattr(source_item, 'SourceIsotopeName') or not source_item.SourceIsotopeName:
                result.add_error(
                    f"SourceSequence[{i}].SourceIsotopeName (300A,0226) is required (Type 1)"
                )

            # Source Isotope Half Life (300A,0228) - Type 1
            if not hasattr(source_item, 'SourceIsotopeHalfLife') or source_item.SourceIsotopeHalfLife is None:
                result.add_error(
                    f"SourceSequence[{i}].SourceIsotopeHalfLife (300A,0228) is required (Type 1)"
                )

            # Reference Air Kerma Rate (300A,022A) - Type 1
            if not hasattr(source_item, 'ReferenceAirKermaRate') or source_item.ReferenceAirKermaRate is None:
                result.add_error(
                    f"SourceSequence[{i}].ReferenceAirKermaRate (300A,022A) is required (Type 1)"
                )

            # Source Strength Reference Date (300A,022C) - Type 1
            if not hasattr(source_item, 'SourceStrengthReferenceDate') or not source_item.SourceStrengthReferenceDate:
                result.add_error(
                    f"SourceSequence[{i}].SourceStrengthReferenceDate (300A,022C) is required (Type 1)"
                )

            # Source Strength Reference Time (300A,022E) - Type 1
            if not hasattr(source_item, 'SourceStrengthReferenceTime') or not source_item.SourceStrengthReferenceTime:
                result.add_error(
                    f"SourceSequence[{i}].SourceStrengthReferenceTime (300A,022E) is required (Type 1)"
                )

        # Validate Application Setup Sequence Type 1 elements
        app_setup_seq = getattr(dataset, 'ApplicationSetupSequence', [])
        for i, setup_item in enumerate(app_setup_seq):
            # Application Setup Type (300A,0232) - Type 1
            if not hasattr(setup_item, 'ApplicationSetupType') or not setup_item.ApplicationSetupType:
                result.add_error(
                    f"ApplicationSetupSequence[{i}].ApplicationSetupType (300A,0232) is required (Type 1)"
                )

            # Application Setup Number (300A,0234) - Type 1
            if not hasattr(setup_item, 'ApplicationSetupNumber') or setup_item.ApplicationSetupNumber is None:
                result.add_error(
                    f"ApplicationSetupSequence[{i}].ApplicationSetupNumber (300A,0234) is required (Type 1)"
                )

            # Total Reference Air Kerma (300A,0250) - Type 1
            if not hasattr(setup_item, 'TotalReferenceAirKerma') or setup_item.TotalReferenceAirKerma is None:
                result.add_error(
                    f"ApplicationSetupSequence[{i}].TotalReferenceAirKerma (300A,0250) is required (Type 1)"
                )

            # Channel Sequence (300A,0280) - Type 1
            # One or more Items shall be included in this Sequence
            channel_seq = getattr(setup_item, 'ChannelSequence', None)
            if not channel_seq:
                result.add_error(
                    f"ApplicationSetupSequence[{i}].ChannelSequence (300A,0280) is required (Type 1)"
                )
            elif len(channel_seq) == 0:
                result.add_error(
                    f"ApplicationSetupSequence[{i}].ChannelSequence (300A,0280) must contain at least one item"
                )
            else:
                # Validate Channel Sequence Type 1 elements
                for j, channel_item in enumerate(channel_seq):
                    # Channel Number (300A,0282) - Type 1
                    if not hasattr(channel_item, 'ChannelNumber') or channel_item.ChannelNumber is None:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelNumber (300A,0282) is required (Type 1)"
                        )

                    # Channel Total Time (300A,0286) - Type 1
                    if not hasattr(channel_item, 'ChannelTotalTime') or channel_item.ChannelTotalTime is None:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelTotalTime (300A,0286) is required (Type 1)"
                        )

                    # Source Movement Type (300A,0288) - Type 1
                    if not hasattr(channel_item, 'SourceMovementType') or not channel_item.SourceMovementType:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].SourceMovementType (300A,0288) is required (Type 1)"
                        )

                    # Referenced Source Number (300C,000E) - Type 1
                    if not hasattr(channel_item, 'ReferencedSourceNumber') or channel_item.ReferencedSourceNumber is None:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ReferencedSourceNumber (300C,000E) is required (Type 1)"
                        )

                    # Number of Control Points (300A,0110) - Type 1
                    if not hasattr(channel_item, 'NumberOfControlPoints') or channel_item.NumberOfControlPoints is None:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfControlPoints (300A,0110) is required (Type 1)"
                        )

                    # Brachy Control Point Sequence (300A,02D0) - Type 1
                    control_point_seq = getattr(channel_item, 'BrachyControlPointSequence', None)
                    if not control_point_seq:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].BrachyControlPointSequence (300A,02D0) is required (Type 1)"
                        )
                    elif len(control_point_seq) == 0:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].BrachyControlPointSequence (300A,02D0) must contain at least one item"
                        )

                    # Channel Length (300A,0284) - Type 2
                    if not hasattr(channel_item, 'ChannelLength'):
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelLength (300A,0284) is required (Type 2)"
                        )

                    # Transfer Tube Number (300A,02A2) - Type 2
                    if not hasattr(channel_item, 'TransferTubeNumber'):
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].TransferTubeNumber (300A,02A2) is required (Type 2)"
                        )

    @staticmethod
    def _validate_semantic_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate semantic consistency and logical constraints.
        """
        # Validate uniqueness constraints
        RTBrachyApplicationSetupsValidator._validate_uniqueness_constraints(dataset, result)

        # Validate value ranges and logical constraints
        RTBrachyApplicationSetupsValidator._validate_value_ranges(dataset, result)

        # Validate date/time consistency
        RTBrachyApplicationSetupsValidator._validate_datetime_consistency(dataset, result)

    @staticmethod
    def _validate_uniqueness_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate uniqueness constraints within sequences."""
        # Validate Source Number uniqueness within Source Sequence
        source_seq = getattr(dataset, 'SourceSequence', [])
        source_numbers = []
        for i, source_item in enumerate(source_seq):
            if hasattr(source_item, 'SourceNumber'):
                source_number = source_item.SourceNumber
                if source_number in source_numbers:
                    result.add_error(
                        f"SourceSequence[{i}].SourceNumber ({source_number}) must be unique within the RT Plan"
                    )
                else:
                    source_numbers.append(source_number)

        # Validate Application Setup Number uniqueness within Application Setup Sequence
        app_setup_seq = getattr(dataset, 'ApplicationSetupSequence', [])
        setup_numbers = []
        for i, setup_item in enumerate(app_setup_seq):
            if hasattr(setup_item, 'ApplicationSetupNumber'):
                setup_number = setup_item.ApplicationSetupNumber
                if setup_number in setup_numbers:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].ApplicationSetupNumber ({setup_number}) must be unique within the RT Plan"
                    )
                else:
                    setup_numbers.append(setup_number)

            # Validate Channel Number uniqueness within each Application Setup
            channel_seq = getattr(setup_item, 'ChannelSequence', [])
            channel_numbers = []
            for j, channel_item in enumerate(channel_seq):
                if hasattr(channel_item, 'ChannelNumber'):
                    channel_number = channel_item.ChannelNumber
                    if channel_number in channel_numbers:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelNumber ({channel_number}) "
                            "must be unique within the Application Setup"
                        )
                    else:
                        channel_numbers.append(channel_number)

    @staticmethod
    def _validate_value_ranges(dataset: Dataset, result: ValidationResult) -> None:
        """Validate value ranges and logical constraints."""
        # Validate Source Sequence value ranges
        source_seq = getattr(dataset, 'SourceSequence', [])
        for i, source_item in enumerate(source_seq):
            # Source Isotope Half Life must be positive
            if hasattr(source_item, 'SourceIsotopeHalfLife'):
                half_life = source_item.SourceIsotopeHalfLife
                if half_life <= 0:
                    result.add_error(
                        f"SourceSequence[{i}].SourceIsotopeHalfLife ({half_life}) must be positive"
                    )

            # Reference Air Kerma Rate must be non-negative (zero for non-gamma sources)
            if hasattr(source_item, 'ReferenceAirKermaRate'):
                kerma_rate = source_item.ReferenceAirKermaRate
                if kerma_rate < 0:
                    result.add_error(
                        f"SourceSequence[{i}].ReferenceAirKermaRate ({kerma_rate}) must be non-negative"
                    )

            # Source Encapsulation Nominal Transmission must be between 0 and 1
            if hasattr(source_item, 'SourceEncapsulationNominalTransmission'):
                transmission = source_item.SourceEncapsulationNominalTransmission
                if transmission < 0 or transmission > 1:
                    result.add_error(
                        f"SourceSequence[{i}].SourceEncapsulationNominalTransmission ({transmission}) "
                        "must be between 0 and 1"
                    )

            # Active Source Diameter and Length must be positive if present
            if hasattr(source_item, 'ActiveSourceDiameter'):
                diameter = source_item.ActiveSourceDiameter
                if diameter <= 0:
                    result.add_error(
                        f"SourceSequence[{i}].ActiveSourceDiameter ({diameter}) must be positive"
                    )

            if hasattr(source_item, 'ActiveSourceLength'):
                length = source_item.ActiveSourceLength
                if length <= 0:
                    result.add_error(
                        f"SourceSequence[{i}].ActiveSourceLength ({length}) must be positive"
                    )

        # Validate Application Setup Sequence value ranges
        app_setup_seq = getattr(dataset, 'ApplicationSetupSequence', [])
        for i, setup_item in enumerate(app_setup_seq):
            # Total Reference Air Kerma must be non-negative
            if hasattr(setup_item, 'TotalReferenceAirKerma'):
                total_kerma = setup_item.TotalReferenceAirKerma
                if total_kerma < 0:
                    result.add_error(
                        f"ApplicationSetupSequence[{i}].TotalReferenceAirKerma ({total_kerma}) must be non-negative"
                    )

            # Validate Channel Sequence value ranges
            channel_seq = getattr(setup_item, 'ChannelSequence', [])
            for j, channel_item in enumerate(channel_seq):
                # Channel Total Time must be positive
                if hasattr(channel_item, 'ChannelTotalTime'):
                    total_time = channel_item.ChannelTotalTime
                    if total_time <= 0:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelTotalTime ({total_time}) "
                            "must be positive"
                        )

                # Number of Control Points must be positive
                if hasattr(channel_item, 'NumberOfControlPoints'):
                    num_points = channel_item.NumberOfControlPoints
                    if num_points <= 0:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfControlPoints ({num_points}) "
                            "must be positive"
                        )

                # Channel Length must be positive if present
                if hasattr(channel_item, 'ChannelLength'):
                    length = channel_item.ChannelLength
                    if length <= 0:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ChannelLength ({length}) "
                            "must be positive"
                        )

    @staticmethod
    def _validate_datetime_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate date/time consistency and format."""
        source_seq = getattr(dataset, 'SourceSequence', [])
        for i, source_item in enumerate(source_seq):
            # Validate Source Strength Reference Date format (YYYYMMDD)
            if hasattr(source_item, 'SourceStrengthReferenceDate'):
                date_str = source_item.SourceStrengthReferenceDate
                try:
                    datetime.strptime(date_str, '%Y%m%d')
                except ValueError:
                    result.add_error(
                        f"SourceSequence[{i}].SourceStrengthReferenceDate ('{date_str}') "
                        "must be in YYYYMMDD format"
                    )

            # Validate Source Strength Reference Time format (HHMMSS or HHMMSS.FFFFFF)
            if hasattr(source_item, 'SourceStrengthReferenceTime'):
                time_str = source_item.SourceStrengthReferenceTime
                valid_time = False
                for time_format in ['%H%M%S', '%H%M%S.%f']:
                    try:
                        datetime.strptime(time_str, time_format)
                        valid_time = True
                        break
                    except ValueError:
                        continue
                if not valid_time:
                    result.add_error(
                        f"SourceSequence[{i}].SourceStrengthReferenceTime ('{time_str}') "
                        "must be in HHMMSS or HHMMSS.FFFFFF format"
                    )

    @staticmethod
    def _validate_cross_references(dataset: Dataset, result: ValidationResult) -> None:
        """
        Validate cross-references between sequences and elements.
        """
        # Get available source numbers for reference validation
        source_seq = getattr(dataset, 'SourceSequence', [])
        available_source_numbers = set()
        for source_item in source_seq:
            if hasattr(source_item, 'SourceNumber'):
                available_source_numbers.add(source_item.SourceNumber)

        # Validate Referenced Source Number in Channel Sequence
        app_setup_seq = getattr(dataset, 'ApplicationSetupSequence', [])
        for i, setup_item in enumerate(app_setup_seq):
            channel_seq = getattr(setup_item, 'ChannelSequence', [])
            for j, channel_item in enumerate(channel_seq):
                if hasattr(channel_item, 'ReferencedSourceNumber'):
                    ref_source_num = channel_item.ReferencedSourceNumber
                    if ref_source_num not in available_source_numbers:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].ReferencedSourceNumber ({ref_source_num}) "
                            f"does not reference a valid Source Number. Available: {sorted(available_source_numbers)}"
                        )

                # Validate Number of Control Points matches actual sequence length
                if (hasattr(channel_item, 'NumberOfControlPoints') and
                    hasattr(channel_item, 'BrachyControlPointSequence')):
                    declared_count = channel_item.NumberOfControlPoints
                    actual_count = len(channel_item.BrachyControlPointSequence)
                    if declared_count != actual_count:
                        result.add_error(
                            f"ApplicationSetupSequence[{i}].ChannelSequence[{j}].NumberOfControlPoints ({declared_count}) "
                            f"does not match actual BrachyControlPointSequence length ({actual_count})"
                        )
