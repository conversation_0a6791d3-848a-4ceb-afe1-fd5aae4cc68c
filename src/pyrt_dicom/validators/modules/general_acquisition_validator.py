"""General Acquisition Module Validator - DICOM PS3.3 C.7.10.1

This validator provides comprehensive validation for the General Acquisition Module,
ensuring compliance with DICOM PS3.3 C.7.10.1 specifications.

Key validation areas:
- UID format compliance (Acquisition UID, Irradiation Event UID)
- Date/time format validation (DA, TM, DT formats)
- Numeric range validation (duration, image counts)
- Logical consistency between related attributes
- VM 1-n support for Irradiation Event UID

All elements in this module are Type 3 (optional), so validation focuses on
format correctness and semantic consistency when elements are present.
"""

from datetime import datetime
import re
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class GeneralAcquisitionValidator(BaseValidator):
    """Validator for General Acquisition Module (C.7.10.1).
    
    Validates all aspects of the General Acquisition Module including:
    - UID format compliance for single and multiple values
    - Date/time format validation with semantic checks
    - Numeric range validation with practical limits
    - Logical consistency between acquisition timing attributes
    - Irradiation event context guidance per DICOM PS3.3 C.********.1
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Acquisition Module requirements on any pydicom Dataset.
        
        Performs comprehensive validation of all General Acquisition Module elements
        per DICOM PS3.3 C.7.10.1. Since all elements are Type 3 (optional), validation
        focuses on format correctness and semantic consistency when elements are present.
        
        Validation includes:
        - UID format validation (length, character set, structure)
        - Date/time format validation (DICOM DA, TM, DT formats)  
        - Numeric range validation (positive values, practical limits)
        - Logical consistency between related timing attributes
        - Irradiation event context guidance for complex scenarios
        
        Args:
            dataset: pydicom Dataset to validate (typically from module.to_dataset())
            config: Optional validation configuration for behavior control
            
        Returns:
            ValidationResult: Comprehensive validation results with structured
                             errors and warnings including DICOM tag references
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Early return if dataset is None or invalid
        if dataset is None:
            result.add_error("Dataset cannot be None for General Acquisition Module validation")
            return result
        
        # Validate UID formats (Acquisition UID, Irradiation Event UID)
        GeneralAcquisitionValidator._validate_uid_formats(dataset, result)
        
        # Validate date/time formats (Date, Time, DateTime)
        GeneralAcquisitionValidator._validate_datetime_formats(dataset, result)
        
        # Validate numeric ranges (Duration, Image Count)
        GeneralAcquisitionValidator._validate_numeric_ranges(dataset, result)
        
        # Validate logical consistency between related attributes
        GeneralAcquisitionValidator._validate_logical_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_uid_formats(dataset: Dataset, result: ValidationResult) -> None:
        """Validate UID format compliance per DICOM PS3.3 specifications.
        
        Validates:
        - Acquisition UID (0008,0017) - Type 3, UI VR
        - Irradiation Event UID (0008,3010) - Type 3, UI VR, VM 1-n
        """
        # Acquisition UID (0008,0017) - Type 3
        acquisition_uid = getattr(dataset, 'AcquisitionUID', '')
        if acquisition_uid:
            GeneralAcquisitionValidator._validate_single_uid(
                acquisition_uid, "Acquisition UID (0008,0017)", result
            )
        
        # Irradiation Event UID (0008,3010) - Type 3, VM 1-n
        if hasattr(dataset, 'IrradiationEventUID'):
            irradiation_uid = getattr(dataset, 'IrradiationEventUID')
            # Handle VM 1-n: can be single string, list, or pydicom MultiValue
            try:
                # Check if it's iterable (list, MultiValue) but not a string
                if hasattr(irradiation_uid, '__iter__') and not isinstance(irradiation_uid, str):
                    # Multiple UIDs case (list or MultiValue)
                    if len(irradiation_uid) == 0:
                        result.add_error(
                            "Irradiation Event UID (0008,3010) list cannot be empty. "
                            "Per DICOM VM 1-n requirement, at least one UID must be provided when attribute is present."
                        )
                    else:
                        for i, uid in enumerate(irradiation_uid):
                            GeneralAcquisitionValidator._validate_single_uid(
                                uid, f"Irradiation Event UID[{i}] (0008,3010)", result
                            )
                else:
                    # Single UID case
                    GeneralAcquisitionValidator._validate_single_uid(
                        irradiation_uid, "Irradiation Event UID (0008,3010)", result
                    )
            except Exception as e:
                # Fallback: treat as single UID and provide helpful error context
                result.add_warning(
                    f"Irradiation Event UID (0008,3010) format could not be determined: {str(e)}. "
                    f"Treating as single UID for validation."
                )
                GeneralAcquisitionValidator._validate_single_uid(
                    str(irradiation_uid), "Irradiation Event UID (0008,3010)", result
                )
    
    @staticmethod
    def _validate_single_uid(uid: str, context: str, result: ValidationResult) -> None:
        """Validate a single UID format according to DICOM PS3.5 specifications.
        
        Validates UID format per DICOM PS3.5 Data Structures and Encoding:
        - Must contain only digits (0-9) and dots (.)
        - Maximum length 64 characters
        - Cannot start or end with dot
        - Cannot contain consecutive dots
        - Should be proper ISO OID format
        
        Args:
            uid: UID string to validate
            context: Context description for error messages (e.g., "Acquisition UID (0008,0017)")
            result: ValidationResult to add errors/warnings to
        """
        # UID format pattern: digits and dots only, per DICOM PS3.5
        uid_pattern = re.compile(r'^[0-9.]+$')
        
        if not uid_pattern.match(uid):
            result.add_error(
                f"{context} format invalid: '{uid}'. "
                f"Per DICOM PS3.5, UIDs must contain only digits (0-9) and dots (.). "
                f"Use a properly formatted ISO OID."
            )
        elif len(uid) > 64:
            result.add_error(
                f"{context} too long: {len(uid)} characters. "
                f"Per DICOM PS3.5, UIDs cannot exceed 64 characters. "
                f"Consider using a shorter UID or registered root."
            )
        elif uid.startswith('.') or uid.endswith('.'):
            result.add_error(
                f"{context} cannot start or end with dot: '{uid}'. "
                f"Per DICOM PS3.5, UIDs must start and end with digits."
            )
        elif '..' in uid:
            result.add_error(
                f"{context} cannot contain consecutive dots: '{uid}'. "
                f"Per DICOM PS3.5, each UID component must be separated by a single dot."
            )
        elif uid == '0':
            result.add_warning(
                f"{context} value '0' is not a valid UID root. "
                f"Per DICOM PS3.5, UIDs should be proper ISO OID format starting with a registered root."
            )
    
    @staticmethod
    def _validate_datetime_formats(dataset: Dataset, result: ValidationResult) -> None:
        """Validate date and time format compliance per DICOM PS3.5.
        
        Validates:
        - Acquisition Date (0008,0022) - Type 3, DA VR (YYYYMMDD format)
        - Acquisition Time (0008,0032) - Type 3, TM VR (HHMMSS.FFFFFF format)
        - Acquisition DateTime (0008,002A) - Type 3, DT VR (YYYYMMDDHHMMSS.FFFFFF format)
        
        Performs both format and semantic validation including range checks.
        """
        
        # Date format: YYYYMMDD
        date_pattern = re.compile(r'^\d{8}$')
        
        # Time format: HHMMSS or HHMMSS.FFFFFF
        time_pattern = re.compile(r'^\d{6}(\.\d{1,6})?$')
        
        # DateTime format: YYYYMMDDHHMMSS.FFFFFF
        datetime_pattern = re.compile(r'^\d{14}(\.\d{1,6})?$')
        
        # Acquisition Date (0008,0022)
        acquisition_date = getattr(dataset, 'AcquisitionDate', '')
        if acquisition_date:
            if not date_pattern.match(acquisition_date):
                result.add_error(
                    f"Acquisition Date (0008,0022) format invalid: '{acquisition_date}'. "
                    f"Must be YYYYMMDD format"
                )
            else:
                # Validate actual date values
                try:
                    year = int(acquisition_date[:4])
                    month = int(acquisition_date[4:6])
                    day = int(acquisition_date[6:8])
                    
                    if year < 2000 or year > datetime.now().year:
                        result.add_warning(
                            f"Acquisition Date (0008,0022) year outside of reasonable range: {year}"
                        )
                    if month < 1 or month > 12:
                        result.add_error(
                            f"Acquisition Date (0008,0022) invalid month: {month}"
                        )
                    if day < 1 or day > 31:
                        result.add_error(
                            f"Acquisition Date (0008,0022) invalid day: {day}"
                        )
                except ValueError:
                    result.add_error(
                        f"Acquisition Date (0008,0022) contains invalid numeric values: '{acquisition_date}'"
                    )
        
        # Acquisition Time (0008,0032)
        acquisition_time = getattr(dataset, 'AcquisitionTime', '')
        if acquisition_time:
            if not time_pattern.match(acquisition_time):
                result.add_error(
                    f"Acquisition Time (0008,0032) format invalid: '{acquisition_time}'. "
                    f"Must be HHMMSS or HHMMSS.FFFFFF format"
                )
            else:
                # Validate actual time values
                try:
                    time_part = acquisition_time.split('.')[0]
                    hour = int(time_part[:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])
                    
                    if hour > 23:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid hour: {hour}"
                        )
                    if minute > 59:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid minute: {minute}"
                        )
                    if second > 59:
                        result.add_error(
                            f"Acquisition Time (0008,0032) invalid second: {second}"
                        )
                except (ValueError, IndexError):
                    result.add_error(
                        f"Acquisition Time (0008,0032) contains invalid numeric values: '{acquisition_time}'"
                    )
        
        # Acquisition DateTime (0008,002A)
        acquisition_datetime = getattr(dataset, 'AcquisitionDateTime', '')
        if acquisition_datetime:
            if not datetime_pattern.match(acquisition_datetime):
                result.add_error(
                    f"Acquisition DateTime (0008,002A) format invalid: '{acquisition_datetime}'. "
                    f"Must be YYYYMMDDHHMMSS.FFFFFF format"
                )
            else:
                # Validate actual datetime values
                try:
                    datetime_part = acquisition_datetime.split('.')[0]
                    year = int(datetime_part[:4])
                    month = int(datetime_part[4:6])
                    day = int(datetime_part[6:8])
                    hour = int(datetime_part[8:10])
                    minute = int(datetime_part[10:12])
                    second = int(datetime_part[12:14])
                    
                    if month < 1 or month > 12:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid month: {month}"
                        )
                    if day < 1 or day > 31:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid day: {day}"
                        )
                    if hour > 23:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid hour: {hour}"
                        )
                    if minute > 59:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid minute: {minute}"
                        )
                    if second > 59:
                        result.add_error(
                            f"Acquisition DateTime (0008,002A) invalid second: {second}"
                        )
                except (ValueError, IndexError):
                    result.add_error(
                        f"Acquisition DateTime (0008,002A) contains invalid numeric values: '{acquisition_datetime}'"
                    )
    
    @staticmethod
    def _validate_numeric_ranges(dataset: Dataset, result: ValidationResult) -> None:
        """Validate numeric value ranges and semantic constraints.
        
        Validates:
        - Acquisition Duration (0018,9073) - Type 3, FD VR (positive values expected)
        - Images in Acquisition (0020,1002) - Type 3, IS VR (positive integer values expected)
        
        Provides warnings for edge cases and errors for invalid numeric types.
        """
        
        # Acquisition Duration (0018,9073) - should be positive
        duration = getattr(dataset, 'AcquisitionDuration', None)
        if duration is not None:
            try:
                duration_float = float(duration)
                if duration_float < 0:
                    result.add_warning(
                        f"Acquisition Duration (0018,9073) should be positive: {duration_float} seconds"
                    )
                elif duration_float == 0:
                    result.add_warning(
                        f"Acquisition Duration (0018,9073) is zero, which may indicate no actual acquisition time"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Acquisition Duration (0018,9073) must be a numeric value: '{duration}'"
                )
        
        # Images in Acquisition (0020,1002) - should be positive integer
        images_count = getattr(dataset, 'ImagesInAcquisition', None)
        if images_count is not None:
            try:
                images_int = int(images_count)
                if images_int < 0:
                    result.add_error(
                        f"Images in Acquisition (0020,1002) cannot be negative: {images_int}"
                    )
                elif images_int == 0:
                    result.add_warning(
                        f"Images in Acquisition (0020,1002) is zero, which may indicate no images were acquired"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Images in Acquisition (0020,1002) must be an integer value: '{images_count}'"
                )
    
    @staticmethod
    def _validate_logical_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate logical consistency between related acquisition attributes.
        
        Checks consistency between:
        - Acquisition Date (0008,0022) and Acquisition Time (0008,0032) vs Acquisition DateTime (0008,002A)
        - Provides guidance about complete timing information
        - Validates irradiation event context per DICOM PS3.3 C.********.1
        
        Focuses on user guidance for proper acquisition timing documentation.
        """
        
        acquisition_date = getattr(dataset, 'AcquisitionDate', '')
        acquisition_time = getattr(dataset, 'AcquisitionTime', '')
        acquisition_datetime = getattr(dataset, 'AcquisitionDateTime', '')
        
        # If both date/time and datetime are present, they should be consistent
        if acquisition_date and acquisition_time and acquisition_datetime:
            expected_datetime = acquisition_date + acquisition_time
            # Remove fractional seconds for comparison if present
            datetime_base = acquisition_datetime.split('.')[0]
            expected_base = expected_datetime.split('.')[0]
            
            if datetime_base != expected_base:
                result.add_warning(
                    f"Acquisition DateTime (0008,002A) '{acquisition_datetime}' is inconsistent "
                    f"with Acquisition Date '{acquisition_date}' and Time '{acquisition_time}'"
                )
        
        # Warn if only one of date or time is present
        if acquisition_date and not acquisition_time:
            result.add_warning(
                "Acquisition Date (0008,0022) is present but Acquisition Time (0008,0032) is missing. "
                "Consider providing both for complete timing information"
            )
        
        if acquisition_time and not acquisition_date:
            result.add_warning(
                "Acquisition Time (0008,0032) is present but Acquisition Date (0008,0022) is missing. "
                "Consider providing both for complete timing information"
            )
        
        # Validate irradiation event context
        GeneralAcquisitionValidator._validate_irradiation_event_context(dataset, result)
    
    @staticmethod
    def _validate_irradiation_event_context(dataset: Dataset, result: ValidationResult) -> None:
        """Validate irradiation event context and provide user guidance.
        
        Per DICOM PS3.3 C.********.1, irradiation events have complex relationships
        with acquisitions that require user understanding:
        
        - Not necessarily 1:1 relationship between irradiation event and acquisition
        - Single acquisition may result from multiple irradiation events
        - Multiple irradiation events may be involved in single acquisition
        - Acquisitions may not involve ionizing radiation (attribute absent)
        
        Provides contextual warnings to help users understand these relationships.
        """
        if hasattr(dataset, 'IrradiationEventUID'):
            irradiation_uid = getattr(dataset, 'IrradiationEventUID')
            # Provide guidance about irradiation event relationships
            # Check if it's multiple values (list, MultiValue) and has more than one element
            try:
                if (hasattr(irradiation_uid, '__iter__') and 
                    not isinstance(irradiation_uid, str) and 
                    len(irradiation_uid) > 1):
                    result.add_warning(
                        f"Multiple Irradiation Event UIDs ({len(irradiation_uid)}) detected. "
                        f"Per DICOM PS3.3 C.********.1, this indicates either: "
                        f"(1) multiple X-ray sources, or (2) quiescent periods between irradiation events "
                        f"during which data gathering continued. Ensure this reflects the actual acquisition scenario."
                    )
            except Exception:
                # If we can't determine multiplicity, skip the warning
                pass
            
            # Only provide guidance for potentially confusing cases
            # (Multiple events need explanation, single events are straightforward)
