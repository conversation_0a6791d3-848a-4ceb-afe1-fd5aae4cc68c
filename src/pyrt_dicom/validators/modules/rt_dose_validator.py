"""RT Dose Module DICOM validation - PS3.3 C.8.8.3

RT Dose Module validator provides comprehensive validation of all DICOM requirements
including conditional logic, enumerated values, sequence structures, and cross-field
dependencies according to DICOM PS3.3 Section C.8.8.3.
"""

from typing import TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.dose_enums import DoseUnits, DoseType
from ...enums.rt_enums import SpatialTransformOfDose, DoseSummationType, TissueHeterogeneityCorrection

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class RTDoseValidator(BaseValidator):
    """Validator for DICOM RT Dose Module (PS3.3 C.8.8.3).

    Validates all Type 1, Type 2, Type 3, Type 1C, and Type 2C requirements
    according to the DICOM standard including complex conditional logic,
    enumerated values, and cross-field dependencies.
    """

    @staticmethod
    def validate_required_elements(data: "Dataset | BaseModule") -> ValidationResult:
        """Validate Type 1 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        result = ValidationResult()

        required_elements = [
            ('DoseUnits', '3004,0002', 'Dose Units'),
            ('DoseType', '3004,0004', 'Dose Type'),
            ('DoseSummationType', '3004,000A', 'Dose Summation Type')
        ]

        for attr_name, tag, description in required_elements:
            if attr_name not in data:
                result.add_error(
                    f"{description} ({tag}) is required for RT Dose Module (Type 1)"
                )

        return result

    @staticmethod
    def validate_conditional_requirements(data: "Dataset | BaseModule") -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        result = ValidationResult()

        # === PIXEL DATA CONDITIONAL REQUIREMENTS ===
        # Type 1C elements required when Pixel Data (7FE0,0010) is present
        if 'PixelData' in data:
            pixel_elements = [
                ('SamplesPerPixel', '0028,0002', 'Samples per Pixel'),
                ('PhotometricInterpretation', '0028,0004', 'Photometric Interpretation'),
                ('BitsAllocated', '0028,0100', 'Bits Allocated'),
                ('BitsStored', '0028,0101', 'Bits Stored'),
                ('HighBit', '0028,0102', 'High Bit'),
                ('PixelRepresentation', '0028,0103', 'Pixel Representation'),
                ('DoseGridScaling', '3004,000E', 'Dose Grid Scaling')
            ]

            for attr_name, tag, description in pixel_elements:
                if attr_name not in data:
                    result.add_error(
                        f"{description} ({tag}) is required when Pixel Data (7FE0,0010) is present (Type 1C)"
                    )

        # === DOSE SUMMATION TYPE CONDITIONAL REQUIREMENTS ===
        dose_summation_type = data.DoseSummationType if 'DoseSummationType' in data else ''

        # Referenced RT Plan Sequence (Type 1C)
        rt_plan_required_types = [
            'PLAN', 'MULTI_PLAN', 'FRACTION', 'BEAM', 'BRACHY',
            'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
        if dose_summation_type in rt_plan_required_types:
            if 'ReferencedRTPlanSequence' not in data:
                result.add_error(
                    f"Referenced RT Plan Sequence (300C,0002) is required when "
                    f"Dose Summation Type is {dose_summation_type} (Type 1C)"
                )
            elif len(getattr(data, 'ReferencedRTPlanSequence', [])) == 0:
                result.add_error(
                    f"Referenced RT Plan Sequence (300C,0002) must contain at least one item when "
                    f"Dose Summation Type is {dose_summation_type} (Type 1C)"
                )

        # Plan Overview Sequence (Type 1C)
        if dose_summation_type == 'PLAN_OVERVIEW':
            if 'PlanOverviewSequence' not in data:
                result.add_error(
                    "Plan Overview Sequence (300C,0116) is required when "
                    "Dose Summation Type is PLAN_OVERVIEW (Type 1C)"
                )

        # Referenced Treatment Record Sequence (Type 1C)
        if dose_summation_type == 'RECORD':
            if 'ReferencedTreatmentRecordSequence' not in data:
                result.add_error(
                    "Referenced Treatment Record Sequence (3008,0030) is required when "
                    "Dose Summation Type is RECORD (Type 1C)"
                )

        # === SPATIAL TRANSFORMATION CONDITIONAL REQUIREMENTS ===
        # Referenced Spatial Registration Sequence (Type 2C)
        spatial_transform = data.SpatialTransformOfDose if 'SpatialTransformOfDose' in data else ''
        if spatial_transform in ['RIGID', 'NON_RIGID']:
            if 'ReferencedSpatialRegistrationSequence' not in data:
                result.add_error(
                    f"Referenced Spatial Registration Sequence (0070,0404) is required when "
                    f"Spatial Transform of Dose is {spatial_transform} (Type 2C)"
                )

        return result

    @staticmethod
    def validate_enumerated_values(data: "Dataset | BaseModule") -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        result = ValidationResult()

        # Dose Units (3004,0002) - Type 1
        if 'DoseUnits' in data:
            dose_units = data.DoseUnits
            valid_units = [units.value for units in DoseUnits]
            BaseValidator.validate_enumerated_value(
                dose_units, valid_units,
                "Dose Units (3004,0002)", result
            )

        # Dose Type (3004,0004) - Type 1
        if 'DoseType' in data:
            dose_type = data.DoseType
            valid_types = [dtype.value for dtype in DoseType]
            BaseValidator.validate_enumerated_value(
                dose_type, valid_types,
                "Dose Type (3004,0004)", result
            )

        # Dose Summation Type (3004,000A) - Type 1
        if 'DoseSummationType' in data:
            dose_summation_type = data.DoseSummationType
            valid_summation_types = [stype.value for stype in DoseSummationType]
            BaseValidator.validate_enumerated_value(
                dose_summation_type, valid_summation_types,
                "Dose Summation Type (3004,000A)", result
            )

        # Spatial Transform of Dose (3004,0005) - Type 3
        if 'SpatialTransformOfDose' in data:
            spatial_transform = data.SpatialTransformOfDose
            valid_transforms = [transform.value for transform in SpatialTransformOfDose]
            BaseValidator.validate_enumerated_value(
                spatial_transform, valid_transforms,
                "Spatial Transform of Dose (3004,0005)", result
            )

        # Tissue Heterogeneity Correction (3004,0014) - Type 3
        if 'TissueHeterogeneityCorrection' in data:
            tissue_correction = data.TissueHeterogeneityCorrection
            valid_corrections = [corr.value for corr in TissueHeterogeneityCorrection]
            BaseValidator.validate_enumerated_value(
                tissue_correction, valid_corrections,
                "Tissue Heterogeneity Correction (3004,0014)", result
            )

        # Photometric Interpretation (0028,0004) - Type 1C with RT Dose constraint
        if 'PhotometricInterpretation' in data:
            photometric_interp = data.PhotometricInterpretation
            if photometric_interp != 'MONOCHROME2':
                result.add_error(
                    "Photometric Interpretation (0028,0004) must be MONOCHROME2 for RT Dose "
                    "(DICOM PS3.3 C.8.8.3.4.2)"
                )

        # Samples per Pixel (0028,0002) - Type 1C with RT Dose constraint
        if 'SamplesPerPixel' in data:
            samples_per_pixel = data.SamplesPerPixel
            if samples_per_pixel != 1:
                result.add_error(
                    "Samples per Pixel (0028,0002) must be 1 for RT Dose "
                    "(DICOM PS3.3 C.8.8.3.4.1)"
                )

        return result

    @staticmethod
    def validate_sequence_structures(data: "Dataset | BaseModule") -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        result = ValidationResult()

        # Referenced RT Plan Sequence validation
        if 'ReferencedRTPlanSequence' in data:
            ref_rt_plan_seq = data.ReferencedRTPlanSequence
            for i, plan_item in enumerate(ref_rt_plan_seq):
                if not plan_item.get('ReferencedSOPClassUID'):
                    result.add_error(
                        f"Referenced RT Plan Sequence item {i}: "
                        "Referenced SOP Class UID (0008,1150) is required"
                    )
                if not plan_item.get('ReferencedSOPInstanceUID'):
                    result.add_error(
                        f"Referenced RT Plan Sequence item {i}: "
                        "Referenced SOP Instance UID (0008,1155) is required"
                    )

        return result

    @staticmethod
    def validate(data: "Dataset | BaseModule", config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors and warnings lists
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(RTDoseValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(RTDoseValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(RTDoseValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(RTDoseValidator.validate_sequence_structures(data))

        # Validate pixel data consistency and DICOM constraints
        result.merge(RTDoseValidator._validate_pixel_data_consistency(data))

        # Validate cross-field dependencies and complex logic
        result.merge(RTDoseValidator._validate_cross_field_dependencies(data))

        return result
    
    @staticmethod
    def _validate_pixel_data_consistency(data: "Dataset | BaseModule") -> ValidationResult:
        """Validate pixel data consistency and DICOM constraints for RT Dose.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        result = ValidationResult()

        # Validate bits allocated/stored/high bit consistency
        bits_allocated = data.BitsAllocated if 'BitsAllocated' in data else None
        bits_stored = data.BitsStored if 'BitsStored' in data else None
        high_bit = data.HighBit if 'HighBit' in data else None

        if bits_allocated is not None:
            # Validate bits allocated values for RT Dose
            if bits_allocated not in [16, 32]:
                result.add_error(
                    "Bits Allocated (0028,0100) must be 16 or 32 for RT Dose "
                    "(DICOM PS3.3 C.8.8.3.4.3)"
                )

            if bits_stored is not None:
                # For RT Doses, Bits Stored must equal Bits Allocated
                if bits_stored != bits_allocated:
                    result.add_error(
                        "Bits Stored (0028,0101) must equal Bits Allocated for RT Dose "
                        "(DICOM PS3.3 C.8.8.3.4.4)"
                    )

                if high_bit is not None:
                    # For RT Doses, High Bit must be one less than Bits Stored
                    if high_bit != bits_stored - 1:
                        result.add_error(
                            "High Bit (0028,0102) must be one less than Bits Stored for RT Dose "
                            "(DICOM PS3.3 C.8.8.3.4.5)"
                        )

        # Validate pixel representation based on dose type
        dose_type = data.DoseType if 'DoseType' in data else ''
        pixel_representation = data.PixelRepresentation if 'PixelRepresentation' in data else None

        if dose_type and pixel_representation is not None:
            if dose_type == 'ERROR' and pixel_representation != 1:
                result.add_error(
                    "Pixel Representation (0028,0103) must be 1 (two's complement) when "
                    "Dose Type is ERROR (DICOM PS3.3 C.8.8.3.4.6)"
                )
            elif dose_type != 'ERROR' and pixel_representation != 0:
                result.add_error(
                    "Pixel Representation (0028,0103) must be 0 (unsigned) when "
                    "Dose Type is not ERROR (DICOM PS3.3 C.8.8.3.4.6)"
                )

        # Validate dose grid scaling
        if 'DoseGridScaling' in data:
            dose_grid_scaling = data.DoseGridScaling
            try:
                scaling_value = float(dose_grid_scaling)
                if scaling_value <= 0:
                    result.add_error(
                        "Dose Grid Scaling (3004,000E) must be positive"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    "Dose Grid Scaling (3004,000E) must be a valid numeric value"
                )

        # Validate normalization point format
        if 'NormalizationPoint' in data:
            normalization_point = data.NormalizationPoint
            if len(normalization_point) != 3:
                result.add_error(
                    "Normalization Point (3004,0008) must contain exactly 3 coordinates (x, y, z)"
                )

        return result

    @staticmethod
    def _validate_cross_field_dependencies(data: "Dataset | BaseModule") -> ValidationResult:
        """Validate cross-field dependencies and complex logic.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        result = ValidationResult()

        dose_summation_type = data.DoseSummationType if 'DoseSummationType' in data else ''
        dose_type = data.DoseType if 'DoseType' in data else ''

        # Multi-plan validation
        if dose_summation_type == 'MULTI_PLAN':
            rt_plan_seq = data.ReferencedRTPlanSequence if 'ReferencedRTPlanSequence' in data else []
            if len(rt_plan_seq) < 2:
                result.add_error(
                    "Referenced RT Plan Sequence (300C,0002) must contain 2 or more items "
                    "when Dose Summation Type is MULTI_PLAN"
                )
        elif dose_summation_type in ['PLAN', 'FRACTION', 'BEAM', 'BRACHY',
                                    'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT']:
            rt_plan_seq = data.ReferencedRTPlanSequence if 'ReferencedRTPlanSequence' in data else []
            if len(rt_plan_seq) > 1:
                result.add_warning(
                    f"Referenced RT Plan Sequence should contain only a single item "
                    f"when Dose Summation Type is {dose_summation_type}"
                )

        # Plan overview sequence count validation
        plan_overview_seq = data.PlanOverviewSequence if 'PlanOverviewSequence' in data else []
        if dose_summation_type == 'PLAN_OVERVIEW' and len(plan_overview_seq) == 0:
            result.add_error(
                "Plan Overview Sequence (300C,0116) must contain one or more items "
                "when Dose Summation Type is PLAN_OVERVIEW"
            )
        elif dose_summation_type in ['PLAN', 'RECORD'] and len(plan_overview_seq) > 1:
            result.add_warning(
                f"Plan Overview Sequence should contain only one item "
                f"when Dose Summation Type is {dose_summation_type}"
            )
        elif dose_summation_type == 'MULTI_PLAN' and len(plan_overview_seq) < 2:
            result.add_warning(
                "Plan Overview Sequence should contain two or more items "
                "when Dose Summation Type is MULTI_PLAN"
            )

        # Validate Derivation Code Sequence consistency
        if 'DerivationCodeSequence' in data:
            derivation_seq = data.DerivationCodeSequence
            for i, derivation in enumerate(derivation_seq):
                if derivation.get('CodeValue') == '121377':  # "Composed with radiobiological effects"
                    if dose_type != 'EFFECTIVE':
                        result.add_error(
                            "Dose Type (3004,0004) must be EFFECTIVE when Derivation Code Sequence "
                            "contains 'Composed with radiobiological effects' (DICOM PS3.3 C.8.8.3.6)"
                        )

        # Validate Plan Overview Index uniqueness
        if 'PlanOverviewSequence' in data:
            plan_overview_seq = data.PlanOverviewSequence
            plan_indices = []
            for i, plan_overview in enumerate(plan_overview_seq):
                plan_index = plan_overview.get('PlanOverviewIndex')
                if plan_index is not None:
                    if plan_index in plan_indices:
                        result.add_error(
                            f"Plan Overview Sequence item {i}: Plan Overview Index ({plan_index}) "
                            "must be unique within the sequence"
                        )
                    else:
                        plan_indices.append(plan_index)

        # Validate grid frame offset vector consistency
        if 'GridFrameOffsetVector' in data:
            grid_offsets = data.GridFrameOffsetVector
            num_frames = data.NumberOfFrames if 'NumberOfFrames' in data else 1
            if len(grid_offsets) != num_frames:
                result.add_warning(
                    f"Grid Frame Offset Vector should contain {num_frames} values "
                    f"(one per frame), but contains {len(grid_offsets)} values"
                )

            # Check that values vary monotonically
            if len(grid_offsets) > 1:
                is_monotonic = all(grid_offsets[i] <= grid_offsets[i+1] for i in range(len(grid_offsets)-1))
                if not is_monotonic:
                    result.add_warning(
                        "Grid Frame Offset Vector values should vary monotonically "
                        "(DICOM PS3.3 C.8.8.3.2)"
                    )

        # Content Date/Time consistency check
        content_date = data.ContentDate if 'ContentDate' in data else None
        content_time = data.ContentTime if 'ContentTime' in data else None
        if content_date is not None and content_time is None:
            result.add_warning(
                "Content Time (0008,0033) should be provided when Content Date (0008,0023) is present"
            )
        elif content_time is not None and content_date is None:
            result.add_warning(
                "Content Date (0008,0023) should be provided when Content Time (0008,0033) is present"
            )

        return result






