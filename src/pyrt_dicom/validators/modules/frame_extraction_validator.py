"""Frame Extraction Module Validator - DICOM PS3.3 C.12.3 validation."""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class FrameExtractionValidator(BaseValidator):
    """Validator for Frame Extraction Module requirements.

    Validates DICOM PS3.3 C.12.3 Frame Extraction Module compliance including:
    - Required Frame Extraction Sequence with at least one item
    - Required Multi-frame Source SOP Instance UID in each sequence item
    - Conditional requirement that exactly one frame extraction method is present:
      * Simple Frame List (0008,1161) - direct list of frame numbers
      * Calculated Frame List (0008,1162) - triplets of start, end, increment
      * Time Range (0008,1163) - start and end times as floating point values
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Use 'in' operator instead of hasattr - works with both Dataset and BaseModule
        if 'FrameExtractionSequence' not in data:
            result.add_error(
                "Missing required Frame Extraction Sequence (0008,1164). "
                "This Type 1 element is mandatory for all Frame Extraction modules. "
                "Add at least one sequence item using FrameExtractionModule.create_frame_extraction_item()."
            )
            return result

        # Direct attribute access - works with both types via __getattr__
        sequence = data.FrameExtractionSequence
        if len(sequence) == 0:
            result.add_error(
                "Frame Extraction Sequence (0008,1164) is empty but must contain at least one item. "
                "Each item describes how frames were extracted from a source multi-frame SOP Instance. "
                "Use FrameExtractionModule.create_frame_extraction_item() to add extraction details."
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Frame Extraction Module has Type 1C requirements within sequence items
        if 'FrameExtractionSequence' not in data:
            return result

        sequence = data.FrameExtractionSequence
        for i, item in enumerate(sequence):
            item_prefix = f"FrameExtractionSequence item {i+1}"

            # Validate required Multi-frame Source SOP Instance UID (Type 1)
            if 'MultiFrameSourceSOPInstanceUID' not in item:
                result.add_error(
                    f"{item_prefix}: Missing required Multi-frame Source SOP Instance UID (0008,1167). "
                    f"This Type 1 element identifies the source multi-frame SOP Instance from which frames were extracted. "
                    f"Provide a valid DICOM UID when creating the frame extraction item."
                )

            # Validate conditional frame list requirements (exactly one must be present - Type 1C)
            frame_list_fields = ['SimpleFrameList', 'CalculatedFrameList', 'TimeRange']
            present_fields = [field for field in frame_list_fields if field in item]

            if len(present_fields) == 0:
                result.add_error(
                    f"{item_prefix}: Missing frame extraction method. Exactly one of the following Type 1C elements "
                    f"must be present: Simple Frame List (0008,1161) for direct frame numbers, "
                    f"Calculated Frame List (0008,1162) for triplet-based selection, or Time Range (0008,1163) "
                    f"for time-based frame extraction. Use FrameExtractionModule.create_frame_extraction_item() "
                    f"with the appropriate parameter (simple_frame_list, calculated_frame_list, or time_range)."
                )
            elif len(present_fields) > 1:
                result.add_error(
                    f"{item_prefix}: Multiple frame extraction methods found: {', '.join(present_fields)}. "
                    f"According to DICOM PS3.3 C.12.3, exactly one extraction method must be present per sequence item. "
                    f"Choose either Simple Frame List (0008,1161), Calculated Frame List (0008,1162), or Time Range (0008,1163) "
                    f"but not multiple methods in the same item."
                )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Frame Extraction Module has no enumerated values defined in DICOM PS3.3 C.12.3
        # All values are numeric (frame numbers, times) or UIDs

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        if 'FrameExtractionSequence' not in data:
            return result

        sequence = data.FrameExtractionSequence

        for i, item in enumerate(sequence):
            item_prefix = f"FrameExtractionSequence item {i+1}"

            # Validate UID format if present
            if 'MultiFrameSourceSOPInstanceUID' in item:
                uid = str(item.MultiFrameSourceSOPInstanceUID)
                if not FrameExtractionValidator._is_valid_uid(uid):
                    result.add_error(
                        f"{item_prefix}: Invalid Multi-frame Source SOP Instance UID (0008,1167) format: '{uid}'. "
                        f"UIDs must consist of numeric components separated by periods (e.g., '1.2.3.4.5.6.7.8.9'). "
                        f"Each component must be a number without leading zeros (except '0' itself)."
                    )

            # Validate the specific frame list type structures
            if 'SimpleFrameList' in item:
                FrameExtractionValidator._validate_simple_frame_list(item, result, item_prefix)
            elif 'CalculatedFrameList' in item:
                FrameExtractionValidator._validate_calculated_frame_list(item, result, item_prefix)
            elif 'TimeRange' in item:
                FrameExtractionValidator._validate_time_range(item, result, item_prefix)

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(FrameExtractionValidator.validate_required_elements(data))

        if config.validate_conditional_requirements and config.validate_sequences:
            result.merge(FrameExtractionValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(FrameExtractionValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(FrameExtractionValidator.validate_sequence_structures(data))

        return result
    

    
    @staticmethod
    def validate_frame_extraction_item_creation(
        multi_frame_source_sop_instance_uid: str,
        simple_frame_list: list[int] | None = None,
        calculated_frame_list: list[int] | None = None,
        time_range: list[float] | None = None
    ) -> ValidationResult:
        """Validate frame extraction item creation parameters.
        
        This method validates the parameters before creating a frame extraction item,
        replacing the exception-based validation that was previously in the module.
        
        Args:
            multi_frame_source_sop_instance_uid: SOP Instance UID (required)
            simple_frame_list: Simple frame list (optional)
            calculated_frame_list: Calculated frame list (optional) 
            time_range: Time range (optional)
            
        Returns:
            ValidationResult with any validation errors
        """
        result = ValidationResult()
        
        # Validate exactly one frame specification is provided
        frame_specs = [simple_frame_list, calculated_frame_list, time_range]
        provided_specs = [spec for spec in frame_specs if spec is not None]
        
        if len(provided_specs) == 0:
            result.add_error(
                "No frame extraction method provided. Exactly one of simple_frame_list, "
                "calculated_frame_list, or time_range must be provided to specify how frames "
                "are extracted from the source multi-frame SOP Instance."
            )
        elif len(provided_specs) > 1:
            result.add_error(
                "Multiple frame extraction methods provided. Exactly one of simple_frame_list, "
                "calculated_frame_list, or time_range must be provided, not multiple methods."
            )
        
        # Validate calculated frame list structure
        if calculated_frame_list is not None and len(calculated_frame_list) % 3 != 0:
            result.add_error(
                "Calculated Frame List must contain triplets (start, end, increment). "
                f"Found {len(calculated_frame_list)} values, but the count must be divisible by 3. "
                "Each triplet specifies: start_frame, end_frame, increment."
            )
        
        # Validate time range structure
        if time_range is not None and len(time_range) != 2:
            result.add_error(
                "Time Range must contain exactly two values (start_time, end_time). "
                f"Found {len(time_range)} values. Provide start and end times as floating point numbers."
            )
        
        return result
    
    @staticmethod  
    def validate_optional_elements_usage(**kwargs) -> ValidationResult:
        """Validate optional elements usage for FrameExtractionModule.
        
        The Frame Extraction Module has no Type 3 elements defined in DICOM PS3.3 C.12.3,
        so this method validates that no unexpected parameters are provided.
        
        Args:
            **kwargs: Keyword arguments that should be empty
            
        Returns:
            ValidationResult with errors if any arguments provided
        """
        result = ValidationResult()
        
        if kwargs:
            result.add_error(
                f"FrameExtractionModule has no optional elements defined in DICOM PS3.3 C.12.3. "
                f"Unexpected arguments provided: {list(kwargs.keys())}. "
                f"The Frame Extraction Module only supports required elements in the Frame Extraction Sequence."
            )
        
        return result
    

    
    @staticmethod
    def _validate_simple_frame_list(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Simple Frame List format."""
        if 'SimpleFrameList' not in item:
            return
        
        frame_list = item.SimpleFrameList
        
        # Check if it's a string (which pydicom treats as iterable but isn't a proper list)
        if isinstance(frame_list, str):
            result.add_error(
                f"{item_prefix}: Simple Frame List (0008,1161) must be a list of integers, not a string. "
                f"Provide frame numbers as a list, e.g., [1, 3, 5, 7] rather than '1357'."
            )
            return
        
        # Check if it's a single integer (pydicom converts single-element lists to scalars)
        if isinstance(frame_list, (int, float)):
            frame_list = [frame_list]  # Convert back to list for validation
        
        # Convert to list if it's a pydicom MultiValue or other sequence type
        try:
            frame_list = list(frame_list)
        except (TypeError, ValueError):
            result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) must be a list")
            return
        
        if len(frame_list) == 0:
            result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) must contain at least one frame number")
            return
        
        # Validate frame numbers are positive integers
        for j, frame_num in enumerate(frame_list):
            try:
                frame_int = int(frame_num)
                if frame_int <= 0:
                    result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) frame {j+1} must be positive, found {frame_int}")
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: Simple Frame List (0008,1161) frame {j+1} must be an integer, found {frame_num}")
    
    @staticmethod
    def _validate_calculated_frame_list(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Calculated Frame List format (triplets of start, end, increment)."""
        if 'CalculatedFrameList' not in item:
            return
        
        frame_list = item.CalculatedFrameList
        
        # Check if it's a string (which pydicom treats as iterable but isn't a proper list)
        if isinstance(frame_list, str):
            result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) must be a list")
            return
        
        # Check if it's a single integer (pydicom converts single-element lists to scalars)
        if isinstance(frame_list, (int, float)):
            frame_list = [frame_list]  # Convert back to list for validation
        
        # Convert to list if it's a pydicom MultiValue or other sequence type
        try:
            frame_list = list(frame_list)
        except (TypeError, ValueError):
            result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) must be a list")
            return
        
        if len(frame_list) % 3 != 0:
            result.add_error(
                f"{item_prefix}: Calculated Frame List (0008,1162) must contain triplets (start, end, increment). "
                f"Found {len(frame_list)} values, but the count must be divisible by 3. "
                f"Each triplet specifies: start_frame, end_frame, increment. "
                f"Example: [1, 10, 2, 20, 30, 1] represents frames 1-10 step 2, and frames 20-30 step 1."
            )
            return
        
        if len(frame_list) == 0:
            result.add_error(
                f"{item_prefix}: Calculated Frame List (0008,1162) is empty but must contain at least one triplet. "
                f"Provide triplets of (start, end, increment) values to specify frame ranges. "
                f"Example: [1, 10, 2] for frames 1, 3, 5, 7, 9 (start=1, end=10, increment=2)."
            )
            return
        
        # Validate triplets
        for j in range(0, len(frame_list), 3):
            triplet_num = j // 3 + 1
            try:
                start = int(frame_list[j])
                end = int(frame_list[j + 1])
                increment = int(frame_list[j + 2])
                
                if start <= 0:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} start must be positive")
                if end <= 0:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} end must be positive")
                if increment <= 0:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} increment must be positive")
                if start > end:
                    result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} start must be <= end")
                
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: Calculated Frame List (0008,1162) triplet {triplet_num} values must be integers")
    
    @staticmethod
    def _validate_time_range(item, result: ValidationResult, item_prefix: str) -> None:
        """Validate Time Range format (start and end times as floating point values)."""
        if 'TimeRange' not in item:
            return
        
        time_range = item.TimeRange
        
        # Check if it's a string (which pydicom treats as iterable but isn't a proper list)
        if isinstance(time_range, str):
            result.add_error(f"{item_prefix}: Time Range (0008,1163) must be a list")
            return
        
        # Check if it's a single number (pydicom converts single-element lists to scalars)
        if isinstance(time_range, (int, float)):
            time_range = [time_range]  # Convert back to list for validation
        
        # Convert to list if it's a pydicom MultiValue or other sequence type
        try:
            time_range = list(time_range)
        except (TypeError, ValueError):
            result.add_error(f"{item_prefix}: Time Range (0008,1163) must be a list")
            return
        
        if len(time_range) != 2:
            result.add_error(
                f"{item_prefix}: Time Range (0008,1163) must contain exactly 2 floating point values (start_time, end_time). "
                f"Found {len(time_range)} values. Provide start and end times as floating point numbers "
                f"representing DICOM time values, e.g., [120000.0, 130000.0] for 12:00:00 to 13:00:00."
            )
            return
        
        # Validate time values are floating point numbers
        for j, time_val in enumerate(time_range):
            time_name = "start" if j == 0 else "end"
            try:
                float_val = float(time_val)
                if float_val < 0:
                    result.add_error(f"{item_prefix}: Time Range (0008,1163) {time_name} time must be non-negative, found {float_val}")
            except (ValueError, TypeError):
                result.add_error(f"{item_prefix}: Time Range (0008,1163) {time_name} time must be a floating point number, found {time_val}")
        
        # Validate start <= end
        try:
            start_time = float(time_range[0])
            end_time = float(time_range[1])
            if start_time > end_time:
                result.add_error(f"{item_prefix}: Time Range (0008,1163) start time must be <= end time")
        except (ValueError, TypeError, IndexError):
            pass  # Already reported above
    
    @staticmethod
    def _is_valid_uid(uid_str: str) -> bool:
        """Check if UID string is in valid format."""
        if not uid_str:
            return False
        
        # Basic UID format check: numbers and dots, no leading/trailing dots
        if uid_str.startswith('.') or uid_str.endswith('.'):
            return False
        
        parts = uid_str.split('.')
        if len(parts) < 2:  # UID must have at least 2 parts
            return False
        
        for part in parts:
            if not part.isdigit() or part.startswith('0') and len(part) > 1:
                return False
        
        return True
