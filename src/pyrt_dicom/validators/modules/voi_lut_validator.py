"""VOI LUT Module Validator - DICOM PS3.3 C.11.2 validation."""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import VoiLutFunction


class VoiLutValidator(BaseValidator):
    """Validator for VOI LUT Module requirements."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate VOI LUT Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate conditional requirements
        VoiLutValidator._validate_conditional_requirements(dataset, result)
        
        # Validate VOI LUT Sequence if present
        if hasattr(dataset, 'VOILUTSequence'):
            VoiLutValidator._validate_voi_lut_sequence(dataset, result, config)
        
        # Validate Window parameters if present
        if hasattr(dataset, 'WindowCenter') or hasattr(dataset, 'WindowWidth'):
            VoiLutValidator._validate_window_parameters(dataset, result, config)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate conditional requirements for VOI LUT or Window parameters.
        
        DICOM PS3.3 C.11.2 specifies:
        - VOI LUT Sequence (0028,3010) is Type 1C: Required if Window Center not present
        - Window Center (0028,1050) is Type 1C: Required if VOI LUT Sequence not present  
        - Window Width (0028,1051) is Type 1C: Required if Window Center present
        """
        has_voi_lut = hasattr(dataset, 'VOILUTSequence')
        has_window_center = hasattr(dataset, 'WindowCenter')
        has_window_width = hasattr(dataset, 'WindowWidth')
        
        # If neither VOI LUT nor Window Center is present, that's valid (identity transformation)
        # But if Window Center is present, Window Width must also be present
        if has_window_center and not has_window_width:
            result.add_error(
                "Window Width (0028,1051) is required when Window Center (0028,1050) is present. "
                "Both elements must be present together as Type 1C conditional requirements."
            )
        elif has_window_width and not has_window_center:
            result.add_error(
                "Window Center (0028,1050) is required when Window Width (0028,1051) is present. "
                "Both elements must be present together as Type 1C conditional requirements."
            )
        
        # Additional validation: warn if both VOI LUT and Window parameters present
        if has_voi_lut and has_window_center:
            result.add_warning(
                "Both VOI LUT Sequence (0028,3010) and Window Center/Width (0028,1050/1051) are present. "
                "DICOM PS3.3 C.11.2 states that only one may be applied at a time for display, "
                "though both may be present to indicate multiple alternative views."
            )
    
    @staticmethod
    def _validate_voi_lut_sequence(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate VOI LUT Sequence requirements.
        
        DICOM PS3.3 C.11.2 specifies:
        - VOI LUT Sequence (0028,3010) must contain one or more items
        - Each item requires LUT Descriptor (0028,3002) and LUT Data (0028,3006) as Type 1
        - LUT Explanation (0028,3003) is Type 3 (optional)
        """
        if not hasattr(dataset, 'VOILUTSequence'):
            return
        
        sequence = dataset.VOILUTSequence
        
        # Must contain at least one item
        if len(sequence) == 0:
            result.add_error(
                "VOI LUT Sequence (0028,3010) must contain at least one item. "
                "DICOM PS3.3 C.11.2 requires 'One or more Items shall be included in this Sequence.'"
            )
            return
        
        # Validate each sequence item
        for i, item in enumerate(sequence, 1):
            # Validate required Type 1 elements in sequence item
            if not hasattr(item, 'LUTDescriptor'):
                result.add_error(
                    f"Missing required LUT Descriptor (0028,3002) in VOI LUT Sequence item {i}. "
                    f"This is a Type 1 element that specifies the format of the LUT Data."
                )
            
            if not hasattr(item, 'LUTData'):
                result.add_error(
                    f"Missing required LUT Data (0028,3006) in VOI LUT Sequence item {i}. "
                    f"This is a Type 1 element containing the actual LUT values."
                )
            
            # Validate LUT Descriptor format and values
            if hasattr(item, 'LUTDescriptor'):
                lut_desc = item.LUTDescriptor
                # Handle pydicom MultiValue objects and other formats
                if hasattr(lut_desc, 'value'):
                    lut_desc = lut_desc.value
                
                # Check if it's a sequence-like object (list, tuple, MultiValue, etc.) with 3 elements
                if not (hasattr(lut_desc, '__len__') and hasattr(lut_desc, '__getitem__') and len(lut_desc) == 3):
                    result.add_error(
                        f"LUT Descriptor (0028,3002) in VOI LUT Sequence item {i} must contain exactly 3 values: "
                        f"[number_of_entries, first_input_value, bits_per_entry]. Found: {lut_desc}"
                    )
                else:
                    # Validate number of entries (first value)
                    num_entries = lut_desc[0]
                    if num_entries <= 0 and num_entries != 0:  # 0 means 65536 entries
                        result.add_error(
                            f"LUT Descriptor first value (number of entries) in item {i} must be positive or 0 (for 65536). "
                            f"Found: {num_entries}"
                        )
                    
                    # Validate bits per entry (third value) - for VOI LUT in Image IOD: 8 or 16 bits
                    bits_per_entry = lut_desc[2]
                    if not (8 <= bits_per_entry <= 16):
                        result.add_error(
                            f"LUT Descriptor third value (bits per entry) in VOI LUT Sequence item {i} "
                            f"must be between 8 and 16 bits. Found: {bits_per_entry}. "
                            f"See DICOM PS3.3 C.******** for VOI LUT requirements."
                        )
            
            # Validate LUT Data consistency with descriptor
            if hasattr(item, 'LUTDescriptor') and hasattr(item, 'LUTData'):
                lut_desc = item.LUTDescriptor
                lut_data = item.LUTData
                
                # Handle pydicom DataElement format
                if hasattr(lut_desc, 'value'):
                    lut_desc = lut_desc.value
                if hasattr(lut_data, 'value'):
                    lut_data = lut_data.value
                
                # Check if descriptor is sequence-like with at least 1 element
                if hasattr(lut_desc, '__len__') and hasattr(lut_desc, '__getitem__') and len(lut_desc) >= 1:
                    expected_entries = 65536 if lut_desc[0] == 0 else lut_desc[0]
                    if len(lut_data) != expected_entries:
                        result.add_warning(
                            f"LUT Data length ({len(lut_data)}) in VOI LUT Sequence item {i} "
                            f"does not match LUT Descriptor number of entries ({expected_entries}). "
                            f"This may cause display issues."
                        )
    
    @staticmethod
    def _validate_window_parameters(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate Window Center and Width parameters.
        
        DICOM PS3.3 C.******** specifies:
        - Window Center (0028,1050) and Window Width (0028,1051) must have same number of values
        - Window Width must always be >= 1 for LINEAR function
        - Window Width must be > 0 for SIGMOID and LINEAR_EXACT functions
        - Window Center & Width Explanation (0028,1055) is optional but should match pairs
        """
        if not (hasattr(dataset, 'WindowCenter') and hasattr(dataset, 'WindowWidth')):
            return
        
        try:
            # Handle both single values and multi-valued fields
            window_center = dataset.WindowCenter
            window_width = dataset.WindowWidth
            
            # Convert to lists for consistent processing, handle pydicom MultiValue
            def to_float_list(value):
                try:
                    if hasattr(value, '__iter__') and not isinstance(value, str):
                        return [float(v) for v in value]
                    else:
                        return [float(value)]
                except (ValueError, TypeError) as e:
                    # Re-raise with context for outer catch
                    raise ValueError(f"Invalid numeric value: {value}") from e
            
            center_values = to_float_list(window_center)
            width_values = to_float_list(window_width)
            
            # Must have same number of values
            if len(center_values) != len(width_values):
                result.add_error(
                    f"Window Center (0028,1050) and Window Width (0028,1051) must have same number of values "
                    f"and be considered as pairs. Center has {len(center_values)} values, "
                    f"Width has {len(width_values)} values. See DICOM PS3.3 C.********."
                )
            
            # Get VOI LUT Function for context-specific validation
            voi_function = getattr(dataset, 'VOILUTFunction', VoiLutFunction.LINEAR.value)
            voi_function_str = str(voi_function)
            
            # Validate Window Width constraints based on function
            for i, width in enumerate(width_values, 1):
                if voi_function_str in [VoiLutFunction.SIGMOID.value, VoiLutFunction.LINEAR_EXACT.value]:
                    if width <= 0:
                        result.add_error(
                            f"Window Width (0028,1051) value {i} must be > 0 for {voi_function_str} function. "
                            f"Found: {width}. See DICOM PS3.3 C.11.2.1.3 for {voi_function_str} requirements."
                        )
                else:  # Default LINEAR function
                    if width < 1:
                        result.add_error(
                            f"Window Width (0028,1051) value {i} must be >= 1 for LINEAR function. "
                            f"Found: {width}. See DICOM PS3.3 C.******** - Window Width shall always be >= 1."
                        )
            
            # Validate Window Center & Width Explanation if present
            if hasattr(dataset, 'WindowCenterWidthExplanation'):
                explanation = dataset.WindowCenterWidthExplanation
                
                # Convert to list for consistent processing, handle pydicom MultiValue
                if hasattr(explanation, '__iter__') and not isinstance(explanation, str):
                    explanation_values = list(explanation)
                else:
                    explanation_values = [explanation]
                
                if len(explanation_values) != len(center_values):
                    result.add_warning(
                        f"Window Center & Width Explanation (0028,1055) should have same number of values "
                        f"as Window Center/Width pairs. Explanation has {len(explanation_values)} values, "
                        f"Center/Width pairs have {len(center_values)} values. "
                        f"Multiple values correspond to multiple Window Center and Width values."
                    )
        
        except (ValueError, TypeError) as e:
            result.add_error(
                f"Invalid Window Center (0028,1050) or Window Width (0028,1051) values: {str(e)}. "
                f"Values must be valid decimal strings (DS VR) representing floating point numbers."
            )
        
        # Validate VOI LUT Function enumerated values
        if config.check_enumerated_values and hasattr(dataset, 'VOILUTFunction'):
            allowed_values = [e.value for e in VoiLutFunction]
            BaseValidator.validate_enumerated_value(
                dataset.VOILUTFunction, allowed_values, 'VOILUTFunction (0028,1056)', result
            )
