"""Multi-frame Module DICOM validation - PS3.3 C.7.6.6

This validator ensures complete compliance with DICOM PS3.3 Section C.7.6.6
Multi-frame Module specification, including all conditional logic for
stereoscopic pairs and frame increment pointer consistency.
"""

from typing import Union, Any
from pydicom import Dataset
from pydicom.tag import BaseTag
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import StereoPairsPresent


class MultiFrameValidator(BaseValidator):
    """Validator for DICOM Multi-frame Module (PS3.3 C.7.6.6).
    
    Implements comprehensive validation of multi-frame DICOM data including:
    - Type 1 required element validation
    - Frame increment pointer consistency and referenced attribute validation
    - Stereoscopic pair conditional logic validation
    - Frame count and sequence consistency validation
    - DICOM standard compliance verification
    
    This validator ensures that multi-frame modules meet all requirements
    for clinical use and interoperability with DICOM viewers and PACS systems.
    """
    
    @staticmethod  
    def validate(dataset_or_module: Dataset | Any, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Multi-frame Module requirements on any pydicom Dataset or module.
        
        Performs comprehensive validation against DICOM PS3.3 Section C.7.6.6
        requirements, including all conditional logic and cross-field validation.
        
        Args:
            dataset_or_module: pydicom Dataset or MultiFrameModule to validate
            config: Optional validation configuration to control validation behavior
                  and error reporting levels
            
        Returns:
            ValidationResult: Structured validation results containing errors and warnings
                           with specific DICOM tag references and actionable guidance
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Handle both module objects and dataset objects
        if hasattr(dataset_or_module, '_dataset'):
            # This is a module object
            module_obj = dataset_or_module
            dataset = module_obj._dataset
            
            # Check for stored conversion errors from module creation
            if hasattr(module_obj, '_frame_increment_pointer_conversion_errors'):
                conversion_result = module_obj._frame_increment_pointer_conversion_errors
                if not conversion_result.is_valid:
                    # Add original conversion errors to the validation result
                    for error in conversion_result.errors:
                        result.add_error(f"Frame Increment Pointer conversion error: {error}")
                    for warning in conversion_result.warnings:
                        result.add_warning(f"Frame Increment Pointer conversion warning: {warning}")
        else:
            # This is a dataset object
            dataset = dataset_or_module
            
            # Check for stored conversion errors from module creation (legacy)
            if hasattr(dataset, '_frame_increment_pointer_conversion_errors'):
                conversion_result = dataset._frame_increment_pointer_conversion_errors
                if not conversion_result.is_valid:
                    # Add original conversion errors to the validation result
                    for error in conversion_result.errors:
                        result.add_error(f"Frame Increment Pointer conversion error: {error}")
                    for warning in conversion_result.warnings:
                        result.add_warning(f"Frame Increment Pointer conversion warning: {warning}")
        
        # Validate Type 1 requirements
        MultiFrameValidator._validate_type1_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            MultiFrameValidator._validate_enumerated_values(dataset, result)
        
        # Validate frame increment pointer consistency
        MultiFrameValidator._validate_frame_increment_consistency(dataset, result)
        
        # Validate stereo pairs consistency
        MultiFrameValidator._validate_stereo_pairs_consistency(dataset, result)
        
        # Validate conditional requirements (Type 1C/2C if any)
        MultiFrameValidator._validate_conditional_requirements(dataset, result)
        
        # Validate frame data consistency
        MultiFrameValidator._validate_frame_data_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) attributes."""
        
        # Number of Frames (0028,0008) Type 1
        if not hasattr(dataset, 'NumberOfFrames'):
            result.add_error(
                "Number of Frames (0028,0008) is required (Type 1). "
                "Multi-frame images must specify the total number of frames. "
                "Even single-frame instances using multi-frame module must set NumberOfFrames=1."
            )
        else:
            if not isinstance(dataset.NumberOfFrames, int):
                result.add_error(
                    f"Number of Frames (0028,0008) must be an integer, got {type(dataset.NumberOfFrames).__name__}. "
                    "DICOM Value Representation IS (Integer String) requires integer values."
                )
            elif dataset.NumberOfFrames < 1:
                result.add_error(
                    f"Number of Frames (0028,0008) must be greater than zero, got {dataset.NumberOfFrames}. "
                    "DICOM PS3.3 C.*******.1 specifies NumberOfFrames shall have a value greater than zero."
                )
        
        # Frame Increment Pointer (0028,0009) Type 1
        if not hasattr(dataset, 'FrameIncrementPointer'):
            result.add_error(
                "Frame Increment Pointer (0028,0009) is required (Type 1). "
                "This attribute identifies which DICOM attributes determine the sequential order of frames. "
                "Even single-frame instances must have FrameIncrementPointer present with at least one value. "
                "See DICOM PS3.3 C.*******.2."
            )
        else:
            fip = dataset.FrameIncrementPointer
            # Handle both single tag and list of tags (pydicom behavior)
            if isinstance(fip, BaseTag):
                tag_list = [fip]
            elif isinstance(fip, (list, tuple)) or hasattr(fip, '__len__'):
                # Handle list, tuple, or MultiValue objects
                if len(fip) == 0:
                    result.add_error(
                        "Frame Increment Pointer (0028,0009) must contain at least one DICOM tag. "
                        "DICOM PS3.3 C.*******.2 requires at least one value that points to an "
                        "attribute present in the dataset."
                    )
                    return
                tag_list = fip
            elif isinstance(fip, int):
                # Handle case where single integer tag is stored
                tag_list = [fip]
            else:
                result.add_error(
                    f"Frame Increment Pointer (0028,0009) must be a DICOM tag or list of DICOM tags, "
                    f"got {type(fip).__name__}. Value Representation AT (Attribute Tag) "
                    f"requires valid DICOM tag references."
                )
                return
                
            # Validate tag format - handle both BaseTag objects and integers
            for i, tag in enumerate(tag_list):
                if not isinstance(tag, (BaseTag, int)):
                    result.add_error(
                        f"Frame Increment Pointer (0028,0009) value {i+1} must be a DICOM Tag object or integer, "
                        f"got {type(tag).__name__}. Ensure tags are properly formatted as AT Value Representation."
                    )
    
    @staticmethod
    def _validate_frame_increment_pointer_conversion(frame_increment_pointer: Any) -> ValidationResult:
        """Validate frame increment pointer format and conversion.
        
        This method validates the frame increment pointer values that would be used
        in module creation, providing early validation before dataset creation.
        
        Args:
            frame_increment_pointer: List of frame increment pointer values to validate
            
        Returns:
            ValidationResult: Validation results with any conversion errors
        """
        result = ValidationResult()
        
        if not frame_increment_pointer:
            result.add_error(
                "Frame Increment Pointer must contain at least one value. "
                "DICOM PS3.3 C.*******.2 requires at least one value that points to an "
                "attribute present in the dataset."
            )
            return result
            
        for i, tag in enumerate(frame_increment_pointer):
            if isinstance(tag, str):
                # Validate string format
                if not MultiFrameValidator._is_valid_dicom_tag(tag):
                    result.add_error(
                        f"Frame Increment Pointer value {i+1} has invalid string format: '{tag}'. "
                        "Expected format: 'GGGG,EEEE' (e.g., '0018,1063') or 'GGGGEEEE' (e.g., '00181063'). "
                        "Ensure group and element are valid 4-digit hexadecimal values."
                    )
                    continue
                    
                # Try conversion to validate format
                try:
                    if ',' in tag:
                        group_str, element_str = tag.split(',')
                        group = int(group_str, 16)
                        element = int(element_str, 16)
                        if group > 0xFFFF or element > 0xFFFF:
                            result.add_error(
                                f"Frame Increment Pointer value {i+1} '{tag}' contains values outside valid range. "
                                "DICOM tags must have group and element values between 0x0000 and 0xFFFF."
                            )
                    else:
                        # Assume it's a hex string like "00181063"
                        tag_value = int(tag, 16)
                        if tag_value > 0xFFFFFFFF:
                            result.add_error(
                                f"Frame Increment Pointer value {i+1} '{tag}' is outside valid DICOM tag range. "
                                "DICOM tags must be 32-bit values (0x00000000 to 0xFFFFFFFF)."
                            )
                except ValueError:
                    result.add_error(
                        f"Frame Increment Pointer value {i+1} '{tag}' contains invalid hexadecimal values. "
                        "Ensure group and element are valid hexadecimal numbers."
                    )
            elif isinstance(tag, BaseTag):
                # Tag objects are assumed valid
                continue
            elif isinstance(tag, int):
                # Validate integer range
                if tag < 0 or tag > 0xFFFFFFFF:
                    result.add_error(
                        f"Frame Increment Pointer value {i+1} ({tag}) is outside valid DICOM tag range. "
                        "DICOM tags must be 32-bit values (0x00000000 to 0xFFFFFFFF)."
                    )
            else:
                result.add_error(
                    f"Frame Increment Pointer value {i+1} has invalid type: {type(tag).__name__}. "
                    "Expected str (DICOM tag format), BaseTag object, or int (32-bit tag value)."
                )
        
        return result
    
    @staticmethod
    def convert_frame_increment_pointer(frame_increment_pointer: Any) -> tuple[list[int], ValidationResult]:
        """Convert frame increment pointer values to proper integer format for AT VR.
        
        This method provides safe conversion of frame increment pointer values from various
        input formats to the integer format required for DICOM AT (Attribute Tag) VR.
        
        Args:
            frame_increment_pointer: List of frame increment pointer values to convert
            
        Returns:
            tuple: (converted_tags, validation_result)
                - converted_tags: List of converted integer tag values 
                - validation_result: Any conversion errors or warnings
        """
        validation_result = MultiFrameValidator._validate_frame_increment_pointer_conversion(frame_increment_pointer)
        converted_tags = []
        
        # If validation failed, return empty list with errors
        if not validation_result.is_valid:
            return converted_tags, validation_result
            
        for tag in frame_increment_pointer:
            if isinstance(tag, str):
                # Convert "GGGG,EEEE" format to integer
                try:
                    if ',' in tag:
                        group_str, element_str = tag.split(',')
                        group = int(group_str, 16)
                        element = int(element_str, 16)
                        converted_tags.append((group << 16) | element)
                    else:
                        # Assume it's a hex string like "00181063"
                        converted_tags.append(int(tag, 16))
                except ValueError:
                    # This should have been caught in validation, but handle gracefully
                    validation_result.add_error(f"Failed to convert tag string '{tag}' to integer")
                    continue
            elif isinstance(tag, BaseTag):
                # Convert Tag object to integer
                converted_tags.append(int(tag))
            elif isinstance(tag, int):
                # Already in correct format
                converted_tags.append(tag)
        
        return converted_tags, validation_result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Stereo Pairs Present
        if hasattr(dataset, 'StereoPairsPresent'):
            BaseValidator.validate_enumerated_value(
                dataset.StereoPairsPresent,
                [e.value for e in StereoPairsPresent],
                "Stereo Pairs Present (0022,0028)",
                result
            )
    
    @staticmethod
    def _validate_frame_increment_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate frame increment pointer consistency."""
        
        if not hasattr(dataset, 'FrameIncrementPointer'):
            return
        
        # Check if referenced attributes are present in dataset
        fip = dataset.FrameIncrementPointer
        # Handle both single tag and list of tags (including MultiValue)
        if isinstance(fip, (list, tuple)) or hasattr(fip, '__len__') and not isinstance(fip, (BaseTag, int)):
            tag_list = fip
        else:
            tag_list = [fip]
            
        for tag in tag_list:
            if isinstance(tag, BaseTag):
                attr_name = MultiFrameValidator._tag_to_attribute_name(tag)
                if attr_name and not hasattr(dataset, attr_name):
                    result.add_warning(
                        f"Frame Increment Pointer (0028,0009) references {tag} ({attr_name}) "
                        f"but this attribute is not present in the dataset"
                    )
        
        # Validate specific frame increment scenarios
        from pydicom.tag import Tag
        frame_time_tag = Tag(0x0018, 0x1063)
        frame_time_vector_tag = Tag(0x0018, 0x1065)
        
        # Check if both frame time tags are present (use the same format as properties)
        fip = dataset.FrameIncrementPointer
        if isinstance(fip, (list, tuple)) or hasattr(fip, '__len__') and not isinstance(fip, (BaseTag, int)):
            has_frame_time = frame_time_tag in fip
            has_frame_time_vector = frame_time_vector_tag in fip
        else:
            has_frame_time = fip == frame_time_tag
            has_frame_time_vector = fip == frame_time_vector_tag
        
        if has_frame_time and has_frame_time_vector:
            result.add_warning(
                "Frame Increment Pointer (0028,0009) references both Frame Time (0018,1063) "
                "and Frame Time Vector (0018,1065). Typically only one should be used."
            )
    
    @staticmethod
    def _validate_stereo_pairs_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate stereo pairs consistency."""
        
        if hasattr(dataset, 'StereoPairsPresent') and dataset.StereoPairsPresent == "YES":
            if hasattr(dataset, 'NumberOfFrames'):
                if dataset.NumberOfFrames % 2 != 0:
                    result.add_warning(
                        f"Stereo Pairs Present (0022,0028) is YES but Number of Frames ({dataset.NumberOfFrames}) "
                        f"is odd. DICOM PS3.3 C.*******.3 specifies that when stereoscopic pairs are present, "
                        f"frames are encoded as left/right pairs. Odd frame counts suggest incomplete stereo pairs. "
                        f"Expected even number for complete left/right pair organization."
                    )
                
                if dataset.NumberOfFrames < 2:
                    result.add_error(
                        f"Stereo Pairs Present (0022,0028) is YES but Number of Frames is {dataset.NumberOfFrames}. "
                        f"At least one complete stereo pair (2 frames minimum) is required when "
                        f"stereoscopic imaging is enabled. DICOM PS3.3 C.*******.3."
                    )
            else:
                result.add_error(
                    "Stereo Pairs Present (0022,0028) is YES but Number of Frames (0028,0008) is missing. "
                    "Cannot validate stereo pair consistency without frame count information."
                )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate conditional requirements for multi-frame module.
        
        Currently the Multi-frame Module (PS3.3 C.7.6.6) has no Type 1C or 2C elements,
        but this method is provided for future extensibility and consistency with
        other validator implementations.
        """
        # No Type 1C or 2C elements defined in DICOM PS3.3 C.7.6.6
        # This method serves as a placeholder for future conditional requirements
        pass
    
    @staticmethod
    def _validate_frame_data_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate overall frame data consistency and logical relationships.
        
        This validation ensures that frame-related attributes work together
        coherently and meet clinical imaging requirements.
        """
        if not hasattr(dataset, 'NumberOfFrames'):
            return  # Already handled in Type 1 validation
            
        frame_count = dataset.NumberOfFrames
        
        # Validate reasonable frame count limits for clinical use
        if frame_count > 10000:
            result.add_warning(
                f"Number of Frames ({frame_count}) is very large. "
                f"While technically valid, frame counts over 10,000 may cause "
                f"performance issues in DICOM viewers and PACS systems. "
                f"Consider verifying this is the intended frame count."
            )
        
        # Check for potential single-frame misuse
        if frame_count == 1 and hasattr(dataset, 'StereoPairsPresent'):
            if dataset.StereoPairsPresent == "YES":
                result.add_error(
                    "Single frame (NumberOfFrames=1) cannot have Stereo Pairs Present=YES. "
                    "Stereoscopic imaging requires at least 2 frames (left/right pair). "
                    "Either increase frame count or set StereoPairsPresent to NO."
                )
    
    @staticmethod
    def _is_valid_dicom_tag(tag: str) -> bool:
        """Check if string is a valid DICOM tag format.
        
        Args:
            tag (str): Tag string to validate
            
        Returns:
            bool: True if valid DICOM tag format
        """
        if not isinstance(tag, str):
            return False
        
        # Check for format "GGGG,EEEE" or "GGGGEEEE"
        if ',' in tag:
            parts = tag.split(',')
            if len(parts) != 2:
                return False
            group, element = parts
        else:
            if len(tag) != 8:
                return False
            group, element = tag[:4], tag[4:]
        
        # Check if both parts are valid hex
        try:
            int(group, 16)
            int(element, 16)
            return len(group) == 4 and len(element) == 4
        except ValueError:
            return False
    
    @staticmethod
    def _tag_to_attribute_name(tag: BaseTag | int) -> str | None:
        """Convert DICOM tag to likely attribute name.
        
        Args:
            tag: DICOM tag object or integer representation
            
        Returns:
            str | None: Attribute name if known, None otherwise
        """
        # Map of common tags to attribute names used in frame increment pointers
        from pydicom.tag import Tag
        
        # Convert integer tags to Tag objects for comparison
        if isinstance(tag, int):
            try:
                tag = Tag(tag)
            except (ValueError, TypeError):
                return None
        
        tag_map = {
            Tag(0x0018, 0x1063): "FrameTime",
            Tag(0x0018, 0x1065): "FrameTimeVector",
            Tag(0x5200, 0x9230): "PerFrameFunctionalGroupsSequence",
            Tag(0x0008, 0x0023): "ContentDate",
            Tag(0x0008, 0x0033): "ContentTime",
            Tag(0x0020, 0x0013): "InstanceNumber",
            Tag(0x3004, 0x000C): "GridFrameOffsetVector",  # RT Dose specific
            Tag(0x0020, 0x0032): "ImagePositionPatient",
            Tag(0x0020, 0x1041): "SliceLocation",
            Tag(0x0018, 0x0050): "SliceThickness"
        }
        
        return tag_map.get(tag)
