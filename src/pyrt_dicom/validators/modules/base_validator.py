"""Base validation framework for DICOM modules."""

import re
from dataclasses import dataclass
from typing import Any
from pydicom import Dataset
from ..validation_result import ValidationResult


@dataclass
class ValidationConfig:
    """Configuration for DICOM validation behavior."""
    strict_mode: bool = False
    check_enumerated_values: bool = True
    validate_sequences: bool = True
    validate_required_elements: bool = True
    validate_conditional_requirements: bool = True
    validate_semantic_constraints: bool = True
    validate_cross_field_dependencies: bool = True
    validate_optional_modules: bool = True
    validate_cross_module_dependencies: bool = True


class BaseValidator:
    """Base validation utilities for DICOM modules."""
    
    @staticmethod
    def validate_enumerated_value(
        value: Any, 
        allowed_values: list[str], 
        field_name: str,
        result: ValidationResult
    ) -> ValidationResult:
        """Validate enumerated values against allowed list.
        
        Args:
            value: Value to validate
            allowed_values: List of allowed string values
            field_name: DICOM field name for error reporting
            result: ValidationResult instance to update
            
        Returns:
            Updated ValidationResult instance
        """
        if value and str(value) not in allowed_values:
            message = f"{field_name} value '{value}' should be one of: {', '.join(allowed_values)}"
            result.add_warning(message)
        
        return result
    
    @staticmethod
    def validate_conditional_requirement(
        condition: bool,
        required_fields: list[str],
        dataset: Dataset,
        error_message: str,
        result: ValidationResult
    ) -> ValidationResult:
        """Validate Type 1C conditional requirements.
        
        Args:
            condition: Whether the condition is met
            required_fields: List of field names that should be present
            dataset: Dataset to check for field presence
            error_message: Error message if requirement not met
            result: ValidationResult instance to update
            
        Returns:
            Updated ValidationResult instance
        """
        if condition:
            missing_fields = [field for field in required_fields if not hasattr(dataset, field)]
            if len(missing_fields) == len(required_fields):
                result.add_error(error_message)
        return result
    
    @staticmethod
    def validate_either_or_requirement(
        condition: bool,
        field_a: str,
        field_b: str,
        dataset: Dataset,
        error_message: str,
        result: ValidationResult
    ) -> ValidationResult:
        """Validate either/or conditional requirements.
        
        Args:
            condition: Whether the condition is met
            field_a: First alternative field name
            field_b: Second alternative field name  
            dataset: Dataset to check
            error_message: Error message if neither field present
            result: ValidationResult instance to update
            
        Returns:
            Updated ValidationResult instance
        """
        if condition:
            has_a = hasattr(dataset, field_a) and getattr(dataset, field_a, None)
            has_b = hasattr(dataset, field_b) and getattr(dataset, field_b, None)
            if not has_a and not has_b:
                result.add_error(error_message)
        return result
        
    @staticmethod
    def validate_uid_format(uid: str, field_name: str, result: ValidationResult) -> None:
        """Validate UID format according to DICOM PS3.5 standard.
        
        Args:
            uid: UID string to validate
            field_name: Name of the field being validated (for error messages)
            result: ValidationResult instance to update with errors
        """
        if not uid:
            result.add_error(f"{field_name} cannot be empty")
            return
            
        # UID format validation according to DICOM PS3.5
        # UIDs are composed of numeric components separated by periods
        # Each component must start with 0-9 and not start with 0 unless it's a single 0
        # Maximum length is 64 characters total
        
        if len(uid) > 64:
            result.add_error(
                f"{field_name} exceeds maximum length of 64 characters (length: {len(uid)})"
            )
            
        # Check for invalid characters (only digits and periods allowed)
        if not re.match(r'^[0-9]+(\.[0-9]+)*$', uid):
            result.add_error(
                f"{field_name} contains invalid characters and may only contain digits (0-9) and periods (.)"
            )
            
        # Check for leading/trailing periods
        if uid.startswith('.') or uid.endswith('.'):
            result.add_error(
                f"{field_name} cannot start or end with a period"
            )
            
        # Check for consecutive periods
        if '..' in uid:
            result.add_error(
                f"{field_name} cannot contain consecutive periods"
            )
            
        # Check for leading zeros in components (except single zero)
        components = uid.split('.')
        for comp in components:
            if comp.startswith('0') and len(comp) > 1:
                result.add_error(
                    f"{field_name} component '{comp}' has leading zero"
                )
    
    @staticmethod
    def validate_date_format(date_value) -> bool:
        """Check if date value is in valid DICOM DA format (YYYYMMDD)."""
        # Extract string value from DICOM data element or use as-is if already string
        if hasattr(date_value, 'value'):
            date_str = str(date_value.value)
        else:
            date_str = str(date_value)
            
        if len(date_str) != 8:
            return False
        
        try:
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])
            
            # Basic range checks
            if not (1900 <= year <= 9999):
                return False
            if not (1 <= month <= 12):
                return False
            if not (1 <= day <= 31):
                return False
            
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_time_format(time_value) -> bool:
        """Check if time value is in valid DICOM TM format (HHMMSS or HHMMSS.FFFFFF)."""
        # Extract string value from DICOM data element or use as-is if already string
        if hasattr(time_value, 'value'):
            time_str = str(time_value.value)
        else:
            time_str = str(time_value)
            
        # Remove fractional seconds if present
        if '.' in time_str:
            base_time, fraction = time_str.split('.', 1)
            if len(fraction) > 6:  # Max 6 digits for microseconds
                return False
        else:
            base_time = time_str
        
        if len(base_time) != 6:
            return False
        
        try:
            hour = int(base_time[:2])
            minute = int(base_time[2:4])
            second = int(base_time[4:6])
            
            # Basic range checks
            if not (0 <= hour <= 23):
                return False
            if not (0 <= minute <= 59):
                return False
            if not (0 <= second <= 59):
                return False
            
            return True
        except ValueError:
            return False

    @staticmethod
    def create_validation_result() -> ValidationResult:
        """Create a new ValidationResult instance.
        
        Returns:
            New ValidationResult instance
        """
        return ValidationResult()
    
    @staticmethod
    def create_validation_result_from_lists(errors: list[str], warnings: list[str]) -> ValidationResult:
        """Create ValidationResult instance from error and warning lists.
        
        Args:
            errors: List of validation errors
            warnings: List of validation warnings
            
        Returns:
            ValidationResult instance populated with the provided errors and warnings
        """
        result = ValidationResult()
        result.extend_errors(errors)
        result.extend_warnings(warnings)
        return result