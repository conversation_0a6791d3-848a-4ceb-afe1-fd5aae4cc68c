"""RT Dose IOD DICOM validation - PS3.3 A.18.3"""

from pydicom import Dataset
from pydicom import uid
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class RTDoseIODValidator(BaseValidator):
    """Validator for DICOM RT Dose IOD (PS3.3 A.18.3).
    
    This validator implements comprehensive validation for RT Dose IODs
    based on the DICOM PS3.3 A.18.3 specification. It validates:
    - Required modules presence and compliance
    - Conditional modules based on dose data characteristics
    - Optional modules when present
    - Cross-module dependencies and consistency
    - IOD-specific requirements and constraints
    """
    
    # RT Dose Storage SOP Class UID
    RT_DOSE_SOP_CLASS_UID = uid.RTDoseStorage
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Dose IOD requirements.
        
        Args:
            dataset: pydicom Dataset to validate against RT Dose IOD requirements
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate base IOD requirements (SOP Class/Instance UIDs)
        RTDoseIODValidator._validate_base_iod_requirements(dataset, result)
        
        # Validate required modules are present and valid
        RTDoseIODValidator._validate_required_modules(dataset, result, config)
        
        # Validate conditional modules if present
        RTDoseIODValidator._validate_conditional_modules(dataset, result, config)
        
        # Validate optional modules if present
        if config.validate_optional_modules:
            RTDoseIODValidator._validate_optional_modules(dataset, result, config)
        
        # Validate cross-module dependencies
        if config.validate_cross_module_dependencies:
            RTDoseIODValidator._validate_cross_module_dependencies(dataset, result, config)
        
        # Validate RT Dose-specific IOD requirements
        if config.validate_conditional_requirements:
            RTDoseIODValidator._validate_rt_dose_iod_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_base_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate base IOD requirements (SOP Class and Instance UIDs)."""
        # Validate SOP Class UID
        sop_class_uid = getattr(dataset, 'SOPClassUID', None)
        if not sop_class_uid:
            result.add_error("SOP Class UID (0008,0016) is required for RT Dose IOD")
        elif sop_class_uid != RTDoseIODValidator.RT_DOSE_SOP_CLASS_UID:
            result.add_error(
                f"SOP Class UID must be '{RTDoseIODValidator.RT_DOSE_SOP_CLASS_UID}' for RT Dose IOD, "
                f"got '{sop_class_uid}'"
            )
        
        # Validate SOP Instance UID
        sop_instance_uid = getattr(dataset, 'SOPInstanceUID', None)
        if not sop_instance_uid:
            result.add_error("SOP Instance UID (0008,0018) is required for RT Dose IOD")
    
    @staticmethod
    def _validate_required_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate that all required modules are present and valid.
        
        Required modules per DICOM PS3.3 A.18.3:
        - Patient Module (C.7.1.1) - M
        - General Study Module (C.7.2.1) - M  
        - RT Series Module (C.8.8.1) - M
        - Frame of Reference Module (C.7.4.1) - M
        - General Equipment Module (C.7.5.1) - M
        - RT Dose Module (C.8.8.3) - M
        - SOP Common Module (C.12.1) - M
        """
        # Import module validators here to avoid circular imports
        from ..modules.patient_validator import PatientValidator
        from ..modules.general_study_validator import GeneralStudyValidator
        from ..modules.rt_series_validator import RTSeriesValidator
        from ..modules.frame_of_reference_validator import FrameOfReferenceValidator
        from ..modules.general_equipment_validator import GeneralEquipmentValidator
        from ..modules.rt_dose_validator import RTDoseValidator
        from ..modules.sop_common_validator import SOPCommonValidator
        
        # Required modules mapping: (validator, module_name, key_elements)
        required_modules = [
            (PatientValidator, 'Patient Module', 
             ['PatientName', 'PatientID', 'PatientBirthDate', 'PatientSex']),
            (GeneralStudyValidator, 'General Study Module',
             ['StudyInstanceUID']),
            (RTSeriesValidator, 'RT Series Module',
             ['Modality', 'SeriesInstanceUID']),
            (FrameOfReferenceValidator, 'Frame of Reference Module',
             ['FrameOfReferenceUID']),
            (GeneralEquipmentValidator, 'General Equipment Module',
             ['Manufacturer']),
            (RTDoseValidator, 'RT Dose Module',
             ['DoseUnits', 'DoseType', 'DoseSummationType']),
            (SOPCommonValidator, 'SOP Common Module',
             ['SOPClassUID', 'SOPInstanceUID'])
        ]
        
        for validator, module_name, key_elements in required_modules:
            # Check if module is present (any key element exists)
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if not module_present:
                result.add_error(f"{module_name} is required for RT Dose IOD but appears to be missing")
            else:
                # Module is present, validate it
                module_result = validator.validate(dataset, config)
                result.merge(module_result)
    
    @staticmethod
    def _validate_conditional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate conditional modules if present.
        
        Conditional modules per DICOM PS3.3 A.18.3:
        - General Image Module (C.7.6.1) - C (Required if dose data contains grid-based doses)
        - Image Plane Module (C.7.6.2) - C (Required if dose data contains grid-based doses)
        - Image Pixel Module (C.7.6.3) - C (Required if dose data contains grid-based doses)
        - Multi-frame Module (C.7.6.6) - C (Required if dose data contains multi-frame pixel data)
        - Frame Extraction Module (C.12.3) - C (Required if SOP Instance created from frame-level retrieve)
        """
        # Import module validators
        from ..modules.general_image_validator import GeneralImageValidator
        from ..modules.image_plane_validator import ImagePlaneValidator
        from ..modules.image_pixel_validator import ImagePixelValidator
        from ..modules.multi_frame_validator import MultiFrameValidator
        from ..modules.frame_extraction_validator import FrameExtractionValidator
        
        # Check for grid-based dose data (presence of PixelData)
        has_pixel_data = hasattr(dataset, 'PixelData')
        
        # Define conditional modules with their conditions
        conditional_modules = [
            # Modules required when grid-based doses are present
            (GeneralImageValidator, 'General Image Module', has_pixel_data,
             ['InstanceNumber'], 'dose data contains grid-based doses'),
            (ImagePlaneValidator, 'Image Plane Module', has_pixel_data,
             ['PixelSpacing', 'ImageOrientationPatient'], 'dose data contains grid-based doses'),
            (ImagePixelValidator, 'Image Pixel Module', has_pixel_data,
             ['Rows', 'Columns', 'BitsAllocated'], 'dose data contains grid-based doses'),
        ]
        
        # Check multi-frame requirement
        number_of_frames = getattr(dataset, 'NumberOfFrames', 1)
        has_multi_frame_data = number_of_frames > 1
        conditional_modules.append(
            (MultiFrameValidator, 'Multi-frame Module', has_multi_frame_data,
             ['NumberOfFrames'], 'dose data contains multi-frame pixel data')
        )
        
        # Check frame extraction requirement (implementation-specific)
        has_frame_extraction = any(hasattr(dataset, attr) for attr in 
                                 ['MultiFrameSourceSOPInstanceUID', 'CalculatedFrameList'])
        conditional_modules.append(
            (FrameExtractionValidator, 'Frame Extraction Module', has_frame_extraction,
             ['MultiFrameSourceSOPInstanceUID'], 'SOP Instance created from frame-level retrieve')
        )
        
        for validator, module_name, condition, key_elements, condition_desc in conditional_modules:
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if condition and not module_present:
                result.add_error(f"{module_name} is required when {condition_desc}")
            elif module_present:
                # Module is present, validate it regardless of condition
                module_result = validator.validate(dataset, config)
                result.merge(module_result)
    
    @staticmethod
    def _validate_optional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate optional modules if present.
        
        Optional modules per DICOM PS3.3 A.18.3:
        - Clinical Trial Subject Module (C.7.1.3) - U
        - Patient Study Module (C.7.2.2) - U
        - Clinical Trial Study Module (C.7.2.3) - U
        - Clinical Trial Series Module (C.7.3.2) - U
        - RT DVH Module (C.8.8.4) - U
        - Common Instance Reference Module (C.12.2) - U
        """
        # Import module validators
        from ..modules.clinical_trial_subject_validator import ClinicalTrialSubjectValidator
        from ..modules.patient_study_validator import PatientStudyValidator
        from ..modules.clinical_trial_study_validator import ClinicalTrialStudyValidator
        from ..modules.clinical_trial_series_validator import ClinicalTrialSeriesValidator
        from ..modules.rt_dvh_validator import RTDVHValidator
        from ..modules.common_instance_reference_validator import CommonInstanceReferenceValidator
        
        # Define optional modules
        optional_modules = [
            (ClinicalTrialSubjectValidator, 'Clinical Trial Subject Module',
             ['ClinicalTrialSponsorName', 'ClinicalTrialProtocolID']),
            (PatientStudyValidator, 'Patient Study Module',
             ['PatientAge', 'PatientWeight', 'PatientSize']),
            (ClinicalTrialStudyValidator, 'Clinical Trial Study Module',
             ['ClinicalTrialTimePointID', 'ClinicalTrialTimePointDescription']),
            (ClinicalTrialSeriesValidator, 'Clinical Trial Series Module',
             ['ClinicalTrialCoordinatingCenterName']),
            (RTDVHValidator, 'RT DVH Module',
             ['DVHSequence', 'ReferencedStructureSetSequence']),
            (CommonInstanceReferenceValidator, 'Common Instance Reference Module',
             ['ReferencedSeriesSequence', 'StudiesContainingOtherReferencedInstancesSequence'])
        ]
        
        for validator, _, key_elements in optional_modules:
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if module_present:
                # Optional module is present, validate it
                module_result = validator.validate(dataset, config)
                result.merge(module_result)
    
    @staticmethod
    def _validate_cross_module_dependencies(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate cross-module dependencies and consistency."""
        from ..cross_module.frame_reference_consistency_validator import FrameReferenceConsistencyValidator
        from ..cross_module.rt_plan_structure_reference_validator import RTPlanStructureReferenceValidator
        
        # Validate Frame of Reference consistency
        frame_ref_result = FrameReferenceConsistencyValidator.validate(dataset, config)
        result.merge(frame_ref_result)
        
        # Validate RT Plan and Structure Set references
        rt_ref_result = RTPlanStructureReferenceValidator.validate(dataset, config)
        result.merge(rt_ref_result)
    
    @staticmethod
    def _validate_rt_dose_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT Dose IOD-specific requirements."""
        # Validate RT Series modality must be RTDOSE
        modality = getattr(dataset, 'Modality', None)
        if modality and modality != 'RTDOSE':
            result.add_error(f"RT Series Module modality must be 'RTDOSE' for RT Dose IOD, got '{modality}'")
        
        # Validate dose parameters if present
        RTDoseIODValidator._validate_dose_parameters(dataset, result)
    
    @staticmethod
    def _validate_dose_parameters(dataset: Dataset, result: ValidationResult) -> None:
        """Validate dose units and type parameters."""
        # Validate dose units
        dose_units = getattr(dataset, 'DoseUnits', None)
        if dose_units and dose_units not in ['GY', 'RELATIVE']:
            result.add_warning(
                f"Dose Units '{dose_units}' should typically be 'GY' or 'RELATIVE'"
            )
        
        # Validate dose type
        dose_type = getattr(dataset, 'DoseType', None)
        if dose_type and dose_type not in ['PHYSICAL', 'EFFECTIVE', 'ERROR']:
            result.add_warning(
                f"Dose Type '{dose_type}' should be 'PHYSICAL', 'EFFECTIVE', or 'ERROR'"
            )