"""RT Image IOD DICOM validation - PS3.3 A.17.3"""

from pydicom import Dataset
from pydicom import uid
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class RTImageIODValidator(BaseValidator):
    """Validator for DICOM RT Image IOD (PS3.3 A.17.3).
    
    This validator implements comprehensive validation for RT Image IODs
    based on the DICOM PS3.3 A.17.3 specification. It validates:
    - Required modules presence and compliance
    - Conditional modules based on image data characteristics
    - Optional modules when present
    - Cross-module dependencies and consistency
    - IOD-specific requirements and constraints
    
    The RT Image IOD is designed to address requirements for image transfer
    found in general radiotherapy applications performed on conventional
    simulators, virtual simulators, and portal imaging devices. Such images
    have conical imaging geometry and may be acquired directly from devices
    or digitized using film digitizers.
    """
    
    # RT Image Storage SOP Class UID
    RT_IMAGE_SOP_CLASS_UID = uid.RTImageStorage
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Image IOD requirements.
        
        Args:
            dataset: pydicom Dataset to validate against RT Image IOD requirements
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Step 1: Validate base IOD requirements (SOP Class/Instance UIDs)
        RTImageIODValidator._validate_base_iod_requirements(dataset, result)
        
        # Step 2: Validate required modules are present and valid
        RTImageIODValidator._validate_required_modules(dataset, result, config)
        
        # Step 3: Validate conditional modules if applicable
        RTImageIODValidator._validate_conditional_modules(dataset, result, config)
        
        # Step 4: Validate optional modules if present
        if config.validate_optional_modules:
            RTImageIODValidator._validate_optional_modules(dataset, result, config)
        
        # Step 5: Validate cross-module dependencies
        if config.validate_cross_module_dependencies:
            RTImageIODValidator._validate_cross_module_dependencies(dataset, result, config)
        
        # Step 6: Validate RT Image-specific IOD requirements
        if config.validate_conditional_requirements:
            RTImageIODValidator._validate_rt_image_iod_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_base_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate base IOD requirements (SOP Class and Instance UIDs)."""
        # Validate SOP Class UID
        sop_class_uid = getattr(dataset, 'SOPClassUID', None)
        if not sop_class_uid:
            result.add_error("SOP Class UID (0008,0016) is required for RT Image IOD")
        elif sop_class_uid != RTImageIODValidator.RT_IMAGE_SOP_CLASS_UID:
            result.add_error(
                f"SOP Class UID must be '{RTImageIODValidator.RT_IMAGE_SOP_CLASS_UID}' for RT Image IOD, "
                f"got '{sop_class_uid}'"
            )
        
        # Validate SOP Instance UID
        sop_instance_uid = getattr(dataset, 'SOPInstanceUID', None)
        if not sop_instance_uid:
            result.add_error("SOP Instance UID (0008,0018) is required for RT Image IOD")
        
        # Optional: Validate UID format
        if sop_instance_uid and not RTImageIODValidator._is_valid_uid(sop_instance_uid):
            result.add_warning(f"SOP Instance UID format may be invalid: {sop_instance_uid}")
    
    @staticmethod
    def _validate_required_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate that all required modules are present and valid.
        
        Required modules per DICOM PS3.3 A.17.3:
        - Patient Module (C.7.1.1) - M
        - General Study Module (C.7.2.1) - M  
        - RT Series Module (C.8.8.1) - M
        - General Equipment Module (C.7.5.1) - M
        - General Acquisition Module (C.7.10.1) - M
        - General Image Module (C.7.6.1) - M
        - Image Pixel Module (C.7.6.3) - M
        - RT Image Module (C.8.8.2) - M
        - SOP Common Module (C.12.1) - M
        """
        # Import module validators here to avoid circular imports
        from ..modules.patient_validator import PatientValidator
        from ..modules.general_study_validator import GeneralStudyValidator
        from ..modules.rt_series_validator import RTSeriesValidator
        from ..modules.general_equipment_validator import GeneralEquipmentValidator
        from ..modules.general_acquisition_validator import GeneralAcquisitionValidator
        from ..modules.general_image_validator import GeneralImageValidator
        from ..modules.image_pixel_validator import ImagePixelValidator
        from ..modules.rt_image_validator import RTImageValidator
        from ..modules.sop_common_validator import SOPCommonValidator
        
        # Required modules mapping: (validator, module_name, key_elements)
        required_modules = [
            (PatientValidator, 'Patient Module', 
             ['PatientName', 'PatientID', 'PatientBirthDate', 'PatientSex']),
            (GeneralStudyValidator, 'General Study Module',
             ['StudyInstanceUID']),
            (RTSeriesValidator, 'RT Series Module',
             ['Modality', 'SeriesInstanceUID']),
            (GeneralEquipmentValidator, 'General Equipment Module',
             ['Manufacturer']),
            (GeneralAcquisitionValidator, 'General Acquisition Module',
             ['AcquisitionDate', 'AcquisitionTime', 'AcquisitionNumber']),
            (GeneralImageValidator, 'General Image Module',
             ['InstanceNumber']),
            (ImagePixelValidator, 'Image Pixel Module',
             ['Rows', 'Columns', 'BitsAllocated', 'SamplesPerPixel']),
            (RTImageValidator, 'RT Image Module',
             ['RTImageLabel', 'ConversionType', 'RTImagePlane']),
            (SOPCommonValidator, 'SOP Common Module',
             ['SOPClassUID', 'SOPInstanceUID'])
        ]
        
        for validator, module_name, key_elements in required_modules:
            # Check if module is present (any key element exists)
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if not module_present:
                result.add_error(f"{module_name} is required for RT Image IOD but appears to be missing")
            else:
                # Module is present, validate it
                module_result = validator.validate(dataset, config)
                result.merge(module_result, prefix=f"{module_name}")
    
    @staticmethod
    def _validate_conditional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate conditional modules if present.
        
        Conditional modules per DICOM PS3.3 A.17.3:
        - Contrast/Bolus Module (C.7.6.4) - C (Required if contrast media was used)
        - Cine Module (C.7.6.5) - C (Required if multi-frame image is a cine image)
        - Multi-frame Module (C.7.6.6) - C (Required if pixel data is multi-frame data)
        - Frame Extraction Module (C.12.3) - C (Required if SOP Instance created from frame-level retrieve)
        """
        # Import module validators
        from ..modules.contrast_bolus_validator import ContrastBolusValidator
        from ..modules.cine_validator import CineValidator
        from ..modules.multi_frame_validator import MultiFrameValidator
        from ..modules.frame_extraction_validator import FrameExtractionValidator
        
        # Check for contrast media usage
        has_contrast = RTImageIODValidator._check_contrast_media_used(dataset)
        
        # Check for cine image characteristics
        has_cine_image = RTImageIODValidator._check_cine_image(dataset)
        
        # Check for multi-frame data
        has_multi_frame_data = RTImageIODValidator._check_multi_frame_data(dataset)
        
        # Check for frame extraction
        has_frame_extraction = RTImageIODValidator._check_frame_extraction(dataset)
        
        # Define conditional modules with their conditions
        conditional_modules = [
            # Contrast/Bolus module required when contrast media was used
            (ContrastBolusValidator, 'Contrast/Bolus Module', has_contrast,
             ['ContrastBolusAgent'], 'contrast media was used in this image'),
             
            # Cine module required when multi-frame image is a cine image
            (CineValidator, 'Cine Module', has_cine_image,
             ['CineRate', 'FrameTime'], 'multi-frame image is a cine image'),
             
            # Multi-frame module required when pixel data is multi-frame data
            (MultiFrameValidator, 'Multi-frame Module', has_multi_frame_data,
             ['NumberOfFrames'], 'pixel data is multi-frame data'),
             
            # Frame Extraction module required when SOP Instance created from frame-level retrieve
            (FrameExtractionValidator, 'Frame Extraction Module', has_frame_extraction,
             ['MultiFrameSourceSOPInstanceUID', 'CalculatedFrameList'], 
             'SOP Instance created in response to frame-level retrieve request')
        ]
        
        for validator, module_name, condition, key_elements, condition_desc in conditional_modules:
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if condition and not module_present:
                result.add_error(f"{module_name} is required when {condition_desc}")
            elif module_present:
                # Module is present, validate it regardless of condition
                module_result = validator.validate(dataset, config)
                result.merge(module_result, prefix=f"{module_name}")
    
    @staticmethod
    def _validate_optional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate optional modules if present.
        
        Optional modules per DICOM PS3.3 A.17.3:
        - Clinical Trial Subject Module (C.7.1.3) - U
        - Patient Study Module (C.7.2.2) - U
        - Clinical Trial Study Module (C.7.2.3) - U
        - Clinical Trial Series Module (C.7.3.2) - U
        - Frame of Reference Module (C.7.4.1) - U
        - General Reference Module (C.12.4) - U
        - Device Module (C.7.6.12) - U
        - Modality LUT Module (C.11.1) - U
        - VOI LUT Module (C.11.2) - U
        - Approval Module (C.8.8.16) - U
        - Common Instance Reference Module (C.12.2) - U
        """
        # Import module validators
        from ..modules.clinical_trial_subject_validator import ClinicalTrialSubjectValidator
        from ..modules.patient_study_validator import PatientStudyValidator
        from ..modules.clinical_trial_study_validator import ClinicalTrialStudyValidator
        from ..modules.clinical_trial_series_validator import ClinicalTrialSeriesValidator
        from ..modules.frame_of_reference_validator import FrameOfReferenceValidator
        from ..modules.general_reference_validator import GeneralReferenceValidator
        from ..modules.device_validator import DeviceValidator
        from ..modules.modality_lut_validator import ModalityLutValidator
        from ..modules.voi_lut_validator import VoiLutValidator
        from ..modules.approval_validator import ApprovalValidator
        from ..modules.common_instance_reference_validator import CommonInstanceReferenceValidator
        
        # Define optional modules
        optional_modules = [
            (ClinicalTrialSubjectValidator, 'Clinical Trial Subject Module',
             ['ClinicalTrialSponsorName', 'ClinicalTrialProtocolID']),
            (PatientStudyValidator, 'Patient Study Module',
             ['PatientAge', 'PatientWeight', 'PatientSize']),
            (ClinicalTrialStudyValidator, 'Clinical Trial Study Module',
             ['ClinicalTrialTimePointID', 'ClinicalTrialTimePointDescription']),
            (ClinicalTrialSeriesValidator, 'Clinical Trial Series Module',
             ['ClinicalTrialCoordinatingCenterName']),
            (FrameOfReferenceValidator, 'Frame of Reference Module',
             ['FrameOfReferenceUID']),
            (GeneralReferenceValidator, 'General Reference Module',
             ['ReferencedImageSequence', 'ReferencedSOPClassUID']),
            (DeviceValidator, 'Device Module',
             ['DeviceSerialNumber', 'DeviceUID']),
            (ModalityLutValidator, 'Modality LUT Module',
             ['ModalityLUTSequence', 'RescaleIntercept', 'RescaleSlope']),
            (VoiLutValidator, 'VOI LUT Module',
             ['WindowCenter', 'WindowWidth', 'VOILUTSequence']),
            (ApprovalValidator, 'Approval Module',
             ['ApprovalStatus']),
            (CommonInstanceReferenceValidator, 'Common Instance Reference Module',
             ['ReferencedSeriesSequence', 'StudiesContainingOtherReferencedInstancesSequence'])
        ]
        
        for validator, module_name, key_elements in optional_modules:
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if module_present:
                # Optional module is present, validate it
                module_result = validator.validate(dataset, config)
                result.merge(module_result, prefix=f"{module_name}")
    
    @staticmethod
    def _validate_cross_module_dependencies(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate cross-module dependencies and consistency."""
        # Import cross-module validators
        from ..cross_module.frame_reference_consistency_validator import FrameReferenceConsistencyValidator
        from ..cross_module.uid_consistency_validator import UIDConsistencyValidator
        from ..cross_module.spatial_consistency_validator import SpatialConsistencyValidator
        from ..cross_module.temporal_consistency_validator import TemporalConsistencyValidator
        
        # Validate Frame of Reference consistency if present
        if hasattr(dataset, 'FrameOfReferenceUID'):
            frame_ref_result = FrameReferenceConsistencyValidator.validate(dataset, config)
            result.merge(frame_ref_result, prefix="Frame of Reference Consistency")
        
        # Validate UID consistency across modules
        uid_result = UIDConsistencyValidator.validate(dataset, config)
        result.merge(uid_result, prefix="UID Consistency")
        
        # Validate spatial consistency if spatial data is present
        if RTImageIODValidator._has_spatial_data(dataset):
            spatial_result = SpatialConsistencyValidator.validate(dataset, config)
            result.merge(spatial_result, prefix="Spatial Consistency")
        
        # Validate temporal consistency if temporal data is present
        if RTImageIODValidator._has_temporal_data(dataset):
            temporal_result = TemporalConsistencyValidator.validate(dataset, config)
            result.merge(temporal_result, prefix="Temporal Consistency")
    
    @staticmethod
    def _validate_rt_image_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT Image IOD-specific requirements."""
        # Validate RT Series modality must be RTIMAGE
        modality = getattr(dataset, 'Modality', None)
        if modality and modality != 'RTIMAGE':
            result.add_error(f"RT Series Module modality must be 'RTIMAGE' for RT Image IOD, got '{modality}'")
        
        # Validate RT Image parameters if present
        RTImageIODValidator._validate_rt_image_parameters(dataset, result)
        
        # Validate image type consistency
        RTImageIODValidator._validate_image_type_consistency(dataset, result)
        
        # Validate beam geometry parameters
        RTImageIODValidator._validate_beam_geometry(dataset, result)
    
    @staticmethod
    def _validate_rt_image_parameters(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT Image specific parameters."""
        # Validate RT Image Plane
        rt_image_plane = getattr(dataset, 'RTImagePlane', None)
        if rt_image_plane and rt_image_plane not in ['NORMAL', 'HALF_BEAM_BLOCK']:
            result.add_warning(
                f"RT Image Plane '{rt_image_plane}' should typically be 'NORMAL' or 'HALF_BEAM_BLOCK'"
            )
        
        # Validate Conversion Type
        conversion_type = getattr(dataset, 'ConversionType', None)
        if conversion_type and conversion_type not in ['DI', 'DV', 'SD', 'SI', 'SV', 'WSD']:
            result.add_warning(
                f"Conversion Type '{conversion_type}' should be a valid DICOM conversion type"
            )
        
        # Validate RT Image SID if present
        rt_image_sid = getattr(dataset, 'RTImageSID', None)
        if rt_image_sid is not None:
            try:
                sid_value = float(rt_image_sid)
                if sid_value <= 0:
                    result.add_error("RT Image SID must be greater than 0")
            except (ValueError, TypeError):
                result.add_error(f"RT Image SID must be a valid numeric value, got '{rt_image_sid}'")
    
    @staticmethod
    def _validate_image_type_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Image Type consistency with RT Image characteristics."""
        image_type = getattr(dataset, 'ImageType', [])
        if not isinstance(image_type, (list, tuple)):
            image_type = [image_type] if image_type else []
        
        # Check for valid RT Image types
        valid_rt_types = ['ORIGINAL', 'DERIVED', 'PRIMARY', 'SECONDARY', 
                         'PORTAL', 'VERIFICATION', 'SIMULATOR', 'DRR']
        
        if image_type:
            # First value should indicate original vs derived
            if image_type[0] not in ['ORIGINAL', 'DERIVED']:
                result.add_warning(
                    f"Image Type first value should be 'ORIGINAL' or 'DERIVED', got '{image_type[0]}'"
                )
            
            # Check for RT-specific image types
            has_rt_type = any(img_type in valid_rt_types for img_type in image_type)
            if not has_rt_type:
                result.add_warning(
                    "Image Type should include RT-specific values like 'PORTAL', 'VERIFICATION', 'SIMULATOR', or 'DRR'"
                )
    
    @staticmethod
    def _validate_beam_geometry(dataset: Dataset, result: ValidationResult) -> None:
        """Validate beam geometry parameters if present."""
        # Validate Gantry Angle if present
        gantry_angle = getattr(dataset, 'GantryAngle', None)
        if gantry_angle is not None:
            try:
                angle_value = float(gantry_angle)
                if not (0 <= angle_value < 360):
                    result.add_warning("Gantry Angle should be between 0 and 360 degrees")
            except (ValueError, TypeError):
                result.add_error(f"Gantry Angle must be a valid numeric value, got '{gantry_angle}'")
        
        # Validate Patient Support Angle if present
        patient_support_angle = getattr(dataset, 'PatientSupportAngle', None)
        if patient_support_angle is not None:
            try:
                angle_value = float(patient_support_angle)
                if not (-180 <= angle_value <= 180):
                    result.add_warning("Patient Support Angle should be between -180 and 180 degrees")
            except (ValueError, TypeError):
                result.add_error(f"Patient Support Angle must be a valid numeric value, got '{patient_support_angle}'")
        
        # Validate Beam Limiting Device Angle if present
        bld_angle = getattr(dataset, 'BeamLimitingDeviceAngle', None)
        if bld_angle is not None:
            try:
                angle_value = float(bld_angle)
                if not (0 <= angle_value < 360):
                    result.add_warning("Beam Limiting Device Angle should be between 0 and 360 degrees")
            except (ValueError, TypeError):
                result.add_error(f"Beam Limiting Device Angle must be a valid numeric value, got '{bld_angle}'")
    
    @staticmethod
    def _check_contrast_media_used(dataset: Dataset) -> bool:
        """Check if contrast media was used in this image."""
        # Check for contrast agent or contrast-related elements
        contrast_indicators = [
            'ContrastBolusAgent',
            'ContrastBolusRoute',
            'ContrastBolusVolume',
            'ContrastBolusStartTime',
            'ContrastBolusStopTime',
            'ContrastBolusIngredientSequence'
        ]
        return any(hasattr(dataset, attr) for attr in contrast_indicators)
    
    @staticmethod
    def _check_cine_image(dataset: Dataset) -> bool:
        """Check if multi-frame image is a cine image."""
        # Check for cine-specific elements and multi-frame data
        has_multi_frame = getattr(dataset, 'NumberOfFrames', 1) > 1
        cine_indicators = [
            'CineRate',
            'FrameTime',
            'FrameTimeVector',
            'RecommendedDisplayFrameRate',
            'CinePlaybackSequence'
        ]
        has_cine_elements = any(hasattr(dataset, attr) for attr in cine_indicators)
        
        # Cine image requires both multi-frame data and cine-specific elements
        return has_multi_frame and has_cine_elements
    
    @staticmethod
    def _check_multi_frame_data(dataset: Dataset) -> bool:
        """Check if pixel data is multi-frame data."""
        number_of_frames = getattr(dataset, 'NumberOfFrames', 1)
        return number_of_frames > 1
    
    @staticmethod
    def _check_frame_extraction(dataset: Dataset) -> bool:
        """Check if SOP Instance was created from frame-level retrieve."""
        frame_extraction_indicators = [
            'MultiFrameSourceSOPInstanceUID',
            'CalculatedFrameList',
            'FrameExtractionSequence'
        ]
        return any(hasattr(dataset, attr) for attr in frame_extraction_indicators)
    
    @staticmethod
    def _has_spatial_data(dataset: Dataset) -> bool:
        """Check if dataset contains spatial information requiring validation."""
        spatial_elements = [
            'ImagePositionPatient', 
            'ImageOrientationPatient',
            'PixelSpacing', 
            'FrameOfReferenceUID',
            'RTImagePosition',
            'RTImageSID',
            'ImagePlanePixelSpacing'
        ]
        return any(hasattr(dataset, element) for element in spatial_elements)
    
    @staticmethod
    def _has_temporal_data(dataset: Dataset) -> bool:
        """Check if dataset contains temporal information requiring validation."""
        temporal_elements = [
            'StudyDate', 'StudyTime', 
            'SeriesDate', 'SeriesTime',
            'AcquisitionDate', 'AcquisitionTime',
            'ContentDate', 'ContentTime',
            'FrameReferenceTime'
        ]
        return any(hasattr(dataset, element) for element in temporal_elements)
    
    @staticmethod
    def _is_valid_uid(uid_value: str) -> bool:
        """Validate UID format per DICOM PS3.5."""
        import re
        # UID pattern: numbers and dots, max 64 chars, no leading/trailing dots
        pattern = r'^[0-9]+(\.[0-9]+)*$'
        return bool(re.match(pattern, uid_value)) and len(uid_value) <= 64 and not uid_value.startswith('.') and not uid_value.endswith('.')