"""CT Image IOD DICOM validation - PS3.3 A.3"""

from pydicom import Dataset
from pydicom import uid
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class CTImageIODValidator(BaseValidator):
    """Validator for DICOM CT Image IOD (PS3.3 A.3).
    
    This validator implements comprehensive validation for CT Image IODs
    based on the DICOM PS3.3 A.3 specification. It validates:
    - Required modules presence and compliance
    - Conditional modules based on CT acquisition characteristics
    - Optional modules when present
    - Cross-module dependencies and consistency
    - IOD-specific requirements and constraints
    """
    
    # CT Image Storage SOP Class UID
    CT_IMAGE_SOP_CLASS_UID = uid.CTImageStorage
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate CT Image IOD requirements.
        
        Args:
            dataset: pydicom Dataset to validate against CT Image IOD requirements
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate base IOD requirements (SOP Class/Instance UIDs)
        CTImageIODValidator._validate_base_iod_requirements(dataset, result)
        
        # Validate required modules are present and valid
        CTImageIODValidator._validate_required_modules(dataset, result, config)
        
        # Validate conditional modules if applicable
        CTImageIODValidator._validate_conditional_modules(dataset, result, config)
        
        # Validate optional modules if present
        if config.validate_optional_modules:
            CTImageIODValidator._validate_optional_modules(dataset, result, config)
        
        # Validate cross-module dependencies
        if config.validate_cross_module_dependencies:
            CTImageIODValidator._validate_cross_module_dependencies(dataset, result, config)
        
        # Validate CT Image-specific IOD requirements
        if config.validate_conditional_requirements:
            CTImageIODValidator._validate_ct_image_iod_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_base_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate base IOD requirements (SOP Class and Instance UIDs)."""
        # Validate SOP Class UID
        sop_class_uid = getattr(dataset, 'SOPClassUID', None)
        if not sop_class_uid:
            result.add_error("SOP Class UID (0008,0016) is required for CT Image IOD")
        elif sop_class_uid != CTImageIODValidator.CT_IMAGE_SOP_CLASS_UID:
            result.add_error(
                f"SOP Class UID must be '{CTImageIODValidator.CT_IMAGE_SOP_CLASS_UID}' for CT Image IOD, "
                f"got '{sop_class_uid}'"
            )
        
        # Validate SOP Instance UID
        sop_instance_uid = getattr(dataset, 'SOPInstanceUID', None)
        if not sop_instance_uid:
            result.add_error("SOP Instance UID (0008,0018) is required for CT Image IOD")
        
        # Optional: Validate UID format
        if sop_instance_uid and not CTImageIODValidator._is_valid_uid(sop_instance_uid):
            result.add_warning(f"SOP Instance UID format may be invalid: {sop_instance_uid}")
    
    @staticmethod
    def _validate_required_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate that all required modules are present and valid.
        
        Required modules per DICOM PS3.3 A.3:
        - Patient Module (C.7.1.1) - M
        - General Study Module (C.7.2.1) - M  
        - General Series Module (C.7.3.1) - M
        - Frame of Reference Module (C.7.4.1) - M
        - General Equipment Module (C.7.5.1) - M
        - General Acquisition Module (C.7.10.1) - M
        - General Image Module (C.7.6.1) - M
        - Image Plane Module (C.7.6.2) - M
        - Image Pixel Module (C.7.6.3) - M
        - CT Image Module (C.8.2.1) - M
        - SOP Common Module (C.12.1) - M
        """
        # Import module validators here to avoid circular imports
        from ..modules.patient_validator import PatientValidator
        from ..modules.general_study_validator import GeneralStudyValidator
        from ..modules.general_series_validator import GeneralSeriesValidator
        from ..modules.frame_of_reference_validator import FrameOfReferenceValidator
        from ..modules.general_equipment_validator import GeneralEquipmentValidator
        from ..modules.general_acquisition_validator import GeneralAcquisitionValidator
        from ..modules.general_image_validator import GeneralImageValidator
        from ..modules.image_plane_validator import ImagePlaneValidator
        from ..modules.image_pixel_validator import ImagePixelValidator
        from ..modules.ct_image_validator import CTImageValidator
        from ..modules.sop_common_validator import SOPCommonValidator
        
        # Required modules mapping: (validator, module_name, key_elements)
        required_modules = [
            (PatientValidator, 'Patient Module', 
             ['PatientName', 'PatientID', 'PatientBirthDate', 'PatientSex']),
            (GeneralStudyValidator, 'General Study Module',
             ['StudyInstanceUID']),
            (GeneralSeriesValidator, 'General Series Module',
             ['Modality', 'SeriesInstanceUID']),
            (FrameOfReferenceValidator, 'Frame of Reference Module',
             ['FrameOfReferenceUID']),
            (GeneralEquipmentValidator, 'General Equipment Module',
             ['Manufacturer']),
            (GeneralAcquisitionValidator, 'General Acquisition Module',
             ['AcquisitionDate', 'AcquisitionTime']),
            (GeneralImageValidator, 'General Image Module',
             ['InstanceNumber']),
            (ImagePlaneValidator, 'Image Plane Module',
             ['PixelSpacing', 'ImageOrientationPatient']),
            (ImagePixelValidator, 'Image Pixel Module',
             ['Rows', 'Columns', 'BitsAllocated']),
            (CTImageValidator, 'CT Image Module',
             ['KVP']),
            (SOPCommonValidator, 'SOP Common Module',
             ['SOPClassUID', 'SOPInstanceUID'])
        ]
        
        for validator, module_name, key_elements in required_modules:
            # Check if module is present (any key element exists)
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if not module_present:
                result.add_error(f"{module_name} is required for CT Image IOD but appears to be missing")
            else:
                # Module is present, validate it
                module_result = validator.validate(dataset, config)
                result.merge(module_result, prefix=f"{module_name}")
    
    @staticmethod
    def _validate_conditional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate conditional modules if present.
        
        Conditional modules per DICOM PS3.3 A.3:
        - Synchronization Module (C.7.4.2) - C (Required if time synchronization was applied)
        - Contrast/Bolus Module (C.7.6.4) - C (Required if contrast media was used)
        - Multi-energy CT Image Module (C.8.2.2) - C (Required if Multi-energy CT Acquisition is YES)
        """
        # Import module validators
        from ..modules.synchronization_validator import SynchronizationValidator
        from ..modules.contrast_bolus_validator import ContrastBolusValidator
        from ..modules.multi_energy_ct_image_validator import MultiEnergyCTImageValidator
        
        # Check for time synchronization requirement
        acquisition_time_synchronized = getattr(dataset, 'AcquisitionTimeSynchronized', None)
        synchronization_required = acquisition_time_synchronized == 'Y'
        
        # Check for contrast media usage
        contrast_bolus_agent = getattr(dataset, 'ContrastBolusAgent', None)
        contrast_required = contrast_bolus_agent is not None
        
        # Check for multi-energy CT requirement
        multi_energy_acquisition = getattr(dataset, 'MultiEnergyCTAcquisition', None)
        multi_energy_required = multi_energy_acquisition == 'YES'
        
        # Define conditional modules with their conditions
        conditional_modules = [
            # Synchronization module
            (SynchronizationValidator, 'Synchronization Module', synchronization_required,
             ['SynchronizationFrameOfReferenceUID'], 'time synchronization was applied'),
            
            # Contrast/Bolus module
            (ContrastBolusValidator, 'Contrast/Bolus Module', contrast_required,
             ['ContrastBolusAgent'], 'contrast media was used in this image'),
            
            # Multi-energy CT Image module
            (MultiEnergyCTImageValidator, 'Multi-energy CT Image Module', multi_energy_required,
             ['MultiEnergyCTAcquisitionSequence'], 'Multi-energy CT Acquisition is YES')
        ]
        
        for validator, module_name, condition, key_elements, condition_desc in conditional_modules:
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if condition and not module_present:
                result.add_error(f"{module_name} is required when {condition_desc}")
            elif module_present:
                # Module is present, validate it regardless of condition
                module_result = validator.validate(dataset, config)
                result.merge(module_result, prefix=f"{module_name}")
    
    @staticmethod
    def _validate_optional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate optional modules if present.
        
        Optional modules per DICOM PS3.3 A.3:
        - Clinical Trial Subject Module (C.7.1.3) - U
        - Patient Study Module (C.7.2.2) - U
        - Clinical Trial Study Module (C.7.2.3) - U
        - Clinical Trial Series Module (C.7.3.2) - U
        - General Reference Module (C.12.4) - U
        - Enhanced Patient Orientation Module (C.7.6.30) - U
        - Device Module (C.7.6.12) - U
        - Specimen Module (C.7.6.22) - U
        - Overlay Plane Module (C.9.2) - U
        - VOI LUT Module (C.11.2) - U
        - Common Instance Reference Module (C.12.2) - U
        """
        # Import module validators
        from ..modules.clinical_trial_subject_validator import ClinicalTrialSubjectValidator
        from ..modules.patient_study_validator import PatientStudyValidator
        from ..modules.clinical_trial_study_validator import ClinicalTrialStudyValidator
        from ..modules.clinical_trial_series_validator import ClinicalTrialSeriesValidator
        from ..modules.general_reference_validator import GeneralReferenceValidator
        from ..modules.enhanced_patient_orientation_validator import EnhancedPatientOrientationValidator
        from ..modules.device_validator import DeviceValidator
        from ..modules.specimen_validator import SpecimenValidator
        from ..modules.overlay_plane_validator import OverlayPlaneValidator
        from ..modules.voi_lut_validator import VoiLutValidator
        from ..modules.common_instance_reference_validator import CommonInstanceReferenceValidator
        
        # Define optional modules
        optional_modules = [
            (ClinicalTrialSubjectValidator, 'Clinical Trial Subject Module',
             ['ClinicalTrialSponsorName', 'ClinicalTrialProtocolID']),
            (PatientStudyValidator, 'Patient Study Module',
             ['PatientAge', 'PatientWeight', 'PatientSize']),
            (ClinicalTrialStudyValidator, 'Clinical Trial Study Module',
             ['ClinicalTrialTimePointID', 'ClinicalTrialTimePointDescription']),
            (ClinicalTrialSeriesValidator, 'Clinical Trial Series Module',
             ['ClinicalTrialCoordinatingCenterName']),
            (GeneralReferenceValidator, 'General Reference Module',
             ['ReferencedImageSequence', 'ReferencedInstanceSequence']),
            (EnhancedPatientOrientationValidator, 'Enhanced Patient Orientation Module',
             ['PatientOrientationInFrameSequence']),
            (DeviceValidator, 'Device Module',
             ['DeviceSequence']),
            (SpecimenValidator, 'Specimen Module',
             ['SpecimenIdentificationSequence']),
            (OverlayPlaneValidator, 'Overlay Plane Module',
             ['OverlayRows', 'OverlayColumns']),
            (VoiLutValidator, 'VOI LUT Module',
             ['VOILUTSequence', 'WindowCenter']),
            (CommonInstanceReferenceValidator, 'Common Instance Reference Module',
             ['ReferencedSeriesSequence', 'StudiesContainingOtherReferencedInstancesSequence'])
        ]
        
        for validator, module_name, key_elements in optional_modules:
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if module_present:
                # Optional module is present, validate it
                module_result = validator.validate(dataset, config)
                result.merge(module_result, prefix=f"{module_name}")
    
    @staticmethod
    def _validate_cross_module_dependencies(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate cross-module dependencies and consistency."""
        # Import cross-module validators
        from ..cross_module.uid_consistency_validator import UIDConsistencyValidator
        from ..cross_module.spatial_consistency_validator import SpatialConsistencyValidator
        from ..cross_module.temporal_consistency_validator import TemporalConsistencyValidator
        
        # Validate UID consistency across modules
        uid_result = UIDConsistencyValidator.validate(dataset, config)
        result.merge(uid_result, prefix="UID Consistency")
        
        # Validate spatial consistency if applicable
        if CTImageIODValidator._has_spatial_data(dataset):
            spatial_result = SpatialConsistencyValidator.validate(dataset, config)
            result.merge(spatial_result, prefix="Spatial Consistency")
        
        # Validate temporal consistency if applicable
        if CTImageIODValidator._has_temporal_data(dataset):
            temporal_result = TemporalConsistencyValidator.validate(dataset, config)
            result.merge(temporal_result, prefix="Temporal Consistency")
    
    @staticmethod
    def _validate_ct_image_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate CT Image IOD-specific requirements."""
        # Validate General Series modality must be CT
        modality = getattr(dataset, 'Modality', None)
        if modality and modality != 'CT':
            result.add_error(f"General Series Module modality must be 'CT' for CT Image IOD, got '{modality}'")
        
        # Validate CT Image-specific parameters
        CTImageIODValidator._validate_ct_parameters(dataset, result)
        
        # Validate Multi-energy CT specific constraints
        CTImageIODValidator._validate_multi_energy_ct_constraints(dataset, result)
    
    @staticmethod
    def _validate_ct_parameters(dataset: Dataset, result: ValidationResult) -> None:
        """Validate CT-specific parameters."""
        # Validate KVP if present
        kvp = getattr(dataset, 'KVP', None)
        if kvp is not None:
            if not isinstance(kvp, (int, float)) or kvp <= 0:
                result.add_error(f"KVP must be a positive number, got '{kvp}'")
            elif kvp < 20 or kvp > 300:
                result.add_warning(f"KVP value '{kvp}' is outside typical range (20-300 kV)")
        
        # Validate Exposure Time if present
        exposure_time = getattr(dataset, 'ExposureTime', None)
        if exposure_time is not None:
            if not isinstance(exposure_time, (int, float)) or exposure_time <= 0:
                result.add_error(f"Exposure Time must be a positive number, got '{exposure_time}'")
        
        # Validate X-Ray Tube Current if present
        tube_current = getattr(dataset, 'XRayTubeCurrent', None)
        if tube_current is not None:
            if not isinstance(tube_current, (int, float)) or tube_current <= 0:
                result.add_error(f"X-Ray Tube Current must be a positive number, got '{tube_current}'")
    
    @staticmethod
    def _validate_multi_energy_ct_constraints(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Multi-energy CT specific constraints per PS3.3 A.3.3.1."""
        multi_energy_acquisition = getattr(dataset, 'MultiEnergyCTAcquisition', None)
        
        if multi_energy_acquisition == 'YES':
            # Contrast/Bolus Module shall be present if contrast was administered
            contrast_agent = getattr(dataset, 'ContrastBolusAgent', None)
            if contrast_agent:
                # Note: We've already validated presence in conditional modules
                # This is just to ensure the constraint is documented
                pass
            
            # Real World Value Mapping Sequence shall be present in General Image Module
            real_world_mapping = getattr(dataset, 'RealWorldValueMappingSequence', None)
            if not real_world_mapping:
                result.add_error(
                    "Real World Value Mapping Sequence (0040,9096) is required in General Image Module "
                    "when Multi-energy CT Acquisition is YES"
                )
            else:
                # Validate Measurement Units Code Sequence uses correct code system
                for mapping_item in real_world_mapping:
                    measurement_units = getattr(mapping_item, 'MeasurementUnitsCodeSequence', None)
                    if measurement_units:
                        for units_item in measurement_units:
                            coding_scheme = getattr(units_item, 'CodingSchemeDesignator', None)
                            if coding_scheme and 'CID 301' not in str(coding_scheme):
                                result.add_warning(
                                    "Measurement Units Code Sequence should use CID 301 "
                                    "'Multi-energy Material Unit' for Multi-energy CT"
                                )
    
    @staticmethod
    def _has_spatial_data(dataset: Dataset) -> bool:
        """Check if dataset contains spatial information requiring validation."""
        spatial_elements = ['ImagePositionPatient', 'ImageOrientationPatient', 
                           'PixelSpacing', 'FrameOfReferenceUID']
        return any(hasattr(dataset, element) for element in spatial_elements)
    
    @staticmethod
    def _has_temporal_data(dataset: Dataset) -> bool:
        """Check if dataset contains temporal information requiring validation."""
        temporal_elements = ['StudyDate', 'StudyTime', 'SeriesDate', 'SeriesTime', 
                            'AcquisitionDate', 'AcquisitionTime']
        return any(hasattr(dataset, element) for element in temporal_elements)
    
    @staticmethod
    def _is_valid_uid(uid_value: str) -> bool:
        """Validate UID format per DICOM PS3.5."""
        import re
        # UID pattern: numbers and dots, max 64 chars, no leading/trailing dots
        pattern = r'^[0-9]+(\.[0-9]+)*$'
        return bool(re.match(pattern, uid_value)) and len(uid_value) <= 64