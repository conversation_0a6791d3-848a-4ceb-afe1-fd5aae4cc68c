"""DICOM validation framework.

This package provides validation functionality for DICOM modules and IODs.
It includes both the traditional dict-based validation pattern and the new
class-based ValidationResult for improved type safety and usability.
"""

from .validation_error import ValidationError
from .validation_result import ValidationResult
from .modules.base_validator import BaseValidator, ValidationConfig

# Import all validator classes
from .modules.approval_validator import ApprovalValidator
from .modules.cine_validator import CineValidator
from .modules.clinical_trial_series_validator import ClinicalTrialSeriesValidator
from .modules.clinical_trial_study_validator import ClinicalTrialStudyValidator
from .modules.clinical_trial_subject_validator import ClinicalTrialSubjectValidator
from .modules.common_instance_reference_validator import CommonInstanceReferenceValidator
from .modules.contrast_bolus_validator import ContrastBolusValidator
from .modules.ct_image_validator import CTImageValidator
from .modules.device_validator import DeviceValidator
from .modules.enhanced_patient_orientation_validator import EnhancedPatientOrientationValidator
from .modules.frame_extraction_validator import FrameExtractionValidator
from .modules.frame_of_reference_validator import FrameOfReferenceValidator
from .modules.general_acquisition_validator import GeneralAcquisitionValidator
from .modules.general_equipment_validator import GeneralEquipmentValidator
from .modules.general_image_validator import GeneralImageValidator
from .modules.general_reference_validator import GeneralReferenceValidator
from .modules.general_series_validator import GeneralSeriesValidator
from .modules.general_study_validator import GeneralStudyValidator
from .modules.image_pixel_validator import ImagePixelValidator
from .modules.image_plane_validator import ImagePlaneValidator
from .modules.modality_lut_validator import ModalityLutValidator
from .modules.multi_energy_ct_image_validator import MultiEnergyCTImageValidator
from .modules.multi_frame_validator import MultiFrameValidator
from .modules.overlay_plane_validator import OverlayPlaneValidator
from .modules.patient_study_validator import PatientStudyValidator
from .modules.patient_validator import PatientValidator
from .modules.roi_contour_validator import ROIContourValidator
from .modules.rt_beams_validator import RTBeamsValidator
from .modules.rt_brachy_application_setups_validator import RTBrachyApplicationSetupsValidator
from .modules.rt_dose_validator import RTDoseValidator
from .modules.rt_dvh_validator import RTDVHValidator
from .modules.rt_fraction_scheme_validator import RTFractionSchemeValidator
from .modules.rt_general_plan_validator import RTGeneralPlanValidator
from .modules.rt_image_validator import RTImageValidator
from .modules.rt_patient_setup_validator import RTPatientSetupValidator
from .modules.rt_prescription_validator import RTPrescriptionValidator
from .modules.rt_roi_observations_validator import RTROIObservationsValidator
from .modules.rt_series_validator import RTSeriesValidator
from .modules.rt_tolerance_tables_validator import RTToleranceTablesValidator
from .modules.sop_common_validator import SOPCommonValidator
from .modules.specimen_validator import SpecimenValidator
from .modules.structure_set_validator import StructureSetValidator
from .modules.synchronization_validator import SynchronizationValidator
from .modules.voi_lut_validator import VoiLutValidator

# Import IOD validators  
from .iods.rt_dose_iod_validator import RTDoseIODValidator


__all__ = [
    # Base classes
    "BaseValidator",
    "ValidationConfig",
    "ValidationError",
    "ValidationResult",
    
    # Validator classes
    "ApprovalValidator",
    "CineValidator",
    "ClinicalTrialSeriesValidator",
    "ClinicalTrialStudyValidator",
    "ClinicalTrialSubjectValidator",
    "CommonInstanceReferenceValidator",
    "ContrastBolusValidator",
    "CTImageValidator",
    "DeviceValidator",
    "EnhancedPatientOrientationValidator",
    "FrameExtractionValidator",
    "FrameOfReferenceValidator",
    "GeneralAcquisitionValidator",
    "GeneralEquipmentValidator",
    "GeneralImageValidator",
    "GeneralReferenceValidator",
    "GeneralSeriesValidator",
    "GeneralStudyValidator",
    "ImagePixelValidator",
    "ImagePlaneValidator",
    "ModalityLutValidator",
    "MultiEnergyCTImageValidator",
    "MultiFrameValidator",
    "OverlayPlaneValidator",
    "PatientStudyValidator",
    "PatientValidator",
    "ROIContourValidator",
    "RTBeamsValidator",
    "RTBrachyApplicationSetupsValidator",
    "RTDoseValidator",
    "RTDVHValidator",
    "RTFractionSchemeValidator",
    "RTGeneralPlanValidator",
    "RTImageValidator",
    "RTPatientSetupValidator",
    "RTPrescriptionValidator",
    "RTROIObservationsValidator",
    "RTSeriesValidator",
    "RTToleranceTablesValidator",
    "SOPCommonValidator",
    "SpecimenValidator",
    "StructureSetValidator",
    "SynchronizationValidator",
    "VoiLutValidator",
    
    # IOD validators
    "RTDoseIODValidator"
]