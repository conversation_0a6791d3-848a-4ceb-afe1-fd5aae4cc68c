"""Spatial Consistency cross-module validator for DICOM datasets."""

from pydicom import Dataset
import math
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class SpatialConsistencyValidator(BaseValidator):
    """Validator for spatial consistency across DICOM modules.
    
    This validator ensures that spatial information is consistent across
    different modules in a DICOM dataset. It validates:
    - Image position and orientation consistency
    - Pixel spacing and slice thickness consistency
    - Frame of reference spatial relationships
    - Coordinate system consistency
    - Geometric parameter validation
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate spatial consistency across modules.
        
        Args:
            dataset: pydicom Dataset to validate for spatial consistency
            config: Validation configuration options
            
        Returns:
            ValidationResult with spatial consistency errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate image position and orientation consistency
        SpatialConsistencyValidator._validate_image_position_orientation(dataset, result)
        
        # Validate pixel spacing consistency
        SpatialConsistencyValidator._validate_pixel_spacing_consistency(dataset, result)
        
        # Validate slice geometry consistency
        SpatialConsistencyValidator._validate_slice_geometry_consistency(dataset, result)
        
        # Validate coordinate system consistency
        SpatialConsistencyValidator._validate_coordinate_system_consistency(dataset, result)
        
        # Validate RT-specific spatial parameters if present
        SpatialConsistencyValidator._validate_rt_spatial_parameters(dataset, result)
        
        # Validate multi-frame spatial consistency if applicable
        if getattr(dataset, 'NumberOfFrames', 1) > 1:
            SpatialConsistencyValidator._validate_multi_frame_spatial_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_image_position_orientation(dataset: Dataset, result: ValidationResult) -> None:
        """Validate image position and orientation parameters."""
        image_position = getattr(dataset, 'ImagePositionPatient', None)
        image_orientation = getattr(dataset, 'ImageOrientationPatient', None)
        
        # Validate Image Position Patient
        if image_position is not None:
            if not SpatialConsistencyValidator._is_valid_position_vector(image_position):
                result.add_error(
                    f"Image Position Patient must be a 3-element numeric vector, got: {image_position}"
                )
        
        # Validate Image Orientation Patient  
        if image_orientation is not None:
            if not SpatialConsistencyValidator._is_valid_orientation_matrix(image_orientation):
                result.add_error(
                    f"Image Orientation Patient must be a 6-element numeric vector, got: {image_orientation}"
                )
            else:
                # Validate orthogonality of orientation vectors
                if not SpatialConsistencyValidator._are_orthogonal_vectors(image_orientation):
                    result.add_warning(
                        "Image Orientation Patient vectors should be orthogonal (dot product ≈ 0)"
                    )
                
                # Validate unit vectors
                if not SpatialConsistencyValidator._are_unit_vectors(image_orientation):
                    result.add_warning(
                        "Image Orientation Patient vectors should be unit vectors (magnitude ≈ 1)"
                    )
        
        # Check consistency between position and orientation if both present
        if image_position is not None and image_orientation is not None:
            SpatialConsistencyValidator._validate_position_orientation_consistency(
                image_position, image_orientation, result
            )
    
    @staticmethod
    def _validate_pixel_spacing_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate pixel spacing consistency across modules."""
        # Get pixel spacing from different modules
        pixel_spacing = getattr(dataset, 'PixelSpacing', None)
        imager_pixel_spacing = getattr(dataset, 'ImagerPixelSpacing', None)
        image_plane_pixel_spacing = getattr(dataset, 'ImagePlanePixelSpacing', None)
        
        # Validate pixel spacing format
        spacings = {
            'PixelSpacing': pixel_spacing,
            'ImagerPixelSpacing': imager_pixel_spacing,  
            'ImagePlanePixelSpacing': image_plane_pixel_spacing
        }
        
        valid_spacings = {}
        for name, spacing in spacings.items():
            if spacing is not None:
                if SpatialConsistencyValidator._is_valid_spacing_vector(spacing):
                    valid_spacings[name] = spacing
                else:
                    result.add_error(
                        f"{name} must be a 2-element positive numeric vector, got: {spacing}"
                    )
        
        # Check consistency between different spacing parameters
        if len(valid_spacings) > 1:
            spacing_values = list(valid_spacings.values())
            reference_spacing = spacing_values[0]
            
            for name, spacing in list(valid_spacings.items())[1:]:
                if not SpatialConsistencyValidator._are_spacings_consistent(reference_spacing, spacing):
                    result.add_warning(
                        f"Pixel spacing inconsistency: {list(valid_spacings.keys())[0]} = {reference_spacing}, "
                        f"{name} = {spacing}"
                    )
    
    @staticmethod
    def _validate_slice_geometry_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate slice geometry parameters."""
        slice_thickness = getattr(dataset, 'SliceThickness', None)
        slice_location = getattr(dataset, 'SliceLocation', None)
        spacing_between_slices = getattr(dataset, 'SpacingBetweenSlices', None)
        
        # Validate slice thickness
        if slice_thickness is not None:
            try:
                thickness_value = float(slice_thickness)
                if thickness_value <= 0:
                    result.add_error("Slice Thickness must be positive")
            except (ValueError, TypeError):
                result.add_error(f"Slice Thickness must be numeric, got: {slice_thickness}")
        
        # Validate slice location
        if slice_location is not None:
            try:
                float(slice_location)
            except (ValueError, TypeError):
                result.add_error(f"Slice Location must be numeric, got: {slice_location}")
        
        # Validate spacing between slices
        if spacing_between_slices is not None:
            try:
                spacing_value = float(spacing_between_slices)
                if spacing_value <= 0:
                    result.add_error("Spacing Between Slices must be positive")
            except (ValueError, TypeError):
                result.add_error(f"Spacing Between Slices must be numeric, got: {spacing_between_slices}")
        
        # Check consistency between slice thickness and spacing
        if slice_thickness is not None and spacing_between_slices is not None:
            try:
                thickness_val = float(slice_thickness)
                spacing_val = float(spacing_between_slices)
                if spacing_val < thickness_val:
                    result.add_warning(
                        f"Spacing Between Slices ({spacing_val}) is less than Slice Thickness ({thickness_val}) - "
                        "slices may overlap"
                    )
            except (ValueError, TypeError):
                pass  # Already reported above
    
    @staticmethod
    def _validate_coordinate_system_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate coordinate system consistency."""
        # Check for coordinate system indicators
        patient_orientation = getattr(dataset, 'PatientOrientation', None)
        laterality = getattr(dataset, 'Laterality', None)
        image_laterality = getattr(dataset, 'ImageLaterality', None)
        
        # Validate patient orientation
        if patient_orientation is not None:
            if not SpatialConsistencyValidator._is_valid_patient_orientation(patient_orientation):
                result.add_error(
                    f"Patient Orientation must contain valid anatomical direction codes, got: {patient_orientation}"
                )
        
        # Check laterality consistency
        if laterality is not None and image_laterality is not None:
            if laterality != image_laterality:
                result.add_warning(
                    f"Laterality ({laterality}) and Image Laterality ({image_laterality}) should be consistent"
                )
        
        # Validate laterality values
        valid_laterality = ['L', 'R', 'U', 'B']  # Left, Right, Unpaired, Both
        if laterality is not None and laterality not in valid_laterality:
            result.add_error(f"Laterality must be one of {valid_laterality}, got: {laterality}")
        if image_laterality is not None and image_laterality not in valid_laterality:
            result.add_error(f"Image Laterality must be one of {valid_laterality}, got: {image_laterality}")
    
    @staticmethod
    def _validate_rt_spatial_parameters(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT-specific spatial parameters if present."""
        # RT Image specific spatial parameters
        rt_image_position = getattr(dataset, 'RTImagePosition', None)
        rt_image_sid = getattr(dataset, 'RTImageSID', None)
        
        # Validate RT Image Position
        if rt_image_position is not None:
            if not SpatialConsistencyValidator._is_valid_position_vector(rt_image_position):
                result.add_error(
                    f"RT Image Position must be a 2-element numeric vector, got: {rt_image_position}"
                )
        
        # Validate RT Image SID (Source to Image Distance)
        if rt_image_sid is not None:
            try:
                sid_value = float(rt_image_sid)
                if sid_value <= 0:
                    result.add_error("RT Image SID must be positive")
            except (ValueError, TypeError):
                result.add_error(f"RT Image SID must be numeric, got: {rt_image_sid}")
        
        # Validate beam geometry angles
        beam_angles = {
            'GantryAngle': (0, 360),
            'PatientSupportAngle': (-180, 180),
            'BeamLimitingDeviceAngle': (0, 360),
            'TableTopEccentricAngle': (-180, 180),
            'TableTopPitchAngle': (-90, 90),
            'TableTopRollAngle': (-180, 180)
        }
        
        for angle_name, (min_val, max_val) in beam_angles.items():
            angle_value = getattr(dataset, angle_name, None)
            if angle_value is not None:
                try:
                    angle_float = float(angle_value)
                    if not (min_val <= angle_float <= max_val):
                        result.add_warning(
                            f"{angle_name} ({angle_float}) should be between {min_val} and {max_val} degrees"
                        )
                except (ValueError, TypeError):
                    result.add_error(f"{angle_name} must be numeric, got: {angle_value}")
    
    @staticmethod
    def _validate_multi_frame_spatial_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate spatial consistency in multi-frame datasets."""
        number_of_frames = getattr(dataset, 'NumberOfFrames', 1)
        
        # Check for per-frame functional groups
        per_frame_functional_groups = getattr(dataset, 'PerFrameFunctionalGroupsSequence', [])
        
        if not per_frame_functional_groups and number_of_frames > 1:
            result.add_warning(
                "Multi-frame dataset should have Per-frame Functional Groups Sequence for spatial parameters"
            )
            return
        
        # Validate spatial parameters in per-frame groups
        if len(per_frame_functional_groups) != number_of_frames:
            result.add_error(
                f"Per-frame Functional Groups Sequence length ({len(per_frame_functional_groups)}) "
                f"must equal Number of Frames ({number_of_frames})"
            )
        
        # Check spatial consistency across frames
        for i, frame_group in enumerate(per_frame_functional_groups):
            # Check for plane position sequence
            plane_position_seq = getattr(frame_group, 'PlanePositionSequence', [])
            if plane_position_seq:
                for plane_pos in plane_position_seq:
                    image_position = getattr(plane_pos, 'ImagePositionPatient', None)
                    if image_position and not SpatialConsistencyValidator._is_valid_position_vector(image_position):
                        result.add_error(
                            f"Invalid Image Position Patient in frame {i+1}: {image_position}"
                        )
            
            # Check for plane orientation sequence
            plane_orientation_seq = getattr(frame_group, 'PlaneOrientationSequence', [])
            if plane_orientation_seq:
                for plane_orient in plane_orientation_seq:
                    image_orientation = getattr(plane_orient, 'ImageOrientationPatient', None)
                    if image_orientation and not SpatialConsistencyValidator._is_valid_orientation_matrix(image_orientation):
                        result.add_error(
                            f"Invalid Image Orientation Patient in frame {i+1}: {image_orientation}"
                        )
    
    @staticmethod
    def _is_valid_position_vector(position) -> bool:
        """Validate position vector format."""
        if not isinstance(position, (list, tuple)):
            return False
        
        # Position should be 2 or 3 elements for different contexts
        if len(position) not in [2, 3]:
            return False
        
        try:
            [float(x) for x in position]
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def _is_valid_orientation_matrix(orientation) -> bool:
        """Validate orientation matrix format."""
        if not isinstance(orientation, (list, tuple)):
            return False
        
        if len(orientation) != 6:
            return False
        
        try:
            [float(x) for x in orientation]
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def _is_valid_spacing_vector(spacing) -> bool:
        """Validate spacing vector format."""
        if not isinstance(spacing, (list, tuple)):
            return False
        
        if len(spacing) != 2:
            return False
        
        try:
            spacing_values = [float(x) for x in spacing]
            return all(x > 0 for x in spacing_values)
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def _are_orthogonal_vectors(orientation) -> bool:
        """Check if orientation vectors are orthogonal."""
        try:
            # Extract row and column direction cosines
            row_cosines = [float(orientation[0]), float(orientation[1]), float(orientation[2])]
            col_cosines = [float(orientation[3]), float(orientation[4]), float(orientation[5])]
            
            # Calculate dot product
            dot_product = sum(r * c for r, c in zip(row_cosines, col_cosines))
            
            # Check if dot product is approximately zero (within tolerance)
            return abs(dot_product) < 1e-6
        except (ValueError, TypeError, IndexError):
            return False
    
    @staticmethod
    def _are_unit_vectors(orientation) -> bool:
        """Check if orientation vectors are unit vectors."""
        try:
            # Extract row and column direction cosines
            row_cosines = [float(orientation[0]), float(orientation[1]), float(orientation[2])]
            col_cosines = [float(orientation[3]), float(orientation[4]), float(orientation[5])]
            
            # Calculate magnitudes
            row_magnitude = math.sqrt(sum(x * x for x in row_cosines))
            col_magnitude = math.sqrt(sum(x * x for x in col_cosines))
            
            # Check if magnitudes are approximately 1 (within tolerance)
            return (abs(row_magnitude - 1.0) < 1e-6 and 
                   abs(col_magnitude - 1.0) < 1e-6)
        except (ValueError, TypeError, IndexError):
            return False
    
    @staticmethod
    def _are_spacings_consistent(spacing1, spacing2, tolerance: float = 1e-6) -> bool:
        """Check if two spacing vectors are consistent within tolerance."""
        try:
            s1 = [float(x) for x in spacing1]
            s2 = [float(x) for x in spacing2]
            
            if len(s1) != len(s2):
                return False
            
            return all(abs(x - y) < tolerance for x, y in zip(s1, s2))
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def _validate_position_orientation_consistency(position, orientation, result: ValidationResult) -> None:
        """Validate consistency between position and orientation vectors."""
        # This could include checks for coordinate system handedness,
        # anatomical consistency, etc. For now, just basic validation
        try:
            pos = [float(x) for x in position]
            orient = [float(x) for x in orientation]
            
            # Check for reasonable coordinate ranges (e.g., not extremely large values)
            max_coord = 10000  # 10 meters in mm
            if any(abs(p) > max_coord for p in pos):
                result.add_warning(
                    f"Image Position Patient has unusually large coordinates: {position}"
                )
            
            # Check for reasonable orientation vector ranges (should be unit vectors ≈ ±1)
            max_orient = 2.0  # Allow some tolerance beyond ±1
            if any(abs(o) > max_orient for o in orient):
                result.add_warning(
                    f"Image Orientation Patient has unusually large values: {orientation}"
                )
        except (ValueError, TypeError):
            pass  # Already validated in other methods
    
    @staticmethod
    def _is_valid_patient_orientation(orientation) -> bool:
        """Validate patient orientation codes."""
        if not isinstance(orientation, (list, tuple)):
            return False
        
        valid_codes = ['A', 'P', 'H', 'F', 'L', 'R']  # Anterior, Posterior, Head, Foot, Left, Right
        
        try:
            for orient in orientation:
                if isinstance(orient, str) and len(orient) > 0:
                    # Each orientation string should contain valid codes
                    if not all(c in valid_codes for c in orient.upper()):
                        return False
                else:
                    return False
            return True
        except (AttributeError, TypeError):
            return False