"""Cross-module validator for anatomical orientation requirements.

This validator checks the relationship between the anatomical orientation type
in the General Series Module and the species information in the Patient Module
according to DICOM PS3.3 C.7.3.1.
"""

from pydicom import Dataset
from ..validation_result import ValidationResult
from ...modules.patient_module import PatientModule
from ...modules.general_series_module import GeneralSeriesModule


class AnatomicalOrientationValidator:
    """Cross-module validator for anatomical orientation requirements.
    
    Validates the Type 1C requirement for Anatomical Orientation Type (0010,2210)
    based on patient species information according to DICOM PS3.3 C.7.3.1:
    
    "Required if the Patient is a non-human organism and the anatomical 
    Frame of Reference is not bipedal. May be present otherwise."
    
    This validator coordinates between:
    - PatientModule: Determines if patient is human or non-human organism
    - GeneralSeriesModule: Contains anatomical orientation type information
    """
    
    @staticmethod
    def validate(patient_module: PatientModule | Dataset | None,
                general_series_module: GeneralSeriesModule | Dataset | None) -> ValidationResult:
        """Validate anatomical orientation requirements across modules.
        
        Args:
            patient_module: Patient module or dataset containing species information
            general_series_module: General series module or dataset with orientation info
            
        Returns:
            ValidationResult: Validation result with errors and warnings
            
        Notes:
            - If either module is None, validation passes (other validators handle required modules)
            - Type 1C validation only applies when patient is confirmed non-human
            - Human patients may have anatomical orientation but it's not required
        """
        result = ValidationResult()
        
        # Skip validation if either module is missing
        if patient_module is None or general_series_module is None:
            return result
        
        # Handle both module objects and raw datasets
        is_non_human = _determine_non_human_status(patient_module)
        has_orientation = _has_anatomical_orientation(general_series_module)
        
        # Type 1C requirement: Required if patient is non-human organism
        if is_non_human and not has_orientation:
            result.add_error(
                "Anatomical Orientation Type (0010,2210) is required (Type 1C) "
                "for non-human organisms. DICOM PS3.3 C.7.3.1 states: "
                "\"Required if the Patient is a non-human organism and the anatomical "
                "Frame of Reference is not bipedal.\""
            )
        
        # Informational validation: orientation present for human patient
        if not is_non_human and has_orientation:
            orientation_value = _get_orientation_value(general_series_module)
            result.add_warning(
                f"Anatomical Orientation Type '{orientation_value}' is present for "
                "human patient. This is permitted but not required by DICOM PS3.3 C.7.3.1."
            )
        
        return result


def _determine_non_human_status(patient_module: PatientModule | Dataset) -> bool:
    """Determine if patient is non-human based on species information.
    
    Args:
        patient_module: Patient module or dataset
        
    Returns:
        bool: True if patient is non-human organism
    """
    if isinstance(patient_module, PatientModule):
        return patient_module.is_non_human
    
    # Handle raw dataset
    return ("PatientSpeciesDescription" in patient_module or
            "PatientSpeciesCodeSequence" in patient_module)


def _has_anatomical_orientation(general_series_module: GeneralSeriesModule | Dataset) -> bool:
    """Check if anatomical orientation type is present.
    
    Args:
        general_series_module: General series module or dataset
        
    Returns:
        bool: True if anatomical orientation type is present
    """
    if isinstance(general_series_module, GeneralSeriesModule):
        return "AnatomicalOrientationType" in general_series_module
    
    # Handle raw dataset
    return hasattr(general_series_module, "AnatomicalOrientationType")


def _get_orientation_value(general_series_module: GeneralSeriesModule | Dataset) -> str:
    """Get anatomical orientation type value.
    
    Args:
        general_series_module: General series module or dataset
        
    Returns:
        str: Anatomical orientation type value or "Unknown"
    """
    if isinstance(general_series_module, GeneralSeriesModule):
        if 'AnatomicalOrientationType' in general_series_module:
            return general_series_module.AnatomicalOrientationType 
        return "Unknown"
    
    # Handle raw dataset
    return getattr(general_series_module, "AnatomicalOrientationType", "Unknown")