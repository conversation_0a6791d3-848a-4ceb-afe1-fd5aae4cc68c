"""Temporal Consistency cross-module validator for DICOM datasets."""

from pydicom import Dataset
from datetime import datetime
from typing import Optional
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class TemporalConsistencyValidator(BaseValidator):
    """Validator for temporal consistency across DICOM modules.
    
    This validator ensures that temporal information is consistent across
    different modules in a DICOM dataset. It validates:
    - Date and time format consistency  
    - Chronological order of events
    - Date/time consistency across modules
    - Frame timing consistency for multi-frame data
    - Acquisition and content timing relationships
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate temporal consistency across modules.
        
        Args:
            dataset: pydicom Dataset to validate for temporal consistency
            config: Validation configuration options
            
        Returns:
            ValidationResult with temporal consistency errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate date and time formats
        TemporalConsistencyValidator._validate_date_time_formats(dataset, result)
        
        # Validate chronological consistency
        TemporalConsistencyValidator._validate_chronological_order(dataset, result)
        
        # Validate study/series/acquisition time relationships
        TemporalConsistencyValidator._validate_hierarchical_timing(dataset, result)
        
        # Validate frame timing consistency for multi-frame data
        if getattr(dataset, 'NumberOfFrames', 1) > 1:
            TemporalConsistencyValidator._validate_frame_timing_consistency(dataset, result)
        
        # Validate RT-specific temporal parameters if present
        TemporalConsistencyValidator._validate_rt_temporal_parameters(dataset, result)
        
        # Validate content vs acquisition timing
        TemporalConsistencyValidator._validate_content_acquisition_timing(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_date_time_formats(dataset: Dataset, result: ValidationResult) -> None:
        """Validate DICOM date and time formats."""
        # Date elements (DA - YYYYMMDD)
        date_elements = [
            'StudyDate', 'SeriesDate', 'AcquisitionDate', 'ContentDate',
            'PatientBirthDate', 'InstanceCreationDate', 'CurveDate',
            'OverlayDate', 'PresentationCreationDate'
        ]
        
        # Time elements (TM - HHMMSS.FFFFFF)
        time_elements = [
            'StudyTime', 'SeriesTime', 'AcquisitionTime', 'ContentTime',
            'PatientBirthTime', 'InstanceCreationTime', 'CurveTime',
            'OverlayTime', 'PresentationCreationTime'
        ]
        
        # DateTime elements (DT - YYYYMMDDHHMMSS.FFFFFF&ZZXX)
        datetime_elements = [
            'AcquisitionDateTime', 'TimezoneOffsetFromUTC'
        ]
        
        # Validate date formats
        for date_elem in date_elements:
            date_value = getattr(dataset, date_elem, None)
            if date_value is not None:
                if not TemporalConsistencyValidator._is_valid_dicom_date(date_value):
                    result.add_error(f"{date_elem} has invalid DICOM date format: {date_value}")
        
        # Validate time formats
        for time_elem in time_elements:
            time_value = getattr(dataset, time_elem, None)
            if time_value is not None:
                if not TemporalConsistencyValidator._is_valid_dicom_time(time_value):
                    result.add_error(f"{time_elem} has invalid DICOM time format: {time_value}")
        
        # Validate datetime formats
        for datetime_elem in datetime_elements:
            datetime_value = getattr(dataset, datetime_elem, None)
            if datetime_value is not None:
                if not TemporalConsistencyValidator._is_valid_dicom_datetime(datetime_value):
                    result.add_error(f"{datetime_elem} has invalid DICOM datetime format: {datetime_value}")
    
    @staticmethod
    def _validate_chronological_order(dataset: Dataset, result: ValidationResult) -> None:
        """Validate chronological order of temporal events."""
        # Extract and parse date/time pairs
        temporal_events = []
        
        # Study level
        study_date = getattr(dataset, 'StudyDate', None)
        study_time = getattr(dataset, 'StudyTime', None)
        if study_date:
            study_datetime = TemporalConsistencyValidator._parse_dicom_datetime(study_date, study_time)
            if study_datetime:
                temporal_events.append(('Study', study_datetime))
        
        # Series level
        series_date = getattr(dataset, 'SeriesDate', None)
        series_time = getattr(dataset, 'SeriesTime', None)
        if series_date:
            series_datetime = TemporalConsistencyValidator._parse_dicom_datetime(series_date, series_time)
            if series_datetime:
                temporal_events.append(('Series', series_datetime))
        
        # Acquisition level
        acquisition_date = getattr(dataset, 'AcquisitionDate', None)
        acquisition_time = getattr(dataset, 'AcquisitionTime', None)
        if acquisition_date:
            acquisition_datetime = TemporalConsistencyValidator._parse_dicom_datetime(acquisition_date, acquisition_time)
            if acquisition_datetime:
                temporal_events.append(('Acquisition', acquisition_datetime))
        
        # Content level
        content_date = getattr(dataset, 'ContentDate', None)
        content_time = getattr(dataset, 'ContentTime', None)
        if content_date:
            content_datetime = TemporalConsistencyValidator._parse_dicom_datetime(content_date, content_time)
            if content_datetime:
                temporal_events.append(('Content', content_datetime))
        
        # Instance creation
        instance_date = getattr(dataset, 'InstanceCreationDate', None)
        instance_time = getattr(dataset, 'InstanceCreationTime', None)
        if instance_date:
            instance_datetime = TemporalConsistencyValidator._parse_dicom_datetime(instance_date, instance_time)
            if instance_datetime:
                temporal_events.append(('Instance Creation', instance_datetime))
        
        # Check chronological order
        if len(temporal_events) > 1:
            # Sort by expected hierarchy (Study -> Series -> Acquisition -> Content -> Instance)
            hierarchy_order = ['Study', 'Series', 'Acquisition', 'Content', 'Instance Creation']
            
            # Sort temporal events by hierarchy order
            def get_hierarchy_index(event_name):
                try:
                    return hierarchy_order.index(event_name)
                except ValueError:
                    return len(hierarchy_order)  # Unknown events go to the end
            
            sorted_events = sorted(temporal_events, key=lambda x: get_hierarchy_index(x[0]))
            
            for i in range(len(sorted_events) - 1):
                current_event, current_time = sorted_events[i]
                next_event, next_time = sorted_events[i + 1]
                
                # Check if later event in hierarchy is actually later in time
                if current_time > next_time:
                    # Allow some tolerance for clock skew (1 minute)
                    time_diff = (current_time - next_time).total_seconds()
                    if time_diff > 60:  # More than 1 minute difference
                        result.add_warning(
                            f"Temporal inconsistency: {current_event} time ({current_time}) "
                            f"is later than {next_event} time ({next_time})"
                        )
    
    @staticmethod
    def _validate_hierarchical_timing(dataset: Dataset, result: ValidationResult) -> None:
        """Validate timing relationships in DICOM hierarchy."""
        # Study/Series/Instance timing should follow hierarchy
        
        # Patient birth date should be before all other dates
        patient_birth_date = getattr(dataset, 'PatientBirthDate', None)
        if patient_birth_date:
            birth_date = TemporalConsistencyValidator._parse_dicom_datetime(patient_birth_date, None)
            if birth_date:
                # Check against study date
                study_date = getattr(dataset, 'StudyDate', None)
                if study_date:
                    study_datetime = TemporalConsistencyValidator._parse_dicom_datetime(study_date, None)
                    if study_datetime and birth_date > study_datetime:
                        result.add_error(
                            f"Patient Birth Date ({patient_birth_date}) is after Study Date ({study_date})"
                        )
        
        # Study date should be before or equal to series date
        study_date = getattr(dataset, 'StudyDate', None)
        series_date = getattr(dataset, 'SeriesDate', None)
        if study_date and series_date:
            study_dt = TemporalConsistencyValidator._parse_dicom_datetime(study_date, None)
            series_dt = TemporalConsistencyValidator._parse_dicom_datetime(series_date, None)
            if study_dt and series_dt and study_dt > series_dt:
                result.add_warning(
                    f"Study Date ({study_date}) is after Series Date ({series_date})"
                )
    
    @staticmethod
    def _validate_frame_timing_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate frame timing consistency for multi-frame data."""
        number_of_frames = getattr(dataset, 'NumberOfFrames', 1)
        
        # Frame time and frame time vector validation
        frame_time = getattr(dataset, 'FrameTime', None)
        frame_time_vector = getattr(dataset, 'FrameTimeVector', None)
        
        # Validate single frame time if present
        if frame_time is not None:
            try:
                frame_time_val = float(frame_time)
                if frame_time_val <= 0:
                    result.add_error("Frame Time must be positive")
            except (ValueError, TypeError):
                result.add_error(f"Frame Time must be numeric, got: {frame_time}")
        
        if frame_time_vector is not None:
            if not isinstance(frame_time_vector, (list, tuple)):
                result.add_error("Frame Time Vector must be a sequence")
            elif len(frame_time_vector) != number_of_frames:
                result.add_error(
                    f"Frame Time Vector length ({len(frame_time_vector)}) "
                    f"must equal Number of Frames ({number_of_frames})"
                )
            else:
                # Validate each frame time value
                for i, ft in enumerate(frame_time_vector):
                    try:
                        frame_time_val = float(ft)
                        if frame_time_val <= 0:
                            result.add_error(f"Frame Time Vector[{i}] must be positive, got: {frame_time_val}")
                    except (ValueError, TypeError):
                        result.add_error(f"Frame Time Vector[{i}] must be numeric, got: {ft}")
        
        # Frame increment pointer validation
        frame_increment_pointer = getattr(dataset, 'FrameIncrementPointer', None)
        if frame_increment_pointer is not None:
            # Should point to valid data elements
            if not isinstance(frame_increment_pointer, (list, tuple)):
                frame_increment_pointer = [frame_increment_pointer]
            
            for pointer in frame_increment_pointer:
                # Validate that pointer refers to existing element
                # This is a simplified check - full validation would require tag parsing
                if not isinstance(pointer, (str, int)):
                    result.add_warning(f"Frame Increment Pointer should be valid data element tag: {pointer}")
        
        # Cine rate validation
        cine_rate = getattr(dataset, 'CineRate', None)
        if cine_rate is not None:
            try:
                rate_value = float(cine_rate)
                if rate_value <= 0:
                    result.add_error("Cine Rate must be positive")
                elif rate_value > 1000:  # Reasonable upper limit
                    result.add_warning(f"Cine Rate ({rate_value}) seems unusually high")
            except (ValueError, TypeError):
                result.add_error(f"Cine Rate must be numeric, got: {cine_rate}")
        
        # Validate per-frame timing if present
        per_frame_groups = getattr(dataset, 'PerFrameFunctionalGroupsSequence', [])
        if per_frame_groups and len(per_frame_groups) == number_of_frames:
            for i, frame_group in enumerate(per_frame_groups):
                # Check for frame content sequence timing
                frame_content_seq = getattr(frame_group, 'FrameContentSequence', [])
                for frame_content in frame_content_seq:
                    frame_reference_time = getattr(frame_content, 'FrameReferenceTime', None)
                    if frame_reference_time is not None:
                        try:
                            ref_time = float(frame_reference_time)
                            if ref_time < 0:
                                result.add_error(f"Frame Reference Time in frame {i+1} should not be negative")
                        except (ValueError, TypeError):
                            result.add_error(f"Frame Reference Time in frame {i+1} must be numeric: {frame_reference_time}")
    
    @staticmethod
    def _validate_rt_temporal_parameters(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT-specific temporal parameters if present."""
        # RT Plan date/time
        rt_plan_date = getattr(dataset, 'RTPlanDate', None)
        rt_plan_time = getattr(dataset, 'RTPlanTime', None)
        
        if rt_plan_date and not TemporalConsistencyValidator._is_valid_dicom_date(rt_plan_date):
            result.add_error(f"RT Plan Date has invalid format: {rt_plan_date}")
        
        if rt_plan_time and not TemporalConsistencyValidator._is_valid_dicom_time(rt_plan_time):
            result.add_error(f"RT Plan Time has invalid format: {rt_plan_time}")
        
        # Treatment date/time
        treatment_date = getattr(dataset, 'TreatmentDate', None)
        treatment_time = getattr(dataset, 'TreatmentTime', None)
        
        if treatment_date and not TemporalConsistencyValidator._is_valid_dicom_date(treatment_date):
            result.add_error(f"Treatment Date has invalid format: {treatment_date}")
        
        if treatment_time and not TemporalConsistencyValidator._is_valid_dicom_time(treatment_time):
            result.add_error(f"Treatment Time has invalid format: {treatment_time}")
        
        # Check RT Plan vs Treatment timing
        if rt_plan_date and treatment_date:
            plan_dt = TemporalConsistencyValidator._parse_dicom_datetime(rt_plan_date, rt_plan_time)
            treatment_dt = TemporalConsistencyValidator._parse_dicom_datetime(treatment_date, treatment_time)
            
            if plan_dt and treatment_dt and plan_dt > treatment_dt:
                result.add_warning(
                    f"RT Plan Date/Time ({plan_dt}) is after Treatment Date/Time ({treatment_dt})"
                )
    
    @staticmethod
    def _validate_content_acquisition_timing(dataset: Dataset, result: ValidationResult) -> None:
        """Validate relationship between content and acquisition timing."""
        acquisition_date = getattr(dataset, 'AcquisitionDate', None)
        acquisition_time = getattr(dataset, 'AcquisitionTime', None)
        content_date = getattr(dataset, 'ContentDate', None)
        content_time = getattr(dataset, 'ContentTime', None)
        
        if acquisition_date and content_date:
            acq_dt = TemporalConsistencyValidator._parse_dicom_datetime(acquisition_date, acquisition_time)
            content_dt = TemporalConsistencyValidator._parse_dicom_datetime(content_date, content_time)
            
            if acq_dt and content_dt:
                # Content time should typically be at or after acquisition time
                if content_dt < acq_dt:
                    time_diff = (acq_dt - content_dt).total_seconds()
                    if time_diff > 60:  # More than 1 minute difference
                        result.add_warning(
                            f"Content Date/Time ({content_dt}) is significantly before "
                            f"Acquisition Date/Time ({acq_dt})"
                        )
    
    @staticmethod
    def _is_valid_dicom_date(date_str: str) -> bool:
        """Validate DICOM date format (YYYYMMDD)."""
        if not isinstance(date_str, str):
            return False
        
        # DICOM date can be 8 digits or less (partial dates allowed)
        if not (1 <= len(date_str) <= 8):
            return False
        
        if not date_str.isdigit():
            return False
        
        # Try to parse as date if full 8 digits
        if len(date_str) == 8:
            try:
                datetime.strptime(date_str, '%Y%m%d')
                return True
            except ValueError:
                return False
        
        return True  # Partial dates are valid
    
    @staticmethod
    def _is_valid_dicom_time(time_str: str) -> bool:
        """Validate DICOM time format (HHMMSS.FFFFFF)."""
        if not isinstance(time_str, str):
            return False
        
        # DICOM time can be 2-14 characters
        if not (2 <= len(time_str) <= 14):
            return False
        
        # Split on decimal point if present
        parts = time_str.split('.')
        if len(parts) > 2:
            return False
        
        time_part = parts[0]
        frac_part = parts[1] if len(parts) == 2 else None
        
        # Time part should be digits only
        if not time_part.isdigit():
            return False
        
        # Fractional part should be digits only
        if frac_part and not frac_part.isdigit():
            return False
        
        # Try to parse time part
        try:
            if len(time_part) >= 2:
                hour = int(time_part[:2])
                if hour > 23:
                    return False
            if len(time_part) >= 4:
                minute = int(time_part[2:4])
                if minute > 59:
                    return False
            if len(time_part) >= 6:
                second = int(time_part[4:6])
                if second > 59:
                    return False
        except ValueError:
            return False
        
        return True
    
    @staticmethod
    def _is_valid_dicom_datetime(datetime_str: str) -> bool:
        """Validate DICOM datetime format (YYYYMMDDHHMMSS.FFFFFF&ZZXX)."""
        if not isinstance(datetime_str, str):
            return False
        
        # Split on timezone offset if present
        tz_parts = datetime_str.split('&') if '&' in datetime_str else [datetime_str]
        if len(tz_parts) > 2:
            return False
        
        datetime_part = tz_parts[0]
        
        # Must be at least 4 digits (YYYY)
        if len(datetime_part) < 4:
            return False
        
        # Extract date and time parts
        if len(datetime_part) >= 8:
            date_part = datetime_part[:8]
            time_part = datetime_part[8:] if len(datetime_part) > 8 else None
        else:
            date_part = datetime_part
            time_part = None
        
        # Validate date part
        if not TemporalConsistencyValidator._is_valid_dicom_date(date_part):
            return False
        
        # Validate time part if present
        if time_part and not TemporalConsistencyValidator._is_valid_dicom_time(time_part):
            return False
        
        return True
    
    @staticmethod
    def _parse_dicom_datetime(date_str: Optional[str], time_str: Optional[str] = None) -> Optional[datetime]:
        """Parse DICOM date and time strings into datetime object."""
        if not date_str:
            return None
        
        try:
            # Handle partial dates by padding with defaults
            padded_date = date_str.ljust(8, '0')[:8]
            
            # Parse date
            dt = datetime.strptime(padded_date, '%Y%m%d')
            
            # Add time if provided
            if time_str:
                # Handle partial times
                time_part = time_str.split('.')[0]  # Remove fractional seconds for parsing
                time_part = time_part.ljust(6, '0')[:6]  # Pad to HHMMSS
                
                try:
                    time_obj = datetime.strptime(time_part, '%H%M%S').time()
                    dt = datetime.combine(dt.date(), time_obj)
                except ValueError:
                    pass  # Use date only if time parsing fails
            
            return dt
        except ValueError:
            return None