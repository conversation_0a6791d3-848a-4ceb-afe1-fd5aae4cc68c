"""UID Consistency cross-module validator for DICOM datasets."""

from pydicom import Dataset
from ..modules.base_validator import BaseValida<PERSON>, ValidationConfig
from ..validation_result import ValidationResult


class UIDConsistencyValidator(BaseValidator):
    """Validator for UID consistency across DICOM modules.
    
    This validator ensures that UIDs referenced across different modules
    are consistent and follow DICOM requirements. It validates:
    - Study Instance UID consistency across modules
    - Series Instance UID consistency across modules  
    - SOP Instance UID uniqueness and format
    - Frame of Reference UID consistency
    - Referenced UID existence and validity
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate UID consistency across modules.
        
        Args:
            dataset: pydicom Dataset to validate for UID consistency
            config: Validation configuration options
            
        Returns:
            ValidationResult with UID consistency errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Study Instance UID consistency
        UIDConsistencyValidator._validate_study_instance_uid_consistency(dataset, result)
        
        # Validate Series Instance UID consistency  
        UIDConsistencyValidator._validate_series_instance_uid_consistency(dataset, result)
        
        # Validate SOP Instance UID requirements
        UIDConsistencyValidator._validate_sop_instance_uid_requirements(dataset, result)
        
        # Validate Frame of Reference UID consistency
        UIDConsistencyValidator._validate_frame_of_reference_uid_consistency(dataset, result)
        
        # Validate referenced UID consistency
        UIDConsistencyValidator._validate_referenced_uid_consistency(dataset, result)
        
        # Validate UID format requirements
        UIDConsistencyValidator._validate_uid_formats(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_study_instance_uid_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Study Instance UID consistency across modules."""
        study_uid = getattr(dataset, 'StudyInstanceUID', None)
        
        if not study_uid:
            result.add_error("Study Instance UID is required for UID consistency validation")
            return
        
        # Check for Study Instance UID in referenced sequences
        referenced_study_sequences = [
            'ReferencedStudySequence',
            'ReferencedPatientSequence'
        ]
        
        for seq_name in referenced_study_sequences:
            sequence = getattr(dataset, seq_name, [])
            if not isinstance(sequence, (list, tuple)):
                continue
                
            for item in sequence:
                if hasattr(item, 'StudyInstanceUID'):
                    ref_study_uid = getattr(item, 'StudyInstanceUID', None)
                    if ref_study_uid and ref_study_uid != study_uid:
                        result.add_warning(
                            f"Referenced Study Instance UID in {seq_name} ({ref_study_uid}) "
                            f"differs from main Study Instance UID ({study_uid})"
                        )
    
    @staticmethod
    def _validate_series_instance_uid_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Series Instance UID consistency across modules."""
        series_uid = getattr(dataset, 'SeriesInstanceUID', None)
        
        if not series_uid:
            result.add_error("Series Instance UID is required for UID consistency validation")
            return
        
        # Check for Series Instance UID in referenced sequences
        referenced_series_sequences = [
            'ReferencedSeriesSequence',
            'ReferencedImageSequence'
        ]
        
        for seq_name in referenced_series_sequences:
            sequence = getattr(dataset, seq_name, [])
            if not isinstance(sequence, (list, tuple)):
                continue
                
            for item in sequence:
                if hasattr(item, 'SeriesInstanceUID'):
                    ref_series_uid = getattr(item, 'SeriesInstanceUID', None)
                    if ref_series_uid and ref_series_uid != series_uid:
                        result.add_warning(
                            f"Referenced Series Instance UID in {seq_name} ({ref_series_uid}) "
                            f"may differ from main Series Instance UID ({series_uid})"
                        )
    
    @staticmethod
    def _validate_sop_instance_uid_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate SOP Instance UID requirements and uniqueness."""
        sop_instance_uid = getattr(dataset, 'SOPInstanceUID', None)
        
        if not sop_instance_uid:
            result.add_error("SOP Instance UID is required for UID consistency validation")
            return
        
        # Validate UID format
        if not UIDConsistencyValidator._is_valid_uid(sop_instance_uid):
            result.add_error(f"SOP Instance UID format is invalid: {sop_instance_uid}")
        
        # Check for duplicate SOP Instance UID in referenced sequences
        referenced_sop_sequences = [
            'ReferencedImageSequence',
            'ReferencedInstanceSequence',
            'ReferencedSOPSequence'
        ]
        
        for seq_name in referenced_sop_sequences:
            sequence = getattr(dataset, seq_name, [])
            if not isinstance(sequence, (list, tuple)):
                continue
                
            for item in sequence:
                if hasattr(item, 'ReferencedSOPInstanceUID'):
                    ref_sop_uid = getattr(item, 'ReferencedSOPInstanceUID', None)
                    if ref_sop_uid == sop_instance_uid:
                        result.add_warning(
                            f"SOP Instance UID references itself in {seq_name} - "
                            "this may indicate a circular reference"
                        )
    
    @staticmethod
    def _validate_frame_of_reference_uid_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Frame of Reference UID consistency if present."""
        frame_of_ref_uid = getattr(dataset, 'FrameOfReferenceUID', None)
        
        if not frame_of_ref_uid:
            return  # Frame of Reference is optional in many IODs
        
        # Validate UID format
        if not UIDConsistencyValidator._is_valid_uid(frame_of_ref_uid):
            result.add_error(f"Frame of Reference UID format is invalid: {frame_of_ref_uid}")
        
        # Check for Frame of Reference UID consistency in sequences
        referenced_frame_sequences = [
            'ReferencedFrameOfReferenceSequence',
            'FrameOfReferenceRelationshipSequence'
        ]
        
        for seq_name in referenced_frame_sequences:
            sequence = getattr(dataset, seq_name, [])
            if not isinstance(sequence, (list, tuple)):
                continue
                
            for item in sequence:
                if hasattr(item, 'FrameOfReferenceUID'):
                    ref_frame_uid = getattr(item, 'FrameOfReferenceUID', None)
                    if ref_frame_uid and ref_frame_uid != frame_of_ref_uid:
                        result.add_warning(
                            f"Referenced Frame of Reference UID in {seq_name} ({ref_frame_uid}) "
                            f"differs from main Frame of Reference UID ({frame_of_ref_uid})"
                        )
    
    @staticmethod
    def _validate_referenced_uid_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate consistency of referenced UIDs in sequences."""
        # Common reference sequences to validate
        reference_sequences = {
            'ReferencedStudySequence': ['StudyInstanceUID'],
            'ReferencedSeriesSequence': ['SeriesInstanceUID'],
            'ReferencedImageSequence': ['ReferencedSOPClassUID', 'ReferencedSOPInstanceUID'],
            'ReferencedInstanceSequence': ['ReferencedSOPClassUID', 'ReferencedSOPInstanceUID'],
            'ReferencedStructureSetSequence': ['ReferencedSOPClassUID', 'ReferencedSOPInstanceUID'],
            'ReferencedRTPlanSequence': ['ReferencedSOPClassUID', 'ReferencedSOPInstanceUID'],
            'ReferencedDoseSequence': ['ReferencedSOPClassUID', 'ReferencedSOPInstanceUID']
        }
        
        for seq_name, required_uids in reference_sequences.items():
            sequence = getattr(dataset, seq_name, [])
            if not isinstance(sequence, (list, tuple)):
                continue
                
            for i, item in enumerate(sequence):
                for uid_name in required_uids:
                    uid_value = getattr(item, uid_name, None)
                    if uid_value:
                        # Validate UID format for SOP Instance UIDs
                        if 'InstanceUID' in uid_name and not UIDConsistencyValidator._is_valid_uid(uid_value):
                            result.add_error(
                                f"{uid_name} in {seq_name}[{i}] has invalid format: {uid_value}"
                            )
                    else:
                        # Check if this is a required field in the sequence
                        if uid_name in ['ReferencedSOPClassUID', 'ReferencedSOPInstanceUID']:
                            result.add_error(
                                f"Missing required {uid_name} in {seq_name}[{i}]"
                            )
    
    @staticmethod
    def _validate_uid_formats(dataset: Dataset, result: ValidationResult) -> None:
        """Validate format of all UID elements in the dataset."""
        # Common UID elements to validate
        uid_elements = [
            'StudyInstanceUID',
            'SeriesInstanceUID', 
            'SOPInstanceUID',
            'FrameOfReferenceUID',
            'SynchronizationFrameOfReferenceUID',
            'MediaStorageSOPInstanceUID',
            'TransferSyntaxUID',
            'ImplementationClassUID'
        ]
        
        for uid_element in uid_elements:
            uid_value = getattr(dataset, uid_element, None)
            if uid_value and not UIDConsistencyValidator._is_valid_uid(uid_value):
                result.add_error(f"{uid_element} has invalid format: {uid_value}")
        
        # Validate UIDs in file meta information if present
        file_meta = getattr(dataset, 'file_meta', None)
        if file_meta:
            meta_uid_elements = [
                'MediaStorageSOPInstanceUID',
                'TransferSyntaxUID',
                'ImplementationClassUID'
            ]
            
            for uid_element in meta_uid_elements:
                uid_value = getattr(file_meta, uid_element, None)
                if uid_value and not UIDConsistencyValidator._is_valid_uid(uid_value):
                    result.add_error(f"File Meta {uid_element} has invalid format: {uid_value}")
    
    @staticmethod
    def _is_valid_uid(uid_value: str) -> bool:
        """Validate UID format per DICOM PS3.5.
        
        Args:
            uid_value: UID string to validate
            
        Returns:
            bool: True if UID format is valid
        """
        import re
        
        if not uid_value or not isinstance(uid_value, str):
            return False
        
        # UID pattern: numbers and dots, max 64 chars, no leading/trailing dots
        pattern = r'^[0-9]+(\.[0-9]+)*$'
        
        # Basic format check
        if not re.match(pattern, uid_value):
            return False
        
        # Length check
        if len(uid_value) > 64:
            return False
        
        # No leading or trailing dots
        if uid_value.startswith('.') or uid_value.endswith('.'):
            return False
        
        # No consecutive dots
        if '..' in uid_value:
            return False
        
        # Each component should not start with 0 (except single 0)
        components = uid_value.split('.')
        for component in components:
            if len(component) > 1 and component.startswith('0'):
                return False
        
        return True