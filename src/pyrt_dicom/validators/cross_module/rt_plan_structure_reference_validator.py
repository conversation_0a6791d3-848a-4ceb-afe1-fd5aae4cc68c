"""RT Plan and Structure Set reference validator for cross-module validation.

This validator ensures that RT modules properly reference RT Plans and 
Structure Sets according to DICOM specifications.
"""

from pydicom import Dataset

from pyrt_dicom.enums.rt_enums import DoseSummationType
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class RTPlanStructureReferenceValidator(BaseValidator):
    """Validator for RT Plan and Structure Set references across modules.
    
    Validates references between RT modules including RT Dose, RT Plan,
    and Structure Set relationships as defined in DICOM PS3.3.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate RT Plan and Structure Set references across modules.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Check RT Dose Module conditional references
        RTPlanStructureReferenceValidator._validate_rt_dose_references(dataset, result)
        
        # Check RT General Plan conditional references
        RTPlanStructureReferenceValidator._validate_rt_plan_references(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_rt_dose_references(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT Dose Module reference requirements."""
        # Check if RT Dose Module is present
        if not (hasattr(dataset, 'DoseUnits') or hasattr(dataset, 'DoseType') or 
                hasattr(dataset, 'DoseSummationType')):
            return  # RT Dose Module not present
        
        dose_summation_type = getattr(dataset, 'DoseSummationType', None)
        
        if dose_summation_type:
            # Type 1C conditional requirements based on Dose Summation Type
            requires_plan_ref = dose_summation_type in [
                DoseSummationType.PLAN.value, 
                DoseSummationType.MULTI_PLAN.value, 
                DoseSummationType.FRACTION.value, 
                DoseSummationType.BEAM.value, 
                DoseSummationType.BRACHY.value
            ]
            requires_structure_ref = dose_summation_type in [
                DoseSummationType.PLAN.value,
                DoseSummationType.MULTI_PLAN.value,
                DoseSummationType.PLAN_OVERVIEW.value
            ]
            
            # Check for Referenced RT Plan Sequence (300C,0002)
            has_plan_ref = hasattr(dataset, 'ReferencedRTPlanSequence')
            
            # Check for Referenced Structure Set Sequence (300C,0060) 
            has_structure_ref = hasattr(dataset, 'ReferencedStructureSetSequence')
            
            # Check for Referenced Image Sequence (0008,1140)
            has_image_ref = hasattr(dataset, 'ReferencedImageSequence')
            
            if requires_plan_ref and not has_plan_ref and not has_structure_ref and not has_image_ref:
                result.add_error(
                    f"Dose Summation Type '{dose_summation_type}' requires either "
                    f"Referenced RT Plan Sequence (300C,0002), Referenced Structure Set Sequence (300C,0060), "
                    f"or Referenced Image Sequence (0008,1140) to be present (Type 1C requirement)"
                )
            
            if requires_structure_ref and not has_structure_ref:
                result.add_error(
                    f"Dose Summation Type '{dose_summation_type}' requires "
                    f"Referenced Structure Set Sequence (300C,0060) to be present (Type 1C requirement)"
                )
    
    @staticmethod
    def _validate_rt_plan_references(dataset: Dataset, result: ValidationResult) -> None:
        """Validate RT General Plan Module reference requirements."""
        # Check if RT General Plan Module is present
        rt_plan_geometry = getattr(dataset, 'RTPlanGeometry', None)
        
        if rt_plan_geometry == 'PATIENT':
            # Type 1C: Referenced Structure Set Sequence is required
            if not hasattr(dataset, 'ReferencedStructureSetSequence'):
                result.add_error(
                    "Referenced Structure Set Sequence (300C,0060) is required when "
                    "RT Plan Geometry (300A,000C) is 'PATIENT' (Type 1C requirement)"
                )
            else:
                # Validate that the sequence is not empty
                ref_structure_seq = getattr(dataset, 'ReferencedStructureSetSequence', [])
                if not ref_structure_seq or len(ref_structure_seq) == 0:
                    result.add_error(
                        "Referenced Structure Set Sequence (300C,0060) cannot be empty when "
                        "RT Plan Geometry (300A,000C) is 'PATIENT'"
                    )