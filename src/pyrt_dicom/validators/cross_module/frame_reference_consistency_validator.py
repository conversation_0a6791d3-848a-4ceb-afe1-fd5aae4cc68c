"""Frame of Reference consistency validator for cross-module validation.

This validator ensures that Frame of Reference UIDs are consistent across
modules that reference the same spatial coordinate system.
"""

from pydicom import Dataset
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class FrameReferenceConsistencyValidator(BaseValidator):
    """Validator for Frame of Reference UID consistency across modules.
    
    Validates that all modules referencing a spatial coordinate system
    use the same Frame of Reference UID, ensuring spatial consistency
    across the entire IOD.
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Frame of Reference UID consistency across modules.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Collect all Frame of Reference UIDs from the dataset
        frame_ref_uids = []
        
        # Frame of Reference Module (0020,0052)
        if hasattr(dataset, 'FrameOfReferenceUID'):
            frame_ref_uids.append(('Frame of Reference Module', dataset.FrameOfReferenceUID))
        
        # Structure Set Module - Referenced Frame of Reference Sequence
        if hasattr(dataset, 'ReferencedFrameOfReferenceSequence'):
            for i, ref_frame_seq in enumerate(dataset.ReferencedFrameOfReferenceSequence):
                if hasattr(ref_frame_seq, 'FrameOfReferenceUID'):
                    frame_ref_uids.append(
                        (f'Referenced Frame of Reference Sequence[{i}]', 
                         ref_frame_seq.FrameOfReferenceUID)
                    )
        
        # Synchronization Module - Synchronization Frame of Reference UID
        if hasattr(dataset, 'SynchronizationFrameOfReferenceUID'):
            frame_ref_uids.append(
                ('Synchronization Module', dataset.SynchronizationFrameOfReferenceUID)
            )
        
        # Validate consistency
        if len(frame_ref_uids) > 1:
            unique_uids = set(uid for _, uid in frame_ref_uids)
            if len(unique_uids) > 1:
                # Multiple different UIDs found
                uid_sources = {}
                for source, uid in frame_ref_uids:
                    if uid not in uid_sources:
                        uid_sources[uid] = []
                    uid_sources[uid].append(source)
                
                result.add_error(
                    "Inconsistent Frame of Reference UIDs found: " +
                    "; ".join([f"'{uid}' in {', '.join(sources)}" 
                              for uid, sources in uid_sources.items()])
                )
        
        # Validate that Frame of Reference UIDs are not empty
        for source, uid in frame_ref_uids:
            if not uid or uid.strip() == "":
                result.add_error(f"Frame of Reference UID is empty in {source}")
        
        return result