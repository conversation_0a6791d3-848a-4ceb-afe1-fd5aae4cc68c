"""Cross-module DICOM validation components.

This package provides validators that check relationships and dependencies
between multiple DICOM modules within IODs. These validators handle complex
validation scenarios that cannot be addressed by individual module validators.
"""

from .anatomical_orientation_validator import AnatomicalOrientationValidator
from .frame_reference_consistency_validator import FrameReferenceConsistencyValidator
from .rt_plan_structure_reference_validator import RTPlanStructureReferenceValidator

__all__ = [
    'AnatomicalOrientationValidator',
    'FrameReferenceConsistencyValidator',
    'RTPlanStructureReferenceValidator'
]