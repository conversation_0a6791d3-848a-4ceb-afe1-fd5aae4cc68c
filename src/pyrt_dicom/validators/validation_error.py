"""Validation exception for DICOM module validation failures.

This module provides a custom exception class for validation failures
that occur during DICOM module validation processes.
"""


class ValidationError(Exception):
    """Exception raised when DICOM module validation fails.
    
    This exception is raised when validation errors are detected that
    require immediate attention or prevent successful processing. It
    provides clear error messages with context for debugging and
    user feedback.
    
    The ValidationError is used in conjunction with ValidationResult
    objects to provide both structured validation reporting and
    exception-based error handling when immediate failure is required.
    
    Attributes:
        message: The error message describing the validation failure
        context: Optional additional context about where the error occurred
        
    Example:
        >>> try:
        ...     module._ensure_required_elements_valid()
        ... except ValidationError as e:
        ...     print(f"Validation failed: {e}")
        ...     # Handle the validation error appropriately
    """
    
    def __init__(self, message: str, context: str | None = None):
        """Initialize ValidationError with message and optional context.
        
        Args:
            message: Error message describing the validation failure
            context: Optional context about where the error occurred
        """
        self.message = message
        self.context = context
        
        # Build the full error message with context if provided
        if context is not None:
            full_message = f"{context}: {message}"
        else:
            full_message = message
            
        super().__init__(full_message)
    
    def __str__(self) -> str:
        """Return string representation of the validation error.
        
        Returns:
            String representation including context if available
        """
        if self.context is not None:
            return f"{self.context}: {self.message}"
        return self.message
    
    def __repr__(self) -> str:
        """Return detailed representation for debugging.
        
        Returns:
            Detailed string representation with class name and attributes
        """
        if self.context is not None:
            return f"ValidationError(message={self.message!r}, context={self.context!r})"
        return f"ValidationError(message={self.message!r})"