"""RT Series Module - DICOM PS3.3 C.8.8.1

The RT Series Module contains attributes that identify and describe
the RT Series performed as part of a Study. There exist significant
differences in the manner in which RT objects as compared to diagnostic
objects. An RT object can be one of several types, and a series of a
given object type may be created over a temporal span of several weeks.
"""
from datetime import datetime, date
from pydicom import Dataset
from pydicom.valuerep import PersonName
from .base_module import BaseModule
from ..enums.series_enums import Modality
from ..validators.modules.base_validator import ValidationConfig
from ..validators.modules.rt_series_validator import RTSeriesValidator
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class RTSeriesModule(BaseModule):
    """RT Series Module implementation for DICOM PS3.3 C.8.8.1.

    Uses composition-based architecture with internal dataset management.
    Contains attributes that identify and describe the RT Series performed as part of a Study.

    The RT Series Module has been created to satisfy the requirements of the standard
    DICOM Query/Retrieve model while including only those Attributes relevant to the
    identification and selection of radiotherapy objects.

    Usage:
        # Create with required elements
        rt_series = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE,
            series_instance_uid="*******.*******.9.10",
            operators_name="Tech^John"  # Type 2 - required but can be empty
        )

        # Add optional elements
        rt_series.with_optional_elements(
            series_number=1,
            series_date="20240101",
            series_time="120000",
            series_description="RT Dose Distribution"
        )

        # Add conditional elements
        rt_series.with_operator_identification(
            operator_identification_sequence=[
                rt_series.create_operator_identification_item(
                    person_identification_code_sequence=[],
                    person_address="123 Main St",
                    person_telephone_numbers=["555-1234"]
                )
            ]
        )

        # Add RT-specific information
        rt_series.with_rt_specific_elements(
            treatment_session_uid="*******.*******.9.12"
        )

        # Generate dataset for IOD integration
        dataset = rt_series.to_dataset()

        # Validate
        result = rt_series.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        modality: str | Modality,
        series_instance_uid: str,
        operators_name: str | PersonName = ""
    ) -> 'RTSeriesModule':
        """Create RTSeriesModule from all required (Type 1 and Type 2) data elements.

        Args:
            modality (str | Modality): Type of equipment that originally acquired the data (0008,0060) Type 1
                Enumerated Values: RTIMAGE, RTDOSE, RTSTRUCT, RTPLAN, RTRECORD
            series_instance_uid (str): Unique identifier for the Series (0020,000E) Type 1
            operators_name (str | PersonName): Name(s) of the operator(s) supporting the Series (0008,1070) Type 2
                Can be empty string but must be present

        Returns:
            RTSeriesModule: New module instance with required data elements set
        """
        instance = cls()
        instance['Modality'] = format_enum_string(modality)
        instance['SeriesInstanceUID'] = series_instance_uid
        instance['OperatorsName'] = operators_name
        return instance
    
    def with_optional_elements(
        self,
        series_number: int | str | None = None,
        series_date: str | datetime | date | None = None,
        series_time: str | datetime | None = None,
        series_description: str | None = None,
        series_description_code_sequence: list[Dataset] | None = None,
        referenced_performed_procedure_step_sequence: list[Dataset] | None = None,
        request_attributes_sequence: list[Dataset] | None = None
    ) -> 'RTSeriesModule':
        """Add optional (Type 3) data elements to the module instance.

        Args:
            series_number (int | str | None): A number that identifies this Series (0020,0011) Type 2
            series_date (str | datetime | date | None): Date the Series started (0008,0021) Type 3
            series_time (str | datetime | None): Time the Series started (0008,0031) Type 3
            series_description (str | None): Description of the Series (0008,103E) Type 3
            series_description_code_sequence (list[Dataset] | None): A coded description of the Series (0008,103F) Type 3
                Only a single Item is permitted in this Sequence
            referenced_performed_procedure_step_sequence (list[Dataset] | None): Uniquely identifies the
                Performed Procedure Step SOP Instance to which the Series is related (0008,1111) Type 3
            request_attributes_sequence (list[Dataset] | None): Sequence that contains Attributes from
                the Imaging Service Request (0040,0275) Type 3

        Returns:
            RTSeriesModule: Self for method chaining
        """
        if series_number is not None:
            self.SeriesNumber = series_number
        if series_date is not None:
            self.SeriesDate = format_date_value(series_date)
        if series_time is not None:
            self.SeriesTime = format_time_value(series_time)
        if series_description is not None:
            self.SeriesDescription = series_description
        if series_description_code_sequence is not None:
            self.SeriesDescriptionCodeSequence = series_description_code_sequence
        if referenced_performed_procedure_step_sequence is not None:
            self.ReferencedPerformedProcedureStepSequence = referenced_performed_procedure_step_sequence
        if request_attributes_sequence is not None:
            self.RequestAttributesSequence = request_attributes_sequence
        return self
    
    def with_operator_identification(
        self,
        operator_identification_sequence: list[Dataset] | None = None
    ) -> 'RTSeriesModule':
        """Add operator identification sequence (Type 3).

        If present, the number and order should correspond to the Value of
        Operators' Name (0008,1070), if present.

        Args:
            operator_identification_sequence (list[Dataset] | None): Identification of the
                operator(s) supporting the Series (0008,1072) Type 3

        Returns:
            RTSeriesModule: Self for method chaining
        """
        if operator_identification_sequence is not None:
            self.OperatorIdentificationSequence = operator_identification_sequence
        return self
    
    def with_rt_specific_elements(
        self,
        treatment_session_uid: str | None = None
    ) -> 'RTSeriesModule':
        """Add RT-specific elements.

        Args:
            treatment_session_uid (str | None): A unique identifier of the RT Treatment Session
                to which Instances in this Series belong (300A,0700) Type 3

        Returns:
            RTSeriesModule: Self for method chaining
        """
        if treatment_session_uid is not None:
            self.TreatmentSessionUID = treatment_session_uid
        return self

    @staticmethod
    def create_operator_identification_item(
        person_identification_code_sequence: list[Dataset] | None = None,
        person_address: str | None = None,
        person_telephone_numbers: list[str] | None = None,
        person_telecom_information: str | None = None,
        institution_name: str | None = None,
        institution_address: str | None = None,
        institutional_department_name: str | None = None,
        institutional_department_type_code_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create an item for Operator Identification Sequence (0008,1072).

        Args:
            person_identification_code_sequence (list[Dataset] | None): Person identification codes
            person_address (str | None): Person's address
            person_telephone_numbers (list[str] | None): Person's telephone numbers
            person_telecom_information (str | None): Person's telecom information
            institution_name (str | None): Institution name
            institution_address (str | None): Institution address
            institutional_department_name (str | None): Department name
            institutional_department_type_code_sequence (list[Dataset] | None): Department type codes

        Returns:
            Dataset: Sequence item with operator identification information
        """
        item = Dataset()
        if person_identification_code_sequence is not None:
            item.PersonIdentificationCodeSequence = person_identification_code_sequence
        if person_address is not None:
            item.PersonAddress = person_address
        if person_telephone_numbers is not None:
            item.PersonTelephoneNumbers = person_telephone_numbers
        if person_telecom_information is not None:
            item.PersonTelecomInformation = person_telecom_information
        if institution_name is not None:
            item.InstitutionName = institution_name
        if institution_address is not None:
            item.InstitutionAddress = institution_address
        if institutional_department_name is not None:
            item.InstitutionalDepartmentName = institutional_department_name
        if institutional_department_type_code_sequence is not None:
            item.InstitutionalDepartmentTypeCodeSequence = institutional_department_type_code_sequence
        return item

    @staticmethod
    def create_series_description_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None
    ) -> Dataset:
        """Create an item for Series Description Code Sequence (0008,103F).

        Note: Only a single Item is permitted in this Sequence.

        Args:
            code_value (str): Code value
            coding_scheme_designator (str): Coding scheme designator
            code_meaning (str): Code meaning
            coding_scheme_version (str | None): Coding scheme version

        Returns:
            Dataset: Sequence item with code sequence information
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        return item

    @property
    def has_operator_info(self) -> bool:
        """Check if operator information is present.

        Returns:
            bool: True if operator-related elements are present
        """
        return any(attr in self for attr in [
            'OperatorsName', 'OperatorIdentificationSequence'
        ])

    @property
    def has_series_description_code(self) -> bool:
        """Check if series description code sequence is present.

        Returns:
            bool: True if series description code sequence is present
        """
        return 'SeriesDescriptionCodeSequence' in self

    @property
    def has_rt_specific_info(self) -> bool:
        """Check if RT-specific information is present.

        Returns:
            bool: True if RT-specific elements are present
        """
        return 'TreatmentSessionUID' in self

    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured with required elements.

        Returns:
            bool: True if all required elements are present
        """
        return ('Modality' in self and
                'SeriesInstanceUID' in self and
                'OperatorsName' in self)

    @property
    def modality_type(self) -> str | None:
        """Get the modality type for this RT series.

        Returns:
            str | None: The modality value or None if not set
        """
        if 'Modality' not in self:
            return None
        return self.Modality

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Series Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with errors and warnings
        """
        return RTSeriesValidator.validate(self._dataset, config)
