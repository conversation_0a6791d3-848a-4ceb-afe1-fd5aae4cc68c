"""
Synchronization Module - DICOM PS3.3 C.7.4.2

The Synchronization Module contains attributes necessary to uniquely identify 
a Frame of Reference that establishes the temporal relationship of SOP Instances.
A synchronized environment may be established based on a shared time of day clock,
and/or on a shared trigger event or synchronization waveform channel.
"""
from .base_module import BaseModule
from ..validators.modules.synchronization_validator import SynchronizationValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators.validation_result import ValidationResult
from ..enums.synchronization_enums import (
    SynchronizationTrigger,
    AcquisitionTimeSynchronized,
    TimeDistributionProtocol
)
from ..utils.dicom_formatters import format_enum_string


class SynchronizationModule(BaseModule):
    """Synchronization Module implementation for DICOM PS3.3 C.7.4.2.
    
    Contains attributes necessary to uniquely identify a Frame of Reference that 
    establishes the temporal relationship of SOP Instances. A synchronized environment
    may be established based on a shared time of day clock, and/or on a shared 
    trigger event or synchronization waveform channel.
    
    Usage:
        # Create with required elements
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )

        # Add optional elements
        sync.with_optional_elements(
            trigger_source_or_type="LINAC_001",
            time_source="NTP_SERVER_001",
            time_distribution_protocol=TimeDistributionProtocol.NTP,
            ntp_source_address="*************"
        )
        
        # Add conditional elements if needed
        sync.with_synchronization_channel(
            synchronization_channel=[1, 2]  # Multiplex group 1, channel 2
        )
        
        # Generate dataset for IOD integration
        dataset = sync.to_dataset()
        
        # Validate
        result = sync.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        synchronization_frame_of_reference_uid: str,
        synchronization_trigger: str | SynchronizationTrigger,
        acquisition_time_synchronized: str | AcquisitionTimeSynchronized
    ) -> 'SynchronizationModule':
        """Create Synchronization Module from all required (Type 1) data elements.
        
        Args:
            synchronization_frame_of_reference_uid (str): UID of common synchronization 
                environment (0020,0200) Type 1. Identifies shared synchronization environment.
            synchronization_trigger (str | SynchronizationTrigger): Data acquisition synchronization
                with external equipment (0018,106A) Type 1. Values: SOURCE, EXTERNAL, PASSTHRU, NO TRIGGER.
            acquisition_time_synchronized (str | AcquisitionTimeSynchronized): Acquisition DateTime
                synchronized with external time reference (0018,1800) Type 1. Values: Y, N.
            
        Returns:
            SynchronizationModule: New module instance with required data elements set
        """
        instance = cls()
        instance['SynchronizationFrameOfReferenceUID'] = synchronization_frame_of_reference_uid
        instance['SynchronizationTrigger'] = format_enum_string(synchronization_trigger)
        instance['AcquisitionTimeSynchronized'] = format_enum_string(acquisition_time_synchronized)
        return instance
    
    def with_optional_elements(
        self,
        trigger_source_or_type: str | None = None,
        time_source: str | None = None,
        time_distribution_protocol: str | TimeDistributionProtocol | None = None,
        ntp_source_address: str | None = None
    ) -> 'SynchronizationModule':
        """Add optional (Type 3) data elements.
        
        Args:
            trigger_source_or_type (str | None): Equipment ID of trigger source and/or 
                type of trigger (0018,1061) Type 3.
            time_source (str | None): ID of equipment or system providing time reference 
                (0018,1801) Type 3.
            time_distribution_protocol (str | TimeDistributionProtocol | None): Method of time
                distribution used to synchronize equipment (0018,1802) Type 3. Values: NTP, IRIG, GPS, SNTP, PTP.
            ntp_source_address (str | None): IP Address of NTP, SNTP, or PTP time source 
                (0018,1803) Type 3. IPv4 in dotted decimal or IPv6 in colon separated hex.
            
        Returns:
            SynchronizationModule: Self for method chaining
        """
        if trigger_source_or_type is not None:
            self.TriggerSourceOrType = trigger_source_or_type
        if time_source is not None:
            self.TimeSource = time_source
        if time_distribution_protocol is not None:
            self.TimeDistributionProtocol = format_enum_string(time_distribution_protocol)
        if ntp_source_address is not None:
            self.NTPSourceAddress = ntp_source_address
        return self
    
    def with_synchronization_channel(
        self,
        synchronization_channel: list[int]
    ) -> 'SynchronizationModule':
        """Add synchronization channel (Type 1C) - required if synchronization channel 
        or trigger is encoded in a waveform in this SOP Instance.
        
        Args:
            synchronization_channel (list[int]): Identifier of waveform channel that records 
                the synchronization channel or trigger (0018,106C) Type 1C. Specified as 
                pair [M,C] where M is multiplex group ordinal and C is channel number ordinal.
            
        Returns:
            SynchronizationModule: Self for method chaining
            
        Raises:
            ValueError: If synchronization_channel is not exactly 2 values
        """
        # Validate that we have exactly 2 values for [M,C] pair
        if len(synchronization_channel) != 2:
            raise ValueError(
                "Synchronization Channel (0018,106C) must be specified as [M,C] pair with exactly 2 values. "
                "M is the ordinal of Sequence Item of Waveform Sequence (5400,0100), "
                "C is the ordinal of Sequence Item of Channel Definition Sequence (003A,0200)."
            )
        
        self.SynchronizationChannel = synchronization_channel
        return self
    
    @property
    def is_source_synchronized(self) -> bool:
        """Check if this equipment provides synchronization to other equipment.
        
        Returns:
            bool: True if synchronization trigger is SOURCE
        """
        return ('SynchronizationTrigger' in self and 
                self.SynchronizationTrigger == 'SOURCE')
    
    @property
    def is_externally_synchronized(self) -> bool:
        """Check if this equipment receives synchronization from external equipment.
        
        Returns:
            bool: True if synchronization trigger is EXTERNAL
        """
        return ('SynchronizationTrigger' in self and 
                self.SynchronizationTrigger == 'EXTERNAL')
    
    @property
    def is_passthrough_synchronized(self) -> bool:
        """Check if this equipment receives and forwards synchronization.
        
        Returns:
            bool: True if synchronization trigger is PASSTHRU
        """
        return ('SynchronizationTrigger' in self and 
                self.SynchronizationTrigger == 'PASSTHRU')
    
    @property
    def has_no_trigger(self) -> bool:
        """Check if data acquisition is not synchronized by common channel or trigger.
        
        Returns:
            bool: True if synchronization trigger is NO TRIGGER
        """
        return ('SynchronizationTrigger' in self and 
                self.SynchronizationTrigger == 'NO TRIGGER')
    
    @property
    def is_time_synchronized(self) -> bool:
        """Check if acquisition time is synchronized with external time reference.
        
        Returns:
            bool: True if acquisition time synchronized is Y
        """
        return ('AcquisitionTimeSynchronized' in self and 
                self.AcquisitionTimeSynchronized == 'Y')
    
    @property
    def has_synchronization_channel(self) -> bool:
        """Check if synchronization channel is specified.
        
        Returns:
            bool: True if synchronization channel is present
        """
        return 'SynchronizationChannel' in self
    
    @property
    def uses_ntp_protocol(self) -> bool:
        """Check if time distribution uses Network Time Protocol.
        
        Returns:
            bool: True if time distribution protocol is NTP or SNTP
        """
        if 'TimeDistributionProtocol' in self:
            return self.TimeDistributionProtocol in ['NTP', 'SNTP']
        return False
    
    @property
    def uses_precision_time_protocol(self) -> bool:
        """Check if time distribution uses IEEE 1588 Precision Time Protocol.
        
        Returns:
            bool: True if time distribution protocol is PTP
        """
        return ('TimeDistributionProtocol' in self and 
                self.TimeDistributionProtocol == 'PTP')
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured with required elements.
        
        Returns:
            bool: True if all required (Type 1) elements are present
        """
        return ('SynchronizationFrameOfReferenceUID' in self and
                'SynchronizationTrigger' in self and
                'AcquisitionTimeSynchronized' in self)
    
    @property
    def requires_synchronization_channel(self) -> bool:
        """Check if synchronization channel is conditionally required.
        
        Note: The DICOM standard states this is required "if synchronization 
        channel or trigger is encoded in a waveform in this SOP Instance".
        This property cannot automatically determine if such a waveform exists,
        so it returns False. Users must call with_synchronization_channel()
        when they know waveform data encodes synchronization information.
        
        Returns:
            bool: False (cannot be determined without waveform context)
        """
        return False
        
    @property 
    def uses_gps_protocol(self) -> bool:
        """Check if time distribution uses Global Positioning System.
        
        Returns:
            bool: True if time distribution protocol is GPS
        """
        return ('TimeDistributionProtocol' in self and
                self.TimeDistributionProtocol == 'GPS')
    
    @property
    def uses_irig_protocol(self) -> bool:
        """Check if time distribution uses Inter Range Instrumentation Group.
        
        Returns:
            bool: True if time distribution protocol is IRIG
        """
        return ('TimeDistributionProtocol' in self and
                self.TimeDistributionProtocol == 'IRIG')
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Synchronization Module data against DICOM standard.
        
        Args:
            config (ValidationConfig | None): Validation configuration options
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return SynchronizationValidator.validate(self._dataset, config)
