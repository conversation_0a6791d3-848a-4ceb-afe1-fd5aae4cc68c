"""RT <PERSON>OI Observations Module - DICOM PS3.3 C.8.8.8

The RT ROI Observations Module specifies the identification and interpretation
of an ROI specified in the Structure Set Module and ROI Contour Module.
"""
from datetime import datetime
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.rt_enums import RTROIInterpretedType, RTROIRelationship, ROIPhysicalProperty
from ..validators.modules.rt_roi_observations_validator import RTROIObservationsValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_string


class RTROIObservationsModule(BaseModule):
    """RT ROI Observations Module implementation for DICOM PS3.3 C.8.8.8.

    Uses composition with internal dataset management rather than inheriting
    from pydicom.Dataset for cleaner separation of concerns.
    Specifies identification and interpretation of ROIs.

    Usage:
        # Create with optional elements (no required elements at top level)
        roi_obs = RTROIObservationsModule.from_required_elements()

        # Add optional elements
        roi_obs.with_optional_elements(
            rt_roi_observations_sequence=[
                RTROIObservationsModule.create_rt_roi_observations_item(
                    observation_number=1,
                    referenced_roi_number=1,
                    rt_roi_interpreted_type=RTROIInterpretedType.PTV,
                    roi_interpreter="Dr. Smith"
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = roi_obs.to_dataset()

        # Validate
        result = roi_obs.validate()
    """

    @classmethod
    def from_required_elements(
        cls
    ) -> 'RTROIObservationsModule':
        """Create RTROIObservationsModule from all required data elements.
        
        Note: This module has no required (Type 1 or Type 2) elements at the top level.
        All elements are Type 3 (optional).
                
        Returns:
            RTROIObservationsModule: New module instance
        """
        instance = cls()
        return instance
    
    def with_optional_elements(
        self,
        rt_roi_observations_sequence: list[Dataset] | None = None
    ) -> 'RTROIObservationsModule':
        """Add optional (Type 3) elements.

        Args:
            rt_roi_observations_sequence (list[Dataset] | None): Sequence of observations related to ROIs (3006,0080) Type 3

        Returns:
            RTROIObservationsModule: Self for method chaining
        """
        if rt_roi_observations_sequence is not None:
            self.RTROIObservationsSequence = rt_roi_observations_sequence
        return self

    def with_roi_interpreter_sequence_conditional(
        self,
        roi_interpreter_sequence: list[Dataset]
    ) -> 'RTROIObservationsModule':
        """Add ROI Interpreter Sequence (Type 1C).

        Required if ROI Creator Sequence (3006,004D) is present in the Structure Set Module
        and the person or device performing the interpretation differs from ROI Creator Sequence.
        May be present otherwise.

        Args:
            roi_interpreter_sequence (list[Dataset]): Person or device performing interpretation (3006,004E)

        Returns:
            RTROIObservationsModule: Self for method chaining
        """
        # Note: This is a Type 1C element that depends on Structure Set Module context
        # The actual conditional validation should be done at the IOD level
        if 'RTROIObservationsSequence' in self:
            for obs_item in self.RTROIObservationsSequence:
                obs_item.ROIInterpreterSequence = roi_interpreter_sequence
        return self
    
    @staticmethod
    def create_rt_roi_observations_item(
        observation_number: int,
        referenced_roi_number: int,
        rt_roi_interpreted_type: str | RTROIInterpretedType = "",
        roi_interpreter: str = "",
        roi_observation_datetime: str | datetime | None = None,
        roi_observation_context_code_sequence: list[Dataset] | None = None,
        rt_related_roi_sequence: list[Dataset] | None = None,
        anatomic_region_sequence: list[Dataset] | None = None,
        segmented_property_category_code_sequence: list[Dataset] | None = None,
        rt_roi_identification_code_sequence: list[Dataset] | None = None,
        therapeutic_role_category_code_sequence: list[Dataset] | None = None,
        therapeutic_role_type_code_sequence: list[Dataset] | None = None,
        related_rt_roi_observations_sequence: list[Dataset] | None = None,
        roi_interpreter_sequence: list[Dataset] | None = None,
        material_id: str | None = None,
        roi_physical_properties_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create RT ROI observations sequence item.

        Args:
            observation_number (int): Identification number of the Observation (3006,0082) Type 1
            referenced_roi_number (int): Uniquely identifies the referenced ROI (3006,0084) Type 1
            rt_roi_interpreted_type (str | RTROIInterpretedType): Type of ROI (3006,00A4) Type 2
            roi_interpreter (str): Name of person performing interpretation (3006,00A6) Type 2
            roi_observation_datetime (str | datetime | None): DateTime observation was last modified (3006,002E) Type 3
            roi_observation_context_code_sequence (list[Dataset] | None): Contexts in which ROI was defined (3006,004F) Type 3
            rt_related_roi_sequence (list[Dataset] | None): Sequence of significantly related ROIs (3006,0030) Type 3
            anatomic_region_sequence (list[Dataset] | None): Anatomic region information (0008,2218) Type 3
            segmented_property_category_code_sequence (list[Dataset] | None): General category of property (0062,0003) Type 3
            rt_roi_identification_code_sequence (list[Dataset] | None): Code used to identify ROI (3006,0086) Type 3
            therapeutic_role_category_code_sequence (list[Dataset] | None): General category of therapeutic role (3010,0064) Type 3
            therapeutic_role_type_code_sequence (list[Dataset] | None): Specific property type of therapeutic role (3010,0065) Type 3
            related_rt_roi_observations_sequence (list[Dataset] | None): Sequence of related ROI Observations (3006,00A0) Type 3
            roi_interpreter_sequence (list[Dataset] | None): Person or device performing interpretation (3006,004E) Type 1C
            material_id (str | None): User-supplied identifier for ROI material (300A,00E1) Type 3
            roi_physical_properties_sequence (list[Dataset] | None): Physical properties of ROI (3006,00B0) Type 3

        Returns:
            Dataset: RT ROI observations sequence item
        """
        item = Dataset()
        item.ObservationNumber = observation_number
        item.ReferencedROINumber = referenced_roi_number
        item.RTROIInterpretedType = format_enum_string(rt_roi_interpreted_type)
        item.ROIInterpreter = roi_interpreter

        # Add optional elements if provided
        if roi_observation_datetime is not None:
            if isinstance(roi_observation_datetime, datetime):
                # Format as DICOM DateTime (DT): YYYYMMDDHHMMSS.FFFFFF&ZZXX
                item.ROIObservationDateTime = roi_observation_datetime.strftime("%Y%m%d%H%M%S")
            else:
                item.ROIObservationDateTime = str(roi_observation_datetime)
        if roi_observation_context_code_sequence is not None:
            item.ROIObservationContextCodeSequence = roi_observation_context_code_sequence
        if rt_related_roi_sequence is not None:
            item.RTRelatedROISequence = rt_related_roi_sequence
        if anatomic_region_sequence is not None:
            item.AnatomicRegionSequence = anatomic_region_sequence
        if segmented_property_category_code_sequence is not None:
            item.SegmentedPropertyCategoryCodeSequence = segmented_property_category_code_sequence
        if rt_roi_identification_code_sequence is not None:
            item.RTROIIdentificationCodeSequence = rt_roi_identification_code_sequence
        if therapeutic_role_category_code_sequence is not None:
            item.TherapeuticRoleCategoryCodeSequence = therapeutic_role_category_code_sequence
        if therapeutic_role_type_code_sequence is not None:
            item.TherapeuticRoleTypeCodeSequence = therapeutic_role_type_code_sequence
        if related_rt_roi_observations_sequence is not None:
            item.RelatedRTROIObservationsSequence = related_rt_roi_observations_sequence
        if roi_interpreter_sequence is not None:
            item.ROIInterpreterSequence = roi_interpreter_sequence
        if material_id is not None:
            item.MaterialID = material_id
        if roi_physical_properties_sequence is not None:
            item.ROIPhysicalPropertiesSequence = roi_physical_properties_sequence

        return item
    
    @staticmethod
    def create_rt_related_roi_item(
        referenced_roi_number: int,
        rt_roi_relationship: str | RTROIRelationship | None = None
    ) -> Dataset:
        """Create RT related ROI sequence item.

        Args:
            referenced_roi_number (int): Uniquely identifies the related ROI (3006,0084) Type 1
            rt_roi_relationship (str | RTROIRelationship | None): Relationship of referenced ROI (3006,0033) Type 3

        Returns:
            Dataset: RT related ROI sequence item
        """
        item = Dataset()
        item.ReferencedROINumber = referenced_roi_number

        if rt_roi_relationship is not None:
            item.RTROIRelationship = format_enum_string(rt_roi_relationship)

        return item
    
    @staticmethod
    def create_roi_physical_properties_item(
        roi_physical_property: str | ROIPhysicalProperty,
        roi_physical_property_value: float,
        roi_elemental_composition_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create ROI physical properties sequence item.

        Args:
            roi_physical_property (str | ROIPhysicalProperty): Physical property type (3006,00B2) Type 1
            roi_physical_property_value (float): Value for physical property (3006,00B4) Type 1
            roi_elemental_composition_sequence (list[Dataset] | None): Elemental composition (3006,00B6) Type 1C

        Returns:
            Dataset: ROI physical properties sequence item
        """
        item = Dataset()
        item.ROIPhysicalProperty = format_enum_string(roi_physical_property)
        item.ROIPhysicalPropertyValue = roi_physical_property_value

        # Type 1C: Required if ROI Physical Property equals ELEM_FRACTION
        property_value = format_enum_string(roi_physical_property)
        if property_value == 'ELEM_FRACTION':
            if roi_elemental_composition_sequence is None:
                raise ValueError("ROI Elemental Composition Sequence is required when ROI Physical Property is ELEM_FRACTION")
            item.ROIElementalCompositionSequence = roi_elemental_composition_sequence
        elif roi_elemental_composition_sequence is not None:
            item.ROIElementalCompositionSequence = roi_elemental_composition_sequence

        return item
    
    @staticmethod
    def create_roi_elemental_composition_item(
        roi_elemental_composition_atomic_number: int,
        roi_elemental_composition_atomic_mass_fraction: float
    ) -> Dataset:
        """Create ROI elemental composition sequence item.

        Args:
            roi_elemental_composition_atomic_number (int): Atomic number of element (3006,00B7) Type 1
            roi_elemental_composition_atomic_mass_fraction (float): Fractional weight of element (3006,00B8) Type 1

        Returns:
            Dataset: ROI elemental composition sequence item
        """
        item = Dataset()
        item.ROIElementalCompositionAtomicNumber = roi_elemental_composition_atomic_number
        item.ROIElementalCompositionAtomicMassFraction = roi_elemental_composition_atomic_mass_fraction
        return item
    
    @staticmethod
    def create_related_rt_roi_observations_item(
        observation_number: int
    ) -> Dataset:
        """Create related RT ROI observations sequence item.

        Args:
            observation_number (int): Uniquely identifies a related ROI Observation (3006,0082) Type 1

        Returns:
            Dataset: Related RT ROI observations sequence item
        """
        item = Dataset()
        item.ObservationNumber = observation_number
        return item

    @staticmethod
    def create_roi_interpreter_item(
        roi_interpreter: str,
        roi_interpreter_role: str | None = None
    ) -> Dataset:
        """Create ROI interpreter sequence item.

        Args:
            roi_interpreter (str): Name of person performing interpretation (3006,00A6) Type 1
            roi_interpreter_role (str | None): Role of interpreter (3006,00A8) Type 3

        Returns:
            Dataset: ROI interpreter sequence item
        """
        item = Dataset()
        item.ROIInterpreter = roi_interpreter

        if roi_interpreter_role is not None:
            item.ROIInterpreterRole = roi_interpreter_role

        return item

    @staticmethod
    def create_code_sequence_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None,
        context_identifier: str | None = None,
        context_uid: str | None = None,
        mapping_resource: str | None = None,
        context_group_version: str | None = None,
        context_group_extension_flag: str | None = None,
        context_group_local_version: str | None = None,
        context_group_extension_creator_uid: str | None = None
    ) -> Dataset:
        """Create code sequence item for various code sequences.

        Args:
            code_value (str): Code value (0008,0100) Type 1
            coding_scheme_designator (str): Coding scheme designator (0008,0102) Type 1
            code_meaning (str): Code meaning (0008,0104) Type 1
            coding_scheme_version (str | None): Coding scheme version (0008,0103) Type 1C
            context_identifier (str | None): Context identifier (0008,010F) Type 3
            context_uid (str | None): Context UID (0008,010B) Type 3
            mapping_resource (str | None): Mapping resource (0008,0105) Type 1C
            context_group_version (str | None): Context group version (0008,0106) Type 1C
            context_group_extension_flag (str | None): Extension flag (0008,010B) Type 3
            context_group_local_version (str | None): Local version (0008,0107) Type 1C
            context_group_extension_creator_uid (str | None): Extension creator UID (0008,010D) Type 1C

        Returns:
            Dataset: Code sequence item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning

        # Add conditional and optional elements
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if context_identifier is not None:
            item.ContextIdentifier = context_identifier
        if context_uid is not None:
            item.ContextUID = context_uid
        if mapping_resource is not None:
            item.MappingResource = mapping_resource
        if context_group_version is not None:
            item.ContextGroupVersion = context_group_version
        if context_group_extension_flag is not None:
            item.ContextGroupExtensionFlag = context_group_extension_flag
        if context_group_local_version is not None:
            item.ContextGroupLocalVersion = context_group_local_version
        if context_group_extension_creator_uid is not None:
            item.ContextGroupExtensionCreatorUID = context_group_extension_creator_uid

        return item

    @property
    def has_observations(self) -> bool:
        """Check if ROI observations are present.

        Returns:
            bool: True if RT ROI Observations Sequence is present
        """
        return 'RTROIObservationsSequence' in self

    @property
    def observation_count(self) -> int:
        """Get the number of ROI observations in this module.

        Returns:
            int: Number of observations in RT ROI Observations Sequence
        """
        if not self.has_observations:
            return 0
        return len(self.RTROIObservationsSequence)

    def get_interpreted_types(self) -> list[str]:
        """Get list of ROI interpreted types present in this module.

        Returns:
            list[str]: List of unique RT ROI interpreted types
        """
        if not self.has_observations:
            return []
        obs_sequence = self.RTROIObservationsSequence
        interpreted_types = []
        for obs_item in obs_sequence:
            interpreted_type = obs_item.get('RTROIInterpretedType', '')
            if interpreted_type and interpreted_type not in interpreted_types:
                interpreted_types.append(interpreted_type)
        return interpreted_types

    @property
    def has_physical_properties(self) -> bool:
        """Check if any ROI observations have physical properties defined.

        Returns:
            bool: True if any observation has ROI Physical Properties Sequence
        """
        if not self.has_observations:
            return False
        obs_sequence = self.RTROIObservationsSequence
        for obs_item in obs_sequence:
            if obs_item.get('ROIPhysicalPropertiesSequence'):
                return True
        return False

    @property
    def has_related_rois(self) -> bool:
        """Check if any ROI observations have related ROIs defined.

        Returns:
            bool: True if any observation has RT Related ROI Sequence
        """
        if not self.has_observations:
            return False
        obs_sequence = self.RTROIObservationsSequence
        for obs_item in obs_sequence:
            if obs_item.get('RTRelatedROISequence'):
                return True
        return False

    @property
    def has_therapeutic_roles(self) -> bool:
        """Check if any ROI observations have therapeutic roles defined.

        Returns:
            bool: True if any observation has therapeutic role sequences
        """
        if not self.has_observations:
            return False
        obs_sequence = self.RTROIObservationsSequence
        for obs_item in obs_sequence:
            if (obs_item.get('TherapeuticRoleCategoryCodeSequence') or
                obs_item.get('TherapeuticRoleTypeCodeSequence')):
                return True
        return False

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT ROI Observations Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return RTROIObservationsValidator.validate(self._dataset, config)
