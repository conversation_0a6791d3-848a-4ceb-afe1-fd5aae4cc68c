"""SOP Common Module - DICOM PS3.3 C.12.1

The SOP Common Module contains attributes that are required for proper 
functioning and identification of the associated SOP Instances. They do not 
specify any semantics about the Real-World Object represented by the IOD.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.common_enums import (
    SyntheticData, SOPInstanceStatus, QueryRetrieveView,
    ContentQualification, LongitudinalTemporalInformationModified,
    OriginalSpecializedEquipment
)
from ..validators.modules.sop_common_validator import SOPCommonValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class SOPCommonModule(BaseModule):
    """SOP Common Module implementation for DICOM PS3.3 C.12.1.
    
    Uses composition with internal dataset management rather than inheriting
    from pydicom.Dataset. Contains attributes required for proper functioning
    and identification of the associated SOP Instances.
    
    Usage:
        # Create with required elements
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.2",
            sop_instance_uid="*******.*******.9"
        )
        
        # Add optional elements
        sop.with_optional_elements(
            instance_creation_date="20240101",
            instance_creation_time="120000",
            instance_creator_uid="*******.*******.10",
            original_specialized_equipment=OriginalSpecializedEquipment.NO,
            synthetic_data=SyntheticData.NO
        )
        
        # Add conditional character set if needed (Type 1C)
        sop.with_specific_character_set("ISO_IR 100")
        
        # Add conditional elements as needed
        sop.with_query_retrieve_view(QueryRetrieveView.ENHANCED)
        sop.with_encrypted_attributes_sequence([encrypted_item])
        
        # Generate dataset for IOD integration
        dataset = sop.to_dataset()
        
        # Validate
        result = sop.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        sop_class_uid: str,
        sop_instance_uid: str
    ) -> 'SOPCommonModule':
        """Create SOPCommonModule from all required (Type 1) data elements.
        
        Args:
            sop_class_uid (str): Uniquely identifies the SOP Class (0008,0016) Type 1
            sop_instance_uid (str): Uniquely identifies the SOP Instance (0008,0018) Type 1
                
        Returns:
            SOPCommonModule: New module instance with required data elements set
        """
        instance = cls()
        instance['SOPClassUID'] = sop_class_uid
        instance['SOPInstanceUID'] = sop_instance_uid
        return instance
    
    def with_specific_character_set(
        self,
        specific_character_set: str | list[str]
    ) -> 'SOPCommonModule':
        """Add specific character set (Type 1C).
        
        Note: Specific Character Set (0008,0005) is Type 1C - required if an 
        expanded or replacement character set is used. This should be present
        when using non-ASCII characters or special character sets.
        
        Args:
            specific_character_set (str | list[str]): Character set that expands or replaces Basic Graphic Set (0008,0005) Type 1C
            
        Returns:
            SOPCommonModule: Self for method chaining
        """
        self.SpecificCharacterSet = specific_character_set
        return self

    def with_query_retrieve_view(
        self,
        query_retrieve_view: QueryRetrieveView | str
    ) -> 'SOPCommonModule':
        """Add query/retrieve view (Type 1C).
        
        Note: Query/Retrieve View (0008,0053) is Type 1C - required if the Instance 
        has ever been converted from its source form as the result of a C-MOVE 
        operation with a specific view.
        
        Args:
            query_retrieve_view (QueryRetrieveView | str): View requested during C-MOVE operation (0008,0053) Type 1C
            
        Returns:
            SOPCommonModule: Self for method chaining
        """
        self.QueryRetrieveView = format_enum_string(query_retrieve_view)
        return self

    def with_encrypted_attributes_sequence(
        self,
        encrypted_attributes_sequence: list[Dataset]
    ) -> 'SOPCommonModule':
        """Add encrypted attributes sequence (Type 1C).
        
        Note: Encrypted Attributes Sequence (0400,0500) is Type 1C - required if 
        application level confidentiality is needed and certain recipients are 
        allowed to decrypt all or portions of the Encrypted Attributes Data Set.
        
        Args:
            encrypted_attributes_sequence (list[Dataset]): Encrypted DICOM data sequence (0400,0500) Type 1C
            
        Returns:
            SOPCommonModule: Self for method chaining
        """
        self.EncryptedAttributesSequence = encrypted_attributes_sequence
        return self

    def with_conversion_source_attributes_sequence(
        self,
        conversion_source_attributes_sequence: list[Dataset]
    ) -> 'SOPCommonModule':
        """Add conversion source attributes sequence (Type 1C).
        
        Note: Conversion Source Attributes Sequence (0020,9172) is Type 1C - required 
        if this Instance was created by conversion from a DICOM source, and 
        Conversion Source Attributes Sequence is not present in Shared or Per-Frame 
        Functional Groups Sequences.
        
        Args:
            conversion_source_attributes_sequence (list[Dataset]): Source images/instances that were converted (0020,9172) Type 1C
            
        Returns:
            SOPCommonModule: Self for method chaining
        """
        self.ConversionSourceAttributesSequence = conversion_source_attributes_sequence
        return self

    def with_hl7_structured_document_reference_sequence(
        self,
        hl7_structured_document_reference_sequence: list[Dataset]
    ) -> 'SOPCommonModule':
        """Add HL7 structured document reference sequence (Type 1C).
        
        Note: HL7 Structured Document Reference Sequence (0040,A390) is Type 1C - 
        required if unencapsulated HL7 Structured Documents are referenced within 
        the Instance. Every such document so referenced is required to have a 
        corresponding Item in this Sequence.
        
        Args:
            hl7_structured_document_reference_sequence (list[Dataset]): HL7 document references (0040,A390) Type 1C
            
        Returns:
            SOPCommonModule: Self for method chaining
        """
        self.HL7StructuredDocumentReferenceSequence = hl7_structured_document_reference_sequence
        return self
    
    def with_optional_elements(
        self,
        instance_creation_date: str | datetime | date | None = None,
        instance_creation_time: str | datetime | None = None,
        instance_coercion_datetime: str | datetime | None = None,
        instance_creator_uid: str | None = None,
        related_general_sop_class_uid: str | list[str] | None = None,
        original_specialized_sop_class_uid: str | None = None,
        original_specialized_equipment: str | OriginalSpecializedEquipment | None = None,
        synthetic_data: str | SyntheticData | None = None,
        coding_scheme_identification_sequence: list[Dataset] | None = None,
        context_group_identification_sequence: list[Dataset] | None = None,
        mapping_resource_identification_sequence: list[Dataset] | None = None,
        timezone_offset_from_utc: str | None = None,
        contributing_equipment_sequence: list[Dataset] | None = None,
        instance_number: int | None = None,
        sop_instance_status: str | SOPInstanceStatus | None = None,
        sop_authorization_datetime: str | datetime | None = None,
        sop_authorization_comment: str | None = None,
        authorization_equipment_certification_number: str | None = None,
        longitudinal_temporal_information_modified: str | LongitudinalTemporalInformationModified | None = None,
        content_qualification: str | ContentQualification | None = None,
        private_data_element_characteristics_sequence: list[Dataset] | None = None,
        instance_origin_status: str | None = None,
        barcode_value: str | None = None
    ) -> 'SOPCommonModule':
        """Add optional (Type 3) elements.
        
        Args:
            instance_creation_date (str | datetime | date | None): Date SOP Instance was created (0008,0012) Type 3
            instance_creation_time (str | datetime | None): Time SOP Instance was created (0008,0013) Type 3
            instance_coercion_datetime (str | datetime | None): Date/time instance was last coerced (0008,0015) Type 3
            instance_creator_uid (str | None): Uniquely identifies device that created instance (0008,0014) Type 3
            related_general_sop_class_uid (str | list[str] | None): Related General SOP Class UID (0008,001A) Type 3
            original_specialized_sop_class_uid (str | None): Original Specialized SOP Class UID (0008,001B) Type 3
            original_specialized_equipment (str | OriginalSpecializedEquipment | None): Medical device specification flag (0008,0103) Type 3
            synthetic_data (str | SyntheticData | None): Whether content was made artificially (0008,001C) Type 3
            coding_scheme_identification_sequence (list[Dataset] | None): Coding scheme identification (0008,0110) Type 3
            context_group_identification_sequence (list[Dataset] | None): Context group identification (0008,0123) Type 3
            mapping_resource_identification_sequence (list[Dataset] | None): Mapping resource identification (0008,0124) Type 3
            timezone_offset_from_utc (str | None): Offset from UTC to timezone (0008,0201) Type 3
            contributing_equipment_sequence (list[Dataset] | None): Contributing equipment (0018,A001) Type 3
            instance_number (int | None): Number that identifies this instance (0020,0013) Type 3
            sop_instance_status (str | SOPInstanceStatus | None): Storage status flag (0100,0410) Type 3
            sop_authorization_datetime (str | datetime | None): Date/time status set to AO (0100,0420) Type 3
            sop_authorization_comment (str | None): Comments for status AO (0100,0424) Type 3
            authorization_equipment_certification_number (str | None): Certification number (0100,0426) Type 3
            longitudinal_temporal_information_modified (str | LongitudinalTemporalInformationModified | None): Whether temporal info modified (0028,0303) Type 3
            content_qualification (str | ContentQualification | None): Content qualification indicator (0018,9004) Type 3
            private_data_element_characteristics_sequence (list[Dataset] | None): Private data element characteristics (0008,0300) Type 3
            instance_origin_status (str | None): Instance origin locality (0400,0600) Type 3
            barcode_value (str | None): Barcode interpreted from scanned label (2200,0005) Type 3
        """
        if instance_creation_date is not None:
            self.InstanceCreationDate = format_date_value(instance_creation_date)
        if instance_creation_time is not None:
            self.InstanceCreationTime = format_time_value(instance_creation_time)
        if instance_coercion_datetime is not None:
            self.InstanceCoercionDateTime = instance_coercion_datetime
        
        if instance_creator_uid is not None:
            self.InstanceCreatorUID = instance_creator_uid
        if related_general_sop_class_uid is not None:
            self.RelatedGeneralSOPClassUID = related_general_sop_class_uid
        if original_specialized_sop_class_uid is not None:
            self.OriginalSpecializedSOPClassUID = original_specialized_sop_class_uid
        if original_specialized_equipment is not None:
            self.OriginalSpecializedEquipment = format_enum_string(original_specialized_equipment)
        
        if synthetic_data is not None:
            self.SyntheticData = format_enum_string(synthetic_data)
        
        if coding_scheme_identification_sequence is not None:
            self.CodingSchemeIdentificationSequence = coding_scheme_identification_sequence
        if context_group_identification_sequence is not None:
            self.ContextGroupIdentificationSequence = context_group_identification_sequence
        if mapping_resource_identification_sequence is not None:
            self.MappingResourceIdentificationSequence = mapping_resource_identification_sequence
        if timezone_offset_from_utc is not None:
            self.TimezoneOffsetFromUTC = timezone_offset_from_utc
        if contributing_equipment_sequence is not None:
            self.ContributingEquipmentSequence = contributing_equipment_sequence
        if instance_number is not None:
            self.InstanceNumber = instance_number
        
        if sop_instance_status is not None:
            self.SOPInstanceStatus = format_enum_string(sop_instance_status)
        if sop_authorization_datetime is not None:
            self.SOPAuthorizationDateTime = sop_authorization_datetime
        
        if sop_authorization_comment is not None:
            self.SOPAuthorizationComment = sop_authorization_comment
        if authorization_equipment_certification_number is not None:
            self.AuthorizationEquipmentCertificationNumber = authorization_equipment_certification_number

        # Additional Type 3 elements
        if longitudinal_temporal_information_modified is not None:
            self.LongitudinalTemporalInformationModified = format_enum_string(longitudinal_temporal_information_modified)
        if content_qualification is not None:
            self.ContentQualification = format_enum_string(content_qualification)
        if private_data_element_characteristics_sequence is not None:
            self.PrivateDataElementCharacteristicsSequence = private_data_element_characteristics_sequence
        if instance_origin_status is not None:
            self.InstanceOriginStatus = instance_origin_status
        if barcode_value is not None:
            self.BarcodeValue = barcode_value
        
        return self

    @staticmethod
    def create_coding_scheme_identification_item(
        coding_scheme_designator: str,
        coding_scheme_registry: str | None = None,
        coding_scheme_uid: str | None = None,
        coding_scheme_external_id: str | None = None,
        coding_scheme_name: str | None = None,
        coding_scheme_version: str | None = None,
        coding_scheme_responsible_organization: str | None = None
    ) -> Dataset:
        """Create coding scheme identification sequence item.

        Args:
            coding_scheme_designator (str): Coding scheme designator (0008,0102) Type 1
            coding_scheme_registry (str | None): External registry name (0008,0112) Type 1C
            coding_scheme_uid (str | None): Coding scheme UID (0008,010C) Type 1C
            coding_scheme_external_id (str | None): External ID (0008,0114) Type 2C
            coding_scheme_name (str | None): Full common name (0008,0115) Type 3
            coding_scheme_version (str | None): Version (0008,0103) Type 3
            coding_scheme_responsible_organization (str | None): Responsible organization (0008,0116) Type 3

        Returns:
            Dataset: Coding scheme identification sequence item
        """
        item = Dataset()
        item.CodingSchemeDesignator = coding_scheme_designator
        
        if coding_scheme_registry is not None:
            item.CodingSchemeRegistry = coding_scheme_registry
        if coding_scheme_uid is not None:
            item.CodingSchemeUID = coding_scheme_uid
        if coding_scheme_external_id is not None:
            item.CodingSchemeExternalID = coding_scheme_external_id
        if coding_scheme_name is not None:
            item.CodingSchemeName = coding_scheme_name
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if coding_scheme_responsible_organization is not None:
            item.CodingSchemeResponsibleOrganization = coding_scheme_responsible_organization
            
        return item

    @staticmethod
    def create_context_group_identification_item(
        context_identifier: str,
        mapping_resource: str,
        context_group_version: str,
        context_uid: str | None = None
    ) -> Dataset:
        """Create context group identification sequence item.

        Args:
            context_identifier (str): Context Group identifier (0008,010F) Type 1
            mapping_resource (str): Mapping Resource identifier (0008,0105) Type 1  
            context_group_version (str): Context Group version (0008,0106) Type 1
            context_uid (str | None): Context Group UID (0008,0117) Type 3

        Returns:
            Dataset: Context group identification sequence item
        """
        item = Dataset()
        item.ContextIdentifier = context_identifier
        item.MappingResource = mapping_resource
        item.ContextGroupVersion = context_group_version
        
        if context_uid is not None:
            item.ContextUID = context_uid
            
        return item

    @staticmethod
    def create_mapping_resource_identification_item(
        mapping_resource: str,
        mapping_resource_uid: str | None = None,
        mapping_resource_name: str | None = None
    ) -> Dataset:
        """Create mapping resource identification sequence item.

        Args:
            mapping_resource (str): Mapping Resource identifier (0008,0105) Type 1
            mapping_resource_uid (str | None): Mapping Resource UID (0008,0118) Type 3
            mapping_resource_name (str | None): Mapping Resource name (0008,0122) Type 3

        Returns:
            Dataset: Mapping resource identification sequence item
        """
        item = Dataset()
        item.MappingResource = mapping_resource
        
        if mapping_resource_uid is not None:
            item.MappingResourceUID = mapping_resource_uid
        if mapping_resource_name is not None:
            item.MappingResourceName = mapping_resource_name
            
        return item

    @staticmethod
    def create_private_data_element_characteristics_item(
        private_group_reference: int,
        private_creator_reference: str,
        private_data_element_definition_sequence: list[Dataset] | None = None,
        block_identifying_information_status: str | None = None,
        nonidentifying_private_elements: list[int] | None = None
    ) -> Dataset:
        """Create private data element characteristics sequence item.

        Args:
            private_group_reference (int): Odd group number for Private Data Element block (0008,0301) Type 1
            private_creator_reference (str): Private Creator Data Element value (0008,0302) Type 1
            private_data_element_definition_sequence (list[Dataset] | None): Definition sequence (0008,0310) Type 3
            block_identifying_information_status (str | None): Identity leakage status (0008,0303) Type 1
            nonidentifying_private_elements (list[int] | None): Safe elements list (0008,0304) Type 1C

        Returns:
            Dataset: Private data element characteristics sequence item
        """
        item = Dataset()
        item.PrivateGroupReference = private_group_reference
        item.PrivateCreatorReference = private_creator_reference
        
        if private_data_element_definition_sequence is not None:
            item.PrivateDataElementDefinitionSequence = private_data_element_definition_sequence
        if block_identifying_information_status is not None:
            item.BlockIdentifyingInformationStatus = block_identifying_information_status
        if nonidentifying_private_elements is not None:
            item.NonidentifyingPrivateElements = nonidentifying_private_elements
            
        return item

    @staticmethod
    def create_contributing_equipment_item(
        purpose_of_reference_code_sequence: list[Dataset],
        manufacturer: str,
        institution_name: str | None = None,
        station_name: str | None = None,
        manufacturers_model_name: str | None = None,
        device_serial_number: str | None = None,
        software_versions: list[str] | None = None,
        device_uid: str | None = None
    ) -> Dataset:
        """Create contributing equipment sequence item.

        Args:
            purpose_of_reference_code_sequence (list[dict]): Purpose of reference (0040,A170) Type 1
            manufacturer (str): Manufacturer of equipment (0008,0070) Type 1
            institution_name (str | None): Institution name (0008,0080) Type 3
            station_name (str | None): Station name (0008,1010) Type 3
            manufacturers_model_name (str | None): Model name (0008,1090) Type 3
            device_serial_number (str | None): Serial number (0018,1000) Type 3
            software_versions (list[str] | None): Software versions (0018,1020) Type 3
            device_uid (str | None): Device UID (0018,1002) Type 3

        Returns:
            Dataset: Contributing equipment sequence item
        """
        item = Dataset()
        item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        item.Manufacturer = manufacturer
        
        if institution_name is not None:
            item.InstitutionName = institution_name
        if station_name is not None:
            item.StationName = station_name
        if manufacturers_model_name is not None:
            item.ManufacturersModelName = manufacturers_model_name
        if device_serial_number is not None:
            item.DeviceSerialNumber = device_serial_number
        if software_versions is not None:
            item.SoftwareVersions = software_versions
        if device_uid is not None:
            item.DeviceUID = device_uid
            
        return item

    @property
    def has_creation_info(self) -> bool:
        """Check if creation information is present.

        Returns:
            bool: True if creation date, time, or creator UID is present
        """
        return ('InstanceCreationDate' in self or
                'InstanceCreationTime' in self or
                'InstanceCreatorUID' in self)

    @property
    def is_synthetic(self) -> bool:
        """Check if this instance contains synthetic data.

        Returns:
            bool: True if synthetic data is YES
        """
        return 'SyntheticData' in self and self.SyntheticData == SyntheticData.YES.value

    @property
    def uses_specialized_equipment(self) -> bool:
        """Check if this instance was created using specialized equipment.

        Returns:
            bool: True if original specialized equipment is YES
        """
        return 'OriginalSpecializedEquipment' in self and self.OriginalSpecializedEquipment == OriginalSpecializedEquipment.YES.value

    @property
    def has_authorization_info(self) -> bool:
        """Check if authorization information is present.

        Returns:
            bool: True if authorization status, datetime, or comment is present
        """
        return 'SOPInstanceStatus' in self or 'SOPAuthorizationDateTime' in self or 'SOPAuthorizationComment' in self

    @property
    def is_encrypted(self) -> bool:
        """Check if encrypted attributes are present.

        Returns:
            bool: True if encrypted attributes sequence is present
        """
        return 'EncryptedAttributesSequence' in self

    @property
    def has_character_set(self) -> bool:
        """Check if specific character set is defined.

        Returns:
            bool: True if specific character set is present
        """
        return 'SpecificCharacterSet' in self

    @property
    def is_converted_instance(self) -> bool:
        """Check if this instance was converted from another format.

        Returns:
            bool: True if conversion source attributes or query/retrieve view is present
        """
        return 'ConversionSourceAttributesSequence' in self or 'QueryRetrieveView' in self

    @property 
    def has_hl7_documents(self) -> bool:
        """Check if HL7 structured documents are referenced.

        Returns:
            bool: True if HL7 structured document reference sequence is present
        """
        return 'HL7StructuredDocumentReferenceSequence' in self

    @property
    def has_private_data_characteristics(self) -> bool:
        """Check if private data element characteristics are defined.

        Returns:
            bool: True if private data element characteristics sequence is present
        """
        return 'PrivateDataElementCharacteristicsSequence' in self

    @property
    def has_contributing_equipment(self) -> bool:
        """Check if contributing equipment information is present.

        Returns:
            bool: True if contributing equipment sequence is present
        """
        return 'ContributingEquipmentSequence' in self

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this SOP Common Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return SOPCommonValidator.validate(self._dataset, config)
