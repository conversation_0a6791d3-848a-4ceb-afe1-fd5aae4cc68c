"""
Frame of Reference Module - DICOM PS3.3 C.7.4.1

The Frame of Reference Module contains attributes necessary to uniquely identify
a Frame of Reference that ensures the spatial relationship of Images within a Series.
"""
from .base_module import BaseModule
from ..enums.common_enums import PositionReferenceIndicator
from ..validators.modules.frame_of_reference_validator import FrameOfReferenceValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_string


class FrameOfReferenceModule(BaseModule):
    """Frame of Reference Module implementation for DICOM PS3.3 C.7.4.1.
    
    The Frame of Reference Module contains attributes necessary to uniquely identify 
    a Frame of Reference that ensures spatial relationship of Images within a Series. 
    It also allows Images across multiple Series to share the same Frame of Reference.
    
    This Frame of Reference (coordinate system) shall be constant for all Images 
    related to a specific Frame of Reference. When identified, the position of the 
    imaging target and origin must remain constant in relationship to the specific 
    Frame of Reference.
    
    DICOM Context:
        - Supports Patient-based coordinate systems with anatomical reference points
        - Supports Slide-based coordinate systems with slide corner references  
        - Supports Corneal coordinate systems with corneal vertex references
        - Allows empty Position Reference Indicator when no meaningful reference exists
          (e.g., mammographic images without releasing breast compression)
    
    Usage:
        # Patient-based frame of reference with anatomical landmark
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.1.*******.*******",
            position_reference_indicator=PositionReferenceIndicator.STERNAL_NOTCH
        )

        # Slide-based frame of reference
        slide_frame = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.*******.*******.10",
            position_reference_indicator=PositionReferenceIndicator.SLIDE_CORNER
        )
        
        # Frame with no meaningful reference point (mammography)
        mammo_frame = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid="1.2.840.10008.3.4.5.*******.10.11",
            position_reference_indicator=""  # Empty for mammographic images
        )
        
        # Check frame type and validate
        if frame_ref.is_patient_based:
            dataset = frame_ref.to_dataset()
            print(f"Patient frame with {dataset.PositionReferenceIndicator} reference")

        result = frame_ref.validate()
        if result.has_errors():
            print(f"Validation errors: {result.errors}")

        # Generate dataset for IOD integration
        dataset = frame_ref.to_dataset()
    """

    @classmethod
    def from_required_elements(
        cls,
        frame_of_reference_uid: str,
        position_reference_indicator: str | PositionReferenceIndicator = ""
    ) -> 'FrameOfReferenceModule':
        """Create FrameOfReferenceModule from all required (Type 1 and Type 2) data elements.

        Args:
            frame_of_reference_uid (str): Unique identifier for Frame of Reference (0020,0052) Type 1.
                Must be a valid DICOM UID that uniquely identifies the Frame of Reference for a Series.
            position_reference_indicator (str | PositionReferenceIndicator): Reference point for Frame of Reference (0020,1040) Type 2.
                Part of imaging target used as reference point. Use PositionReferenceIndicator enum for standardized values:
                PositionReferenceIndicator.STERNAL_NOTCH, PositionReferenceIndicator.SLIDE_CORNER, etc.
                String values are also accepted. May be zero length when no meaningful reference point exists.

        Returns:
            FrameOfReferenceModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance['FrameOfReferenceUID'] = frame_of_reference_uid
        instance['PositionReferenceIndicator'] = format_enum_string(position_reference_indicator)
        return instance
    
    def with_optional_elements(self) -> 'FrameOfReferenceModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Frame of Reference Module has no Type 3 elements defined in DICOM PS3.3 C.7.4.1.
        This method is provided for API consistency but accepts no parameters.
        
        Returns:
            FrameOfReferenceModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_anatomical_reference_indicators() -> dict[str, str]:
        """Get common anatomical reference indicators for patient-related frames of reference.
        
        Returns:
            dict[str, str]: Dictionary of reference indicator codes and descriptions
        """
        return {
            "ILIAC_CREST": "Iliac crest anatomical landmark",
            "ORBITAL_MEDIAL": "Orbital-medial anatomical landmark", 
            "STERNAL_NOTCH": "Sternal notch anatomical landmark",
            "SYMPHYSIS_PUBIS": "Symphysis pubis anatomical landmark",
            "XIPHOID": "Xiphoid process anatomical landmark",
            "LOWER_COSTAL_MARGIN": "Lower costal margin anatomical landmark",
            "EXTERNAL_AUDITORY_MEATUS": "External auditory meatus anatomical landmark"
        }
    
    @staticmethod
    def create_slide_reference_indicator() -> str:
        """Get slide corner reference indicator for slide-related frames of reference.
        
        Returns:
            str: Slide corner reference indicator
        """
        return "SLIDE_CORNER"
    
    @staticmethod
    def create_corneal_reference_indicators() -> dict[str, str]:
        """Get corneal vertex reference indicators for corneal coordinate systems.
        
        Returns:
            dict[str, str]: Dictionary of corneal reference indicators
        """
        return {
            "CORNEAL_VERTEX_R": "Corneal vertex for right eye",
            "CORNEAL_VERTEX_L": "Corneal vertex for left eye"
        }
    
    @property
    def is_patient_based(self) -> bool:
        """Check if this appears to be a patient-based frame of reference.

        Returns:
            bool: True if position reference indicator is anatomical
        """
        if 'PositionReferenceIndicator' not in self:
            return False

        anatomical_indicators = self.create_anatomical_reference_indicators()
        return self.PositionReferenceIndicator in anatomical_indicators
    
    @property
    def is_slide_based(self) -> bool:
        """Check if this is a slide-based frame of reference.

        Returns:
            bool: True if position reference indicator is SLIDE_CORNER
        """
        return ('PositionReferenceIndicator' in self and
                self.PositionReferenceIndicator == "SLIDE_CORNER")

    @property
    def is_corneal_based(self) -> bool:
        """Check if this is a corneal coordinate system frame of reference.

        Returns:
            bool: True if position reference indicator is corneal vertex
        """
        if 'PositionReferenceIndicator' not in self:
            return False

        corneal_indicators = self.create_corneal_reference_indicators()
        return self.PositionReferenceIndicator in corneal_indicators
    
    @property
    def corneal_eye_side(self) -> str | None:
        """Get the eye side for corneal coordinate systems.

        Returns:
            str | None: "RIGHT", "LEFT", or None if not corneal-based
        """
        if not self.is_corneal_based:
            return None

        if self.PositionReferenceIndicator == "CORNEAL_VERTEX_R":
            return "RIGHT"
        elif self.PositionReferenceIndicator == "CORNEAL_VERTEX_L":
            return "LEFT"

        return None

    @property
    def has_meaningful_reference(self) -> bool:
        """Check if position reference indicator has meaningful content.

        Returns:
            bool: True if position reference indicator is not empty
        """
        return ('PositionReferenceIndicator' in self and
                len(self.PositionReferenceIndicator.strip()) > 0)
    
    @property
    def reference_type(self) -> str:
        """Get the type of reference frame.

        Returns:
            Reference frame type (str): "PATIENT", "SLIDE", "CORNEAL", "UNKNOWN", or "EMPTY"
        """
        if 'PositionReferenceIndicator' not in self:
            return "UNKNOWN"

        if len(self.PositionReferenceIndicator.strip()) == 0:
            return "EMPTY"
        elif self.is_patient_based:
            return "PATIENT"
        elif self.is_slide_based:
            return "SLIDE"
        elif self.is_corneal_based:
            return "CORNEAL"
        else:
            return "UNKNOWN"

    def is_compatible_with(self, other: 'FrameOfReferenceModule') -> bool:
        """Check if this frame of reference is compatible with another.

        Args:
            other (FrameOfReferenceModule): Other frame of reference to compare

        Returns:
            bool: True if frames of reference are compatible (same UID)
        """
        if not isinstance(other, FrameOfReferenceModule):
            return False

        return ('FrameOfReferenceUID' in self and
                'FrameOfReferenceUID' in other and
                self.FrameOfReferenceUID == other.FrameOfReferenceUID)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Frame of Reference Module instance.

        Args:
            config: Optional validation configuration
            **kwargs: Optional elements to validate (will be checked by validator)

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return FrameOfReferenceValidator.validate(self.to_dataset(), config)
