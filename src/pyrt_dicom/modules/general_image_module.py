"""General Image Module - DICOM PS3.3 C.7.6.1

The General Image Module contains attributes that identify and describe
an image within a particular Series.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.image_enums import (
    QualityControlImage, BurnedInAnnotation, RecognizableVisualFeatures,
    LossyImageCompression, PresentationLUTShape, ImageLaterality,
    LossyImageCompressionMethod, PhotometricInterpretation, ImageType
)
from ..validators.modules.general_image_validator import GeneralImageValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class GeneralImageModule(BaseModule):
    """General Image Module implementation for DICOM PS3.3 C.7.6.1.

    Uses composition with internal dataset management rather than
    inheriting from pydicom.Dataset for cleaner separation of concerns.
    Contains attributes that identify and describe an image within a particular Series.

    This module implements all conditional logic requirements from DICOM PS3.3 C.7.6.1:
    - Patient Orientation (Type 2C): Required when no spatial orientation from other modules
    - Content Date/Time (Type 2C): Required together for temporally related series
    - Image Type structure validation with enumerated value checking
    - Icon Image Sequence constraints (single item, specific formats)
    - Lossy compression consistency validation

    Note: This module includes references to the General Anatomy Optional Macro
    (Table 10-7) which is not yet implemented but is planned for future work.

    Usage Examples:
        # Basic RT Dose image with minimal requirements
        dose_image = GeneralImageModule.from_required_elements(
            instance_number="1"
        ).with_patient_orientation(
            patient_orientation=""  # Empty for dose grids is acceptable
        ).with_optional_elements(
            image_type=[ImageType.DERIVED, ImageType.SECONDARY, ImageType.DOSE]
        )

        # Clinical image with full spatial and temporal information
        clinical_image = GeneralImageModule.from_required_elements(
            instance_number="001"
        ).with_patient_orientation(
            patient_orientation="A\\F"  # Anterior-Foot orientation
        ).with_temporal_elements(
            content_date="20240315",
            content_time="143022.123"
        ).with_optional_elements(
            image_type=[ImageType.ORIGINAL, ImageType.PRIMARY],
            image_comments="Contrast-enhanced axial slice",
            quality_control_image=QualityControlImage.NO,
            burned_in_annotation=BurnedInAnnotation.NO,
            recognizable_visual_features=RecognizableVisualFeatures.YES
        )

        # Image with lossy compression information
        compressed_image = GeneralImageModule.from_required_elements(
            instance_number="5"
        ).with_patient_orientation(
            patient_orientation="L\\H"  # Left-Head orientation
        ).with_optional_elements(
            image_type=[ImageType.DERIVED, ImageType.SECONDARY],
            lossy_image_compression=LossyImageCompression.COMPRESSED,
            lossy_image_compression_ratio=[2.5],
            lossy_image_compression_method=[LossyImageCompressionMethod.ISO_10918_1]
        )

        # Image with icon representation
        icon_item = GeneralImageModule.create_icon_image_item(
            rows=64, columns=64,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,  # Use enum for type safety
            pixel_data=icon_pixel_data
        )
        image_with_icon = GeneralImageModule.from_required_elements(
            instance_number="10"
        ).with_patient_orientation(
            patient_orientation="P\\F"
        ).with_optional_elements(
            icon_image_sequence=[icon_item]
        )

        # Generate dataset for IOD integration
        dataset = clinical_image.to_dataset()
        
        # Comprehensive validation
        result = clinical_image.validate()
        if not result.is_valid:
            for error in result.errors:
                print(f"Error: {error}")

        # Check module state
        if clinical_image.has_temporal_info:
            print("Image includes temporal information")
        if clinical_image.has_patient_orientation:
            print("Image includes patient orientation")
    """

    @classmethod
    def from_required_elements(
        cls,
        instance_number: str | int = ""
    ) -> 'GeneralImageModule':
        """Create GeneralImageModule from all required (Type 1 and Type 2) data elements.

        Args:
            instance_number (str | int): Number that identifies this image (0020,0013) Type 2.
                A number that identifies this image. Previously named Image Number.
                Required but may be empty string.

        Returns:
            GeneralImageModule: New module instance with required data elements set
        """
        instance = cls()
        instance['InstanceNumber'] = instance_number
        return instance
    
    def with_optional_elements(
        self,
        image_type: list[str] | list[ImageType] | None = None,
        image_comments: str | None = None,
        quality_control_image: str | QualityControlImage | None = None,
        burned_in_annotation: str | BurnedInAnnotation | None = None,
        recognizable_visual_features: str | RecognizableVisualFeatures | None = None,
        lossy_image_compression: str | LossyImageCompression | None = None,
        lossy_image_compression_ratio: list[float] | None = None,
        lossy_image_compression_method: list[str] | list[LossyImageCompressionMethod] | str | LossyImageCompressionMethod | None = None,
        icon_image_sequence: list[Dataset] | None = None,
        presentation_lut_shape: str | PresentationLUTShape | None = None,
        real_world_value_mapping_sequence: list[Dataset] | None = None,
        image_laterality: str | ImageLaterality | None = None
    ) -> 'GeneralImageModule':
        """Add optional (Type 3) data elements.
        
        Args:
            image_type (list[str | ImageType] | None): Image identification characteristics (0008,0008) Type 3.
                Multi-valued: [pixel_data_char, patient_exam_char, modality_specific, ...]
                Use ImageType enum for standardized values: ImageType.ORIGINAL, ImageType.PRIMARY, ImageType.DOSE, etc.
                String values are also accepted for compatibility.
            image_comments (str | None): User-defined comments about the image (0020,4000) Type 3
            quality_control_image (str | QualityControlImage | None): Quality control material presence (0028,0300) Type 3
            burned_in_annotation (str | BurnedInAnnotation | None): Burned in annotation presence (0028,0301) Type 3
            recognizable_visual_features (str | RecognizableVisualFeatures | None): Visual features presence (0028,0302) Type 3
            lossy_image_compression (str | LossyImageCompression | None): Lossy compression flag (0028,2110) Type 3
            lossy_image_compression_ratio (list[float] | None): Compression ratio values (0028,2112) Type 3
            lossy_image_compression_method (list[str] | list[LossyImageCompressionMethod] | None): Compression methods (0028,2114) Type 3
            icon_image_sequence (list[Dataset] | None): Representative icon image (0088,0200) Type 3
            presentation_lut_shape (str | PresentationLUTShape | None): Presentation LUT transformation (2050,0020) Type 3
            real_world_value_mapping_sequence (list[Dataset] | None): Real world value mapping (0040,9096) Type 3
            image_laterality (str | ImageLaterality | None): Laterality of body part (0020,0062) Type 3
            
        Returns:
            GeneralImageModule: Self with optional elements added
        """
        if image_type is not None:
            # Convert enum values to strings while preserving list structure
            formatted_image_type = [format_enum_string(item) for item in image_type]
            self.ImageType = formatted_image_type
        if image_comments is not None:
            self.ImageComments = image_comments
        if quality_control_image is not None:
            self.QualityControlImage = format_enum_string(quality_control_image)
        if burned_in_annotation is not None:
            self.BurnedInAnnotation = format_enum_string(burned_in_annotation)
        if recognizable_visual_features is not None:
            self.RecognizableVisualFeatures = format_enum_string(recognizable_visual_features)
        if lossy_image_compression is not None:
            self.LossyImageCompression = format_enum_string(lossy_image_compression)
        if lossy_image_compression_ratio is not None:
            self.LossyImageCompressionRatio = lossy_image_compression_ratio
        if lossy_image_compression_method is not None:
            if isinstance(lossy_image_compression_method, list):
                self.LossyImageCompressionMethod = [format_enum_string(method) for method in lossy_image_compression_method]
            else:
                self.LossyImageCompressionMethod = [format_enum_string(lossy_image_compression_method)]
        if icon_image_sequence is not None:
            self.IconImageSequence = icon_image_sequence
        if presentation_lut_shape is not None:
            self.PresentationLUTShape = format_enum_string(presentation_lut_shape)
        if real_world_value_mapping_sequence is not None:
            self.RealWorldValueMappingSequence = real_world_value_mapping_sequence
        if image_laterality is not None:
            self.ImageLaterality = format_enum_string(image_laterality)
        return self

    def with_patient_orientation(
        self,
        patient_orientation: str = ""
    ) -> 'GeneralImageModule':
        """Add patient orientation (Type 2C - conditional requirement).

        Patient Orientation is Type 2C - required if image does not require
        Image Orientation (Patient) (0020,0037) and Image Position (Patient) (0020,0032)
        or if image does not require Image Orientation (Slide) (0048,0102).

        Per DICOM PS3.3 C.*******.1, patient orientation specifies the anatomical
        direction of the positive row axis (left to right) and positive column axis
        (top to bottom) of the image.

        Args:
            patient_orientation (str): Patient direction of rows and columns (0020,0020) Type 2C.
                Format: "row_direction\\column_direction" (e.g., "A\\F" for anterior-foot).
                
                For BIPED anatomical orientation (default):
                - A (anterior), P (posterior), R (right), L (left), H (head), F (foot)
                
                For QUADRUPED anatomical orientation:
                - LE (left), RT (right), D (dorsal), V (ventral), CR (cranial),
                - CD (caudal), R (rostral), M (medial), L (lateral), PR (proximal),
                - DI (distal), PA (palmar), PL (plantar)
                
                Empty string ("") is acceptable for dose grids and other cases where
                spatial orientation is not meaningful.

        Returns:
            GeneralImageModule: Self with patient orientation added
            
        Note:
            This method sets the patient orientation without validation of spatial
            consistency. The validator will check conditional requirements based on
            the presence of other spatial orientation attributes in the complete dataset.
        """
        self.PatientOrientation = patient_orientation
        return self

    def with_temporal_elements(
        self,
        content_date: str | datetime | date | None = None,
        content_time: str | datetime | None = None
    ) -> 'GeneralImageModule':
        """Add temporal elements for temporally related series.

        Content Date and Content Time are Type 2C - required if image is part
        of a Series in which the images are temporally related. Both should be
        provided together to maintain temporal consistency.

        Args:
            content_date (str | datetime | date | None): Date image pixel data creation started (0008,0023) Type 2C.
                DICOM DA format (YYYYMMDD). Previously known as Image Date.
            content_time (str | datetime | None): Time image pixel data creation started (0008,0033) Type 2C.
                DICOM TM format (HHMMSS.FFFFFF). Can include fractional seconds.

        Returns:
            GeneralImageModule: Self with temporal elements added
            
        Examples:
            # Both date and time provided (recommended)
            module.with_temporal_elements(
                content_date="20240315",
                content_time="143022.123"
            )
            
            # Using datetime objects
            now = datetime.now()
            module.with_temporal_elements(
                content_date=now.date(),
                content_time=now
            )
        """
        if content_date is not None:
            self.ContentDate = format_date_value(content_date)
        if content_time is not None:
            self.ContentTime = format_time_value(content_time)
        return self
    
    @staticmethod
    def create_icon_image_item(
        rows: int,
        columns: int,
        samples_per_pixel: int = 1,
        photometric_interpretation: str | PhotometricInterpretation = PhotometricInterpretation.MONOCHROME2,
        bits_allocated: int = 8,
        bits_stored: int = 8,
        high_bit: int = 7,
        pixel_representation: int = 0,
        pixel_data: bytes | None = None
    ) -> Dataset:
        """Create icon image sequence item.
        
        Args:
            rows (int): Number of rows in icon
            columns (int): Number of columns in icon
            samples_per_pixel (int): Number of samples per pixel (default: 1)
            photometric_interpretation (str | PhotometricInterpretation): Photometric interpretation (default: PhotometricInterpretation.MONOCHROME2).
                Use PhotometricInterpretation enum for type safety: PhotometricInterpretation.MONOCHROME2, PhotometricInterpretation.RGB, etc.
            bits_allocated (int): Bits allocated per sample (default: 8)
            bits_stored (int): Bits stored per sample (default: 8)
            high_bit (int): High bit position (default: 7)
            pixel_representation (int): Pixel representation (default: 0)
            pixel_data (bytes | None): Icon pixel data
            
        Returns:
            Dataset: Icon image sequence item
        """
        item = Dataset()
        item.Rows = rows
        item.Columns = columns
        item.SamplesPerPixel = samples_per_pixel
        item.PhotometricInterpretation = photometric_interpretation
        item.BitsAllocated = bits_allocated
        item.BitsStored = bits_stored
        item.HighBit = high_bit
        item.PixelRepresentation = pixel_representation
        
        if pixel_data is not None:
            item.PixelData = pixel_data
        
        return item

    @staticmethod
    def create_real_world_value_mapping_item(
        real_world_value_mapping_sequence: list[Dataset] | None = None,
        real_world_value_first_value_mapped: float | None = None,
        real_world_value_last_value_mapped: float | None = None,
        real_world_value_lut_data: list[float] | None = None
    ) -> Dataset:
        """Create Real World Value Mapping Sequence item.

        Args:
            real_world_value_mapping_sequence (list[Dataset] | None): Nested mapping sequence
            real_world_value_first_value_mapped (float | None): First mapped value
            real_world_value_last_value_mapped (float | None): Last mapped value
            real_world_value_lut_data (list[float] | None): LUT data values

        Returns:
            Dataset: Real World Value Mapping Sequence item
        """
        item = Dataset()

        if real_world_value_mapping_sequence is not None:
            item.RealWorldValueMappingSequence = real_world_value_mapping_sequence
        if real_world_value_first_value_mapped is not None:
            item.RealWorldValueFirstValueMapped = real_world_value_first_value_mapped
        if real_world_value_last_value_mapped is not None:
            item.RealWorldValueLastValueMapped = real_world_value_last_value_mapped
        if real_world_value_lut_data is not None:
            item.RealWorldValueLUTData = real_world_value_lut_data

        return item

    @property
    def is_quality_control(self) -> bool:
        """Check if this is a quality control image.

        Returns:
            bool: True if quality control image indicator is YES or BOTH
        """
        return 'QualityControlImage' in self and self.QualityControlImage in ["YES", "BOTH"]

    @property
    def has_burned_in_annotation(self) -> bool:
        """Check if image has burned in annotation.

        Returns:
            bool: True if burned in annotation indicator is YES
        """
        return 'BurnedInAnnotation' in self and self.BurnedInAnnotation == "YES"

    @property
    def has_recognizable_features(self) -> bool:
        """Check if image has recognizable visual features.

        Returns:
            bool: True if recognizable visual features indicator is YES
        """
        return 'RecognizableVisualFeatures' in self and self.RecognizableVisualFeatures == "YES"

    @property
    def is_lossy_compressed(self) -> bool:
        """Check if image has undergone lossy compression.

        Returns:
            bool: True if lossy image compression indicator is 01
        """
        return 'LossyImageCompression' in self and self.LossyImageCompression == "01"

    @property
    def has_temporal_info(self) -> bool:
        """Check if temporal information is present.

        Returns:
            bool: True if both content date and time are present (recommended for consistency)
        """
        return ('ContentDate' in self and 'ContentTime' in self)
    
    @property
    def has_complete_temporal_info(self) -> bool:
        """Check if complete temporal information is present.
        
        Alias for has_temporal_info for clarity.
        
        Returns:
            bool: True if both content date and time are present
        """
        return self.has_temporal_info
    
    @property
    def has_partial_temporal_info(self) -> bool:
        """Check if partial temporal information is present.
        
        This indicates an inconsistent state that should be avoided.
        
        Returns:
            bool: True if only content date or time is present (inconsistent state)
        """
        has_date = 'ContentDate' in self
        has_time = 'ContentTime' in self
        return (has_date or has_time) and not (has_date and has_time)

    @property
    def has_patient_orientation(self) -> bool:
        """Check if patient orientation is present.

        Returns:
            bool: True if patient orientation is present and not empty
        """
        return 'PatientOrientation' in self and self.PatientOrientation != ""

    @property
    def has_icon_image(self) -> bool:
        """Check if icon image sequence is present.

        Returns:
            bool: True if icon image sequence is present and not empty
        """
        return 'IconImageSequence' in self and len(self.IconImageSequence) > 0
    
    @property
    def has_real_world_value_mapping(self) -> bool:
        """Check if real world value mapping sequence is present.
        
        Returns:
            bool: True if real world value mapping sequence is present and not empty
        """
        return ('RealWorldValueMappingSequence' in self and 
                len(self.RealWorldValueMappingSequence) > 0)
    
    @property
    def image_type_summary(self) -> str:
        """Get a human-readable summary of the image type.
        
        Returns:
            str: Summary of image type or 'Not specified' if not present
        """
        if 'ImageType' not in self:
            return "Not specified"
        
        image_type = self.ImageType
        if isinstance(image_type, str):
            return image_type
        elif hasattr(image_type, '__iter__') and len(image_type) >= 2:
            return f"{image_type[0]} {image_type[1]}"
        elif hasattr(image_type, '__iter__') and len(image_type) == 1:
            return image_type[0]
        else:
            return str(image_type)
    
    @property
    def is_temporally_related(self) -> bool:
        """Check if this image is part of a temporally related series.
        
        Based on the presence of Content Date and Time elements.
        
        Returns:
            bool: True if temporal information suggests temporal relationship
        """
        return self.has_complete_temporal_info

    @staticmethod
    def create_image_type(
        pixel_data_characteristic: str | ImageType,
        patient_exam_characteristic: str | ImageType,
        *modality_specific: str | ImageType
    ) -> list[str]:
        """Create a properly structured ImageType list.

        Helper method to create ImageType values following DICOM PS3.3 C.*******.2 structure.

        Args:
            pixel_data_characteristic (str | ImageType): Value 1 - Pixel Data Characteristics.
                Use ImageType.ORIGINAL or ImageType.DERIVED
            patient_exam_characteristic (str | ImageType): Value 2 - Patient Examination Characteristics.
                Use ImageType.PRIMARY or ImageType.SECONDARY
            *modality_specific (str | ImageType): Value 3+ - Modality/Implementation Specific.
                Use ImageType.DOSE, ImageType.AXIAL, ImageType.LOCALIZER, etc.

        Returns:
            list[str]: Properly formatted ImageType list for DICOM

        Examples:
            # RT Dose image
            dose_type = GeneralImageModule.create_image_type(
                ImageType.DERIVED, ImageType.SECONDARY, ImageType.DOSE
            )

            # Original CT axial image
            ct_type = GeneralImageModule.create_image_type(
                ImageType.ORIGINAL, ImageType.PRIMARY, ImageType.AXIAL
            )

            # Reformatted MPR image
            mpr_type = GeneralImageModule.create_image_type(
                ImageType.DERIVED, ImageType.SECONDARY, ImageType.REFORMATTED, ImageType.MPR
            )
        """
        image_type_list = [
            format_enum_string(pixel_data_characteristic),
            format_enum_string(patient_exam_characteristic)
        ]

        # Add modality-specific values
        for value in modality_specific:
            image_type_list.append(format_enum_string(value))

        return image_type_list

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this General Image Module instance.

        Performs comprehensive validation including:
        - Type 2 required element presence (Instance Number)
        - Type 2C conditional requirements (Patient Orientation, Content Date/Time)
        - Type 3 optional element format and enumerated values
        - Sequence structure validation (Icon Image, Real World Value Mapping)
        - Cross-field consistency (lossy compression attributes)
        - DICOM standard compliance (PS3.3 C.7.6.1)

        Args:
            config: Optional validation configuration to control validation scope

        Returns:
            ValidationResult: Structured validation result with 'errors' and 'warnings' lists.
                             Each message includes DICOM tag references and standard citations.
        """
        return GeneralImageValidator.validate(self._dataset, config)
