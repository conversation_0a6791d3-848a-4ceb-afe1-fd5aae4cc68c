"""
Device Module - DICOM PS3.3 C.7.6.12

The Device Module identifies and describes devices or calibration objects 
(e.g., catheters, markers, baskets) or other quality control materials 
that are associated with a Study and/or image.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.equipment_enums import DeviceDiameterUnits, Manufacturer
from ..validators.modules.device_validator import DeviceValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_enum_string


class DeviceModule(BaseModule):
    """Device Module implementation for DICOM PS3.3 C.7.6.12.
    
    Uses composition with internal dataset management rather than inheriting
    from pydicom.Dataset for cleaner separation of concerns.
    Identifies and describes devices or calibration objects that are associated 
    with a Study and/or image.
    
    Usage:
        # Create with device sequence including optional details
        device = DeviceModule.from_required_elements(
            device_sequence=[
                DeviceModule.create_device_item(
                    code_value="A-04000",
                    coding_scheme_designator="SRT", 
                    code_meaning="Catheter",
                    manufacturer=Manufacturer.SIEMENS,
                    device_diameter=2.5,
                    device_diameter_units=DeviceDiameterUnits.MM,
                    device_description="Diagnostic catheter"
                )
            ]
        )
        
        # Validate
        result = device.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        device_sequence: list[Dataset]
    ) -> 'DeviceModule':
        """Create DeviceModule from all required (Type 1) data elements.
        
        Args:
            device_sequence (list[Dataset]): Sequence of devices used (0050,0010) Type 1.
                Each item must include Code Sequence Macro attributes (CodeValue, 
                CodingSchemeDesignator, CodeMeaning). Use create_device_item() to
                create properly formatted device sequence items.
                
        Returns:
            DeviceModule: New module instance with required data elements set
        """
        instance = cls()
        instance['DeviceSequence'] = device_sequence
        return instance
    
    
    def with_optional_elements(self) -> 'DeviceModule':
        """No optional elements at module level - all device details are specified in sequence items.
        
        Use create_device_item() to create device sequence items with optional attributes
        like manufacturer, device dimensions, serial numbers, etc.
        
        Returns:
            DeviceModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_device_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        manufacturer: str | Manufacturer | None = None,
        manufacturers_model_name: str | None = None,
        device_serial_number: str | None = None,
        device_id: str | None = None,
        device_length: float | None = None,
        device_diameter: float | None = None,
        device_diameter_units: str | DeviceDiameterUnits | None = None,
        device_volume: float | None = None,
        inter_marker_distance: float | None = None,
        device_description: str | None = None,
        date_of_manufacture: str | datetime | date | None = None
    ) -> Dataset:
        """Create device sequence item with Code Sequence Macro and optional device details.
        
        This is the primary method for creating device items. All device details should
        be specified here rather than added later.

        Args:
            code_value (str): Code value for device type (0008,0100)
            coding_scheme_designator (str): Coding scheme designator (0008,0102) 
            code_meaning (str): Code meaning (0008,0104)
            manufacturer (str | Manufacturer | None): Manufacturer of the device (0008,0070) Type 3.
                Use Manufacturer enum for standardized manufacturer names.
            manufacturers_model_name (str | None): Manufacturer's model name (0008,1090) Type 3
            device_serial_number (str | None): Device serial number (0018,1000) Type 3
            device_id (str | None): User-supplied identifier (0018,1003) Type 3
            device_length (float | None): Length in mm (0050,0014) Type 3
            device_diameter (float | None): Unit diameter (0050,0016) Type 3
            device_diameter_units (str | DeviceDiameterUnits | None): Units for diameter (0050,0017) Type 2C
            device_volume (float | None): Volume in ml (0050,0018) Type 3
            inter_marker_distance (float | None): Distance between markers in mm (0050,0019) Type 3
            device_description (str | None): Free form description (0050,0020) Type 3
            date_of_manufacture (str | datetime | date | None): Date of manufacture (0018,1204) Type 3

        Returns:
            Dataset: Device sequence item
            
        Note:
            Device Diameter Units (0050,0017) is Type 2C - required if Device Diameter 
            (0050,0016) is present. Valid units: FR (French), GA (Gauge), IN (Inch), MM (Millimeter)
        """
        # Note: Validation of conditional requirements (diameter units when diameter present)
        # is handled by DeviceValidator.validate() method

        item = Dataset()
        
        # Code Sequence Macro attributes (required)
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning

        # Optional device attributes (Type 3)
        if manufacturer is not None:
            item.Manufacturer = format_enum_string(manufacturer)
        if manufacturers_model_name is not None:
            item.ManufacturerModelName = manufacturers_model_name
        if device_serial_number is not None:
            item.DeviceSerialNumber = device_serial_number
        if device_id is not None:
            item.DeviceID = device_id
        if device_length is not None:
            item.DeviceLength = str(device_length)
        if device_diameter is not None:
            item.DeviceDiameter = str(device_diameter)
            # Device Diameter Units is Type 2C (required when diameter present) 
            # Only set if provided - validation handled by DeviceValidator
            if device_diameter_units is not None:
                item.DeviceDiameterUnits = format_enum_string(device_diameter_units)
        if device_volume is not None:
            item.DeviceVolume = str(device_volume)
        if inter_marker_distance is not None:
            item.InterMarkerDistance = str(inter_marker_distance)
        if device_description is not None:
            item.DeviceDescription = device_description
        if date_of_manufacture is not None:
            # Format date to DICOM DA format (YYYYMMDD)
            item.DateOfManufacture = format_date_value(date_of_manufacture)

        return item

    @staticmethod
    def create_catheter_device(
        manufacturer: str | Manufacturer = Manufacturer.SIEMENS,
        diameter: float = 2.5,
        diameter_units: DeviceDiameterUnits = DeviceDiameterUnits.MM,
        description: str = "Diagnostic catheter"
    ) -> Dataset:
        """Create a standardized catheter device item.

        Args:
            manufacturer: Device manufacturer (default: Siemens)
            diameter: Catheter diameter (default: 2.5)
            diameter_units: Diameter units (default: MM)
            description: Device description (default: "Diagnostic catheter")

        Returns:
            Dataset: Device sequence item for catheter
        """
        return DeviceModule.create_device_item(
            code_value="A-04000",
            coding_scheme_designator="SRT",
            code_meaning="Catheter",
            manufacturer=manufacturer,
            device_diameter=diameter,
            device_diameter_units=diameter_units,
            device_description=description
        )

    @staticmethod
    def create_marker_device(
        manufacturer: str | Manufacturer = Manufacturer.VARIAN,
        description: str = "Fiducial marker"
    ) -> Dataset:
        """Create a standardized fiducial marker device item.

        Args:
            manufacturer: Device manufacturer (default: Varian)
            description: Device description (default: "Fiducial marker")

        Returns:
            Dataset: Device sequence item for fiducial marker
        """
        return DeviceModule.create_device_item(
            code_value="A-25000",
            coding_scheme_designator="SRT",
            code_meaning="Marker",
            manufacturer=manufacturer,
            device_description=description
        )

    @staticmethod
    def create_calibration_object(
        manufacturer: str | Manufacturer = Manufacturer.PHILIPS,
        description: str = "Quality control phantom"
    ) -> Dataset:
        """Create a standardized calibration object device item.

        Args:
            manufacturer: Device manufacturer (default: Philips)
            description: Device description (default: "Quality control phantom")

        Returns:
            Dataset: Device sequence item for calibration object
        """
        return DeviceModule.create_device_item(
            code_value="A-26000",
            coding_scheme_designator="SRT",
            code_meaning="Calibration object",
            manufacturer=manufacturer,
            device_description=description
        )

    @property
    def device_count(self) -> int:
        """Get the number of devices in the sequence.
        
        Returns:
            int: Number of device items
        """
        if 'DeviceSequence' not in self:
            return 0
        
        return len(self.DeviceSequence)
    
    @property
    def has_device_measurements(self) -> bool:
        """Check if any device has measurement data.
        
        Returns:
            bool: True if any device has length, diameter, or volume data
        """
        if 'DeviceSequence' not in self:
            return False
        
        for device in self.DeviceSequence:
            if any(key in device for key in ['DeviceLength', 'DeviceDiameter', 'DeviceVolume', 'InterMarkerDistance']):
                return True
        
        return False
    
    # Public validation convenience methods with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return DeviceValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()

    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return DeviceValidator.validate_conditional_requirements(self)

    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return DeviceValidator.validate_enumerated_values(self)

    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return DeviceValidator.validate_sequence_structures(self)

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return DeviceValidator.validate(self, config)  # Direct self reference for performance

    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.

        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            DeviceValidator.validate_required_elements,
            "Required elements validation failed"
        )

    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.

        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            DeviceValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )

    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.

        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            DeviceValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )

    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence requirements are valid, raise exception if not.

        Raises:
            ValidationError: If sequence requirements are invalid
        """
        self._validate_and_raise(
            DeviceValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )
