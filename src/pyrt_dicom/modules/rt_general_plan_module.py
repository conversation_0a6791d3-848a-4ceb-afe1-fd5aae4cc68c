"""RT General Plan Module - DICOM PS3.3 C.8.8.9"""
from datetime import datetime, date
from .base_module import BaseModule
from ..enums.rt_enums import RTPlanStatus, PlanIntent, RTPlanGeometry, RTPlanRelationship
from ..validators.modules.rt_general_plan_validator import RTGeneralPlanValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from pydicom import Dataset
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class RTGeneralPlanModule(BaseModule):
    """RT General Plan Module implementation for DICOM PS3.3 C.8.8.9.

    Uses composition with internal dataset management rather than
    inheriting from pydicom.Dataset for cleaner separation of concerns.
    Contains general information about the RT Plan.

    This module implements all conditional logic requirements from DICOM PS3.3 C.8.8.9:
    - Referenced Structure Set Sequence (Type 1C): Required when RT Plan Geometry is PATIENT
    - RT Plan Relationship validation for Referenced RT Plan Sequence items
    - Treatment Site Code Sequence structure validation
    - RT Assertions Sequence basic structure validation

    Usage:
        # Create with required elements
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Treatment Plan 1",
            rt_plan_date="20240101",
            rt_plan_time="120000",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )

        # Add optional elements
        plan.with_optional_elements(
            rt_plan_name="Primary Treatment Plan",
            rt_plan_description="Curative treatment for lung cancer",
            rt_plan_status=RTPlanStatus.CLINICAL,
            instance_number="1",
            treatment_protocols="RTOG 0617",
            plan_intent=PlanIntent.CURATIVE,
            treatment_site="Lung"
        )

        # Add conditional structure set reference if geometry is PATIENT
        plan.with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )

        # Generate dataset for IOD integration
        dataset = plan.to_dataset()

        # Validate
        result = plan.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        rt_plan_label: str,
        rt_plan_date: str | datetime | date = "",
        rt_plan_time: str | datetime = "",
        rt_plan_geometry: str | RTPlanGeometry = ""
    ) -> 'RTGeneralPlanModule':
        """Create RTGeneralPlanModule from all required (Type 1 and Type 2) data elements.

        Args:
            rt_plan_label: User-defined label for treatment plan (300A,0002) Type 1
            rt_plan_date: Date treatment plan was last modified (300A,0006) Type 2
            rt_plan_time: Time treatment plan was last modified (300A,0007) Type 2
            rt_plan_geometry: Whether RT Plan is based on patient or treatment device geometry (300A,000C) Type 1

        Returns:
            RTGeneralPlanModule: New module instance with required data elements set
        """
        instance = cls()
        instance['RTPlanLabel'] = rt_plan_label
        instance['RTPlanDate'] = format_date_value(rt_plan_date)
        instance['RTPlanTime'] = format_time_value(rt_plan_time)
        instance['RTPlanGeometry'] = format_enum_string(rt_plan_geometry)
        return instance
    
    def with_optional_elements(
        self,
        rt_plan_name: str | None = None,
        rt_plan_description: str | None = None,
        rt_plan_status: str | RTPlanStatus | None = None,
        instance_number: str | None = None,
        treatment_protocols: str | None = None,
        plan_intent: str | PlanIntent | None = None,
        treatment_site: str | None = None,
        treatment_site_code_sequence: list[Dataset] | None = None,
        referenced_dose_sequence: list[Dataset] | None = None,
        referenced_rt_plan_sequence: list[Dataset] | None = None,
        frame_of_reference_to_displayed_coordinate_system_transformation_matrix: list[float] | None = None,
        rt_assertions_sequence: list[Dataset] | None = None
    ) -> 'RTGeneralPlanModule':
        """Add optional (Type 3) elements.

        Args:
            rt_plan_name: User-defined name for treatment plan (300A,0003) Type 3
            rt_plan_description: User-defined description of treatment plan (300A,0004) Type 3
            rt_plan_status: Plan approval status (300A,0002) Type 3
            instance_number: A number that identifies this object Instance (0020,0013) Type 3
            treatment_protocols: Planned treatment protocols (300A,0009) Type 3
            plan_intent: Intent of this plan (300A,000A) Type 3
            treatment_site: Free-text label describing anatomical treatment site (3010,0077) Type 3
            treatment_site_code_sequence: Coded description of treatment site (3010,0078) Type 3
            referenced_dose_sequence: Related Instances of RT Dose (300C,0080) Type 3
            referenced_rt_plan_sequence: Related Instances of RT Plan (300C,0002) Type 3
            frame_of_reference_to_displayed_coordinate_system_transformation_matrix: 4x4 transformation matrix (0070,030B) Type 3
            rt_assertions_sequence: Assertions made for this instance (0044,0110) Type 3

        Returns:
            RTGeneralPlanModule: Self for method chaining
        """
        if rt_plan_name is not None:
            self.RTPlanName = rt_plan_name
        if rt_plan_description is not None:
            self.RTPlanDescription = rt_plan_description
        if rt_plan_status is not None:
            self.RTPlanStatus = format_enum_string(rt_plan_status)
        if instance_number is not None:
            self.InstanceNumber = instance_number
        if treatment_protocols is not None:
            self.TreatmentProtocols = treatment_protocols
        if plan_intent is not None:
            self.PlanIntent = format_enum_string(plan_intent)
        if treatment_site is not None:
            self.TreatmentSite = treatment_site
        if treatment_site_code_sequence is not None:
            self.TreatmentSiteCodeSequence = treatment_site_code_sequence
        if referenced_dose_sequence is not None:
            self.ReferencedDoseSequence = referenced_dose_sequence
        if referenced_rt_plan_sequence is not None:
            self.ReferencedRTPlanSequence = referenced_rt_plan_sequence
        if frame_of_reference_to_displayed_coordinate_system_transformation_matrix is not None:
            self.FrameOfReferenceToDisplayedCoordinateSystemTransformationMatrix = frame_of_reference_to_displayed_coordinate_system_transformation_matrix
        if rt_assertions_sequence is not None:
            self.RTAssertionsSequence = rt_assertions_sequence
        return self
    
    def with_structure_set_reference(
        self,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> 'RTGeneralPlanModule':
        """Add referenced structure set (Type 1C - required if RT Plan Geometry is PATIENT, optional otherwise).
        
        Per DICOM standard, only a single structure set reference is allowed.

        Args:
            referenced_sop_class_uid: Referenced SOP Class UID (0008,1150)
            referenced_sop_instance_uid: Referenced SOP Instance UID (0008,1155)

        Returns:
            RTGeneralPlanModule: Self for method chaining
            
        Raises:
            ValueError: If Referenced Structure Set Sequence is already present
        """
        if 'ReferencedStructureSetSequence' in self:
            raise ValueError("Referenced Structure Set Sequence (300C,0060) is already present. DICOM standard allows only a single structure set reference.")
        
        structure_set_item = Dataset()
        structure_set_item.ReferencedSOPClassUID = referenced_sop_class_uid
        structure_set_item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        self.ReferencedStructureSetSequence = [structure_set_item]
        return self
    
    @staticmethod
    def create_treatment_site_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None,
        treatment_site_modifier_code_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create treatment site code sequence item.

        Args:
            code_value: Code value (0008,0100)
            coding_scheme_designator: Coding scheme designator (0008,0102)
            code_meaning: Code meaning (0008,0104)
            coding_scheme_version: Coding scheme version (0008,0103)
            treatment_site_modifier_code_sequence: Treatment site modifier codes (3010,0089)

        Returns:
            Dataset: Treatment site code sequence item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if treatment_site_modifier_code_sequence is not None:
            item.TreatmentSiteModifierCodeSequence = treatment_site_modifier_code_sequence
        return item
    
    @staticmethod
    def create_referenced_rt_plan_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        rt_plan_relationship: str | RTPlanRelationship
    ) -> Dataset:
        """Create referenced RT plan sequence item.

        Args:
            referenced_sop_class_uid: Referenced SOP Class UID (0008,1150)
            referenced_sop_instance_uid: Referenced SOP Instance UID (0008,1155)
            rt_plan_relationship: Relationship of referenced plan (300A,0055)

        Returns:
            Dataset: Referenced RT plan sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        item.RTPlanRelationship = format_enum_string(rt_plan_relationship)
        return item
    
    @property
    def is_patient_based(self) -> bool:
        """Check if plan is based on patient geometry.

        Returns:
            bool: True if RT Plan Geometry is PATIENT
        """
        return 'RTPlanGeometry' in self and self.RTPlanGeometry == "PATIENT"

    @property
    def has_structure_set_reference(self) -> bool:
        """Check if structure set reference is present.

        Returns:
            bool: True if Referenced Structure Set Sequence is present
        """
        return 'ReferencedStructureSetSequence' in self

    @property
    def has_treatment_site_info(self) -> bool:
        """Check if treatment site information is present.

        Returns:
            bool: True if treatment site or treatment site code sequence is present
        """
        return ('TreatmentSite' in self or
                'TreatmentSiteCodeSequence' in self)

    @property
    def is_clinical_plan(self) -> bool:
        """Check if this is a clinical plan.

        Returns:
            bool: True if RT Plan Status is CLINICAL
        """
        return 'RTPlanStatus' in self and self.RTPlanStatus == RTPlanStatus.CLINICAL.value

    @property
    def is_research_plan(self) -> bool:
        """Check if this is a research plan.

        Returns:
            bool: True if RT Plan Status is RESEARCH
        """
        return 'RTPlanStatus' in self and self.RTPlanStatus == RTPlanStatus.RESEARCH.value

    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.

        Returns:
            bool: True if required elements are present
        """
        return ('RTPlanLabel' in self and
                'RTPlanGeometry' in self)

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT General Plan Module instance.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        return RTGeneralPlanValidator.validate(self.to_dataset(), config)
