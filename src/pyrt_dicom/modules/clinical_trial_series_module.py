"""Clinical Trial Series Module - DICOM PS3.3 C.7.3.2

The Clinical Trial Series Module contains attributes that identify a Series 
in the context of a clinical trial or research.
"""
from .base_module import BaseModule
from ..validators.modules.base_validator import ValidationConfig
from ..validators.modules.clinical_trial_series_validator import ClinicalTrialSeriesValidator
from pyrt_dicom.validators import ValidationResult


class ClinicalTrialSeriesModule(BaseModule):
    """Clinical Trial Series Module implementation for DICOM PS3.3 C.7.3.2.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that identify a Series in the context of a clinical trial or research.
    
    Usage:
        # Create with required elements (Type 2: coordinating center name required but can be empty)
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"  # Type 2, or "" if unknown
        )
        
        # Add optional elements
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="ISSUER001", 
            clinical_trial_series_description="Baseline CT imaging"
        )
        
        # Validate
        result = trial_series.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        clinical_trial_coordinating_center_name: str = ""
    ) -> 'ClinicalTrialSeriesModule':
        """Create ClinicalTrialSeriesModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            clinical_trial_coordinating_center_name (str): Name of the institution responsible for 
                coordinating the medical imaging data for the clinical trial or research (0012,0060) Type 2
        
        Returns:
            ClinicalTrialSeriesModule: New dataset instance with required elements
        """
        instance = cls()
        instance.ClinicalTrialCoordinatingCenterName = clinical_trial_coordinating_center_name
        return instance
    
    def with_optional_elements(
        self,
        clinical_trial_series_id: str | None = None,
        issuer_of_clinical_trial_series_id: str | None = None,
        clinical_trial_series_description: str | None = None
    ) -> 'ClinicalTrialSeriesModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            clinical_trial_series_id (str | None): Identifier for the Series (0012,0071) Type 3
            issuer_of_clinical_trial_series_id (str | None): Assigning Authority for series ID (0012,0073) Type 3
            clinical_trial_series_description (str | None): Description of the Series (0012,0072) Type 3
            
        Returns:
            ClinicalTrialSeriesModule: Self with optional elements added
        """
        if clinical_trial_series_id is not None:
            self.ClinicalTrialSeriesID = clinical_trial_series_id
        if issuer_of_clinical_trial_series_id is not None:
            self.IssuerOfClinicalTrialSeriesID = issuer_of_clinical_trial_series_id
        if clinical_trial_series_description is not None:
            self.ClinicalTrialSeriesDescription = clinical_trial_series_description
        return self
    
    @property
    def has_coordinating_center_info(self) -> bool:
        """Check if coordinating center information is present and non-empty.
        
        Returns:
            bool: True if coordinating center name is present and has a value
        """
        return 'ClinicalTrialCoordinatingCenterName' in self and bool(self.ClinicalTrialCoordinatingCenterName)
    
    @property
    def has_series_identification(self) -> bool:
        """Check if series identification information is present.
        
        Returns:
            bool: True if series ID or description is present
        """
        return any(attr in self for attr in [
            'ClinicalTrialSeriesID', 'ClinicalTrialSeriesDescription'
        ])
    
    @property
    def has_issuer_info(self) -> bool:
        """Check if issuer information is present.
        
        Returns:
            bool: True if issuer of series ID is present
        """
        return 'IssuerOfClinicalTrialSeriesID' in self
    
    # Public convenience methods for specific validation checks with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return ClinicalTrialSeriesValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()
    
    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return ClinicalTrialSeriesValidator.validate_conditional_requirements(self)
    
    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return ClinicalTrialSeriesValidator.validate_enumerated_values(self)
    
    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return ClinicalTrialSeriesValidator.validate_sequence_structures(self)
    
    def check_vr_constraints(self) -> ValidationResult:
        """Check VR (Value Representation) constraints with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for VR constraint violations
        """
        return ClinicalTrialSeriesValidator.validate_vr_constraints(self)
    
    def check_semantic_relationships(self) -> ValidationResult:
        """Check semantic relationships between elements with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with warnings for semantic issues
        """
        return ClinicalTrialSeriesValidator.validate_semantic_relationships(self)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.
        
        Args:
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return ClinicalTrialSeriesValidator.validate(self, config)  # Direct self reference for performance
    
    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.
        
        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            ClinicalTrialSeriesValidator.validate_required_elements,
            "Required elements validation failed"
        )
    
    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            ClinicalTrialSeriesValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )
    
    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.
        
        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            ClinicalTrialSeriesValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )
    
    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If sequence requirements are invalid
        """
        self._validate_and_raise(
            ClinicalTrialSeriesValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )
    
    def _ensure_vr_constraints_valid(self) -> None:
        """Ensure VR constraints are valid, raise exception if not.
        
        Raises:
            ValidationError: If VR constraints are invalid
        """
        self._validate_and_raise(
            ClinicalTrialSeriesValidator.validate_vr_constraints,
            "VR constraint validation failed"
        )
    
    def _ensure_semantic_relationships_valid(self) -> None:
        """Ensure semantic relationships are valid, raise exception if not.
        
        Raises:
            ValidationError: If semantic relationships have issues (warnings promoted to errors)
        """
        result = ClinicalTrialSeriesValidator.validate_semantic_relationships(self)
        if result.has_errors or result.has_warnings:  # Treat warnings as errors in strict validation
            from pyrt_dicom.validators.validation_error import ValidationError
            issues = result.errors + result.warnings
            raise ValidationError(f"Semantic relationship validation failed: {'; '.join(issues)}")
