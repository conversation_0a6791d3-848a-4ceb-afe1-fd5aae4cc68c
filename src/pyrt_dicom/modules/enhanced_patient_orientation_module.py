"""Enhanced Patient Orientation Module - DICOM PS3.3 C.7.6.30

The Enhanced Patient Orientation Module describes the patient orientation
with respect to gravity and equipment using three sequence attributes from
the Patient Orientation and Equipment Relationship Macro.
"""
from typing import Union
from .base_module import BaseModule
from ..enums.patient_enums import (
    PatientOrientationCode,
    PatientOrientationModifierCode,
    PatientEquipmentRelationshipCode
)
from ..validators.modules.enhanced_patient_orientation_validator import EnhancedPatientOrientationValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..validators.validation_error import ValidationError
from ..utils.dicom_formatters import format_enum_string
from pydicom import Dataset


class EnhancedPatientOrientationModule(BaseModule):
    """Enhanced Patient Orientation Module implementation for DICOM PS3.3 C.7.6.30.

    Uses composition with internal dataset management rather than inheriting from
    pydicom.Dataset for cleaner separation of concerns. Describes the patient
    orientation with respect to gravity and to the equipment using three Attributes
    invoked from the Patient Orientation and Equipment Relationship Macro Attributes.

    This module includes "Table 10-15a Patient Orientation and Equipment Relationship
    Macro Attributes" as specified in DICOM PS3.3. The macro inclusion provides
    additional attributes beyond the three core sequences and is planned for future
    implementation.

    Usage:
        # Create with required elements using standardized orientation enums
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientOrientationCode.RECUMBENT,
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientOrientationModifierCode.SUPINE,
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientEquipmentRelationshipCode.HEAD_FIRST,
                    coding_scheme_designator="SCT",
                    code_meaning="head first"
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = orientation.to_dataset()

        # Validate including cross-sequence consistency
        result = orientation.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        patient_orientation_code_sequence: list[Dataset],
        patient_orientation_modifier_code_sequence: list[Dataset],
        patient_equipment_relationship_code_sequence: list[Dataset]
    ) -> 'EnhancedPatientOrientationModule':
        """Create Enhanced Patient Orientation Module from all required (Type 1) data elements.

        Args:
            patient_orientation_code_sequence (list[Dataset]): Rough orientation of imaged part
                with respect to gravity (0054,0410) Type 1. Describes vertical, horizontal,
                or in-between positioning.
            patient_orientation_modifier_code_sequence (list[Dataset]): Detailed description
                of orientation and positioning (0054,0412) Type 1. Provides more specific
                positioning information.
            patient_equipment_relationship_code_sequence (list[Dataset]): Orientation of patient
                with respect to imaging equipment (3010,0030) Type 1. Describes relationship
                to gantry/equipment.

        Returns:
            EnhancedPatientOrientationModule: New module instance with required data elements set
        """
        instance = cls()
        instance['PatientOrientationCodeSequence'] = patient_orientation_code_sequence
        instance['PatientOrientationModifierCodeSequence'] = patient_orientation_modifier_code_sequence
        instance['PatientEquipmentRelationshipCodeSequence'] = patient_equipment_relationship_code_sequence
        return instance
    
    def with_optional_elements(self, **kwargs) -> 'EnhancedPatientOrientationModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Enhanced Patient Orientation Module has no Type 3 elements defined in DICOM PS3.3 C.7.6.30.
        This method is provided for API consistency but accepts no parameters.
        
        Args:
            **kwargs: No optional elements are supported
            
        Returns:
            EnhancedPatientOrientationModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided
        """
        if kwargs:
            raise ValueError(f"EnhancedPatientOrientationModule has no optional elements. Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    @staticmethod
    def create_code_sequence_item(
        code_value: str | PatientOrientationCode | PatientOrientationModifierCode | PatientEquipmentRelationshipCode,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None,
        context_identifier: str | None = None,
        context_uid: str | None = None,
        mapping_resource: str | None = None,
        context_group_version: str | None = None,
        context_group_extension_flag: str | None = None,
        context_group_local_version: str | None = None,
        context_group_extension_creator_uid: str | None = None
    ) -> Dataset:
        """Create code sequence item for orientation code sequences.

        Args:
            code_value (str | PatientOrientationCode | PatientOrientationModifierCode | PatientEquipmentRelationshipCode):
                Code Value (0008,0100) Type 1. Use orientation enums for standardized SNOMED CT codes.
            coding_scheme_designator (str): Coding Scheme Designator (0008,0102) Type 1
            code_meaning (str): Code Meaning (0008,0104) Type 1
            coding_scheme_version (str | None): Coding Scheme Version (0008,0103) Type 1C
            context_identifier (str | None): Context Identifier (0008,010F) Type 3
            context_uid (str | None): Context UID (0008,0117) Type 3
            mapping_resource (str | None): Mapping Resource (0008,0105) Type 1C
            context_group_version (str | None): Context Group Version (0008,0106) Type 1C
            context_group_extension_flag (str | None): Context Group Extension Flag (0008,010B) Type 3
            context_group_local_version (str | None): Context Group Local Version (0008,0107) Type 1C
            context_group_extension_creator_uid (str | None): Context Group Extension Creator UID (0008,010D) Type 1C
            
        Returns:
            Dataset: Code sequence item with required and optional attributes
        """
        item = Dataset()
        item.CodeValue = format_enum_string(code_value)
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning
        
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if context_identifier is not None:
            item.ContextIdentifier = context_identifier
        if context_uid is not None:
            item.ContextUID = context_uid
        if mapping_resource is not None:
            item.MappingResource = mapping_resource
        if context_group_version is not None:
            item.ContextGroupVersion = context_group_version
        if context_group_extension_flag is not None:
            item.ContextGroupExtensionFlag = context_group_extension_flag
        if context_group_local_version is not None:
            item.ContextGroupLocalVersion = context_group_local_version
        if context_group_extension_creator_uid is not None:
            item.ContextGroupExtensionCreatorUID = context_group_extension_creator_uid
            
        return item

    @staticmethod
    def create_supine_head_first_orientation() -> 'EnhancedPatientOrientationModule':
        """Create common supine head-first orientation configuration.

        Returns:
            EnhancedPatientOrientationModule: Module configured for supine head-first positioning
        """
        return EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientOrientationCode.RECUMBENT,
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientOrientationModifierCode.SUPINE,
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientEquipmentRelationshipCode.HEAD_FIRST,
                    coding_scheme_designator="SCT",
                    code_meaning="head first"
                )
            ]
        )

    @staticmethod
    def create_prone_feet_first_orientation() -> 'EnhancedPatientOrientationModule':
        """Create common prone feet-first orientation configuration.

        Returns:
            EnhancedPatientOrientationModule: Module configured for prone feet-first positioning
        """
        return EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientOrientationCode.RECUMBENT,
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientOrientationModifierCode.PRONE,
                    coding_scheme_designator="SCT",
                    code_meaning="prone"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientEquipmentRelationshipCode.FEET_FIRST,
                    coding_scheme_designator="SCT",
                    code_meaning="feet first"
                )
            ]
        )

    @staticmethod
    def create_lateral_decubitus_orientation(side: str = "left") -> 'EnhancedPatientOrientationModule':
        """Create lateral decubitus orientation configuration.

        Args:
            side (str): Side for decubitus position ("left" or "right")

        Returns:
            EnhancedPatientOrientationModule: Module configured for lateral decubitus positioning
        """
        if side.lower() == "left":
            modifier_code = PatientOrientationModifierCode.LEFT_LATERAL
            code_meaning = "left lateral"
        elif side.lower() == "right":
            modifier_code = PatientOrientationModifierCode.RIGHT_LATERAL
            code_meaning = "right lateral"
        else:
            raise ValueError("Side must be 'left' or 'right'")

        return EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientOrientationCode.LATERAL,
                    coding_scheme_designator="SCT",
                    code_meaning="lateral"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=modifier_code,
                    coding_scheme_designator="SCT",
                    code_meaning=code_meaning
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value=PatientEquipmentRelationshipCode.HEAD_FIRST,
                    coding_scheme_designator="SCT",
                    code_meaning="head first"
                )
            ]
        )

    @property
    def has_orientation_data(self) -> bool:
        """Check if orientation data is present.

        Returns:
            bool: True if all required orientation sequences are present
        """
        return ('PatientOrientationCodeSequence' in self and
                'PatientOrientationModifierCodeSequence' in self and
                'PatientEquipmentRelationshipCodeSequence' in self)

    @property
    def is_recumbent(self) -> bool:
        """Check if patient is in recumbent position.

        Returns:
            bool: True if patient orientation indicates recumbent position
        """
        if 'PatientOrientationCodeSequence' not in self:
            return False

        for item in self.PatientOrientationCodeSequence:
            if (item.get('CodeValue') == '102538003' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False

    @property
    def is_erect(self) -> bool:
        """Check if patient is in erect position.

        Returns:
            bool: True if patient orientation indicates erect position
        """
        if 'PatientOrientationCodeSequence' not in self:
            return False

        for item in self.PatientOrientationCodeSequence:
            if (item.get('CodeValue') == 'C86043' and
                item.get('CodingSchemeDesignator') == 'NCIt'):
                return True
        return False

    @property
    def is_headfirst(self) -> bool:
        """Check if patient is positioned headfirst.

        Returns:
            bool: True if patient equipment relationship indicates headfirst
        """
        if 'PatientEquipmentRelationshipCodeSequence' not in self:
            return False

        for item in self.PatientEquipmentRelationshipCodeSequence:
            if (item.get('CodeValue') == '102540008' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False

    @property
    def is_feetfirst(self) -> bool:
        """Check if patient is positioned feetfirst.

        Returns:
            bool: True if patient equipment relationship indicates feetfirst
        """
        if 'PatientEquipmentRelationshipCodeSequence' not in self:
            return False

        for item in self.PatientEquipmentRelationshipCodeSequence:
            if (item.get('CodeValue') == '102541007' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False
    @property
    def is_supine(self) -> bool:
        """Check if patient is in supine position.

        Returns:
            bool: True if patient orientation modifier indicates supine position
        """
        if 'PatientOrientationModifierCodeSequence' not in self:
            return False

        for item in self.PatientOrientationModifierCodeSequence:
            if (item.get('CodeValue') == '40199007' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False

    @property
    def is_prone(self) -> bool:
        """Check if patient is in prone position.

        Returns:
            bool: True if patient orientation modifier indicates prone position
        """
        if 'PatientOrientationModifierCodeSequence' not in self:
            return False

        for item in self.PatientOrientationModifierCodeSequence:
            if (item.get('CodeValue') == '1240000' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False
        
    @property
    def is_semi_erect(self) -> bool:
        """Check if patient is in semi-erect position.

        Returns:
            bool: True if patient orientation indicates semi-erect position
        """
        if 'PatientOrientationCodeSequence' not in self:
            return False

        for item in self.PatientOrientationCodeSequence:
            if (item.get('CodeValue') == '102539006' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False
    
    @property
    def is_consistent_combination(self) -> bool:
        """Check if orientation, modifier, and equipment sequences are logically consistent.

        Returns:
            bool: True if the combination of orientation, modifier, and equipment
                  relationship codes represents a valid logical combination
        """
        if not self.has_orientation_data:
            return False

        # Get primary orientation codes
        orientation_codes = [item.get('CodeValue', '') for item in self.PatientOrientationCodeSequence]
        modifier_codes = [item.get('CodeValue', '') for item in self.PatientOrientationModifierCodeSequence]

        # Check for mutually exclusive orientations
        if ('102538003' in orientation_codes and 'C86043' in orientation_codes):  # recumbent and erect
            return False
        if ('102538003' in orientation_codes and '102539006' in orientation_codes):  # recumbent and semi-erect
            return False
        if ('C86043' in orientation_codes and '102539006' in orientation_codes):  # erect and semi-erect
            return False

        # Check orientation-modifier compatibility
        if '102538003' in orientation_codes:  # recumbent
            # Should have supine, prone, or similar recumbent modifiers
            valid_modifiers = ['40199007', '1240000']  # supine, prone
            if not any(code in valid_modifiers for code in modifier_codes):
                return False

        if 'C86043' in orientation_codes:  # erect
            # Should have standing, sitting, or similar erect modifiers
            valid_modifiers = ['10904000', '33586001']  # standing, sitting
            if not any(code in valid_modifiers for code in modifier_codes):
                return False

        if '102539006' in orientation_codes:  # semi-erect
            # Semi-erect typically pairs with sitting or other intermediate positions
            valid_modifiers = ['33586001']  # sitting is most common for semi-erect
            if not any(code in valid_modifiers for code in modifier_codes):
                # This is more of a warning than a hard failure for semi-erect
                pass

        return True

    def _get_sequence_codes(self, sequence_attr: str) -> list[str]:
        """Helper method to extract code values from a sequence attribute.

        Args:
            sequence_attr (str): Name of the sequence attribute

        Returns:
            list[str]: List of code values from the sequence
        """
        if sequence_attr not in self or not self[sequence_attr]:
            return []

        return [item.get('CodeValue', '') for item in self[sequence_attr] if item.get('CodeValue')]

    # Public validation convenience methods with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return EnhancedPatientOrientationValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()

    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return EnhancedPatientOrientationValidator.validate_conditional_requirements(self)

    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return EnhancedPatientOrientationValidator.validate_enumerated_values(self)

    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return EnhancedPatientOrientationValidator.validate_sequence_structures(self)

    def check_cross_sequence_consistency(self) -> ValidationResult:
        """Check cross-sequence logical consistency with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid cross-sequence consistency
        """
        return EnhancedPatientOrientationValidator.validate_cross_sequence_consistency(self)

    def validate(self, config: Union[ValidationConfig, None] = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return EnhancedPatientOrientationValidator.validate(self, config)  # Direct self reference for performance

    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.

        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            EnhancedPatientOrientationValidator.validate_required_elements,
            "Required elements validation failed"
        )

    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.

        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            EnhancedPatientOrientationValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )

    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.

        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            EnhancedPatientOrientationValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )

    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence requirements are valid, raise exception if not.

        Raises:
            ValidationError: If sequence requirements are invalid
        """
        self._validate_and_raise(
            EnhancedPatientOrientationValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )

    def _ensure_cross_sequence_consistency_valid(self) -> None:
        """Ensure cross-sequence consistency is valid, raise exception if not.

        Raises:
            ValidationError: If cross-sequence consistency is invalid
        """
        self._validate_and_raise(
            EnhancedPatientOrientationValidator.validate_cross_sequence_consistency,
            "Cross-sequence consistency validation failed"
        )
