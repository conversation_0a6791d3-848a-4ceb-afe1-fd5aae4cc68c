"""Image Pixel Module - DICOM PS3.3 C.7.6.3

The Image Pixel Module describes and encodes the pixel data of the image.
This module implements the Image Pixel Module (C.7.6.3), Image Pixel Macro (C.*******),
and Image Pixel Description Macro (C.*******) as defined in DICOM PS3.3.

Key Conditional Logic:
- Pixel Data OR Pixel Data Provider URL (one required)
- Planar Configuration required if Samples per Pixel > 1
- Palette Color tables required if Photometric Interpretation = PALETTE COLOR
- Extended Offset Table Lengths required if Extended Offset Table present
- Complex photometric interpretation constraints for samples per pixel
"""
from .base_module import BaseModule
from ..enums.image_enums import (
    PhotometricInterpretation, PlanarConfiguration, PixelRepresentation
)
from ..validators.modules.image_pixel_validator import ImagePixelValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_int, format_enum_string


class ImagePixelModule(BaseModule):
    """Image Pixel Module implementation for DICOM PS3.3 C.7.6.3.

    Uses composition with internal dataset management for clean separation of concerns.
    Describes and encodes the pixel data of the image including the Image Pixel Module,
    Image Pixel Macro, and Image Pixel Description Macro.

    This module handles complex conditional logic including:
    - Mutual exclusivity between Pixel Data and Pixel Data Provider URL
    - Photometric interpretation constraints on samples per pixel
    - Planar configuration requirements for multi-sample images
    - Palette color lookup table requirements
    - Bit allocation and representation validation

    Usage:
        # Create with required elements (Type 1)
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=16,
            bits_stored=12,
            high_bit=11,
            pixel_representation=PixelRepresentation.UNSIGNED
        )

        # Add pixel data (Type 1C - one required)
        pixel.with_pixel_data(pixel_data=b'...')

        # Add color configuration for multi-sample images (Type 1C)
        pixel.with_color_configuration(planar_configuration=PlanarConfiguration.COLOR_BY_PIXEL)

        # Add palette color elements if PALETTE COLOR (Type 1C)
        pixel.with_palette_color(
            red_palette_descriptor=[256, 0, 16],
            green_palette_descriptor=[256, 0, 16],
            blue_palette_descriptor=[256, 0, 16],
            red_palette_data=b'...',
            green_palette_data=b'...',
            blue_palette_data=b'...'
        )

        # Add optional elements (Type 3)
        pixel.with_optional_elements(
            smallest_image_pixel_value=0,
            largest_image_pixel_value=4095
        )

        # Generate dataset for IOD integration
        dataset = pixel.to_dataset()

        # Validate against DICOM standard
        result = pixel.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        samples_per_pixel: int,
        photometric_interpretation: str | PhotometricInterpretation,
        rows: int,
        columns: int,
        bits_allocated: int,
        bits_stored: int,
        high_bit: int,
        pixel_representation: int | PixelRepresentation
    ) -> 'ImagePixelModule':
        """Create ImagePixelModule from all required (Type 1) data elements.

        All parameters are Type 1 (required) according to DICOM PS3.3 C.*******
        Image Pixel Description Macro.

        Args:
            samples_per_pixel: Number of samples (planes) in image (0028,0002)
                - 1 for monochrome/palette color
                - 3 for RGB/YBR color models
            photometric_interpretation: Pixel data interpretation (0028,0004)
                - MONOCHROME1, MONOCHROME2, RGB, YBR_FULL, PALETTE COLOR, etc.
            rows: Number of rows in image (0028,0010)
                - Must be positive integer
                - For YBR_FULL_422: must be even for vertical subsampling
            columns: Number of columns in image (0028,0011)
                - Must be positive integer
                - For YBR_FULL_422: must be even for horizontal subsampling
            bits_allocated: Bits allocated for each pixel sample (0028,0100)
                - Must be 1 or multiple of 8
            bits_stored: Bits stored for each pixel sample (0028,0101)
                - Must be ≤ bits_allocated
            high_bit: Most significant bit for pixel sample data (0028,0102)
                - Must equal bits_stored - 1
            pixel_representation: Data representation (0028,0103)
                - 0 for unsigned integers, 1 for signed integers

        Returns:
            ImagePixelModule: New module instance with required data elements set
        """
        instance = cls()
        instance['SamplesPerPixel'] = samples_per_pixel
        instance['PhotometricInterpretation'] = format_enum_string(photometric_interpretation)
        instance['Rows'] = rows
        instance['Columns'] = columns
        instance['BitsAllocated'] = bits_allocated
        instance['BitsStored'] = bits_stored
        instance['HighBit'] = high_bit
        instance['PixelRepresentation'] = format_enum_int(pixel_representation)
        return instance
    
    def with_pixel_data(
        self,
        pixel_data: bytes | None = None,
        pixel_data_provider_url: str | None = None
    ) -> 'ImagePixelModule':
        """Add pixel data (Type 1C - exactly one required).

        According to DICOM PS3.3 C.7.6.3, either Pixel Data (7FE0,0010) or
        Pixel Data Provider URL (0028,7FE0) is required, but not both.

        Args:
            pixel_data: Pixel data stream (7FE0,0010) Type 1C
                - Required if Pixel Data Provider URL not present
                - Contains actual pixel samples for the image
            pixel_data_provider_url: URL of pixel data provider (0028,7FE0) Type 1C
                - Required if Pixel Data not present
                - For JPIP transfer syntaxes (1.2.840.10008.********/95)

        Returns:
            ImagePixelModule: Self for method chaining
        """
        if pixel_data is not None:
            self.PixelData = pixel_data
        elif pixel_data_provider_url is not None:
            self.PixelDataProviderURL = pixel_data_provider_url

        return self
    
    def with_color_configuration(
        self,
        planar_configuration: int | PlanarConfiguration
    ) -> 'ImagePixelModule':
        """Add color configuration for multi-sample images (Type 1C).

        According to DICOM PS3.3 C.*******, Planar Configuration (0028,0006) is
        required if Samples per Pixel (0028,0002) > 1.

        Args:
            planar_configuration: Color encoding method (0028,0006) Type 1C
                - 0: color-by-pixel (R1,G1,B1,R2,G2,B2,...)
                - 1: color-by-plane (R1,R2,R3,...,G1,G2,G3,...,B1,B2,B3,...)
                - Required if Samples per Pixel > 1
                - Should not be present if Samples per Pixel = 1

        Returns:
            ImagePixelModule: Self for method chaining
        """
        self.PlanarConfiguration = format_enum_int(planar_configuration)
        return self
    
    def with_pixel_aspect_ratio(
        self,
        pixel_aspect_ratio: list[int]
    ) -> 'ImagePixelModule':
        """Add pixel aspect ratio for non-square pixels (Type 1C).

        According to DICOM PS3.3 C.*******, Pixel Aspect Ratio (0028,0034) is
        required if the aspect ratio values do not have a ratio of 1:1 AND the
        physical pixel spacing is not specified by:
        - Pixel Spacing (0028,0030), or
        - Imager Pixel Spacing (0018,1164), or
        - Nominal Scanned Pixel Spacing (0018,2010)

        Args:
            pixel_aspect_ratio: Vertical:horizontal pixel size ratio (0028,0034) Type 1C
                - Format: [vertical_size, horizontal_size] as integer values
                - Example: [1, 1] for square pixels, [6, 5] for 0.30mm/0.25mm pixels
                - Required if aspect ratio ≠ 1:1 and no physical spacing specified

        Returns:
            ImagePixelModule: Self for method chaining
        """
        self.PixelAspectRatio = pixel_aspect_ratio
        return self
    
    def with_palette_color(
        self,
        red_palette_descriptor: list[int],
        green_palette_descriptor: list[int],
        blue_palette_descriptor: list[int],
        red_palette_data: bytes,
        green_palette_data: bytes,
        blue_palette_data: bytes
    ) -> 'ImagePixelModule':
        """Add palette color lookup tables (Type 1C - all required together).

        According to DICOM PS3.3 C.*******, all palette color lookup table elements
        are required if Photometric Interpretation (0028,0004) = PALETTE COLOR or
        Pixel Presentation (0008,9205) = COLOR or MIXED.

        Args:
            red_palette_descriptor: Red palette format (0028,1101) Type 1C
                - Format: [entries, first_value, bits_per_entry]
                - entries: number of LUT entries (0 means 65536)
                - first_value: first input value mapped
                - bits_per_entry: 8 or 16 bits per entry
            green_palette_descriptor: Green palette format (0028,1102) Type 1C
                - Same format as red_palette_descriptor
            blue_palette_descriptor: Blue palette format (0028,1103) Type 1C
                - Same format as red_palette_descriptor
            red_palette_data: Red palette data (0028,1201) Type 1C
                - Actual lookup table data for red component
            green_palette_data: Green palette data (0028,1202) Type 1C
                - Actual lookup table data for green component
            blue_palette_data: Blue palette data (0028,1203) Type 1C
                - Actual lookup table data for blue component

        Returns:
            ImagePixelModule: Self for method chaining
        """
        self.RedPaletteColorLookupTableDescriptor = red_palette_descriptor
        self.GreenPaletteColorLookupTableDescriptor = green_palette_descriptor
        self.BluePaletteColorLookupTableDescriptor = blue_palette_descriptor
        self.RedPaletteColorLookupTableData = red_palette_data
        self.GreenPaletteColorLookupTableData = green_palette_data
        self.BluePaletteColorLookupTableData = blue_palette_data
        return self
    
    def with_extended_offset_table(
        self,
        extended_offset_table: bytes,
        extended_offset_table_lengths: bytes
    ) -> 'ImagePixelModule':
        """Add extended offset table for encapsulated pixel data (Type 3/1C).

        According to DICOM PS3.3 C.7.6.3, Extended Offset Table (7FE0,0001) is Type 3,
        but Extended Offset Table Lengths (7FE0,0002) is Type 1C - required if
        Extended Offset Table is present.

        Args:
            extended_offset_table: Frame byte offsets (7FE0,0001) Type 3
                - Byte offsets to Item Tags of frames in encapsulated pixel data
                - Only for encapsulated format with frames in separate fragments
            extended_offset_table_lengths: Frame byte lengths (7FE0,0002) Type 1C
                - Required if Extended Offset Table present
                - Byte lengths of frames in encapsulated pixel data

        Returns:
            ImagePixelModule: Self for method chaining
        """
        self.ExtendedOffsetTable = extended_offset_table
        self.ExtendedOffsetTableLengths = extended_offset_table_lengths
        return self

    def with_pixel_padding_range_limit(
        self,
        pixel_padding_range_limit: int
    ) -> 'ImagePixelModule':
        """Add pixel padding range limit (Type 1C).

        According to DICOM PS3.3 C.7.6.3, Pixel Padding Range Limit (0028,0121) is
        required if pixel padding is to be defined as a range rather than a single value.
        Must be used together with Pixel Padding Value (0028,0120) from General Equipment Module.

        Args:
            pixel_padding_range_limit: Pixel padding range limit (0028,0121) Type 1C
                - Represents one limit (inclusive) of padding value range
                - VR determined by Pixel Representation (US if unsigned, SS if signed)

        Returns:
            ImagePixelModule: Self for method chaining
        """
        self.PixelPaddingRangeLimit = pixel_padding_range_limit
        return self

    def with_optional_elements(
        self,
        smallest_image_pixel_value: int | None = None,
        largest_image_pixel_value: int | None = None,
        icc_profile: bytes | None = None,
        color_space: str | None = None
    ) -> 'ImagePixelModule':
        """Add optional (Type 3) data elements.

        Args:
            smallest_image_pixel_value: Minimum pixel value (0028,0106) Type 3
                - Minimum actual pixel value encountered in this image
            largest_image_pixel_value: Maximum pixel value (0028,0107) Type 3
                - Maximum actual pixel value encountered in this image
            icc_profile: ICC color profile (0028,2000) Type 3
                - ICC Profile encoding transformation to PCS-Values
                - Defines color space of pixel data values
            color_space: Color space identifier (0028,2002) Type 3
                - Label identifying well-known color space
                - Must be consistent with ICC Profile if present

        Returns:
            ImagePixelModule: Self for method chaining
        """
        if smallest_image_pixel_value is not None:
            self.SmallestImagePixelValue = smallest_image_pixel_value
        if largest_image_pixel_value is not None:
            self.LargestImagePixelValue = largest_image_pixel_value
        if icc_profile is not None:
            self.ICCProfile = icc_profile
        if color_space is not None:
            self.ColorSpace = color_space
        return self
    
    @property
    def is_monochrome(self) -> bool:
        """Check if image is monochrome.

        Returns:
            bool: True if photometric interpretation is MONOCHROME1 or MONOCHROME2
        """
        if 'PhotometricInterpretation' not in self:
            return False
        return self.PhotometricInterpretation in [
            PhotometricInterpretation.MONOCHROME1.value,
            PhotometricInterpretation.MONOCHROME2.value
        ]

    @property
    def is_color(self) -> bool:
        """Check if image is color.

        Returns:
            bool: True if photometric interpretation indicates color image
        """
        if 'PhotometricInterpretation' not in self:
            return False
        return self.PhotometricInterpretation in [
            "RGB", "YBR_FULL", "YBR_FULL_422", "YBR_PARTIAL_420",
            "YBR_ICT", "YBR_RCT", "XYB", "PALETTE COLOR"
        ]

    @property
    def is_palette_color(self) -> bool:
        """Check if image uses palette color.

        Returns:
            bool: True if photometric interpretation is PALETTE COLOR
        """
        return ('PhotometricInterpretation' in self and
                self.PhotometricInterpretation == "PALETTE COLOR")

    @property
    def is_signed(self) -> bool:
        """Check if pixel representation is signed.

        Returns:
            bool: True if pixel representation indicates signed data (2's complement)
        """
        return ('PixelRepresentation' in self and
                self.PixelRepresentation in [1, "1"])

    @property
    def is_unsigned(self) -> bool:
        """Check if pixel representation is unsigned.

        Returns:
            bool: True if pixel representation indicates unsigned data
        """
        return ('PixelRepresentation' in self and
                self.PixelRepresentation in [0, "0"])
    
    @property
    def bytes_per_pixel(self) -> int | None:
        """Calculate bytes per pixel sample.

        Returns:
            int | None: Number of bytes per pixel sample, or None if not determinable
        """
        if 'BitsAllocated' not in self:
            return None
        return (self.BitsAllocated + 7) // 8

    @property
    def total_pixels(self) -> int | None:
        """Calculate total number of pixels.

        Returns:
            int | None: Total pixel count (rows × columns), or None if not determinable
        """
        if 'Rows' not in self or 'Columns' not in self:
            return None
        return self.Rows * self.Columns

    @property
    def expected_pixel_data_size(self) -> int | None:
        """Calculate expected pixel data size in bytes.

        Note: This calculation assumes uncompressed pixel data. For compressed data,
        actual size will be different. If SamplesPerPixel is not present, it is assumed to be 1.

        Returns:
            int | None: Expected pixel data size in bytes, or None if not determinable
        """
        total_pixels = self.total_pixels
        bytes_per_pixel = self.bytes_per_pixel
        if total_pixels is None or bytes_per_pixel is None:
            return None

        samples_per_pixel = self.SamplesPerPixel if 'SamplesPerPixel' in self else 1

        # Special case for YBR_FULL_422 with horizontal subsampling
        if ('PhotometricInterpretation' in self and
            self.PhotometricInterpretation == "YBR_FULL_422"):
            # YBR_FULL_422 has 2 Y values per CB/CR pair, so effective samples = 2
            return total_pixels * 2 * bytes_per_pixel

        return total_pixels * samples_per_pixel * bytes_per_pixel

    @property
    def requires_planar_configuration(self) -> bool:
        """Check if planar configuration is required.

        Returns:
            bool: True if Samples per Pixel > 1 (Type 1C requirement)
        """
        return ('SamplesPerPixel' in self and
                self.SamplesPerPixel > 1)

    @property
    def requires_palette_color_tables(self) -> bool:
        """Check if palette color lookup tables are required.

        Returns:
            bool: True if Photometric Interpretation is PALETTE COLOR (Type 1C requirement)
        """
        return self.is_palette_color

    @property
    def has_pixel_data(self) -> bool:
        """Check if pixel data is present.

        Returns:
            bool: True if either Pixel Data or Pixel Data Provider URL is present
        """
        return ('PixelData' in self or
                'PixelDataProviderURL' in self)

    @property
    def has_palette_color_tables(self) -> bool:
        """Check if all palette color lookup tables are present.

        Returns:
            bool: True if all 6 palette color attributes are present
        """
        required_attrs = [
            'RedPaletteColorLookupTableDescriptor',
            'GreenPaletteColorLookupTableDescriptor',
            'BluePaletteColorLookupTableDescriptor',
            'RedPaletteColorLookupTableData',
            'GreenPaletteColorLookupTableData',
            'BluePaletteColorLookupTableData'
        ]
        return all(attr in self for attr in required_attrs)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Image Pixel Module instance against DICOM standard.

        Validates all Type 1, Type 1C conditional requirements, enumerated values,
        and pixel data consistency according to DICOM PS3.3 C.7.6.3.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult: ValidationResult with 'errors' and 'warnings' lists
        """
        return ImagePixelValidator.validate(self._dataset, config)
