"""
RT Fraction Scheme Module - DICOM PS3.3 C.8.8.13

The RT Fraction Scheme Module contains information describing the fractionation
of the treatment plan. It specifies the number of fractions and the beams or
brachy application setups to be used in each fraction group.
"""
from .base_module import BaseModule
from ..enums.rt_enums import BeamDoseMeaning
from ..enums.dose_enums import DoseType
from ..validators.modules.rt_fraction_scheme_validator import RTFractionSchemeValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_string
from pydicom import Dataset


class RTFractionSchemeModule(BaseModule):
    """RT Fraction Scheme Module implementation for DICOM PS3.3 C.8.8.13.

    Uses composition with internal dataset management for cleaner separation of concerns.
    Contains information describing the fractionation of the treatment plan.

    This module describes single or multiple schemes of dose descriptions. Each Sequence
    Item contains dose specification information, fractionation patterns, and either beam
    or brachytherapy application setup specifications. The design allows a beam or
    brachytherapy application setup to be used in multiple fraction schemes.

    Key Conditional Requirements:
    - Referenced Beam Sequence (300C,0004) is Type 1C: Required if Number of Beams > 0
    - Referenced Brachy Application Setup Sequence (300C,000A) is Type 1C: Required if Number of Brachy Application Setups > 0
    - Number of Beams and Number of Brachy Application Setups are mutually exclusive (cannot both be > 0)
    - Beam Dose Type (300A,0090) is Type 1C: Required if Alternate Beam Dose is present
    - Alternate Beam Dose Type (300A,0092) is Type 1C: Required if Alternate Beam Dose is present
    - Fraction Pattern length must equal 7 × Number of Fraction Pattern Digits Per Day × Repeat Fraction Cycle Length

    Usage:
        # Create with required elements - beam-based fraction scheme
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(
            referenced_beam_number=1,
            beam_dose=200.0
        )

        fraction_group = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=25,
            number_of_beams=1,
            number_of_brachy_application_setups=0,
            referenced_beam_sequence=[beam_item]
        )

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[fraction_group]
        )

        # Add optional fractionation pattern
        fraction_scheme.with_fraction_pattern(
            number_of_fraction_pattern_digits_per_day=1,
            repeat_fraction_cycle_length=1,
            fraction_pattern="1111100"  # Monday-Friday treatment
        )

        # Generate dataset for IOD integration
        dataset = fraction_scheme.to_dataset()

        # Validate
        result = fraction_scheme.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        fraction_group_sequence: list[Dataset]
    ) -> 'RTFractionSchemeModule':
        """Create RTFractionSchemeModule from all required (Type 1) data elements.

        Args:
            fraction_group_sequence: Sequence of fraction groups (300A,0070) Type 1.
                One or more Items shall be included in this Sequence. Each item must
                contain the required elements for a fraction group.

        Returns:
            RTFractionSchemeModule: New module instance with required data elements set

        Raises:
            ValueError: If fraction_group_sequence is empty or None
        """
        if not fraction_group_sequence:
            raise ValueError("Fraction Group Sequence is required and cannot be empty (Type 1)")

        instance = cls()
        instance['FractionGroupSequence'] = fraction_group_sequence
        return instance

    def with_optional_elements(
        self
    ) -> 'RTFractionSchemeModule':
        """Add optional (Type 3) elements.

        Note: This module has no Type 3 elements at the top level.
        All optional elements are within the Fraction Group Sequence items.
        Use the create_fraction_group_item() method to include optional elements
        in individual fraction groups.

        Returns:
            RTFractionSchemeModule: Self for method chaining
        """
        return self

    def with_fraction_pattern(
        self,
        fraction_group_number: int,
        number_of_fraction_pattern_digits_per_day: int,
        repeat_fraction_cycle_length: int,
        fraction_pattern: str
    ) -> 'RTFractionSchemeModule':
        """Add fraction pattern information to a specific fraction group.

        The fractionation pattern describes treatment delivery schedule but does not
        indicate actual start of treatment or timing. Pattern starts on Monday.

        Args:
            fraction_group_number: Fraction group to modify (300A,0071)
            number_of_fraction_pattern_digits_per_day: Digits per day in pattern (300A,0079) Type 3
            repeat_fraction_cycle_length: Number of weeks in pattern cycle (300A,007A) Type 3
            fraction_pattern: Pattern of 0s (no treatment) and 1s (treatment) (300A,007B) Type 3.
                Length must equal 7 × digits_per_day × cycle_length

        Returns:
            RTFractionSchemeModule: Self for method chaining

        Raises:
            ValueError: If fraction group not found or pattern length is incorrect
        """
        # Validate pattern length
        expected_length = 7 * number_of_fraction_pattern_digits_per_day * repeat_fraction_cycle_length
        if len(fraction_pattern) != expected_length:
            raise ValueError(
                f"Fraction Pattern length ({len(fraction_pattern)}) must equal "
                f"7 × {number_of_fraction_pattern_digits_per_day} × {repeat_fraction_cycle_length} = {expected_length}"
            )

        # Validate pattern contains only 0s and 1s
        if not all(c in '01' for c in fraction_pattern):
            raise ValueError("Fraction Pattern must contain only '0' and '1' characters")

        # Find and update the fraction group
        fraction_groups = self.FractionGroupSequence if 'FractionGroupSequence' in self else []
        for group in fraction_groups:
            if 'FractionGroupNumber' in group and group.FractionGroupNumber == fraction_group_number:
                group.NumberOfFractionPatternDigitsPerDay = number_of_fraction_pattern_digits_per_day
                group.RepeatFractionCycleLength = repeat_fraction_cycle_length
                group.FractionPattern = fraction_pattern
                return self

        raise ValueError(f"Fraction group with number {fraction_group_number} not found")

    def with_beam_dose_meaning(
        self,
        fraction_group_number: int,
        beam_dose_meaning: str | BeamDoseMeaning
    ) -> 'RTFractionSchemeModule':
        """Add beam dose meaning to a specific fraction group.

        Args:
            fraction_group_number: Fraction group to modify (300A,0071)
            beam_dose_meaning: Meaning of beam dose (300A,008B) Type 3.
                BEAM_LEVEL: Beam Dose value is individually calculated for this Beam
                FRACTION_LEVEL: Dose is calculated on fraction level, Beam Dose is nominally distributed

        Returns:
            RTFractionSchemeModule: Self for method chaining

        Raises:
            ValueError: If fraction group not found
        """
        # Find and update the fraction group
        fraction_groups = self.FractionGroupSequence if 'FractionGroupSequence' in self else []
        for group in fraction_groups:
            if group.get('FractionGroupNumber') == fraction_group_number:
                group.BeamDoseMeaning = format_enum_string(beam_dose_meaning)
                return self

        raise ValueError(f"Fraction group with number {fraction_group_number} not found")
    
    @staticmethod
    def create_fraction_group_item(
        fraction_group_number: int,
        number_of_fractions_planned: int = 0,
        number_of_beams: int = 0,
        number_of_brachy_application_setups: int = 0,
        fraction_group_description: str | None = None,
        definition_source_sequence: list[Dataset] | None = None,
        referenced_dose_sequence: list[Dataset] | None = None,
        referenced_dose_reference_sequence: list[Dataset] | None = None,
        number_of_fraction_pattern_digits_per_day: int | None = None,
        fraction_pattern: str | None = None,
        repeat_fraction_cycle_length: int | None = None,
        beam_dose_meaning: str | BeamDoseMeaning | None = None,
        referenced_beam_sequence: list[Dataset] | None = None,
        referenced_brachy_application_setup_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create fraction group sequence item with proper DICOM validation.

        Args:
            fraction_group_number: Identification number of the Fraction Group (300A,0071) Type 1.
                Must be unique within the RT Plan.
            number_of_fractions_planned: Total number of treatments prescribed (300A,0078) Type 2.
                Default 0 for empty value.
            number_of_beams: Number of beams in this fraction group (300A,0080) Type 1.
                If > 0, Number of Brachy Application Setups shall equal zero.
            number_of_brachy_application_setups: Number of brachy setups (300A,00A0) Type 1.
                If > 0, Number of Beams shall equal zero.
            fraction_group_description: User-defined description (300A,0072) Type 3
            definition_source_sequence: Source of fraction group information (0008,1156) Type 3.
                References RT Radiation Set Storage instances. Includes SOP Instance Reference Macro.
            referenced_dose_sequence: Related RT Dose instances (300C,0080) Type 3.
                For grids, isodose curves, and point doses. Includes SOP Instance Reference Macro.
            referenced_dose_reference_sequence: Dose references for this group (300C,0050) Type 3
            number_of_fraction_pattern_digits_per_day: Digits per day in pattern (300A,0079) Type 3
            fraction_pattern: Pattern of 0s/1s describing treatment (300A,007B) Type 3.
                Length = 7 × digits_per_day × cycle_length. Starts on Monday.
            repeat_fraction_cycle_length: Number of weeks in pattern cycle (300A,007A) Type 3
            beam_dose_meaning: Meaning of beam dose values (300A,008B) Type 3.
                BEAM_LEVEL or FRACTION_LEVEL
            referenced_beam_sequence: Treatment beams in this group (300C,0004) Type 1C.
                Required if Number of Beams > 0. One or more Items required.
            referenced_brachy_application_setup_sequence: Brachy setups (300C,000A) Type 1C.
                Required if Number of Brachy Application Setups > 0. One or more Items required.

        Returns:
            Dataset: Fraction group sequence item with all specified attributes

        Raises:
            ValueError: If conditional requirements are violated or mutual exclusivity is broken
        """
        # Validate mutual exclusivity
        if number_of_beams > 0 and number_of_brachy_application_setups > 0:
            raise ValueError(
                "Number of Beams and Number of Brachy Application Setups cannot both be greater than zero"
            )

        # Validate Type 1C conditional requirements
        if number_of_beams > 0 and referenced_beam_sequence is None:
            raise ValueError(
                "Referenced Beam Sequence (300C,0004) is required when Number of Beams > 0 (Type 1C)"
            )

        if number_of_brachy_application_setups > 0 and referenced_brachy_application_setup_sequence is None:
            raise ValueError(
                "Referenced Brachy Application Setup Sequence (300C,000A) is required when "
                "Number of Brachy Application Setups > 0 (Type 1C)"
            )

        # Validate fraction pattern consistency if all pattern elements provided
        if (fraction_pattern is not None and
            number_of_fraction_pattern_digits_per_day is not None and
            repeat_fraction_cycle_length is not None):
            expected_length = 7 * number_of_fraction_pattern_digits_per_day * repeat_fraction_cycle_length
            if len(fraction_pattern) != expected_length:
                raise ValueError(
                    f"Fraction Pattern length ({len(fraction_pattern)}) must equal "
                    f"7 × {number_of_fraction_pattern_digits_per_day} × {repeat_fraction_cycle_length} = {expected_length}"
                )
            if not all(c in '01' for c in fraction_pattern):
                raise ValueError("Fraction Pattern must contain only '0' and '1' characters")

        # Create the dataset item
        item = Dataset()

        # Set Type 1 elements
        item.FractionGroupNumber = fraction_group_number
        item.NumberOfBeams = number_of_beams
        item.NumberOfBrachyApplicationSetups = number_of_brachy_application_setups

        # Set Type 2 elements
        item.NumberOfFractionsPlanned = number_of_fractions_planned

        # Set Type 1C conditional elements
        if referenced_beam_sequence is not None:
            item.ReferencedBeamSequence = referenced_beam_sequence
        if referenced_brachy_application_setup_sequence is not None:
            item.ReferencedBrachyApplicationSetupSequence = referenced_brachy_application_setup_sequence

        # Set Type 3 optional elements if provided
        if fraction_group_description is not None:
            item.FractionGroupDescription = fraction_group_description
        if definition_source_sequence is not None:
            item.DefinitionSourceSequence = definition_source_sequence
        if referenced_dose_sequence is not None:
            item.ReferencedDoseSequence = referenced_dose_sequence
        if referenced_dose_reference_sequence is not None:
            item.ReferencedDoseReferenceSequence = referenced_dose_reference_sequence
        if number_of_fraction_pattern_digits_per_day is not None:
            item.NumberOfFractionPatternDigitsPerDay = number_of_fraction_pattern_digits_per_day
        if fraction_pattern is not None:
            item.FractionPattern = fraction_pattern
        if repeat_fraction_cycle_length is not None:
            item.RepeatFractionCycleLength = repeat_fraction_cycle_length
        if beam_dose_meaning is not None:
            item.BeamDoseMeaning = format_enum_string(beam_dose_meaning)

        return item
    
    @staticmethod
    def create_referenced_beam_item(
        referenced_beam_number: int,
        referenced_dose_reference_uid: str | None = None,
        beam_dose: float | None = None,
        beam_dose_type: str | DoseType | None = None,
        alternate_beam_dose: float | None = None,
        alternate_beam_dose_type: str | DoseType | None = None,
        beam_meterset: float | None = None,
        beam_delivery_duration_limit: float | None = None,
        dose_calibration_conditions_verified_flag: str | None = None,
        dose_calibration_conditions_sequence: list[Dataset] | None = None,
        radiation_device_configuration_and_commissioning_key_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create referenced beam sequence item with proper DICOM validation.

        Args:
            referenced_beam_number: Beam Number within RT Plan (300C,0006) Type 1.
                Identifies Beam specified by Beam Number (300A,00C0) within Beam Sequence.
            referenced_dose_reference_uid: Dose Reference UID (300A,0083) Type 3.
                Identifies primary target for this beam in RT Prescription Module.
            beam_dose: Dose in Gy due to current beam for one fraction (300A,0084) Type 3
            beam_dose_type: Type of Beam Dose (300A,0090) Type 1C.
                PHYSICAL or EFFECTIVE. Required if Alternate Beam Dose is present.
                Shall not have same value as Alternate Beam Dose Type.
            alternate_beam_dose: Alternate dose in Gy (300A,0091) Type 3
            alternate_beam_dose_type: Type of Alternate Beam Dose (300A,0092) Type 1C.
                PHYSICAL or EFFECTIVE. Required if Alternate Beam Dose is present.
                Shall not have same value as Beam Dose Type.
            beam_meterset: Machine setting in MU or minutes (300A,0086) Type 3.
                As defined by Primary Dosimeter Unit in RT Beams Module.
            beam_delivery_duration_limit: Expected maximum delivery time in sec (300A,00C5) Type 3.
                Includes factor > 1 for normal delivery variations.
            dose_calibration_conditions_verified_flag: Calibration verification (300C,0123) Type 3.
                YES or NO. Indicates if verifiable calibration conditions were used.
            dose_calibration_conditions_sequence: Calibration conditions (300C,0120) Type 1C.
                Required if Verified Flag = YES and Radiation Device Config sequence absent.
            radiation_device_configuration_and_commissioning_key_sequence: Config keys (300A,065A) Type 1C.
                Required if Verified Flag = YES and Dose Calibration Conditions sequence absent.
                Includes Content Item Macro.

        Returns:
            Dataset: Referenced beam sequence item with all specified attributes

        Raises:
            ValueError: If conditional requirements are violated
        """
        # Validate Type 1C conditional requirements for alternate beam dose
        if alternate_beam_dose is not None:
            if beam_dose_type is None:
                raise ValueError(
                    "Beam Dose Type (300A,0090) is required when Alternate Beam Dose is present (Type 1C)"
                )
            if alternate_beam_dose_type is None:
                raise ValueError(
                    "Alternate Beam Dose Type (300A,0092) is required when Alternate Beam Dose is present (Type 1C)"
                )
            # Validate dose types are different
            beam_type_value = format_enum_string(beam_dose_type)
            alt_type_value = format_enum_string(alternate_beam_dose_type)
            if beam_type_value == alt_type_value:
                raise ValueError(
                    "Beam Dose Type and Alternate Beam Dose Type shall not have the same value"
                )

        # Validate calibration conditions logic
        if dose_calibration_conditions_verified_flag == "YES":
            has_calibration_seq = dose_calibration_conditions_sequence is not None
            has_device_config_seq = radiation_device_configuration_and_commissioning_key_sequence is not None

            if not has_calibration_seq and not has_device_config_seq:
                raise ValueError(
                    "Either Dose Calibration Conditions Sequence (300C,0120) or "
                    "Radiation Device Configuration and Commissioning Key Sequence (300A,065A) "
                    "is required when Dose Calibration Conditions Verified Flag = YES (Type 1C)"
                )

        # Create the dataset item
        item = Dataset()

        # Set Type 1 elements
        item.ReferencedBeamNumber = referenced_beam_number

        # Set Type 3 optional elements if provided
        if referenced_dose_reference_uid is not None:
            item.ReferencedDoseReferenceUID = referenced_dose_reference_uid
        if beam_dose is not None:
            item.BeamDose = beam_dose
        if beam_dose_type is not None:
            item.BeamDoseType = format_enum_string(beam_dose_type)
        if alternate_beam_dose is not None:
            item.AlternateBeamDose = alternate_beam_dose
        if alternate_beam_dose_type is not None:
            item.AlternateBeamDoseType = format_enum_string(alternate_beam_dose_type)
        if beam_meterset is not None:
            item.BeamMeterset = beam_meterset
        if beam_delivery_duration_limit is not None:
            item.BeamDeliveryDurationLimit = beam_delivery_duration_limit
        if dose_calibration_conditions_verified_flag is not None:
            item.DoseCalibrationConditionsVerifiedFlag = dose_calibration_conditions_verified_flag
        if dose_calibration_conditions_sequence is not None:
            item.DoseCalibrationConditionsSequence = dose_calibration_conditions_sequence
        if radiation_device_configuration_and_commissioning_key_sequence is not None:
            item.RadiationDeviceConfigurationAndCommissioningKeySequence = radiation_device_configuration_and_commissioning_key_sequence

        return item
    
    @staticmethod
    def create_referenced_brachy_application_setup_item(
        referenced_brachy_application_setup_number: int,
        brachy_application_setup_dose_specification_point: list[float] | None = None,
        brachy_application_setup_dose: float | None = None,
        referenced_dose_reference_uid: str | None = None
    ) -> Dataset:
        """Create referenced brachy application setup sequence item.

        Args:
            referenced_brachy_application_setup_number: Setup Number within RT Plan (300C,000C) Type 1.
                Identifies Brachy Application Setup specified by Brachy Application Setup Number
                within Brachy Application Setup Sequence in RT Brachy Application Setups Module.
            brachy_application_setup_dose_specification_point: Coordinates (x,y,z) in mm (300A,00A2) Type 3.
                Point in Patient-Based Coordinate System where dose is specified.
                Single point used for dose normalization, distinct from Referenced Dose Reference Sequence.
            brachy_application_setup_dose: Dose in Gy at specification point (300A,00A4) Type 3.
                Dose due to current Brachy Application Setup.
            referenced_dose_reference_uid: Dose Reference UID (300A,0083) Type 3.
                Identifies primary target for this brachy setup in RT Prescription Module.

        Returns:
            Dataset: Referenced brachy application setup sequence item
        """
        item = Dataset()

        # Set Type 1 elements
        item.ReferencedBrachyApplicationSetupNumber = referenced_brachy_application_setup_number

        # Set Type 3 optional elements if provided
        if brachy_application_setup_dose_specification_point is not None:
            item.BrachyApplicationSetupDoseSpecificationPoint = brachy_application_setup_dose_specification_point
        if brachy_application_setup_dose is not None:
            item.BrachyApplicationSetupDose = brachy_application_setup_dose
        if referenced_dose_reference_uid is not None:
            item.ReferencedDoseReferenceUID = referenced_dose_reference_uid

        return item

    @staticmethod
    def create_referenced_dose_reference_item(
        referenced_dose_reference_number: int,
        constraint_weight: float | None = None,
        delivery_warning_dose: float | None = None,
        delivery_maximum_dose: float | None = None,
        target_minimum_dose: float | None = None,
        target_prescription_dose: float | None = None,
        target_maximum_dose: float | None = None,
        target_underdose_volume_fraction: float | None = None,
        organ_at_risk_full_volume_dose: float | None = None,
        organ_at_risk_limit_dose: float | None = None,
        organ_at_risk_maximum_dose: float | None = None,
        organ_at_risk_overdose_volume_fraction: float | None = None
    ) -> Dataset:
        """Create referenced dose reference sequence item.

        Args:
            referenced_dose_reference_number: Dose Reference Number (300C,0051) Type 1.
                Identifies Dose Reference in RT Prescription Module.
            constraint_weight: Relative importance of constraint (300A,0021) Type 3.
                High values represent more important constraints.
            delivery_warning_dose: Warning dose in Gy (300A,0022) Type 3.
                Dose that should trigger action when reached or exceeded.
            delivery_maximum_dose: Maximum deliverable dose in Gy (300A,0023) Type 3
            target_minimum_dose: Minimum permitted dose in Gy (300A,0025) Type 3.
                For TARGET Dose Reference Type only.
            target_prescription_dose: Prescribed dose in Gy (300A,0026) Type 3.
                For TARGET Dose Reference Type only.
            target_maximum_dose: Maximum permitted dose in Gy (300A,0027) Type 3.
                For TARGET Dose Reference Type only.
            target_underdose_volume_fraction: Max underdose fraction % (300A,0028) Type 3.
                For TARGET with VOLUME Structure Type only.
            organ_at_risk_full_volume_dose: Max dose to entire volume in Gy (300A,002A) Type 3.
                For ORGAN_AT_RISK with VOLUME Structure Type only.
            organ_at_risk_limit_dose: Max permitted dose in Gy (300A,002B) Type 3.
                For ORGAN_AT_RISK with VOLUME Structure Type only.
            organ_at_risk_maximum_dose: Max dose to non-overdosed part in Gy (300A,002C) Type 3.
                For ORGAN_AT_RISK with VOLUME Structure Type only.
            organ_at_risk_overdose_volume_fraction: Max overdose fraction % (300A,002D) Type 3.
                For ORGAN_AT_RISK with VOLUME Structure Type only.

        Returns:
            Dataset: Referenced dose reference sequence item
        """
        item = Dataset()

        # Set Type 1 elements
        item.ReferencedDoseReferenceNumber = referenced_dose_reference_number

        # Set Type 3 optional elements if provided
        if constraint_weight is not None:
            item.ConstraintWeight = constraint_weight
        if delivery_warning_dose is not None:
            item.DeliveryWarningDose = delivery_warning_dose
        if delivery_maximum_dose is not None:
            item.DeliveryMaximumDose = delivery_maximum_dose
        if target_minimum_dose is not None:
            item.TargetMinimumDose = target_minimum_dose
        if target_prescription_dose is not None:
            item.TargetPrescriptionDose = target_prescription_dose
        if target_maximum_dose is not None:
            item.TargetMaximumDose = target_maximum_dose
        if target_underdose_volume_fraction is not None:
            item.TargetUnderdoseVolumeFraction = target_underdose_volume_fraction
        if organ_at_risk_full_volume_dose is not None:
            item.OrganAtRiskFullVolumeDose = organ_at_risk_full_volume_dose
        if organ_at_risk_limit_dose is not None:
            item.OrganAtRiskLimitDose = organ_at_risk_limit_dose
        if organ_at_risk_maximum_dose is not None:
            item.OrganAtRiskMaximumDose = organ_at_risk_maximum_dose
        if organ_at_risk_overdose_volume_fraction is not None:
            item.OrganAtRiskOverdoseVolumeFraction = organ_at_risk_overdose_volume_fraction

        return item

    @staticmethod
    def create_dose_calibration_conditions_item(
        absorbed_dose_to_meterset_ratio: float,
        delineated_radiation_field_size: list[float],
        calibration_reference_point_depth: float,
        source_to_surface_distance: float,
        calibration_datetime: str = ""
    ) -> Dataset:
        """Create dose calibration conditions sequence item.

        Args:
            absorbed_dose_to_meterset_ratio: Ratio of absorbed dose in Gy to Meterset (300C,0121) Type 1.
                In reference conditions as defined by Primary Dosimeter Unit.
            delineated_radiation_field_size: Field size in mm [X, Y] (300C,0122) Type 1.
                In IEC BEAM LIMITING DEVICE coordinate system.
            calibration_reference_point_depth: Depth in mm from phantom surface (300C,0124) Type 1
            source_to_surface_distance: Distance in mm from source to phantom surface (300A,0130) Type 1.
                During calibration.
            calibration_datetime: Date and time of calibration (0018,1203) Type 2.
                DICOM DT format. Empty string for empty value.

        Returns:
            Dataset: Dose calibration conditions sequence item
        """
        item = Dataset()

        # Set Type 1 elements
        item.AbsorbedDoseToMetersetRatio = absorbed_dose_to_meterset_ratio
        item.DelineatedRadiationFieldSize = delineated_radiation_field_size
        item.CalibrationReferencePointDepth = calibration_reference_point_depth
        item.SourceToSurfaceDistance = source_to_surface_distance

        # Set Type 2 elements
        item.CalibrationDateTime = calibration_datetime

        return item

    @property
    def has_fraction_groups(self) -> bool:
        """Check if fraction groups are present.

        Returns:
            bool: True if Fraction Group Sequence is present and not empty
        """
        return 'FractionGroupSequence' in self and len(self.FractionGroupSequence) > 0

    @property
    def fraction_group_count(self) -> int:
        """Get the number of fraction groups in this module.

        Returns:
            int: Number of fraction groups in Fraction Group Sequence
        """
        if 'FractionGroupSequence' not in self:
            return 0
        return len(self.FractionGroupSequence)

    @property
    def has_beam_based_groups(self) -> bool:
        """Check if any fraction groups use beam-based delivery.

        Returns:
            bool: True if any fraction group has Number of Beams > 0
        """
        if 'FractionGroupSequence' not in self:
            return False
        return any(group.get('NumberOfBeams', 0) > 0 for group in self.FractionGroupSequence)

    @property
    def has_brachy_based_groups(self) -> bool:
        """Check if any fraction groups use brachytherapy-based delivery.

        Returns:
            bool: True if any fraction group has Number of Brachy Application Setups > 0
        """
        if 'FractionGroupSequence' not in self:
            return False
        return any(group.get('NumberOfBrachyApplicationSetups', 0) > 0 for group in self.FractionGroupSequence)

    @property
    def has_fraction_patterns(self) -> bool:
        """Check if any fraction groups have fractionation patterns defined.

        Returns:
            bool: True if any fraction group has Fraction Pattern defined
        """
        if 'FractionGroupSequence' not in self:
            return False
        return any('FractionPattern' in group for group in self.FractionGroupSequence)

    def get_total_fractions_planned(self) -> int:
        """Get the total number of fractions planned across all groups.

        Returns:
            int: Total number of fractions planned across all fraction groups
        """
        if 'FractionGroupSequence' not in self:
            return 0
        total_fractions = 0
        for fraction_item in self.FractionGroupSequence:
            fractions_planned = fraction_item.get('NumberOfFractionsPlanned', 0)
            total_fractions += fractions_planned
        return total_fractions

    def get_fraction_group_numbers(self) -> list[int]:
        """Get all fraction group numbers in this module.

        Returns:
            list[int]: List of fraction group numbers
        """
        if 'FractionGroupSequence' not in self:
            return []
        return [group.get('FractionGroupNumber') for group in self.FractionGroupSequence
                if 'FractionGroupNumber' in group]

    def get_fraction_group_by_number(self, fraction_group_number: int) -> Dataset | None:
        """Get a specific fraction group by its number.

        Args:
            fraction_group_number: Fraction Group Number to find

        Returns:
            Dataset: Fraction group dataset or None if not found
        """
        if 'FractionGroupSequence' not in self:
            return None
        for group in self.FractionGroupSequence:
            if group.get('FractionGroupNumber') == fraction_group_number:
                return group
        return None

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Fraction Scheme Module instance against DICOM standard.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult with errors and warnings
        """
        return RTFractionSchemeValidator.validate(self.to_dataset(), config)
