"""
Structure Set Module - DICOM PS3.3 C.8.8.5

A structure set defines a set of areas of significance. Each area can be associated 
with a Frame of Reference and zero or more images.
"""
from datetime import datetime, date
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.rt_enums import ROIGenerationAlgorithm
from ..validators.modules.structure_set_validator import StructureSetValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators.validation_result import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class StructureSetModule(BaseModule):
    """Structure Set Module implementation for DICOM PS3.3 C.8.8.5.
    
    Uses composition with internal dataset management for clean separation of concerns.
    Defines a set of areas of significance associated with Frame of Reference and images.
    
    Usage:
        # Create with required elements
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Primary Structure Set",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        # Add optional elements
        structure_set.with_optional_elements(
            structure_set_name="Lung Cancer Structures",
            structure_set_description="Primary target and organs at risk",
            instance_number="1"
        )
        
        # Add frame of reference information
        structure_set.with_frame_of_reference(
            frame_of_reference_uid="*******.*******.9",
            rt_referenced_study_sequence=[
                StructureSetModule.create_rt_referenced_study_item(
                    referenced_sop_class_uid="1.2.840.10008.*******.1",
                    referenced_sop_instance_uid="*******.*******.10"
                )
            ]
        )
        
        # Add ROI definitions
        structure_set.with_roi_definitions([
            StructureSetModule.create_structure_set_roi_item(
                roi_number=1,
                referenced_frame_of_reference_uid="*******.*******.9",
                roi_name="PTV",
                roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
            )
        ])
        
        # Generate dataset for IOD integration
        dataset = structure_set.to_dataset()
        
        # Validate
        result = structure_set.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        structure_set_label: str,
        structure_set_date: str | datetime | date = "",
        structure_set_time: str | datetime = ""
    ) -> 'StructureSetModule':
        """Create StructureSetModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            structure_set_label (str): User-defined label for Structure Set (3006,0002) Type 1
            structure_set_date (str | datetime | date): Date Structure Set was last modified (3006,0008) Type 2
            structure_set_time (str | datetime): Time Structure Set was last modified (3006,0009) Type 2
                
        Returns:
            StructureSetModule: New module instance with required data elements set
        """
        instance = cls()
        instance['StructureSetLabel'] = structure_set_label
        instance['StructureSetDate'] = format_date_value(structure_set_date)
        instance['StructureSetTime'] = format_time_value(structure_set_time)
        return instance
    
    def with_optional_elements(
        self,
        structure_set_name: str | None = None,
        structure_set_description: str | None = None,
        instance_number: str | None = None,
        source_series_information_sequence: list[Dataset] | None = None,
        predecessor_structure_set_sequence: list[Dataset] | None = None
    ) -> 'StructureSetModule':
        """Add optional (Type 3) elements.
        
        Args:
            structure_set_name (str | None): User-defined name for Structure Set (3006,0004) Type 3
            structure_set_description (str | None): User-defined description for Structure Set (3006,0006) Type 3
            instance_number (str | None): A number that identifies this object Instance (0020,0013) Type 3
            source_series_information_sequence (list[Dataset] | None): Information about Image Series sources (3006,004C) Type 3
            predecessor_structure_set_sequence (list[Dataset] | None): Structure Set used to derive current one (3006,0018) Type 3
            
        Returns:
            StructureSetModule: Self for method chaining
        """
        if structure_set_name is not None:
            self.StructureSetName = structure_set_name
        if structure_set_description is not None:
            self.StructureSetDescription = structure_set_description
        if instance_number is not None:
            self.InstanceNumber = instance_number
        if source_series_information_sequence is not None:
            self.SourceSeriesInformationSequence = source_series_information_sequence
        if predecessor_structure_set_sequence is not None:
            self.PredecessorStructureSetSequence = predecessor_structure_set_sequence
        return self
    
    def with_frame_of_reference(
        self,
        frame_of_reference_uid: str,
        rt_referenced_study_sequence: list[Dataset] | None = None
    ) -> 'StructureSetModule':
        """Add referenced frame of reference information (Type 3).
        
        Args:
            frame_of_reference_uid (str): Uniquely identifies Frame of Reference (0020,0052)
            rt_referenced_study_sequence (list[Dataset] | None): Studies containing referenced Series (3006,0012)
            
        Returns:
            StructureSetModule: Self for method chaining
        """
        frame_ref_item = Dataset()
        frame_ref_item.FrameOfReferenceUID = frame_of_reference_uid
        if rt_referenced_study_sequence is not None:
            frame_ref_item.RTReferencedStudySequence = rt_referenced_study_sequence
        
        if 'ReferencedFrameOfReferenceSequence' not in self:
            self.ReferencedFrameOfReferenceSequence = []
        self.ReferencedFrameOfReferenceSequence.append(frame_ref_item)
        return self
    
    def with_roi_definitions(
        self,
        structure_set_roi_sequence: list[Dataset]
    ) -> 'StructureSetModule':
        """Add ROI definitions (Type 3).
        
        Args:
            structure_set_roi_sequence (list[Dataset]): ROIs for current Structure Set (3006,0020)
            
        Returns:
            StructureSetModule: Self for method chaining
        """
        self.StructureSetROISequence = structure_set_roi_sequence
        return self
    
    @staticmethod
    def create_rt_referenced_study_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        rt_referenced_series_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create RT referenced study sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            rt_referenced_series_sequence (list[Dataset] | None): Referenced series within study

        Returns:
            Dataset: RT referenced study sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        if rt_referenced_series_sequence is not None:
            item.RTReferencedSeriesSequence = rt_referenced_series_sequence
        return item
    
    @staticmethod
    def create_rt_referenced_series_item(
        series_instance_uid: str,
        contour_image_sequence: list[Dataset]
    ) -> Dataset:
        """Create RT referenced series sequence item.

        Args:
            series_instance_uid (str): Unique identifier for the Series
            contour_image_sequence (list[Dataset]): Images used in defining Structure Set

        Returns:
            Dataset: RT referenced series sequence item
        """
        item = Dataset()
        item.SeriesInstanceUID = series_instance_uid
        item.ContourImageSequence = contour_image_sequence
        return item
    
    @staticmethod
    def create_contour_image_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> Dataset:
        """Create contour image sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID

        Returns:
            Dataset: Contour image sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        return item
    
    @staticmethod
    def create_structure_set_roi_item(
        roi_number: int,
        referenced_frame_of_reference_uid: str,
        roi_name: str = "",
        roi_generation_algorithm: str | ROIGenerationAlgorithm = "",
        roi_description: str | None = None,
        roi_volume: float | None = None,
        roi_datetime: str | datetime | None = None,
        roi_generation_description: str | None = None,
        conceptual_volume_identification_sequence: list[Dataset] | None = None,
        rt_protocol_code_sequence: list[Dataset] | None = None,
        roi_creator_sequence: list[Dataset] | None = None,
        roi_derivation_algorithm_identification_sequence: list[Dataset] | None = None,
        derivation_code_sequence: list[Dataset] | None = None,
        definition_source_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create structure set ROI sequence item.

        Args:
            roi_number (int): Identification number of the ROI (3006,0022) Type 1
            referenced_frame_of_reference_uid (str): Frame of Reference UID (3006,0024) Type 1
            roi_name (str): User-defined name for ROI (3006,0026) Type 2
            roi_generation_algorithm (str | ROIGenerationAlgorithm): Algorithm used to generate ROI (3006,0036) Type 2
            roi_description (str | None): User-defined description for ROI (3006,0028) Type 3
            roi_volume (float | None): Volume of ROI in cubic centimeters (3006,002C) Type 3
            roi_datetime (str | datetime | None): DateTime when ROI was last modified (3006,002D) Type 3
            roi_generation_description (str | None): Description of technique used (3006,0038) Type 3
            conceptual_volume_identification_sequence (list[Dataset] | None): Conceptual Volume ID (3010,00A0) Type 3
            rt_protocol_code_sequence (list[Dataset] | None): RT Protocol codes (3010,005B) Type 3
            roi_creator_sequence (list[Dataset] | None): Person or device that modified ROI (3006,004D) Type 3
            roi_derivation_algorithm_identification_sequence (list[Dataset] | None): Software algorithm (3006,0037) Type 3
            derivation_code_sequence (list[Dataset] | None): How ROI was derived (0008,9215) Type 3
            definition_source_sequence (list[Dataset] | None): Source of ROI Contour info (0008,1156) Type 3

        Returns:
            Dataset: Structure set ROI sequence item
        """
        item = Dataset()
        item.ROINumber = roi_number
        item.ReferencedFrameOfReferenceUID = referenced_frame_of_reference_uid
        item.ROIName = roi_name
        item.ROIGenerationAlgorithm = format_enum_string(roi_generation_algorithm)

        # Add optional elements if provided
        if roi_description is not None:
            item.ROIDescription = roi_description
        if roi_volume is not None:
            item.ROIVolume = roi_volume
        if roi_datetime is not None:
            if isinstance(roi_datetime, datetime):
                item.ROIDateTime = roi_datetime.strftime("%Y%m%d%H%M%S")
            else:
                item.ROIDateTime = str(roi_datetime)
        if roi_generation_description is not None:
            item.ROIGenerationDescription = roi_generation_description
        if conceptual_volume_identification_sequence is not None:
            item.ConceptualVolumeIdentificationSequence = conceptual_volume_identification_sequence
        if rt_protocol_code_sequence is not None:
            item.RTProtocolCodeSequence = rt_protocol_code_sequence
        if roi_creator_sequence is not None:
            item.ROICreatorSequence = roi_creator_sequence
        if roi_derivation_algorithm_identification_sequence is not None:
            item.ROIDerivationAlgorithmIdentificationSequence = roi_derivation_algorithm_identification_sequence
        if derivation_code_sequence is not None:
            item.DerivationCodeSequence = derivation_code_sequence
        if definition_source_sequence is not None:
            item.DefinitionSourceSequence = definition_source_sequence

        return item
    
    @property
    def has_frame_references(self) -> bool:
        """Check if frame of reference information is present.
        
        Returns:
            bool: True if Referenced Frame of Reference Sequence is present
        """
        return 'ReferencedFrameOfReferenceSequence' in self
    
    @property
    def has_roi_definitions(self) -> bool:
        """Check if ROI definitions are present.
        
        Returns:
            bool: True if Structure Set ROI Sequence is present
        """
        return 'StructureSetROISequence' in self
    
    @property
    def roi_count(self) -> int:
        """Get the number of ROIs defined in this structure set.
        
        Returns:
            int: Number of ROIs in Structure Set ROI Sequence
        """
        if 'StructureSetROISequence' not in self:
            return 0
        return len(self.StructureSetROISequence)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Structure Set Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return StructureSetValidator.validate(self._dataset, config)
