"""
Contrast/Bolus Module - DICOM PS3.3 C.7.6.4

The Contrast/Bolus Module contains attributes that describe the contrast/bolus 
used in the acquisition of the Image.
"""
from datetime import time
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.image_enums import ContrastBolusIngredient, ContrastBolusAgent
from ..validators.modules.contrast_bolus_validator import ContrastBolusValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_time_value, format_enum_string


class ContrastBolusModule(BaseModule):
    """Contrast/Bolus Module implementation for DICOM PS3.3 C.7.6.4.
    
    Uses composition with internal dataset management for cleaner separation
    of concerns. Contains attributes that describe the contrast/bolus agent 
    used in the acquisition of the Image.
    
    Usage:
        # Create with required elements using standardized agent enum
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent=ContrastBolusAgent.IOHEXOL
        )
        
        # Add optional elements
        contrast_bolus.with_optional_elements(
            contrast_bolus_agent_sequence=[
                ContrastBolusModule.create_contrast_agent_code_item(
                    code_value="C-B0322",
                    coding_scheme_designator="SRT",
                    code_meaning="Iodinated contrast agent"
                )
            ],
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0,
            contrast_bolus_start_time="120000",
            contrast_bolus_stop_time="120030",
            contrast_bolus_total_dose=50.0,
            contrast_flow_rate=[5.0],
            contrast_flow_duration=[20.0],
            contrast_bolus_ingredient=ContrastBolusIngredient.IOHEXOL,
            contrast_bolus_ingredient_concentration=370.0
        )
        
        # Add administration route with additional drugs
        contrast_bolus.with_administration_route(
            administration_route_sequence=[
                ContrastBolusModule.create_administration_route_code_item(
                    code_value="47625008",
                    coding_scheme_designator="SCT",
                    code_meaning="Intravenous route"
                )
            ],
            additional_drug_sequence=[
                ContrastBolusModule.create_additional_drug_code_item(
                    code_value="387467008",
                    coding_scheme_designator="SCT",
                    code_meaning="Saline"
                )
            ]
        )
        
        # Generate dataset for IOD integration
        dataset = contrast_bolus.to_dataset()
        
        # Validate
        result = contrast_bolus.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        # Type 2 elements as kwargs with empty string defaults (required but can be empty)
        contrast_bolus_agent: str | ContrastBolusAgent = ""
    ) -> 'ContrastBolusModule':
        """Create Contrast/Bolus Module with all required (Type 2) elements.

        Args:
            contrast_bolus_agent (str | ContrastBolusAgent): Contrast or bolus agent (0018,0010) Type 2.
                Use ContrastBolusAgent enum for standardized agents: ContrastBolusAgent.IOHEXOL,
                ContrastBolusAgent.GADOPENTETATE, etc. String values are also accepted.
        """
        instance = cls()
        
        # Set Type 2 elements - required but can be empty
        instance['ContrastBolusAgent'] = format_enum_string(contrast_bolus_agent)
        
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        contrast_bolus_agent_sequence: list[Dataset] | None = None,
        contrast_bolus_route: str | None = None,
        contrast_bolus_volume: float | None = None,
        contrast_bolus_start_time: str | time | None = None,
        contrast_bolus_stop_time: str | time | None = None,
        contrast_bolus_total_dose: float | None = None,
        contrast_flow_rate: list[float] | None = None,
        contrast_flow_duration: list[float] | None = None,
        contrast_bolus_ingredient: str | ContrastBolusIngredient | None = None,
        contrast_bolus_ingredient_concentration: float | None = None
    ) -> 'ContrastBolusModule':
        """Add optional (Type 3) elements.
        
        Args:
            contrast_bolus_agent_sequence: Contrast agent sequence (0018,0012) Type 3
            contrast_bolus_route: Administration route (0018,1040) Type 3
            contrast_bolus_volume: Volume injected in milliliters (0018,1041) Type 3
            contrast_bolus_start_time: Time of start of injection (0018,1042) Type 3
            contrast_bolus_stop_time: Time of end of contrast injection (0018,1043) Type 3
            contrast_bolus_total_dose: Total amount in milliliters of undiluted agent (0018,1044) Type 3
            contrast_flow_rate: Rate(s) of injection(s) in milliliters/sec (0018,1046) Type 3
            contrast_flow_duration: Duration(s) of injection(s) in seconds (0018,1047) Type 3
            contrast_bolus_ingredient: Active ingredient of agent (0018,1048) Type 3
            contrast_bolus_ingredient_concentration: Milligrams per milliliter (0018,1049) Type 3
        """
        # Validate flow rate and flow duration consistency before setting
        if contrast_flow_rate is not None and contrast_flow_duration is not None:
            if len(contrast_flow_rate) != len(contrast_flow_duration):
                raise ValueError(
                    f"Contrast Flow Rate and Contrast Flow Duration must have the same number of values. "
                    f"Flow Rate has {len(contrast_flow_rate)} values, Flow Duration has {len(contrast_flow_duration)} values. "
                    f"Each Contrast Flow Duration Value shall correspond to a Value of Contrast Flow Rate (DICOM PS3.3 C.7.6.4)"
                )
        
        # Validate logical consistency between volume and total dose
        if contrast_bolus_volume is not None and contrast_bolus_total_dose is not None:
            if contrast_bolus_total_dose > contrast_bolus_volume:
                raise ValueError(
                    f"Contrast/Bolus Total Dose ({contrast_bolus_total_dose} ml) cannot exceed "
                    f"Contrast/Bolus Volume ({contrast_bolus_volume} ml). "
                    f"Total dose represents undiluted contrast agent amount"
                )
        
        # Set Type 3 elements using helper methods
        if contrast_bolus_agent_sequence is not None:
            self.ContrastBolusAgentSequence = contrast_bolus_agent_sequence
            
        if contrast_bolus_route is not None:
            self.ContrastBolusRoute = contrast_bolus_route
            
        if contrast_bolus_volume is not None:
            self.ContrastBolusVolume = contrast_bolus_volume
        
        # Format time values using utility functions
        if contrast_bolus_start_time is not None:
            self.ContrastBolusStartTime = format_time_value(contrast_bolus_start_time)
        
        if contrast_bolus_stop_time is not None:
            self.ContrastBolusStopTime = format_time_value(contrast_bolus_stop_time)
        
        if contrast_bolus_total_dose is not None:
            self.ContrastBolusTotalDose = contrast_bolus_total_dose
            
        if contrast_flow_rate is not None:
            self.ContrastFlowRate = contrast_flow_rate
            
        if contrast_flow_duration is not None:
            self.ContrastFlowDuration = contrast_flow_duration
            
        # Format enum value using utility function
        if contrast_bolus_ingredient is not None:
            self.ContrastBolusIngredient = format_enum_string(contrast_bolus_ingredient)
            
        if contrast_bolus_ingredient_concentration is not None:
            self.ContrastBolusIngredientConcentration = contrast_bolus_ingredient_concentration
        
        return self
    
    def with_administration_route(
        self,
        administration_route_sequence: list[Dataset] | None = None,
        additional_drug_sequence: list[Dataset] | None = None
    ) -> 'ContrastBolusModule':
        """Add administration route information.
        
        According to DICOM PS3.3 C.7.6.4, the Administration Route Sequence should 
        contain only a single Item. Additional Drug Sequence is nested within the 
        Administration Route Sequence items.
        
        Args:
            administration_route_sequence: Route of administration sequence (0018,0014) Type 3
                Should contain only a single Item per DICOM standard
            additional_drug_sequence: Additional drug sequence (0018,002A) Type 3
                Nested within Administration Route Sequence items
        """
        # Validate single item constraint per DICOM standard
        if administration_route_sequence is not None and len(administration_route_sequence) > 1:
            raise ValueError(
                f"Administration Route Sequence (0018,0014) should contain only a single Item "
                f"according to DICOM PS3.3 C.7.6.4. Provided {len(administration_route_sequence)} items."
            )
        
        if administration_route_sequence is not None:
            self.ContrastBolusAdministrationRouteSequence = administration_route_sequence
        
        # Additional drug sequence is nested within administration route sequence
        if administration_route_sequence and additional_drug_sequence:
            for route_item in administration_route_sequence:
                route_item.AdditionalDrugSequence = additional_drug_sequence
        
        return self
    
    @staticmethod
    def create_contrast_agent_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str | None = None
    ) -> Dataset:
        """Create Contrast/Bolus Agent Sequence code item.
        
        Args:
            code_value: Code value for contrast agent
            coding_scheme_designator: Coding scheme designator
            code_meaning: Human readable meaning of the code
            
        Returns:
            pydicom.dataset.Dataset representing contrast agent code item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        if code_meaning:
            item.CodeMeaning = code_meaning
        return item
    
    @staticmethod
    def create_administration_route_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str | None = None
    ) -> Dataset:
        """Create Administration Route Sequence code item.
        
        Args:
            code_value: Code value for administration route
            coding_scheme_designator: Coding scheme designator
            code_meaning: Human readable meaning of the code
            
        Returns:
            pydicom.dataset.Dataset representing administration route code item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        if code_meaning:
            item.CodeMeaning = code_meaning
        return item
    
    @staticmethod
    def create_additional_drug_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str | None = None
    ) -> Dataset:
        """Create Additional Drug Sequence code item.
        
        Args:
            code_value: Code value for additional drug
            coding_scheme_designator: Coding scheme designator
            code_meaning: Human readable meaning of the code
            
        Returns:
            pydicom.dataset.Dataset representing additional drug code item
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        if code_meaning:
            item.CodeMeaning = code_meaning
        return item
    
    @property
    def has_contrast_agent(self) -> bool:
        """Check if contrast/bolus agent is specified.
        
        Returns:
            bool: True if Contrast/Bolus Agent is present and not empty
        """
        return 'ContrastBolusAgent' in self and bool(self.ContrastBolusAgent)
    
    @property
    def has_agent_sequence(self) -> bool:
        """Check if contrast/bolus agent sequence is present.
        
        Returns:
            bool: True if Contrast/Bolus Agent Sequence is present
        """
        return 'ContrastBolusAgentSequence' in self
    
    @property
    def has_administration_route(self) -> bool:
        """Check if administration route information is present.
        
        Returns:
            bool: True if route or route sequence is present
        """
        return ('ContrastBolusRoute' in self or 
                'ContrastBolusAdministrationRouteSequence' in self)
    
    @property
    def has_timing_information(self) -> bool:
        """Check if timing information is present.
        
        Returns:
            bool: True if start time, stop time, or flow duration is present
        """
        return ('ContrastBolusStartTime' in self or 
                'ContrastBolusStopTime' in self or
                'ContrastFlowDuration' in self)
    
    @property
    def has_volume_information(self) -> bool:
        """Check if volume or dose information is present.
        
        Returns:
            bool: True if volume, total dose, or flow rate is present
        """
        return ('ContrastBolusVolume' in self or 
                'ContrastBolusTotalDose' in self or
                'ContrastFlowRate' in self)
    
    @property
    def has_ingredient_information(self) -> bool:
        """Check if ingredient information is present.
        
        Returns:
            bool: True if ingredient or concentration is present
        """
        return ('ContrastBolusIngredient' in self or 
                'ContrastBolusIngredientConcentration' in self)
    
    @property
    def is_iodine_based(self) -> bool:
        """Check if contrast agent is iodine-based.
        
        Returns:
            bool: True if ingredient is iodine-based
        """
        return 'ContrastBolusIngredient' in self and str(self.ContrastBolusIngredient).startswith("IO")
    
    @property
    def is_gadolinium_based(self) -> bool:
        """Check if contrast agent is gadolinium-based.
        
        Returns:
            bool: True if ingredient is gadolinium-based
        """
        return 'ContrastBolusIngredient' in self and str(self.ContrastBolusIngredient).startswith("GADO")
    
    def get_flow_rate_count(self) -> int:
        """Get the number of flow rate values.
        
        Returns:
            int: Number of flow rate values
        """
        if 'ContrastFlowRate' not in self:
            return 0
        flow_rates = self.ContrastFlowRate
        if not flow_rates:
            return 0
        # Handle both single values (DSfloat) and lists
        if hasattr(flow_rates, '__iter__') and not isinstance(flow_rates, str):
            return len(flow_rates)
        else:
            return 1  # Single value
    
    def get_flow_duration_count(self) -> int:
        """Get the number of flow duration values.
        
        Returns:
            int: Number of flow duration values
        """
        if 'ContrastFlowDuration' not in self:
            return 0
        flow_durations = self.ContrastFlowDuration
        if not flow_durations:
            return 0
        # Handle both single values (DSfloat) and lists  
        if hasattr(flow_durations, '__iter__') and not isinstance(flow_durations, str):
            return len(flow_durations)
        else:
            return 1  # Single value
    
    @property
    def has_flow_rate_duration_consistency(self) -> bool:
        """Check if flow rate and flow duration have consistent counts.
        
        According to DICOM PS3.3 C.7.6.4: "Each Contrast Flow Duration Value 
        shall correspond to a Value of Contrast Flow Rate (0018,1046)."
        
        Returns:
            bool: True if both are absent or have matching counts
        """
        flow_rate_count = self.get_flow_rate_count()
        flow_duration_count = self.get_flow_duration_count()
        
        # If neither is present, consistency is maintained
        if flow_rate_count == 0 and flow_duration_count == 0:
            return True
            
        # If only one is present, consistency is maintained
        if flow_rate_count == 0 or flow_duration_count == 0:
            return True
            
        # If both are present, they must have the same count
        return flow_rate_count == flow_duration_count
    
    @property
    def has_dose_volume_consistency(self) -> bool:
        """Check logical consistency between total dose and volume.
        
        According to DICOM standard example: total dose represents undiluted 
        contrast agent amount and should not exceed the injected volume.
        
        Returns:
            bool: True if consistent or if either value is missing
        """
        volume = self.ContrastBolusVolume if 'ContrastBolusVolume' in self else None
        total_dose = self.ContrastBolusTotalDose if 'ContrastBolusTotalDose' in self else None
        
        # If either is missing, consistency check passes
        if volume is None or total_dose is None:
            return True
            
        # Total dose should not exceed volume
        return total_dose <= volume
    
    @property
    def has_ingredient_concentration_pairing(self) -> bool:
        """Check if ingredient and concentration are appropriately paired.
        
        While both are optional, they logically should be used together 
        for complete contrast agent characterization.
        
        Returns:
            bool: True if both present, both absent, or appropriately paired
        """
        ingredient = self.ContrastBolusIngredient if 'ContrastBolusIngredient' in self else ''
        concentration = self.ContrastBolusIngredientConcentration if 'ContrastBolusIngredientConcentration' in self else None
        
        # Both present or both absent is ideal
        both_present = bool(ingredient) and concentration is not None
        both_absent = not bool(ingredient) and concentration is None
        
        return both_present or both_absent
    
    # Public convenience methods for specific validation checks with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return ContrastBolusValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()

    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return ContrastBolusValidator.validate_conditional_requirements(self)

    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return ContrastBolusValidator.validate_enumerated_values(self)

    def check_sequence_structures(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return ContrastBolusValidator.validate_sequence_structures(self)

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        return ContrastBolusValidator.validate(self, config)  # Direct self reference for performance

    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.

        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            ContrastBolusValidator.validate_required_elements,
            "Required elements validation failed"
        )

    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.

        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            ContrastBolusValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )

    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.

        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            ContrastBolusValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )

    def _ensure_sequence_structures_valid(self) -> None:
        """Ensure sequence structures are valid, raise exception if not.

        Raises:
            ValidationError: If sequence structures are invalid
        """
        self._validate_and_raise(
            ContrastBolusValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )
