"""
RT Image Module - DICOM PS3.3 C.8.8.2

The RT Image Module describes RT-specific characteristics of a projection image.
The image described by these Attributes must be a radiotherapy image acquired
or calculated using a conical imaging geometry.
"""

from pydicom import Dataset
from .base_module import BaseModule
from ..utils.dicom_formatters import format_enum_int, format_enum_string
from ..enums.rt_enums import (
    BlockType,
    ConversionType,
    FluenceDataSource,
    ReportedValuesOrigin,
    RTImagePlane,
    PrimaryDosimeterUnit,
    PixelIntensityRelationshipSign,
    RTBeamLimitingDeviceType,
    BlockDivergence,
    BlockMountingPosition,
    EnhancedRTBeamLimitingDeviceDefinitionFlag,
)
from ..enums.image_enums import PhotometricInterpretation, PixelRepresentation
from ..enums.series_enums import PatientPosition
from ..validators.modules.rt_image_validator import RTImageValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult


class RTImageModule(BaseModule):
    """RT Image Module implementation for DICOM PS3.3 C.8.8.2.

    Uses composition with internal dataset management rather than
    inheriting from pydicom.Dataset for cleaner separation of concerns.
    Describes RT-specific characteristics of a projection image acquired
    or calculated using a conical imaging geometry.

    Usage:
        # Create with required elements
        rt_image = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image 1",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            conversion_type=ConversionType.DI,
            rt_image_plane=RTImagePlane.NORMAL
        )

        # Add optional elements
        rt_image.with_optional_elements(
            rt_image_name="Portal verification image",
            rt_image_description="Verification image for beam 1",
            pixel_intensity_relationship="LIN",
            radiation_machine_name="Varian TrueBeam",
            primary_dosimeter_unit=PrimaryDosimeterUnit.MU,
            radiation_machine_sad=1000.0,
            rt_image_sid=1500.0
        )

        # Add conditional elements
        rt_image.with_pixel_intensity_relationship_sign(
            pixel_intensity_relationship_sign=PixelIntensityRelationshipSign.POSITIVE
        )

        # Add exposure information
        rt_image.with_exposure_sequence([
            rt_image.create_exposure_item(
                kvp=120.0,
                x_ray_tube_current=200.0,
                exposure_time=100.0,
                meterset_exposure=50.0
            )
        ])

        # Generate dataset for IOD integration
        dataset = rt_image.to_dataset()
        
        # Validate
        result = rt_image.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        samples_per_pixel: int,
        photometric_interpretation: str | PhotometricInterpretation,
        bits_allocated: int,
        bits_stored: int,
        high_bit: int,
        pixel_representation: str | PixelRepresentation,
        rt_image_label: str,
        image_type: list[str],
        conversion_type: str | ConversionType = "",
        rt_image_plane: str | RTImagePlane = "",
    ) -> "RTImageModule":
        """Create RTImageModule from all required (Type 1 and Type 2) data elements.

        Args:
            samples_per_pixel (int): Number of samples (planes) in this image (0028,0002) Type 1
            photometric_interpretation (str | PhotometricInterpretation): Interpretation of pixel data (0028,0004) Type 1
            bits_allocated (int): Number of bits allocated for each pixel sample (0028,0100) Type 1
            bits_stored (int): Number of bits stored for each pixel sample (0028,0101) Type 1
            high_bit (int): Most significant bit for each pixel sample (0028,0102) Type 1
            pixel_representation (str | PixelRepresentation): Data representation of pixel samples (0028,0103) Type 1
            rt_image_label (str): User-defined label for RT Image (3002,0002) Type 1
            image_type (list[str]): Image identification characteristics (0008,0008) Type 1
            conversion_type (str | ConversionType): Kind of image conversion (0008,0064) Type 2
            rt_image_plane (str | RTImagePlane): Whether image plane is normal to beam axis (3002,000C) Type 1

        Returns:
            RTImageModule: New module instance with required data elements set
        """
        instance = cls()
        instance['SamplesPerPixel'] = samples_per_pixel
        instance['PhotometricInterpretation'] = format_enum_string(
            photometric_interpretation
        )
        instance['BitsAllocated'] = bits_allocated
        instance['BitsStored'] = bits_stored
        instance['HighBit'] = high_bit
        instance['PixelRepresentation'] = format_enum_int(pixel_representation)
        instance['RTImageLabel'] = rt_image_label
        instance['ImageType'] = image_type
        instance['ConversionType'] = format_enum_string(conversion_type)
        instance['RTImagePlane'] = format_enum_string(rt_image_plane)
        return instance

    def with_optional_elements(
        self,
        rt_image_name: str | None = None,
        rt_image_description: str | None = None,
        pixel_intensity_relationship: str | None = None,
        x_ray_image_receptor_translation: list[float] | None = None,
        x_ray_image_receptor_angle: float | None = None,
        image_plane_pixel_spacing: list[float] | None = None,
        rt_image_position: list[float] | None = None,
        radiation_machine_name: str | None = None,
        primary_dosimeter_unit: str | PrimaryDosimeterUnit | None = None,
        radiation_machine_sad: float | None = None,
        radiation_machine_ssd: float | None = None,
        rt_image_sid: float | None = None,
        source_to_reference_object_distance: float | None = None,
        referenced_rt_plan_sequence: list[Dataset] | None = None,
        referenced_beam_number: int | None = None,
        referenced_fraction_group_number: int | None = None,
        fraction_number: int | None = None,
        clinical_fraction_number: int | None = None,
        start_cumulative_meterset_weight: float | None = None,
        end_cumulative_meterset_weight: float | None = None,
        gantry_angle: float | None = None,
        gantry_pitch_angle: float | None = None,
        beam_limiting_device_angle: float | None = None,
        patient_support_angle: float | None = None,
        table_top_pitch_angle: float | None = None,
        table_top_roll_angle: float | None = None,
        table_top_vertical_position: float | None = None,
        table_top_longitudinal_position: float | None = None,
        table_top_lateral_position: float | None = None,
        table_top_eccentric_axis_distance: float | None = None,
        table_top_eccentric_angle: float | None = None,
        isocenter_position: list[float] | None = None,
        exposure_time: float | None = None,
        exposure_time_in_ms: float | None = None,
        meterset_exposure: float | None = None,
        enhanced_rt_beam_limiting_device_definition_flag: (
            str | EnhancedRTBeamLimitingDeviceDefinitionFlag | None
        ) = None,
    ) -> "RTImageModule":
        """Add optional (Type 3) elements.

        Args:
            rt_image_name (str | None): User-defined name for RT Image (3002,0003) Type 3
            rt_image_description (str | None): User-defined description of RT Image (3002,0004) Type 3
            pixel_intensity_relationship (str | None): Relationship between pixel values and X-Ray beam intensity (0028,1040) Type 3
            x_ray_image_receptor_translation (list[float] | None): Position of IEC X-RAY IMAGE RECEPTOR System (3002,000D) Type 3
            x_ray_image_receptor_angle (float | None): X-Ray Image Receptor Angle (3002,000E) Type 2
            image_plane_pixel_spacing (list[float] | None): Physical distance between pixel centers (3002,0011) Type 2
            rt_image_position (list[float] | None): Coordinates of upper left corner (3002,0012) Type 2
            radiation_machine_name (str | None): Name identifying radiation machine (3002,0020) Type 2
            primary_dosimeter_unit (str | PrimaryDosimeterUnit | None): Measurement unit of machine dosimeter (300A,00B3) Type 2
            radiation_machine_sad (float | None): Source to Gantry rotation axis distance (3002,0022) Type 2
            radiation_machine_ssd (float | None): Source to Patient surface distance (3002,0024) Type 3
            rt_image_sid (float | None): Distance from source to image plane (3002,0026) Type 2
            source_to_reference_object_distance (float | None): Source to reference object distance (3002,0028) Type 3
            referenced_rt_plan_sequence (list[Dataset] | None): RT Plan associated with image (300C,0002) Type 3
            referenced_beam_number (int | None): Beam Number within RT Plan (300C,0006) Type 3
            referenced_fraction_group_number (int | None): Fraction Group identifier (300C,0022) Type 3
            fraction_number (int | None): Fraction Number during which image was acquired (3002,0029) Type 3
            clinical_fraction_number (int | None): RT Treatment Fraction identifier (300A,0705) Type 3
            start_cumulative_meterset_weight (float | None): Cumulative Meterset Weight at start (300C,0008) Type 3
            end_cumulative_meterset_weight (float | None): Cumulative Meterset Weight at end (300C,0009) Type 3
            gantry_angle (float | None): Treatment machine gantry angle (300A,011E) Type 3
            gantry_pitch_angle (float | None): Gantry Pitch Angle (300A,014A) Type 3
            beam_limiting_device_angle (float | None): Beam limiting device angle (300A,0120) Type 3
            patient_support_angle (float | None): Patient Support angle (300A,0122) Type 3
            table_top_pitch_angle (float | None): Table Top Pitch Angle (300A,0140) Type 3
            table_top_roll_angle (float | None): Table Top Roll Angle (300A,0144) Type 3
            table_top_vertical_position (float | None): Table Top Vertical position (300A,0128) Type 3
            table_top_longitudinal_position (float | None): Table Top Longitudinal position (300A,0129) Type 3
            table_top_lateral_position (float | None): Table Top Lateral position (300A,012A) Type 3
            table_top_eccentric_axis_distance (float | None): Distance from IEC PATIENT SUPPORT to IEC TABLE TOP ECCENTRIC (300A,0124) Type 3
            table_top_eccentric_angle (float | None): Table Top (non-isocentric) angle (300A,0125) Type 3
            isocenter_position (list[float] | None): Isocenter coordinates (x,y,z) in mm (300A,012C) Type 3
            exposure_time (float | None): Time of X-Ray exposure in msec (0018,1150) Type 3
            exposure_time_in_ms (float | None): Duration of X-Ray exposure in msec (0018,9328) Type 3
            meterset_exposure (float | None): Treatment machine Meterset duration (3002,0032) Type 3
            enhanced_rt_beam_limiting_device_definition_flag (str | EnhancedRTBeamLimitingDeviceDefinitionFlag | None): Enhanced RT Beam Limiting Device flag (3008,00A3) Type 3

        Returns:
            RTImageModule: Self for method chaining
        """
        if rt_image_name is not None:
            self.RTImageName = rt_image_name
        if rt_image_description is not None:
            self.RTImageDescription = rt_image_description
        if pixel_intensity_relationship is not None:
            self.PixelIntensityRelationship = pixel_intensity_relationship
        if x_ray_image_receptor_translation is not None:
            self.XRayImageReceptorTranslation = x_ray_image_receptor_translation
        if x_ray_image_receptor_angle is not None:
            self.XRayImageReceptorAngle = x_ray_image_receptor_angle
        if image_plane_pixel_spacing is not None:
            self.ImagePlanePixelSpacing = image_plane_pixel_spacing
        if rt_image_position is not None:
            self.RTImagePosition = rt_image_position
        if radiation_machine_name is not None:
            self.RadiationMachineName = radiation_machine_name
        if primary_dosimeter_unit is not None:
            self.PrimaryDosimeterUnit = format_enum_string(primary_dosimeter_unit)
        if radiation_machine_sad is not None:
            self.RadiationMachineSAD = radiation_machine_sad
        if radiation_machine_ssd is not None:
            self.RadiationMachineSSD = radiation_machine_ssd
        if rt_image_sid is not None:
            self.RTImageSID = rt_image_sid
        if source_to_reference_object_distance is not None:
            self.SourceToReferenceObjectDistance = source_to_reference_object_distance
        if referenced_rt_plan_sequence is not None:
            self.ReferencedRTPlanSequence = referenced_rt_plan_sequence
        if referenced_beam_number is not None:
            self.ReferencedBeamNumber = referenced_beam_number
        if referenced_fraction_group_number is not None:
            self.ReferencedFractionGroupNumber = referenced_fraction_group_number
        if fraction_number is not None:
            self.FractionNumber = fraction_number
        if clinical_fraction_number is not None:
            self.ClinicalFractionNumber = clinical_fraction_number
        if start_cumulative_meterset_weight is not None:
            self.StartCumulativeMetersetWeight = start_cumulative_meterset_weight
        if end_cumulative_meterset_weight is not None:
            self.EndCumulativeMetersetWeight = end_cumulative_meterset_weight
        if gantry_angle is not None:
            self.GantryAngle = gantry_angle
        if gantry_pitch_angle is not None:
            self.GantryPitchAngle = gantry_pitch_angle
        if beam_limiting_device_angle is not None:
            self.BeamLimitingDeviceAngle = beam_limiting_device_angle
        if patient_support_angle is not None:
            self.PatientSupportAngle = patient_support_angle
        if table_top_pitch_angle is not None:
            self.TableTopPitchAngle = table_top_pitch_angle
        if table_top_roll_angle is not None:
            self.TableTopRollAngle = table_top_roll_angle
        if table_top_vertical_position is not None:
            self.TableTopVerticalPosition = table_top_vertical_position
        if table_top_longitudinal_position is not None:
            self.TableTopLongitudinalPosition = table_top_longitudinal_position
        if table_top_lateral_position is not None:
            self.TableTopLateralPosition = table_top_lateral_position
        if table_top_eccentric_axis_distance is not None:
            self.TableTopEccentricAxisDistance = table_top_eccentric_axis_distance
        if table_top_eccentric_angle is not None:
            self.TableTopEccentricAngle = table_top_eccentric_angle
        if isocenter_position is not None:
            self.IsocenterPosition = isocenter_position
        if exposure_time is not None:
            self.ExposureTime = exposure_time
        if exposure_time_in_ms is not None:
            self.ExposureTimeInms = exposure_time_in_ms
        if meterset_exposure is not None:
            self.MetersetExposure = meterset_exposure
        if enhanced_rt_beam_limiting_device_definition_flag is not None:
            self.EnhancedRTBeamLimitingDeviceDefinitionFlag = format_enum_string(
                enhanced_rt_beam_limiting_device_definition_flag
            )
        return self

    def with_pixel_intensity_relationship_sign(
        self, pixel_intensity_relationship_sign: int | PixelIntensityRelationshipSign
    ) -> "RTImageModule":
        """Add pixel intensity relationship sign (Type 1C - required if Pixel Intensity Relationship is present).

        Args:
            pixel_intensity_relationship_sign (int | PixelIntensityRelationshipSign): Sign of relationship (0028,1041) Type 1C

        Returns:
            RTImageModule: Self for method chaining

        Note:
            Type 1C validation (relationship to Pixel Intensity Relationship) is handled by the validator.
        """
        self.PixelIntensityRelationshipSign = format_enum_int(
            pixel_intensity_relationship_sign
        )
        return self

    def with_rt_image_orientation(
        self, rt_image_orientation: list[float]
    ) -> "RTImageModule":
        """Add RT image orientation (Type 2C - required if RT Image Plane is NON_NORMAL).

        Args:
            rt_image_orientation (list[float]): Direction cosines of first row and column (3002,0010) Type 2C

        Returns:
            RTImageModule: Self for method chaining

        Note:
            Type 2C validation (relationship to RT Image Plane) is handled by the validator.
        """
        self.RTImageOrientation = rt_image_orientation
        return self

    def with_reported_values_origin(
        self, reported_values_origin: str | ReportedValuesOrigin
    ) -> "RTImageModule":
        """Add reported values origin (Type 2C - required if Image Type Value 3 is SIMULATOR or PORTAL).

        Args:
            reported_values_origin (str | ReportedValuesOrigin): Origin of parameter values (3002,000A) Type 2C

        Returns:
            RTImageModule: Self for method chaining

        Note:
            Type 2C validation (relationship to Image Type Value 3) is handled by the validator.
        """
        self.ReportedValuesOrigin = format_enum_string(reported_values_origin)
        return self

    def with_patient_position(
        self, patient_position: str | PatientPosition
    ) -> "RTImageModule":
        """Add patient position (Type 1C - required if Isocenter Position is present).

        Args:
            patient_position (str | PatientPosition): Patient position descriptor (0018,5100) Type 1C

        Returns:
            RTImageModule: Self for method chaining

        Note:
            Type 1C validation (relationship to Isocenter Position) is handled by the validator.
        """
        self.PatientPosition = format_enum_string(patient_position)
        return self

    def with_exposure_sequence(
        self, exposure_sequence: list[Dataset]
    ) -> "RTImageModule":
        """Add exposure sequence (Type 3).

        Args:
            exposure_sequence (list[Dataset]): Sequence of Exposure parameter sets (3002,0030) Type 3

        Returns:
            RTImageModule: Self for method chaining
        """
        self.ExposureSequence = exposure_sequence
        return self

    def with_fluence_map_sequence(
        self,
        fluence_data_source: str | FluenceDataSource,
        fluence_data_scale: float | None = None,
    ) -> "RTImageModule":
        """Add fluence map sequence (Type 1C - required if Image Type Value 3 is FLUENCE).

        Args:
            fluence_data_source (str | FluenceDataSource): Source of fluence data (3002,0041) Type 1
            fluence_data_scale (float | None): Meterset corresponding to fluence map cell value of 1.0 (3002,0042) Type 3

        Returns:
            RTImageModule: Self for method chaining

        Note:
            Type 1C validation (relationship to Image Type Value 3) is handled by the validator.
        """
        fluence_item = Dataset()
        fluence_item.FluenceDataSource = format_enum_string(fluence_data_source)
        if fluence_data_scale is not None:
            fluence_item.FluenceDataScale = fluence_data_scale

        self.FluenceMapSequence = [fluence_item]
        return self

    @staticmethod
    def create_exposure_item(
        referenced_frame_number: int | None = None,
        kvp: float | None = None,
        x_ray_tube_current: float | None = None,
        x_ray_tube_current_in_ma: float | None = None,
        exposure_time: float | None = None,
        exposure_time_in_ms: float | None = None,
        meterset_exposure: float | None = None,
        diaphragm_position: list[float] | None = None,
        primary_fluence_mode_sequence: list[Dataset] | None = None,
        beam_limiting_device_sequence: list[Dataset] | None = None,
        enhanced_rt_beam_limiting_opening_sequence: list[Dataset] | None = None,
        gantry_angle: float | None = None,
        gantry_pitch_angle: float | None = None,
        beam_limiting_device_angle: float | None = None,
        patient_support_angle: float | None = None,
        table_top_pitch_angle: float | None = None,
        table_top_roll_angle: float | None = None,
        table_top_vertical_position: float | None = None,
        table_top_longitudinal_position: float | None = None,
        table_top_lateral_position: float | None = None,
        applicator_sequence: list[Dataset] | None = None,
        general_accessory_sequence: list[Dataset] | None = None,
        number_of_blocks: int | None = None,
        block_sequence: list[Dataset] | None = None,
    ) -> Dataset:
        """Create exposure sequence item.

        Args:
            referenced_frame_number (int | None): Frame number for multi-frame images (0008,1160) Type 1C
            kvp (float | None): Peak kilo voltage output (0018,0060) Type 2C
            x_ray_tube_current (float | None): X-Ray Tube Current in mA (0018,1151) Type 2C
            x_ray_tube_current_in_ma (float | None): X-Ray Tube Current in mA (0018,9330) Type 3
            exposure_time (float | None): Time of X-Ray exposure in msec (0018,1150) Type 2C
            exposure_time_in_ms (float | None): Duration of X-Ray exposure in msec (0018,9328) Type 3
            meterset_exposure (float | None): Treatment machine Meterset duration (3002,0032) Type 2C
            diaphragm_position (list[float] | None): Positions of diaphragm jaw pairs (3002,0034) Type 3
            primary_fluence_mode_sequence (list[Dataset] | None): Primary fluence mode (3002,0050) Type 3
            beam_limiting_device_sequence (list[Dataset] | None): Beam limiting device positions (300A,00B6) Type 3
            enhanced_rt_beam_limiting_opening_sequence (list[Dataset] | None): Enhanced beam limiting openings (3008,00A2) Type 2C
            gantry_angle (float | None): Gantry angle (300A,011E) Type 3
            gantry_pitch_angle (float | None): Gantry Pitch Angle (300A,014A) Type 3
            beam_limiting_device_angle (float | None): Beam limiting device angle (300A,0120) Type 3
            patient_support_angle (float | None): Patient Support angle (300A,0122) Type 3
            table_top_pitch_angle (float | None): Table Top Pitch Angle (300A,0140) Type 3
            table_top_roll_angle (float | None): Table Top Roll Angle (300A,0144) Type 3
            table_top_vertical_position (float | None): Table Top Vertical position (300A,0128) Type 3
            table_top_longitudinal_position (float | None): Table Top Longitudinal position (300A,0129) Type 3
            table_top_lateral_position (float | None): Table Top Lateral position (300A,012A) Type 3
            applicator_sequence (list[Dataset] | None): Applicators associated with Beam (300A,0107) Type 3
            general_accessory_sequence (list[Dataset] | None): General Accessories (300A,0420) Type 3
            number_of_blocks (int | None): Number of shielding blocks (300A,00F0) Type 1
            block_sequence (list[Dataset] | None): Blocks associated with Beam (300A,00F4) Type 2C

        Returns:
            Dataset: Exposure sequence item
        """
        item = Dataset()

        # Add elements if provided
        if referenced_frame_number is not None:
            item.ReferencedFrameNumber = referenced_frame_number
        if kvp is not None:
            item.KVP = kvp
        if x_ray_tube_current is not None:
            item.XRayTubeCurrent = x_ray_tube_current
        if x_ray_tube_current_in_ma is not None:
            item.XRayTubeCurrentInmA = x_ray_tube_current_in_ma
        if exposure_time is not None:
            item.ExposureTime = exposure_time
        if exposure_time_in_ms is not None:
            item.ExposureTimeInms = exposure_time_in_ms
        if meterset_exposure is not None:
            item.MetersetExposure = meterset_exposure
        if diaphragm_position is not None:
            item.DiaphragmPosition = diaphragm_position
        if primary_fluence_mode_sequence is not None:
            item.PrimaryFluenceModeSequence = primary_fluence_mode_sequence
        if beam_limiting_device_sequence is not None:
            item.BeamLimitingDeviceSequence = beam_limiting_device_sequence
        if enhanced_rt_beam_limiting_opening_sequence is not None:
            item.EnhancedRTBeamLimitingOpeningSequence = (
                enhanced_rt_beam_limiting_opening_sequence
            )
        if gantry_angle is not None:
            item.GantryAngle = gantry_angle
        if gantry_pitch_angle is not None:
            item.GantryPitchAngle = gantry_pitch_angle
        if beam_limiting_device_angle is not None:
            item.BeamLimitingDeviceAngle = beam_limiting_device_angle
        if patient_support_angle is not None:
            item.PatientSupportAngle = patient_support_angle
        if table_top_pitch_angle is not None:
            item.TableTopPitchAngle = table_top_pitch_angle
        if table_top_roll_angle is not None:
            item.TableTopRollAngle = table_top_roll_angle
        if table_top_vertical_position is not None:
            item.TableTopVerticalPosition = table_top_vertical_position
        if table_top_longitudinal_position is not None:
            item.TableTopLongitudinalPosition = table_top_longitudinal_position
        if table_top_lateral_position is not None:
            item.TableTopLateralPosition = table_top_lateral_position
        if applicator_sequence is not None:
            item.ApplicatorSequence = applicator_sequence
        if general_accessory_sequence is not None:
            item.GeneralAccessorySequence = general_accessory_sequence
        if number_of_blocks is not None:
            item.NumberOfBlocks = number_of_blocks
        if block_sequence is not None:
            item.BlockSequence = block_sequence

        return item

    @staticmethod
    def create_beam_limiting_device_item(
        rt_beam_limiting_device_type: str | RTBeamLimitingDeviceType,
        number_of_leaf_jaw_pairs: int,
        source_to_beam_limiting_device_distance: float | None = None,
        leaf_position_boundaries: list[float] | None = None,
        leaf_jaw_positions: list[float] | None = None,
    ) -> Dataset:
        """Create beam limiting device sequence item.

        Args:
            rt_beam_limiting_device_type (str | RTBeamLimitingDeviceType): Type of beam limiting device (300A,00B8) Type 1
            number_of_leaf_jaw_pairs (int): Number of leaf or jaw pairs (300A,00BC) Type 1
            source_to_beam_limiting_device_distance (float | None): Source to device distance (300A,00BA) Type 3
            leaf_position_boundaries (list[float] | None): Boundaries of leaves (300A,00BE) Type 2C
            leaf_jaw_positions (list[float] | None): Positions of leaf or jaw pairs (300A,011C) Type 1C

        Returns:
            Dataset: Beam limiting device sequence item
        """
        item = Dataset()
        item.RTBeamLimitingDeviceType = format_enum_string(rt_beam_limiting_device_type)
        item.NumberOfLeafJawPairs = number_of_leaf_jaw_pairs

        if source_to_beam_limiting_device_distance is not None:
            item.SourceToBeamLimitingDeviceDistance = (
                source_to_beam_limiting_device_distance
            )
        if leaf_position_boundaries is not None:
            item.LeafPositionBoundaries = leaf_position_boundaries
        if leaf_jaw_positions is not None:
            item.LeafJawPositions = leaf_jaw_positions

        return item

    @staticmethod
    def create_block_item(
        block_number: int,
        block_type: str | BlockType,
        block_divergence: str | BlockDivergence = "",
        source_to_block_tray_distance: float = 0.0,
        material_id: str = "",
        block_number_of_points: int = 0,
        block_data: list[float] | None = None,
        block_tray_id: str | None = None,
        tray_accessory_code: str | None = None,
        accessory_code: str | None = None,
        block_mounting_position: str | BlockMountingPosition | None = None,
        block_name: str | None = None,
        block_thickness: float | None = None,
    ) -> Dataset:
        """Create block sequence item.

        Args:
            block_number (int): Identification Number of the Block (300A,00FC) Type 1
            block_type (str | BlockType): Type of block (300A,00F8) Type 1
            block_divergence (str | BlockDivergence): Presence of geometrical divergence (300A,00FA) Type 2
            source_to_block_tray_distance (float): Source to block tray distance (300A,00F6) Type 2
            material_id (str): Material used to manufacture Block (300A,00E1) Type 2
            block_number_of_points (int): Number of (x,y) pairs defining block edge (300A,0104) Type 2
            block_data (list[float] | None): Data stream of (x,y) pairs (300A,0106) Type 2
            block_tray_id (str | None): User-supplied identifier for block tray (300A,00F5) Type 3
            tray_accessory_code (str | None): Tray identifier for bar-code reader (300A,0355) Type 3
            accessory_code (str | None): Block identifier for bar-code reader (300A,00F9) Type 3
            block_mounting_position (str | BlockMountingPosition | None): Side of tray block is mounted (300A,00FB) Type 3
            block_name (str | None): User-defined name for block (300A,00FE) Type 3
            block_thickness (float | None): Physical thickness of block (300A,0100) Type 3

        Returns:
            Dataset: Block sequence item
        """
        item = Dataset()
        item.BlockNumber = block_number
        item.BlockType = format_enum_string(block_type)
        item.BlockDivergence = format_enum_string(block_divergence)
        item.SourceToBlockTrayDistance = source_to_block_tray_distance
        item.MaterialID = material_id
        item.BlockNumberOfPoints = block_number_of_points

        if block_data is not None:
            item.BlockData = block_data
        if block_tray_id is not None:
            item.BlockTrayID = block_tray_id
        if tray_accessory_code is not None:
            item.TrayAccessoryCode = tray_accessory_code
        if accessory_code is not None:
            item.AccessoryCode = accessory_code
        if block_mounting_position is not None:
            item.BlockMountingPosition = format_enum_string(block_mounting_position)
        if block_name is not None:
            item.BlockName = block_name
        if block_thickness is not None:
            item.BlockThickness = block_thickness

        return item

    @property
    def is_portal_image(self) -> bool:
        """Check if this is a portal image.

        Returns:
            bool: True if Image Type Value 3 is PORTAL
        """
        if "ImageType" not in self:
            return False
        image_type = self.ImageType
        return len(image_type) > 2 and image_type[2] == "PORTAL"

    @property
    def is_simulator_image(self) -> bool:
        """Check if this is a simulator image.

        Returns:
            bool: True if Image Type Value 3 is SIMULATOR
        """
        if "ImageType" not in self:
            return False
        image_type = self.ImageType
        return len(image_type) > 2 and image_type[2] == "SIMULATOR"

    @property
    def is_fluence_map(self) -> bool:
        """Check if this is a fluence map.

        Returns:
            bool: True if Image Type Value 3 is FLUENCE
        """
        if "ImageType" not in self:
            return False
        image_type = self.ImageType
        return len(image_type) > 2 and image_type[2] == "FLUENCE"

    @property
    def has_exposure_data(self) -> bool:
        """Check if exposure data is present.

        Returns:
            bool: True if Exposure Sequence is present
        """
        return "ExposureSequence" in self

    @property
    def has_beam_geometry(self) -> bool:
        """Check if beam geometry information is present.

        Returns:
            bool: True if gantry angle or beam limiting device angle is present
        """
        return "GantryAngle" in self or "BeamLimitingDeviceAngle" in self

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Image Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with errors and warnings
        """
        return RTImageValidator.validate(self.to_dataset(), config)
