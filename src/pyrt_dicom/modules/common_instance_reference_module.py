"""Common Instance Reference Module - DICOM PS3.3 C.12.2

The Common Instance Reference Module defines the Attributes that describe
the hierarchical relationships of any SOP Instances referenced from other
Modules within the Instance in which this Module occurs.

This module implements the composition-based architecture with internal dataset
management for cleaner separation of concerns and improved memory efficiency.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..validators.modules.common_instance_reference_validator import CommonInstanceReferenceValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult

class CommonInstanceReferenceModule(BaseModule):
    """Common Instance Reference Module implementation for DICOM PS3.3 C.12.2.

    Uses composition with internal dataset management rather than inheriting from
    pydicom.Dataset for cleaner separation of concerns and improved memory efficiency.

    Describes the hierarchical relationships of any SOP Instances referenced
    from other Modules within the Instance in which this Module occurs.

    Key Features:
    - Type 1C conditional sequences for same-study and cross-study references
    - SOP Instance Reference Macro (Table 10-11) implementation
    - Series and Instance Reference Macro (Table 10-4) implementation
    - Comprehensive validation of hierarchical reference structures

    Usage:
        # Create with no required elements (all are Type 1C conditional)
        reference = CommonInstanceReferenceModule.from_required_elements()

        # Add references to instances in this study
        reference.with_referenced_series(
            referenced_series_sequence=[
                CommonInstanceReferenceModule.create_referenced_series_item(
                    series_instance_uid="*******.*******.9",
                    referenced_instance_sequence=[
                        CommonInstanceReferenceModule.create_referenced_instance_item(
                            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
                            referenced_sop_instance_uid="*******.*******.10"
                        )
                    ]
                )
            ]
        )

        # Add references to instances in other studies
        reference.with_other_studies(
            studies_containing_other_referenced_instances_sequence=[
                CommonInstanceReferenceModule.create_other_study_item(
                    study_instance_uid="*******.*******.11"
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = reference.to_dataset()

        # Validate
        result = reference.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'CommonInstanceReferenceModule':
        """Create CommonInstanceReferenceModule with no required elements (all are Type 1C).
        
        Returns:
            CommonInstanceReferenceModule: New module instance
        """
        return cls()
    
    def with_referenced_series(
        self,
        referenced_series_sequence: list[Dataset]
    ) -> 'CommonInstanceReferenceModule':
        """Add references to instances in this study.

        Referenced Series Sequence (0008,1115) is Type 1C - required if
        this Instance references Instances in this Study.

        Args:
            referenced_series_sequence: Sequence of series containing referenced instances (0008,1115) Type 1C.
                Each item must include SeriesInstanceUID and ReferencedInstanceSequence.
                Use create_referenced_series_item() to create properly structured items.

        Returns:
            CommonInstanceReferenceModule: Self for method chaining
        """
        self.ReferencedSeriesSequence = referenced_series_sequence
        return self
    
    def with_other_studies(
        self,
        studies_containing_other_referenced_instances_sequence: list[Dataset]
    ) -> 'CommonInstanceReferenceModule':
        """Add references to instances in other studies.

        Studies Containing Other Referenced Instances Sequence (0008,1200) is Type 1C -
        required if this Instance references Instances in other Studies.

        Args:
            studies_containing_other_referenced_instances_sequence: Studies containing other referenced instances (0008,1200) Type 1C.
                Each item must include StudyInstanceUID and Series and Instance Reference Macro attributes.
                Use create_other_study_item() to create properly structured items.

        Returns:
            CommonInstanceReferenceModule: Self for method chaining
        """
        self.StudiesContainingOtherReferencedInstancesSequence = studies_containing_other_referenced_instances_sequence
        return self
    
    def with_optional_elements(self) -> 'CommonInstanceReferenceModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Common Instance Reference Module has no Type 3 elements defined in DICOM PS3.3 C.12.2.
        This method is provided for API consistency but accepts no parameters.
            
        Returns:
            CommonInstanceReferenceModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_referenced_series_item(
        series_instance_uid: str,
        referenced_instance_sequence: list[Dataset]
    ) -> Dataset:
        """Create referenced series sequence item.
        
        Args:
            series_instance_uid (str): Unique identifier of the Series (0020,000E) Type 1
            referenced_instance_sequence (list[Dataset]): Referenced instances in this series (0008,114A) Type 1
                
        Returns:
            Dataset: Referenced series sequence item
        """
        item = Dataset()
        item.SeriesInstanceUID = series_instance_uid
        item.ReferencedInstanceSequence = referenced_instance_sequence
        return item
    
    @staticmethod
    def create_referenced_instance_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_frame_number: list[int] | None = None,
        referenced_segment_number: list[int] | None = None
    ) -> Dataset:
        """Create referenced instance sequence item implementing SOP Instance Reference Macro (Table 10-11).
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150) Type 1
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155) Type 1
            referenced_frame_number (list[int] | None): Referenced frame numbers (0008,1160) Type 1C.
                Required if Referenced SOP Instance is a multi-frame image and the reference does not apply to all frames.
            referenced_segment_number (list[int] | None): Referenced segment numbers (0062,000B) Type 1C.
                Required if the Referenced SOP Instance is a Segmentation and the reference does not apply to all segments.
                
        Returns:
            Dataset: Referenced instance sequence item with SOP Instance Reference Macro attributes
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        if referenced_frame_number is not None:
            item.ReferencedFrameNumber = referenced_frame_number
        if referenced_segment_number is not None:
            item.ReferencedSegmentNumber = referenced_segment_number
        return item
    
    @staticmethod
    def create_other_study_item(
        study_instance_uid: str,
        referenced_series_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create studies containing other referenced instances sequence item.
        
        Args:
            study_instance_uid (str): Unique identifier of the Study (0020,000D) Type 1
            referenced_series_sequence (list[Dataset] | None): Series and Instance Reference Macro attributes
                
        Returns:
            Dataset: Studies containing other referenced instances sequence item
        """
        item = Dataset()
        item.StudyInstanceUID = study_instance_uid
        if referenced_series_sequence is not None:
            item.ReferencedSeriesSequence = referenced_series_sequence
        return item
    
    @property
    def has_referenced_series(self) -> bool:
        """Check if referenced series are present.

        Returns:
            bool: True if referenced series sequence is present
        """
        return 'ReferencedSeriesSequence' in self

    @property
    def has_other_studies(self) -> bool:
        """Check if other studies are referenced.

        Returns:
            bool: True if studies containing other referenced instances sequence is present
        """
        return 'StudiesContainingOtherReferencedInstancesSequence' in self

    @property
    def referenced_series_count(self) -> int:
        """Get the number of referenced series.

        Returns:
            int: Number of referenced series items
        """
        if 'ReferencedSeriesSequence' not in self:
            return 0
        return len(self.ReferencedSeriesSequence)

    @property
    def other_studies_count(self) -> int:
        """Get the number of other studies referenced.

        Returns:
            int: Number of other studies items
        """
        if 'StudiesContainingOtherReferencedInstancesSequence' not in self:
            return 0
        return len(self.StudiesContainingOtherReferencedInstancesSequence)
    
    @property
    def has_any_references(self) -> bool:
        """Check if this module contains any instance references.
        
        Returns:
            bool: True if either referenced series or other studies sequences are present
        """
        return self.has_referenced_series or self.has_other_studies
    
    @property
    def total_instance_count(self) -> int:
        """Get the total number of referenced instances across all sequences.

        Returns:
            int: Total count of referenced instances
        """
        count = 0

        # Count instances in Referenced Series Sequence
        if 'ReferencedSeriesSequence' in self:
            for series in self.ReferencedSeriesSequence:
                count += len(series.get('ReferencedInstanceSequence', []))

        # Count instances in Studies Containing Other Referenced Instances Sequence
        if 'StudiesContainingOtherReferencedInstancesSequence' in self:
            for study in self.StudiesContainingOtherReferencedInstancesSequence:
                for series in study.get('ReferencedSeriesSequence', []):
                    count += len(series.get('ReferencedInstanceSequence', []))

        return count

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Common Instance Reference Module instance.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors and warnings lists
        """
        return CommonInstanceReferenceValidator.validate(self.to_dataset(), config)
