"""
RT Brachy Application Setups Module - DICOM PS3.3 C.8.8.15

The RT Brachy Application Setups Module describes the application of a brachytherapy
radiotherapy treatment. It contains one or more sources, each associated with one or
more Channels. A Channel is a device by which a source is placed in its intended
treatment position or positions.

This module implements the complete DICOM PS3.3 C.8.8.15 specification with proper
conditional logic for Type 1C/2C elements and comprehensive sequence support.
"""
from datetime import datetime, date
from .base_module import BaseModule
from ..enums.rt_enums import (
    BrachyTreatmentTechnique, BrachyTreatmentType, ApplicationSetupType,
    SourceType, SourceMovementType, BrachyAccessoryDeviceType,
    SourceApplicatorType, SourceStrengthUnits
)
from ..validators.modules.rt_brachy_application_setups_validator import RTBrachyApplicationSetupsValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string
from pydicom import Dataset, Sequence


class RTBrachyApplicationSetupsModule(BaseModule):
    """RT Brachy Application Setups Module implementation for DICOM PS3.3 C.8.8.15.

    Uses composition with internal dataset management for cleaner separation of concerns.
    Contains information defining brachytherapy application setups for treatment delivery.

    The module supports the complete DICOM specification including:
    - Treatment machine information
    - Source library definitions
    - Application setup configurations with channels and control points
    - Brachy accessory devices and channel shields
    - Conditional logic for Type 1C/2C elements

    Usage:
        # Create with required elements
        brachy_module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[
                RTBrachyApplicationSetupsModule.create_treatment_machine_item(
                    treatment_machine_name="HDR Unit 1"
                )
            ],
            source_sequence=[
                RTBrachyApplicationSetupsModule.create_source_item(
                    source_number=1,
                    source_type=SourceType.POINT,
                    source_isotope_name="Ir-192",
                    source_isotope_half_life=73.8,
                    reference_air_kerma_rate=40800.0,
                    source_strength_reference_date="20240101",
                    source_strength_reference_time="120000"
                )
            ],
            application_setup_sequence=[
                RTBrachyApplicationSetupsModule.create_application_setup_item(
                    application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
                    application_setup_number=1
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = brachy_module.to_dataset()

        # Validate
        result = brachy_module.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        brachy_treatment_technique: BrachyTreatmentTechnique | str,
        brachy_treatment_type: BrachyTreatmentType | str,
        treatment_machine_sequence: list[Dataset],
        source_sequence: list[Dataset],
        application_setup_sequence: list[Dataset]
    ) -> 'RTBrachyApplicationSetupsModule':
        """Create RTBrachyApplicationSetupsModule from all required (Type 1) data elements.

        Args:
            brachy_treatment_technique: Type of brachytherapy treatment technique (300A,0200) Type 1
                Enumerated Values: INTRALUMENARY, INTRACAVITARY, INTERSTITIAL, CONTACT, INTRAVASCULAR, PERMANENT
            brachy_treatment_type: Type of brachytherapy treatment (300A,0202) Type 1
                Defined Terms: MANUAL, HDR, MDR, LDR, PDR
            treatment_machine_sequence: Treatment machine information (300A,0206) Type 1
                Only a single Item shall be included in this Sequence
            source_sequence: Sequence of sources to be used within Application Setups (300A,0210) Type 1
                One or more Items shall be included in this Sequence
            application_setup_sequence: Sequence of Application Setups for current RT Plan (300A,0230) Type 1
                One or more Items shall be included in this Sequence

        Returns:
            RTBrachyApplicationSetupsModule: New module instance with required data elements set
        """
        instance = cls()
        instance['BrachyTreatmentTechnique'] = format_enum_string(brachy_treatment_technique)
        instance['BrachyTreatmentType'] = format_enum_string(brachy_treatment_type)
        instance['TreatmentMachineSequence'] = Sequence(treatment_machine_sequence)
        instance['SourceSequence'] = Sequence(source_sequence)
        instance['ApplicationSetupSequence'] = Sequence(application_setup_sequence)
        return instance
    
    def with_optional_elements(self) -> 'RTBrachyApplicationSetupsModule':
        """Add optional (Type 3) elements.

        Note: The core DICOM PS3.3 C.8.8.15 RT Brachy Application Setups Module contains only:
        - Type 1: BrachyTreatmentTechnique, BrachyTreatmentType, TreatmentMachineSequence,
                  SourceSequence, ApplicationSetupSequence (required)

        There are no Type 3 (optional) elements defined at the top level of this module.
        All optional elements are within the nested sequences (Treatment Machine, Source,
        Application Setup, etc.).

        This method is provided for API consistency with other modules.

        Returns:
            RTBrachyApplicationSetupsModule: Self for method chaining
        """
        # No Type 3 elements defined in DICOM PS3.3 C.8.8.15 RT Brachy Application Setups Module
        return self
    
    @staticmethod
    def create_treatment_machine_item(
        treatment_machine_name: str = "",
        manufacturer: str | None = None,
        institution_name: str | None = None,
        institution_address: str | None = None,
        institutional_department_name: str | None = None,
        institutional_department_type_code_sequence: list[Dataset] | None = None,
        manufacturers_model_name: str | None = None,
        device_serial_number: str | None = None,
        date_of_manufacture: str | date | datetime | None = None,
        date_of_installation: str | date | datetime | None = None
    ) -> Dataset:
        """Create treatment machine sequence item.

        Args:
            treatment_machine_name: User-defined name identifying treatment machine (300A,00B2) Type 2
            manufacturer: Manufacturer of the equipment (0008,0070) Type 3
            institution_name: Institution where equipment is located (0008,0080) Type 3
            institution_address: Mailing address of the institution (0008,0081) Type 3
            institutional_department_name: Department in the institution (0008,1040) Type 3
            institutional_department_type_code_sequence: Coded description of department type (0008,1041) Type 3
            manufacturers_model_name: Manufacturer's model name (0008,1090) Type 3
            device_serial_number: Manufacturer's serial number (0018,1000) Type 3
            date_of_manufacture: Date equipment was originally manufactured (0018,1204) Type 3
            date_of_installation: Date equipment was installed (0018,1205) Type 3

        Returns:
            Dataset: Treatment machine sequence item
        """
        item = Dataset()
        item.TreatmentMachineName = treatment_machine_name

        # Add optional elements if provided
        if manufacturer is not None:
            item.Manufacturer = manufacturer
        if institution_name is not None:
            item.InstitutionName = institution_name
        if institution_address is not None:
            item.InstitutionAddress = institution_address
        if institutional_department_name is not None:
            item.InstitutionalDepartmentName = institutional_department_name
        if institutional_department_type_code_sequence is not None:
            item.InstitutionalDepartmentTypeCodeSequence = Sequence(institutional_department_type_code_sequence)
        if manufacturers_model_name is not None:
            item.ManufacturerModelName = manufacturers_model_name
        if device_serial_number is not None:
            item.DeviceSerialNumber = device_serial_number
        if date_of_manufacture is not None:
            item.DateOfManufacture = format_date_value(date_of_manufacture)
        if date_of_installation is not None:
            item.DateOfInstallation = format_date_value(date_of_installation)

        return item
    
    @staticmethod
    def create_source_item(
        source_number: int,
        source_type: SourceType | str,
        source_isotope_name: str,
        source_isotope_half_life: float,
        reference_air_kerma_rate: float,
        source_strength_reference_date: str | date | datetime,
        source_strength_reference_time: str | datetime,
        source_serial_number: str | None = None,
        source_model_id: str | None = None,
        source_description: str | None = None,
        source_manufacturer: str | None = None,
        active_source_diameter: float | None = None,
        active_source_length: float | None = None,
        material_id: str | None = None,
        source_encapsulation_nominal_thickness: float | None = None,
        source_encapsulation_nominal_transmission: float | None = None,
        source_strength_units: SourceStrengthUnits | str | None = None,
        source_strength: float | None = None
    ) -> Dataset:
        """Create source sequence item.

        Args:
            source_number: Identification number of the Source (300A,0212) Type 1
                Must be unique within the RT Plan
            source_type: Type of Source (300A,0214) Type 1
                Defined Terms: POINT, LINE, CYLINDER, SPHERE
            source_isotope_name: Name of Isotope (300A,0226) Type 1
            source_isotope_half_life: Half-life of Isotope in days (300A,0228) Type 1
            reference_air_kerma_rate: Air Kerma Rate in air (300A,022A) Type 1
                In Gy h^-1 at 1 m. Value shall be zero for non-gamma sources
            source_strength_reference_date: Reference date for Air Kerma Rate (300A,022C) Type 1
            source_strength_reference_time: Reference time for Air Kerma Rate (300A,022E) Type 1
            source_serial_number: Identifier for the Source Instance (3008,0105) Type 3
            source_model_id: Identifier for the Source Model (300A,021B) Type 3
            source_description: Description of the source (300A,021C) Type 3
            source_manufacturer: Manufacturer of Source (300A,0216) Type 3
            active_source_diameter: Diameter of active Source in mm (300A,0218) Type 3
            active_source_length: Length of active Source in mm (300A,021A) Type 3
            material_id: User-supplied identifier for encapsulation material (300A,00E1) Type 3
            source_encapsulation_nominal_thickness: Nominal thickness of wall in mm (300A,0222) Type 3
            source_encapsulation_nominal_transmission: Nominal transmission through wall (300A,0224) Type 3
                Between 0 and 1
            source_strength_units: Measurement unit of Source Strength (300A,0229) Type 1C
                Required if the source is not a gamma-emitting (photon) source
                Enumerated Values: AIR_KERMA_RATE, DOSE_RATE_WATER
            source_strength: Source Strength of Isotope (300A,022B) Type 1C
                Required if the source is not a gamma-emitting (photon) source

        Returns:
            Dataset: Source sequence item
        """
        item = Dataset()
        item.SourceNumber = source_number
        item.SourceType = format_enum_string(source_type)
        item.SourceIsotopeName = source_isotope_name
        item.SourceIsotopeHalfLife = source_isotope_half_life
        item.ReferenceAirKermaRate = reference_air_kerma_rate
        item.SourceStrengthReferenceDate = format_date_value(source_strength_reference_date)
        item.SourceStrengthReferenceTime = format_time_value(source_strength_reference_time)

        # Add optional elements if provided
        if source_serial_number is not None:
            item.SourceSerialNumber = source_serial_number
        if source_model_id is not None:
            item.SourceModelID = source_model_id
        if source_description is not None:
            item.SourceDescription = source_description
        if source_manufacturer is not None:
            item.SourceManufacturer = source_manufacturer
        if active_source_diameter is not None:
            item.ActiveSourceDiameter = active_source_diameter
        if active_source_length is not None:
            item.ActiveSourceLength = active_source_length
        if material_id is not None:
            item.MaterialID = material_id
        if source_encapsulation_nominal_thickness is not None:
            item.SourceEncapsulationNominalThickness = source_encapsulation_nominal_thickness
        if source_encapsulation_nominal_transmission is not None:
            item.SourceEncapsulationNominalTransmission = source_encapsulation_nominal_transmission

        # Type 1C elements - conditional on source type
        if source_strength_units is not None:
            item.SourceStrengthUnits = format_enum_string(source_strength_units)
        if source_strength is not None:
            item.SourceStrength = source_strength

        return item

    @staticmethod
    def create_application_setup_item(
        application_setup_type: ApplicationSetupType | str,
        application_setup_number: int,
        application_setup_name: str | None = None,
        application_setup_manufacturer: str | None = None,
        template_number: int | None = None,
        template_type: str | None = None,
        template_name: str | None = None,
        referenced_reference_image_sequence: list[Dataset] | None = None,
        total_reference_air_kerma: float | None = None,
        brachy_accessory_device_sequence: list[Dataset] | None = None,
        channel_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create application setup sequence item.

        Args:
            application_setup_type: Type of Application Setup (300A,0232) Type 1
                Defined Terms: FLETCHER_SUIT, DELCLOS, BLOEDORN, JOSLIN_FLYNN, CHANDIGARH,
                MANCHESTER, HENSCHKE, NASOPHARYNGEAL, OESOPHAGEAL, ENDOBRONCHIAL,
                SYED_NEBLETT, ENDORECTAL, PERINEAL
            application_setup_number: Identification number of the Application Setup (300A,0234) Type 1
                Must be unique within the RT Plan
            application_setup_name: User-defined name for Application Setup (300A,0236) Type 3
            application_setup_manufacturer: Manufacturer of Application Setup (300A,0238) Type 3
            template_number: Identification number of the Template (300A,0240) Type 3
                Must be unique within the Application Setup
            template_type: User-defined type for Template Device (300A,0242) Type 3
            template_name: User-defined name for Template Device (300A,0244) Type 3
            referenced_reference_image_sequence: Reference images for validation (300C,0042) Type 3
            total_reference_air_kerma: Total Reference Air Kerma for current Application Setup (300A,0250) Type 1
                Product of Air Kerma Rate of all Sources in all Channels with their respective Channel Times (Gy at 1 m)
                Value shall be zero for non-gamma sources
            brachy_accessory_device_sequence: Brachy Accessory Devices (300A,0260) Type 3
            channel_sequence: Channels for current Application Setup (300A,0280) Type 1
                One or more Items shall be included in this Sequence

        Returns:
            Dataset: Application setup sequence item
        """
        item = Dataset()
        item.ApplicationSetupType = format_enum_string(application_setup_type)
        item.ApplicationSetupNumber = application_setup_number
        item.TotalReferenceAirKerma = total_reference_air_kerma if total_reference_air_kerma is not None else 0.0

        # Add optional elements if provided
        if application_setup_name is not None:
            item.ApplicationSetupName = application_setup_name
        if application_setup_manufacturer is not None:
            item.ApplicationSetupManufacturer = application_setup_manufacturer
        if template_number is not None:
            item.TemplateNumber = template_number
        if template_type is not None:
            item.TemplateType = template_type
        if template_name is not None:
            item.TemplateName = template_name
        if referenced_reference_image_sequence is not None:
            item.ReferencedReferenceImageSequence = Sequence(referenced_reference_image_sequence)
        if brachy_accessory_device_sequence is not None:
            item.BrachyAccessoryDeviceSequence = Sequence(brachy_accessory_device_sequence)
        if channel_sequence is not None:
            item.ChannelSequence = Sequence(channel_sequence)

        return item

    @staticmethod
    def create_brachy_accessory_device_item(
        brachy_accessory_device_number: int,
        brachy_accessory_device_id: str,
        brachy_accessory_device_type: BrachyAccessoryDeviceType | str,
        brachy_accessory_device_name: str | None = None,
        material_id: str | None = None,
        brachy_accessory_device_nominal_thickness: float | None = None,
        brachy_accessory_device_nominal_transmission: float | None = None,
        referenced_roi_number: int | None = None
    ) -> Dataset:
        """Create brachy accessory device sequence item.

        Args:
            brachy_accessory_device_number: Identification number of the Brachy Accessory Device (300A,0262) Type 2
                Must be unique within the Application Setup
            brachy_accessory_device_id: User or machine supplied identifier (300A,0263) Type 2
            brachy_accessory_device_type: Type of Brachy Accessory Device (300A,0264) Type 1
                Defined Terms: SHIELD, DILATATION, MOLD, PLAQUE, FLAB
            brachy_accessory_device_name: User-defined name (300A,0266) Type 3
            material_id: User-supplied identifier for material (300A,00E1) Type 3
            brachy_accessory_device_nominal_thickness: Nominal thickness in mm (300A,026A) Type 3
            brachy_accessory_device_nominal_transmission: Nominal transmission (300A,026C) Type 3
                Between 0 and 1
            referenced_roi_number: ROI representing the Brachy Accessory (3006,0084) Type 2

        Returns:
            Dataset: Brachy accessory device sequence item
        """
        item = Dataset()
        item.BrachyAccessoryDeviceNumber = brachy_accessory_device_number
        item.BrachyAccessoryDeviceID = brachy_accessory_device_id
        item.BrachyAccessoryDeviceType = format_enum_string(brachy_accessory_device_type)

        # Add optional elements if provided
        if brachy_accessory_device_name is not None:
            item.BrachyAccessoryDeviceName = brachy_accessory_device_name
        if material_id is not None:
            item.MaterialID = material_id
        if brachy_accessory_device_nominal_thickness is not None:
            item.BrachyAccessoryDeviceNominalThickness = brachy_accessory_device_nominal_thickness
        if brachy_accessory_device_nominal_transmission is not None:
            item.BrachyAccessoryDeviceNominalTransmission = brachy_accessory_device_nominal_transmission
        if referenced_roi_number is not None:
            item.ReferencedROINumber = referenced_roi_number

        return item

    @staticmethod
    def create_channel_item(
        channel_number: int,
        channel_total_time: float,
        source_movement_type: SourceMovementType | str,
        referenced_source_number: int,
        number_of_control_points: int,
        brachy_control_point_sequence: list[Dataset],
        channel_length: float | None = None,
        number_of_pulses: int | None = None,
        pulse_repetition_interval: float | None = None,
        source_applicator_number: int | None = None,
        source_applicator_id: str | None = None,
        source_applicator_type: SourceApplicatorType | str | None = None,
        source_applicator_name: str | None = None,
        source_applicator_length: float | None = None,
        source_applicator_tip_length: float | None = None,
        source_applicator_manufacturer: str | None = None,
        material_id: str | None = None,
        source_applicator_wall_nominal_thickness: float | None = None,
        source_applicator_wall_nominal_transmission: float | None = None,
        source_applicator_step_size: float | None = None,
        applicator_shape_referenced_roi_number: int | None = None,
        referenced_roi_number: int | None = None,
        transfer_tube_number: int | None = None,
        transfer_tube_length: float | None = None,
        channel_effective_length: float | None = None,
        channel_inner_length: float | None = None,
        afterloader_channel_id: str | None = None,
        channel_shield_sequence: list[Dataset] | None = None,
        final_cumulative_time_weight: float | None = None
    ) -> Dataset:
        """Create channel sequence item.

        Args:
            channel_number: Identification number of the Channel (300A,0282) Type 1
                Must be unique within the Application Setup
            channel_total_time: Total amount of time for current Channel in sec (300A,0286) Type 1
            source_movement_type: Type of Source movement (300A,0288) Type 1
                Defined Terms: STEPWISE, FIXED, OSCILLATING, UNIDIRECTIONAL
            referenced_source_number: Referenced Source within Source Sequence (300C,000E) Type 1
            number_of_control_points: Number of control points in Channel (300A,0110) Type 1
            brachy_control_point_sequence: Sequence of machine configurations (300A,02D0) Type 1
            channel_length: Length of Channel in mm (300A,0284) Type 2
            number_of_pulses: Number of Pulses per fraction (300A,028A) Type 1C
                Required if Brachy Treatment Type is PDR
            pulse_repetition_interval: Pulse repetition interval in sec (300A,028C) Type 1C
                Required if Brachy Treatment Type is PDR
            source_applicator_number: Identification number of Source Applicator (300A,0290) Type 3
            source_applicator_id: User or machine supplied identifier (300A,0291) Type 2C
                Required if Source Applicator Number is present
            source_applicator_type: Type of Source Applicator (300A,0292) Type 1C
                Required if Source Applicator Number is present. Defined Terms: FLEXIBLE, RIGID
            source_applicator_name: User-defined name for Source Applicator (300A,0294) Type 3
            source_applicator_length: Length of Source Applicator in mm (300A,0296) Type 1C
                Required if Source Applicator Number is present
            source_applicator_tip_length: Length of Source Applicator Tip in mm (300A,0274) Type 2C
                Required if Channel Effective Length is present
            source_applicator_manufacturer: Manufacturer of Source Applicator (300A,0298) Type 3
            material_id: User-supplied identifier for material of Source Applicator wall (300A,00E1) Type 3
            source_applicator_wall_nominal_thickness: Nominal Thickness of wall in mm (300A,029C) Type 3
            source_applicator_wall_nominal_transmission: Nominal Transmission through wall (300A,029E) Type 3
                Between 0 and 1
            source_applicator_step_size: Distance between adjacent dwell positions in mm (300A,02A0) Type 1C
                Required if Source Movement Type is STEPWISE
            applicator_shape_referenced_roi_number: ROI representing Applicator shape (300A,02A1) Type 3
            referenced_roi_number: ROI representing Channel shape (3006,0084) Type 2C
                Required if Source Applicator Number is present
            transfer_tube_number: Identification number of Transfer Tube (300A,02A2) Type 2
            transfer_tube_length: Length of Transfer Tube in mm (300A,02A4) Type 2C
                Required if Transfer Tube Number is non-null
            channel_effective_length: Length of Channel in mm (300A,0271) Type 3
            channel_inner_length: Total physical inner length of channel in mm (300A,0272) Type 2C
                Required if Channel Effective Length is present
            afterloader_channel_id: Identification of Channel connection on afterloader (300A,0273) Type 3
            channel_shield_sequence: Channel Shields associated with current Channel (300A,02B0) Type 3
            final_cumulative_time_weight: Value of Cumulative Time Weight for final Control Point (300A,02C8) Type 1C
                Required if Cumulative Time Weight is non-null in Control Points

        Returns:
            Dataset: Channel sequence item
        """
        item = Dataset()
        item.ChannelNumber = channel_number
        item.ChannelTotalTime = channel_total_time
        item.SourceMovementType = format_enum_string(source_movement_type)
        item.ReferencedSourceNumber = referenced_source_number
        item.NumberOfControlPoints = number_of_control_points
        item.BrachyControlPointSequence = Sequence(brachy_control_point_sequence)

        # Type 2 elements
        if channel_length is not None:
            item.ChannelLength = channel_length
        if transfer_tube_number is not None:
            item.TransferTubeNumber = transfer_tube_number

        # Type 1C elements - conditional requirements
        if number_of_pulses is not None:
            item.NumberOfPulses = number_of_pulses
        if pulse_repetition_interval is not None:
            item.PulseRepetitionInterval = pulse_repetition_interval
        if source_applicator_step_size is not None:
            item.SourceApplicatorStepSize = source_applicator_step_size
        if final_cumulative_time_weight is not None:
            item.FinalCumulativeTimeWeight = final_cumulative_time_weight

        # Type 2C elements - conditional requirements
        if source_applicator_id is not None:
            item.SourceApplicatorID = source_applicator_id
        if source_applicator_type is not None:
            item.SourceApplicatorType = format_enum_string(source_applicator_type)
        if source_applicator_length is not None:
            item.SourceApplicatorLength = source_applicator_length
        if source_applicator_tip_length is not None:
            item.SourceApplicatorTipLength = source_applicator_tip_length
        if referenced_roi_number is not None:
            item.ReferencedROINumber = referenced_roi_number
        if transfer_tube_length is not None:
            item.TransferTubeLength = transfer_tube_length
        if channel_inner_length is not None:
            item.ChannelInnerLength = channel_inner_length

        # Type 3 elements
        if source_applicator_number is not None:
            item.SourceApplicatorNumber = source_applicator_number
        if source_applicator_name is not None:
            item.SourceApplicatorName = source_applicator_name
        if source_applicator_manufacturer is not None:
            item.SourceApplicatorManufacturer = source_applicator_manufacturer
        if material_id is not None:
            item.MaterialID = material_id
        if source_applicator_wall_nominal_thickness is not None:
            item.SourceApplicatorWallNominalThickness = source_applicator_wall_nominal_thickness
        if source_applicator_wall_nominal_transmission is not None:
            item.SourceApplicatorWallNominalTransmission = source_applicator_wall_nominal_transmission
        if applicator_shape_referenced_roi_number is not None:
            item.ApplicatorShapeReferencedROINumber = applicator_shape_referenced_roi_number
        if channel_effective_length is not None:
            item.ChannelEffectiveLength = channel_effective_length
        if afterloader_channel_id is not None:
            item.AfterloaderChannelID = afterloader_channel_id
        if channel_shield_sequence is not None:
            item.ChannelShieldSequence = Sequence(channel_shield_sequence)

        return item
    @property
    def has_application_setups(self) -> bool:
        """Check if application setup data is present.

        Returns:
            bool: True if Application Setup Sequence is present and not empty
        """
        return ('ApplicationSetupSequence' in self and
                len(self.ApplicationSetupSequence) > 0)

    @property
    def application_setup_count(self) -> int:
        """Get the number of application setups in this module.

        Returns:
            int: Number of setups in Application Setup Sequence
        """
        if 'ApplicationSetupSequence' in self:
            return len(self.ApplicationSetupSequence)
        return 0

    @property
    def has_sources(self) -> bool:
        """Check if source data is present.

        Returns:
            bool: True if Source Sequence is present and not empty
        """
        return ('SourceSequence' in self and
                len(self.SourceSequence) > 0)

    @property
    def source_count(self) -> int:
        """Get the number of sources in this module.

        Returns:
            int: Number of sources in Source Sequence
        """
        if 'SourceSequence' in self:
            return len(self.SourceSequence)
        return 0

    @property
    def has_treatment_machine(self) -> bool:
        """Check if treatment machine data is present.

        Returns:
            bool: True if Treatment Machine Sequence is present and not empty
        """
        return ('TreatmentMachineSequence' in self and
                len(self.TreatmentMachineSequence) > 0)

    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured with all required elements.

        Returns:
            bool: True if all required Type 1 elements are present
        """
        return ('BrachyTreatmentTechnique' in self and
                'BrachyTreatmentType' in self and
                self.has_treatment_machine and
                self.has_sources and
                self.has_application_setups)

    @property
    def is_pdr_treatment(self) -> bool:
        """Check if this is a PDR (Pulsed Dose Rate) treatment.

        Returns:
            bool: True if Brachy Treatment Type is PDR
        """
        return ('BrachyTreatmentType' in self and
                self.BrachyTreatmentType == BrachyTreatmentType.PDR.value)

    @property
    def is_permanent_implant(self) -> bool:
        """Check if this is a permanent implant treatment.

        Returns:
            bool: True if Brachy Treatment Technique is PERMANENT
        """
        return ('BrachyTreatmentTechnique' in self and
                self.BrachyTreatmentTechnique == BrachyTreatmentTechnique.PERMANENT.value)

    def get_application_setup_numbers(self) -> list[int]:
        """Get list of application setup numbers present in this module.

        Returns:
            list[int]: List of setup numbers
        """
        if 'ApplicationSetupSequence' not in self:
            return []

        setup_numbers = []
        for setup_item in self.ApplicationSetupSequence:
            setup_number = getattr(setup_item, 'ApplicationSetupNumber', None)
            if setup_number is not None:
                setup_numbers.append(setup_number)
        return setup_numbers

    def get_application_setup_by_number(self, setup_number: int) -> Dataset | None:
        """Get application setup by its number.

        Args:
            setup_number: Setup number to find

        Returns:
            Dataset | None: Setup item or None if not found
        """
        if 'ApplicationSetupSequence' not in self:
            return None

        for setup_item in self.ApplicationSetupSequence:
            if getattr(setup_item, 'ApplicationSetupNumber', None) == setup_number:
                return setup_item
        return None

    def get_source_numbers(self) -> list[int]:
        """Get list of source numbers present in this module.

        Returns:
            list[int]: List of source numbers
        """
        if 'SourceSequence' not in self:
            return []

        source_numbers = []
        for source_item in self.SourceSequence:
            source_number = getattr(source_item, 'SourceNumber', None)
            if source_number is not None:
                source_numbers.append(source_number)
        return source_numbers

    def get_source_by_number(self, source_number: int) -> Dataset | None:
        """Get source by its number.

        Args:
            source_number: Source number to find

        Returns:
            Dataset | None: Source item or None if not found
        """
        if 'SourceSequence' not in self:
            return None

        for source_item in self.SourceSequence:
            if getattr(source_item, 'SourceNumber', None) == source_number:
                return source_item
        return None

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT Brachy Application Setups Module instance.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult with errors and warnings
        """
        return RTBrachyApplicationSetupsValidator.validate(self._dataset, config)
