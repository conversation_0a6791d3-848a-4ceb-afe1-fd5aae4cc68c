"""
RT DVH Module - DICOM PS3.3 C.8.8.4

The RT DVH Module provides for the inclusion of differential or cumulative
dose volume histogram data. The data contained within this Module may supplement
dose data in the RT Dose Module, or it may be present in the absence of other dose data.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.dose_enums import DoseUnits, DoseType
from ..enums.rt_enums import DVHType, DVHROIContributionType, DVHVolumeUnits
from ..validators.modules.rt_dvh_validator import RTDVHValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_string


class RTDVHModule(BaseModule):
    """RT DVH Module implementation for DICOM PS3.3 C.8.8.4.

    Uses composition with internal dataset management for cleaner separation of concerns.
    Provides for inclusion of differential or cumulative dose volume histogram data.

    The RT DVH Module contains dose-volume histogram data that may supplement dose data
    in the RT Dose Module or be present independently. DVH data describes the relationship
    between dose and volume for regions of interest (ROIs) in radiotherapy treatment planning.

    Usage:
        # Create with required elements
        dvh = RTDVHModule.from_required_elements(
            referenced_structure_set_sequence=[
                RTDVHModule.create_referenced_structure_set_item(
                    referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                    referenced_sop_instance_uid="*******.*******.9"
                )
            ],
            dvh_sequence=[
                RTDVHModule.create_dvh_item(
                    dvh_referenced_roi_sequence=[
                        RTDVHModule.create_dvh_referenced_roi_item(
                            referenced_roi_number=1,
                            dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
                        )
                    ],
                    dvh_type=DVHType.CUMULATIVE,
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dvh_dose_scaling=1.0,
                    dvh_volume_units=DVHVolumeUnits.CM3,
                    dvh_number_of_bins=100,
                    dvh_data=[0.1, 10.5, 0.2, 9.8]  # D1V1, D2V2, ...
                )
            ]
        )

        # Add optional elements
        dvh.with_optional_elements(
            dvh_normalization_point=[100.0, 50.0, 25.0],
            dvh_normalization_dose_value=200.0
        )

        # Generate dataset for IOD integration
        dataset = dvh.to_dataset()

        # Validate
        result = dvh.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        referenced_structure_set_sequence: list[Dataset],
        dvh_sequence: list[Dataset]
    ) -> 'RTDVHModule':
        """Create RTDVHModule from all required (Type 1) data elements.

        Args:
            referenced_structure_set_sequence: Structure Set containing structures for DVHs (300C,0060) Type 1.
                Sequence of one Class/Instance pair describing Structure Set containing structures
                that are used to calculate Dose-Volume Histograms (DVHs). Only a single Item
                shall be included in this Sequence.
            dvh_sequence: Sequence of DVHs (3004,0050) Type 1. One or more Items shall be
                included in this Sequence.

        Returns:
            RTDVHModule: New module instance with required data elements set
        """
        instance = cls()
        instance['ReferencedStructureSetSequence'] = referenced_structure_set_sequence
        instance['DVHSequence'] = dvh_sequence
        return instance

    def with_optional_elements(
        self,
        dvh_normalization_point: list[float] | None = None,
        dvh_normalization_dose_value: float | None = None
    ) -> 'RTDVHModule':
        """Add optional (Type 3) elements.

        Args:
            dvh_normalization_point: Coordinates (x, y, z) of common DVH normalization point
                (3004,0040) Type 3. Coordinates in the Patient-Based Coordinate System
                described in Section C.7.6.2.1.1 (mm).
            dvh_normalization_dose_value: Dose Value at DVH Normalization Point (3004,0042) Type 3.
                Used as reference for individual DVHs when Dose Units (3004,0002) is RELATIVE.

        Returns:
            RTDVHModule: Self for method chaining
        """
        if dvh_normalization_point is not None:
            self.DVHNormalizationPoint = dvh_normalization_point
        if dvh_normalization_dose_value is not None:
            self.DVHNormalizationDoseValue = dvh_normalization_dose_value
        return self
    
    @staticmethod
    def create_referenced_structure_set_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> Dataset:
        """Create referenced structure set sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID

        Returns:
            Dataset: Referenced structure set sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        return item
    
    @staticmethod
    def create_dvh_item(
        dvh_referenced_roi_sequence: list[Dataset],
        dvh_type: str | DVHType,
        dose_units: str | DoseUnits,
        dose_type: str | DoseType,
        dvh_dose_scaling: float,
        dvh_volume_units: str | DVHVolumeUnits,
        dvh_number_of_bins: int,
        dvh_data: list[float],
        dvh_minimum_dose: float | None = None,
        dvh_maximum_dose: float | None = None,
        dvh_mean_dose: float | None = None
    ) -> Dataset:
        """Create DVH sequence item.

        Args:
            dvh_referenced_roi_sequence: Referenced ROIs used to calculate DVH (3004,0060) Type 1.
                One or more Items shall be included in this Sequence.
            dvh_type: Type of DVH (3004,0001) Type 1. Enumerated Values: DIFFERENTIAL,
                CUMULATIVE, NATURAL.
            dose_units: Dose axis units (3004,0002) Type 1. Enumerated Values: GY (Gray),
                RELATIVE (dose relative to reference value specified in DVH Normalization Dose Value).
            dose_type: Type of dose (3004,0004) Type 1. Defined Terms: PHYSICAL, EFFECTIVE, ERROR.
            dvh_dose_scaling: Scaling factor for dose bin widths (3004,0052) Type 1. When multiplied
                by the dose bin widths found in DVH Data, yields dose bin widths in the dose units
                as specified by Dose Units.
            dvh_volume_units: Volume axis units (3004,0054) Type 1. Defined Terms: CM3, PERCENT, PER_U.
            dvh_number_of_bins: Number of bins n used to store DVH Data (3004,0056) Type 1.
            dvh_data: Data stream describing dose bin widths and volumes (3004,0058) Type 1.
                Format: D1V1, D2V2, DnVn where Dn are dose bin widths and Vn are associated volumes.
            dvh_minimum_dose: Minimum calculated dose to ROI(s) (3004,0070) Type 3.
            dvh_maximum_dose: Maximum calculated dose to ROI(s) (3004,0072) Type 3.
            dvh_mean_dose: Mean calculated dose to ROI(s) (3004,0074) Type 3.

        Returns:
            Dataset: DVH sequence item
        """
        item = Dataset()
        item.DVHReferencedROISequence = dvh_referenced_roi_sequence
        item.DVHType = format_enum_string(dvh_type)
        item.DoseUnits = format_enum_string(dose_units)
        item.DoseType = format_enum_string(dose_type)
        item.DVHDoseScaling = dvh_dose_scaling
        item.DVHVolumeUnits = format_enum_string(dvh_volume_units)
        item.DVHNumberOfBins = dvh_number_of_bins
        item.DVHData = dvh_data

        # Add optional elements if provided
        if dvh_minimum_dose is not None:
            item.DVHMinimumDose = dvh_minimum_dose
        if dvh_maximum_dose is not None:
            item.DVHMaximumDose = dvh_maximum_dose
        if dvh_mean_dose is not None:
            item.DVHMeanDose = dvh_mean_dose

        return item
    
    @staticmethod
    def create_dvh_referenced_roi_item(
        referenced_roi_number: int,
        dvh_roi_contribution_type: str | DVHROIContributionType
    ) -> Dataset:
        """Create DVH referenced ROI sequence item.

        Args:
            referenced_roi_number: ROI Number used to calculate DVH (3006,0084) Type 1.
                Uniquely identifies ROI used to calculate DVH specified by ROI Number in
                Structure Set ROI Sequence in Structure Set Module within RT Structure Set
                referenced by referenced RT Plan in Referenced RT Plan Sequence in RT Dose Module.
            dvh_roi_contribution_type: Whether volume within ROI is included or excluded in DVH
                (3004,0062) Type 1. Enumerated Values: INCLUDED, EXCLUDED.

        Returns:
            Dataset: DVH referenced ROI sequence item
        """
        item = Dataset()
        item.ReferencedROINumber = referenced_roi_number
        item.DVHROIContributionType = format_enum_string(dvh_roi_contribution_type)
        return item
    
    @property
    def has_normalization_info(self) -> bool:
        """Check if DVH normalization information is present.

        Returns:
            bool: True if normalization point or dose value is present
        """
        return ('DVHNormalizationPoint' in self or
                'DVHNormalizationDoseValue' in self)

    @property
    def has_dvh_sequence(self) -> bool:
        """Check if DVH sequence is present.

        Returns:
            bool: True if DVH sequence is present
        """
        return 'DVHSequence' in self
    
    @property
    def dvh_count(self) -> int:
        """Get the number of DVHs in this module.

        Returns:
            int: Number of DVHs in DVH Sequence
        """
        if not self.has_dvh_sequence:
            return 0
        return len(self.DVHSequence)

    @property
    def total_roi_count(self) -> int:
        """Get the total number of ROIs referenced across all DVHs.

        Returns:
            int: Total number of ROI references
        """
        if not self.has_dvh_sequence:
            return 0
        total_rois = 0
        for dvh_item in self.DVHSequence:
            roi_sequence = dvh_item.get('DVHReferencedROISequence', [])
            total_rois += len(roi_sequence)
        return total_rois

    @property
    def has_relative_dose_units(self) -> bool:
        """Check if any DVH uses RELATIVE dose units.

        Returns:
            bool: True if any DVH uses RELATIVE dose units
        """
        if not self.has_dvh_sequence:
            return False
        return any(
            dvh_item.get('DoseUnits', '') == DoseUnits.RELATIVE.value
            for dvh_item in self.DVHSequence
        )

    @property
    def requires_normalization_dose_value(self) -> bool:
        """Check if DVH normalization dose value should be present.

        Returns:
            bool: True if any DVH uses RELATIVE dose units (requiring normalization dose value)
        """
        return self.has_relative_dose_units

    def get_dvh_types(self) -> list[str]:
        """Get list of DVH types present in this module.

        Returns:
            list[str]: List of unique DVH types
        """
        if not self.has_dvh_sequence:
            return []
        dvh_types = []
        for dvh_item in self.DVHSequence:
            dvh_type = dvh_item.get('DVHType', '')
            if dvh_type and dvh_type not in dvh_types:
                dvh_types.append(dvh_type)
        return dvh_types

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this RT DVH Module instance.

        Args:
            config: Optional validation configuration

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return RTDVHValidator.validate(self._dataset, config)
