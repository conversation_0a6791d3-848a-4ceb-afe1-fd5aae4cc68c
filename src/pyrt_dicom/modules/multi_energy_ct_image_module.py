"""
Multi-energy CT Image Module - DICOM PS3.3 C.8.2.2

The Multi-energy CT Image Module contains attributes that describe a Multi-energy CT image.
"""
from datetime import datetime
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.image_enums import MultiEnergySourceTechnique, MultiEnergyDetectorType
from ..validators.modules.multi_energy_ct_image_validator import MultiEnergyCTImageValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_string


class MultiEnergyCTImageModule(BaseModule):
    """Multi-energy CT Image Module implementation for DICOM PS3.3 C.8.2.2.
    
    Uses composition with internal dataset management rather than
    inheriting from pydicom.Dataset for cleaner separation of concerns.
    Contains attributes that describe a Multi-energy CT image with complex
    nested sequences for X-Ray sources, detectors, and acquisition paths.
    
    Usage:
        # Create with required elements
        multi_energy_ct = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=[
                MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                    x_ray_source_sequence=[
                        MultiEnergyCTImageModule.create_x_ray_source_item(
                            x_ray_source_index=1,
                            x_ray_source_id="SOURCE_001",
                            multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                            source_start_datetime="20240101120000.000000",
                            source_end_datetime="20240101120030.000000",
                            switching_phase_number=1
                        )
                    ],
                    x_ray_detector_sequence=[
                        MultiEnergyCTImageModule.create_x_ray_detector_item(
                            x_ray_detector_index=1,
                            x_ray_detector_id="DETECTOR_001",
                            multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                            nominal_max_energy=120.0,
                            nominal_min_energy=20.0
                        )
                    ],
                    path_sequence=[
                        MultiEnergyCTImageModule.create_path_item(
                            path_index=1,
                            referenced_x_ray_source_index=1,
                            referenced_x_ray_detector_index=1
                        )
                    ]
                )
            ]
        )
        
        # Add optional elements
        multi_energy_ct.with_optional_elements(
            multi_energy_acquisition_description="Dual-energy CT acquisition"
        )
        
        # Generate dataset for IOD integration
        dataset = multi_energy_ct.to_dataset()
        
        # Validate
        result = multi_energy_ct.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        # Type 1 elements as positional args (user MUST provide value)
        multi_energy_ct_acquisition_sequence: list[Dataset]
    ) -> 'MultiEnergyCTImageModule':
        """Create Multi-energy CT Image Module with all required (Type 1) elements.
        
        Args:
            multi_energy_ct_acquisition_sequence: Multi-energy CT acquisition attributes (0018,9362) Type 1
        """
        instance = cls()
        
        # Set Type 1 elements directly to internal dataset
        instance[(0x0018, 0x9362)] = multi_energy_ct_acquisition_sequence
        
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        multi_energy_acquisition_description: str | None = None
    ) -> 'MultiEnergyCTImageModule':
        """Add optional (Type 3) elements.
        
        Note: The MultiEnergyAcquisitionDescription is actually part of the 
        acquisition sequence items, not the module level.
        """
        # The multi_energy_acquisition_description parameter is for 
        # consistency with API but the actual attribute is within
        # the Multi-energy CT Acquisition Sequence items
        # This method is provided for API consistency
        
        return self
    
    @staticmethod
    def create_multi_energy_acquisition_item(
        x_ray_source_sequence: list[Dataset],
        x_ray_detector_sequence: list[Dataset],
        path_sequence: list[Dataset],
        multi_energy_acquisition_description: str | None = None
    ) -> Dataset:
        """Create Multi-energy CT Acquisition Sequence item.

        Args:
            x_ray_source_sequence: Multi-energy CT X-Ray Source Sequence (0018,9365) Type 1
            x_ray_detector_sequence: Multi-energy CT X-Ray Detector Sequence (0018,936F) Type 1
            path_sequence: Multi-energy CT Path Sequence (0018,9379) Type 1
            multi_energy_acquisition_description: Human readable summary (0018,937B) Type 3

        Returns:
            Dataset representing multi-energy acquisition item
        """
        item = Dataset()
        item.add_new((0x0018, 0x9365), 'SQ', x_ray_source_sequence)     # MultiEnergyCTXRaySourceSequence
        item.add_new((0x0018, 0x936F), 'SQ', x_ray_detector_sequence)   # MultiEnergyCTXRayDetectorSequence
        item.add_new((0x0018, 0x9379), 'SQ', path_sequence)              # MultiEnergyCTPathSequence
        if multi_energy_acquisition_description:
            item.add_new((0x0018, 0x937B), 'UT', multi_energy_acquisition_description)  # MultiEnergyAcquisitionDescription
        return item
    
    @staticmethod
    def create_x_ray_source_item(
        x_ray_source_index: int,
        x_ray_source_id: str,
        multi_energy_source_technique: str | MultiEnergySourceTechnique,
        source_start_datetime: str | datetime,
        source_end_datetime: str | datetime,
        switching_phase_number: int | None = None,
        switching_phase_nominal_duration: float | None = None,
        switching_phase_transition_duration: float | None = None,
        generator_power: float | None = None
    ) -> Dataset:
        """Create Multi-energy CT X-Ray Source Sequence item.

        Args:
            x_ray_source_index: Identification number of this Item (0018,9366) Type 1
            x_ray_source_id: Identifier of the physical X-Ray source (0018,9367) Type 1
            multi_energy_source_technique: Technique used to acquire Multi-energy data (0018,9368) Type 1
            source_start_datetime: Date and time source was first used (0018,9369) Type 1
            source_end_datetime: Date and time source was last used (0018,936A) Type 1
            switching_phase_number: Number to identify switching phase (0018,936B) Type 1C
            switching_phase_nominal_duration: Duration in target KV (0018,936C) Type 3
            switching_phase_transition_duration: Duration leaving target KV (0018,936D) Type 3
            generator_power: Power in kW going into X-Ray generator (0018,1170) Type 3

        Returns:
            Dataset representing X-Ray source item
        """
        # Format datetime strings if needed (DICOM DT format)
        if isinstance(source_start_datetime, datetime):
            source_start_datetime = source_start_datetime.strftime("%Y%m%d%H%M%S.%f")
        if isinstance(source_end_datetime, datetime):
            source_end_datetime = source_end_datetime.strftime("%Y%m%d%H%M%S.%f")

        # Format enum values using utility function
        formatted_technique = format_enum_string(multi_energy_source_technique)

        item = Dataset()
        item.add_new((0x0018, 0x9366), 'US', x_ray_source_index)           # XRaySourceIndex
        item.add_new((0x0018, 0x9367), 'UC', x_ray_source_id)              # XRaySourceID  
        item.add_new((0x0018, 0x9368), 'CS', formatted_technique)          # MultiEnergySourceTechnique
        item.add_new((0x0018, 0x9369), 'DT', source_start_datetime)        # SourceStartDateTime
        item.add_new((0x0018, 0x936A), 'DT', source_end_datetime)          # SourceEndDateTime

        # Type 1C: Required if Multi-energy Source Technique is "SWITCHING_SOURCE"
        if formatted_technique == "SWITCHING_SOURCE":
            if switching_phase_number is not None:
                item.add_new((0x0018, 0x936B), 'US', switching_phase_number)  # SwitchingPhaseNumber
            else:
                # This is a validation error - switching phase number is required for switching sources
                # But we'll allow creation and let validation catch it
                pass

        # Type 3 optional elements
        if switching_phase_nominal_duration is not None:
            item.add_new((0x0018, 0x936C), 'DS', switching_phase_nominal_duration)  # SwitchingPhaseNominalDuration
        if switching_phase_transition_duration is not None:
            item.add_new((0x0018, 0x936D), 'DS', switching_phase_transition_duration)  # SwitchingPhaseTransitionDuration
        if generator_power is not None:
            item.add_new((0x0018, 0x1170), 'IS', generator_power)  # GeneratorPower

        return item
    
    @staticmethod
    def create_x_ray_detector_item(
        x_ray_detector_index: int,
        x_ray_detector_id: str,
        multi_energy_detector_type: str | MultiEnergyDetectorType,
        nominal_max_energy: float | None = None,
        nominal_min_energy: float | None = None,
        x_ray_detector_label: str | None = None,
        effective_bin_energy: float | None = None
    ) -> Dataset:
        """Create Multi-energy CT X-Ray Detector Sequence item.

        Args:
            x_ray_detector_index: Identification number of this Item (0018,9370) Type 1
            x_ray_detector_id: Identifier of the physical X-Ray detector (0018,9371) Type 1
            multi_energy_detector_type: Technology used to detect multiple energies (0018,9372) Type 1
            nominal_max_energy: Nominal maximum energy in keV (0018,9374) Type 1C
            nominal_min_energy: Nominal minimum energy in keV (0018,9375) Type 1C
            x_ray_detector_label: Label of this Item (0018,9373) Type 3
            effective_bin_energy: Energy of heterogeneous photon beam (0018,936E) Type 3

        Returns:
            Dataset representing X-Ray detector item
        """
        # Format enum values using utility function
        formatted_detector_type = format_enum_string(multi_energy_detector_type)

        item = Dataset()
        item.add_new((0x0018, 0x9370), 'US', x_ray_detector_index)         # XRayDetectorIndex
        item.add_new((0x0018, 0x9371), 'UC', x_ray_detector_id)            # XRayDetectorID
        item.add_new((0x0018, 0x9372), 'CS', formatted_detector_type)      # MultiEnergyDetectorType

        # Type 1C: Required if Multi-energy Detector Type is PHOTON_COUNTING
        if formatted_detector_type == "PHOTON_COUNTING":
            if nominal_max_energy is not None:
                item.add_new((0x0018, 0x9374), 'DS', nominal_max_energy)   # NominalMaxEnergy
            if nominal_min_energy is not None:
                item.add_new((0x0018, 0x9375), 'DS', nominal_min_energy)   # NominalMinEnergy
        else:
            # May be present otherwise
            if nominal_max_energy is not None:
                item.add_new((0x0018, 0x9374), 'DS', nominal_max_energy)   # NominalMaxEnergy
            if nominal_min_energy is not None:
                item.add_new((0x0018, 0x9375), 'DS', nominal_min_energy)   # NominalMinEnergy

        # Type 3 optional elements
        if x_ray_detector_label is not None:
            item.add_new((0x0018, 0x9373), 'ST', x_ray_detector_label)     # XRayDetectorLabel
        if effective_bin_energy is not None:
            item.add_new((0x0018, 0x936E), 'DS', effective_bin_energy)     # EffectiveBinEnergy

        return item
    
    @staticmethod
    def create_path_item(
        path_index: int,
        referenced_x_ray_source_index: int,
        referenced_x_ray_detector_index: int
    ) -> Dataset:
        """Create Multi-energy CT Path Sequence item.

        Args:
            path_index: Identification number of the element (0018,937A) Type 1
            referenced_x_ray_source_index: References X-Ray Source Index (0018,9377) Type 1
            referenced_x_ray_detector_index: References X-Ray Detector Index (0018,9376) Type 1

        Returns:
            Dataset representing path item
        """
        item = Dataset()
        item.add_new((0x0018, 0x937A), 'US', path_index)                           # MultiEnergyCTPathIndex
        item.add_new((0x0018, 0x9377), 'US', referenced_x_ray_source_index)       # ReferencedXRaySourceIndex
        item.add_new((0x0018, 0x9376), 'US', referenced_x_ray_detector_index)     # ReferencedXRayDetectorIndex
        return item
    
    @property
    def has_acquisition_description(self) -> bool:
        """Check if multi-energy acquisition description is present.
        
        Returns:
            bool: True if Multi-energy Acquisition Description is present
        """
        # Check within acquisition sequence items for acquisition description
        if (0x0018, 0x9362) in self:  # MultiEnergyCTAcquisitionSequence
            for acq_item in self[(0x0018, 0x9362)]:
                if (0x0018, 0x937B) in acq_item:  # MultiEnergyAcquisitionDescription
                    return True
        return False
    
    @property
    def acquisition_sequence_count(self) -> int:
        """Get the number of items in Multi-energy CT Acquisition Sequence.
        
        Returns:
            int: Number of acquisition sequence items
        """
        if (0x0018, 0x9362) in self:  # MultiEnergyCTAcquisitionSequence
            return len(self[(0x0018, 0x9362)].value)
        return 0
    
    @property
    def has_switching_sources(self) -> bool:
        """Check if any X-Ray sources use switching technique.
        
        Returns:
            bool: True if any source uses SWITCHING_SOURCE technique
        """
        if (0x0018, 0x9362) in self:  # MultiEnergyCTAcquisitionSequence
            for acq_item in self[(0x0018, 0x9362)]:
                if (0x0018, 0x9365) in acq_item:  # MultiEnergyCTXRaySourceSequence
                    for source_item in acq_item[0x0018, 0x9365].value:
                        if (0x0018, 0x9368) in source_item and source_item[0x0018, 0x9368].value == "SWITCHING_SOURCE":  # MultiEnergySourceTechnique
                            return True
        return False
    
    @property
    def has_photon_counting_detectors(self) -> bool:
        """Check if any detectors use photon counting technology.
        
        Returns:
            bool: True if any detector uses PHOTON_COUNTING type
        """
        if (0x0018, 0x9362) in self:  # MultiEnergyCTAcquisitionSequence
            for acq_item in self[(0x0018, 0x9362)]:
                if (0x0018, 0x936F) in acq_item:  # MultiEnergyCTXRayDetectorSequence
                    for detector_item in acq_item[0x0018, 0x936F].value:
                        if (0x0018, 0x9372) in detector_item and detector_item[0x0018, 0x9372].value == "PHOTON_COUNTING":  # MultiEnergyDetectorType
                            return True
        return False
    
    def get_source_count(self) -> int:
        """Get total number of X-Ray sources across all acquisition items.
        
        Returns:
            int: Total number of X-Ray sources
        """
        total = 0
        if (0x0018, 0x9362) in self:  # MultiEnergyCTAcquisitionSequence
            for acq_item in self[(0x0018, 0x9362)]:
                if (0x0018, 0x9365) in acq_item:  # MultiEnergyCTXRaySourceSequence
                    total += len(acq_item[0x0018, 0x9365].value)
        return total
    
    def get_detector_count(self) -> int:
        """Get total number of X-Ray detectors across all acquisition items.
        
        Returns:
            int: Total number of X-Ray detectors
        """
        total = 0
        if (0x0018, 0x9362) in self:  # MultiEnergyCTAcquisitionSequence
            for acq_item in self[(0x0018, 0x9362)]:
                if (0x0018, 0x936F) in acq_item:  # MultiEnergyCTXRayDetectorSequence
                    total += len(acq_item[0x0018, 0x936F].value)
        return total
    
    def get_path_count(self) -> int:
        """Get total number of acquisition paths across all acquisition items.
        
        Returns:
            int: Total number of acquisition paths
        """
        total = 0
        if (0x0018, 0x9362) in self:  # MultiEnergyCTAcquisitionSequence
            for acq_item in self[(0x0018, 0x9362)]:
                if (0x0018, 0x9379) in acq_item:  # MultiEnergyCTPathSequence
                    total += len(acq_item[0x0018, 0x9379].value)
        return total
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Multi-energy CT Image Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return MultiEnergyCTImageValidator.validate(self._dataset, config)
