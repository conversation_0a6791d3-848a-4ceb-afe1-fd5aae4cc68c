"""
Base Module - Abstract base class for all DICOM modules.

This abstract base class provides the common interface and functionality
that all DICOM modules should implement, using composition with internal
dataset management rather than inheriting from pydicom.Dataset for
cleaner separation of concerns.

IMPORTANT: Modules are NOT intended to be saved! Do NOT include any file 
metadata or save methods in module classes, as these are reserved for IOD classes.
"""
from abc import ABC, abstractmethod
import copy
import pydicom
from typing import Any, Callable, Tuple, Union
from pydicom.dataelem import DataElement
from pydicom.tag import BaseTag, TagType
from pyrt_dicom.validators.validation_error import ValidationError
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.validators.modules.base_validator import ValidationConfig


class BaseModule(ABC):
    """Abstract base class for all DICOM modules.
    
    Uses composition with internal dataset management rather than
    inheriting from pydicom.Dataset for cleaner separation of concerns.
    
    Key Design Principles:
    - Clean IntelliSense: Only methods and properties visible, no raw DICOM attributes
    - DICOM Type Clarity: Type 1 (args), Type 2 (kwargs=""), Type 3 (kwargs=None)
    - Method-Based API: All data setting through explicit named parameter methods
    - Fluent Interface: Methods return self for chaining
    - Validation Integration: Built-in validation with structured error/warning reporting
    
    Note: Modules are NOT intended to be saved directly. File operations and
    metadata are handled by IOD classes that compose modules.
    """
    
    def __init__(self):
        """Initialize module with internal dataset."""
        self._dataset = pydicom.Dataset()
    
    def __contains__(self, key: str | int | Tuple[int, int] | BaseTag) -> bool:
        """Check if element exists in module.
        
        Args:
            key: DICOM tag key (e.g., "PatientName" or "0010,0010")
            
        Returns:
            bool: True if element exists in the module
        """
        return key in self._dataset
    
    def __getattr__(self, name: str) -> Any:
        """Get DICOM attribute value with attribute notation.
        
        Args:
            name: DICOM tag name (e.g., "PatientName")
            
        Returns:
            Any: Value of the data element
            
        Raises:
            AttributeError: If element is not found
        """
        # Avoid recursion by using object.__getattribute__
        try:
            dataset = object.__getattribute__(self, '_dataset')
            return getattr(dataset, name)
        except AttributeError:
            raise AttributeError(
                f"'{type(self).__name__}' object has no attribute '{name}'. "
                f"Check if this is a valid DICOM attribute."
            )
    
    def __getitem__(self, key: str | int | Tuple[int, int] | BaseTag) -> DataElement:
        """Get DICOM attribute value with bracket notation.
        
        Args:
            key: DICOM tag key (e.g., "PatientName" or "0010,0010")
            
        Returns:
            DataElement: The data element object
            
        Raises:
            KeyError: If element is not found
        """
        return self._dataset[key]
    
    def __setattr__(self, name: str, value: Any) -> None:
        """Set DICOM attribute value with attribute notation.
        
        Args:
            name: DICOM tag name (e.g., "PatientName")
            value: Value to set
            
        Raises:
            AttributeError: If element is not found
        """
        # Handle initialization of internal attributes
        if name.startswith('_') or name in ('_dataset',):
            object.__setattr__(self, name, value)
        else:
            # Delegate to dataset for DICOM attributes
            if hasattr(self, '_dataset'):
                setattr(self._dataset, name, value)
            else:
                # During initialization, store in object dict
                object.__setattr__(self, name, value)
    
    def __setitem__(self, key: str | int | Tuple[int, int] | TagType, value: Any) -> None:
        """Set element value with bracket notation.
        
        Args:
            key: DICOM tag key (e.g., "PatientName" or "0010,0010")
            value: Value to set for the data element
        """
        # Use the same logic as set() method
        if isinstance(key, str):
            setattr(self._dataset, key, value)
        else:
            # For tag tuples/objects, use add_new which creates proper DataElement
            from pydicom.tag import Tag
            tag = Tag(key) if not isinstance(key, BaseTag) else key
            vr = self._guess_vr_for_value(value)
            self._dataset.add_new(tag, vr, value)
    
    def __delitem__(self, key: str | int | Tuple[int, int] | TagType) -> None:
        """Delete element with del statement.
        
        Args:
            key: DICOM tag key (e.g., "PatientName" or "0010,0010")
            
        Raises:
            KeyError: If element is not found
        """
        del self._dataset[key]
    
    def __len__(self) -> int:
        """Get the number of DICOM data elements in this module.
        
        Returns:
            int: Number of data elements
        """
        return len(self._dataset)
    
    def get(self, key: str | int | Tuple[int, int] | BaseTag, default: Any = None) -> DataElement | Any:
        """Get DICOM attribute value.
        
        Args:
            key: DICOM tag key (e.g., "PatientName" or "0010,0010")
            default: Default value to return if element is not found
            
        Returns:
            DataElement | Any: DataElement if found, default if not found
        """
        return self._dataset.get(key, default)
    
    def set(self, key: str | int | Tuple[int, int] | BaseTag, value: Any, vr: str | None = None) -> None:
        """Set DICOM element value.
        Args:
            key: DICOM tag key (e.g., "PatientName" or "0010,0010")
            value: Value to set for the data element
            vr: VR to use for the data element (optional - only applies if key is a tag tuple/object)
        """
        # Use pydicom's attribute assignment which handles DataElement creation
        if isinstance(key, str):
            setattr(self._dataset, key, value)
        else:
            # For tag tuples/objects, use add_new which creates proper DataElement
            from pydicom.tag import Tag
            tag = Tag(key) if not isinstance(key, BaseTag) else key
            vr = vr or self._guess_vr_for_value(value)
            self._dataset.add_new(tag, vr, value)
    
    def keys(self):
        """Get all element keys from the module.
        
        Returns:
            Iterator of DICOM tag keys
        """
        return self._dataset.keys()
    
    def values(self):
        """Get all element values from the module.
        
        Returns:
            Iterator of DICOM element values
        """
        return self._dataset.values()
    
    def items(self):
        """Get all (tag, element) pairs from the module.
        
        Returns:
            Iterator of (tag, element) pairs
        """
        return self._dataset.items()
    
    def clear(self) -> None:
        """Remove all data elements from the module."""
        self._dataset.clear()
    
    def copy(self) -> pydicom.Dataset:
        """Create a copy of the internal dataset.
        
        Returns:
            pydicom.Dataset: Copy of the module's dataset
        """
        return copy.deepcopy(self._dataset)
    
    def _guess_vr_for_value(self, value: Any) -> str:
        """Guess appropriate VR for a value.
        
        Args:
            value: The value to guess VR for
            
        Returns:
            str: Best guess VR for the value
        """
        if isinstance(value, str):
            if len(value) <= 16:
                return "LO"  # Long String
            else:
                return "ST"  # Short Text
        elif isinstance(value, int):
            return "IS"  # Integer String
        elif isinstance(value, float):
            return "DS"  # Decimal String
        elif isinstance(value, list):
            return "SQ"  # Sequence
        else:
            return "LO"  # Default to Long String
    
    @classmethod
    @abstractmethod
    def from_required_elements(cls, *args, **kwargs) -> 'BaseModule':
        """Create module instance from all required (Type 1 and Type 2) data elements.
        
        This method must be implemented by each module subclass to define their
        specific required elements according to the DICOM standard.
        
        Pattern:
        - Type 1 elements as positional args (user MUST provide value)
        - Type 2 elements as kwargs with empty string defaults (required but can be empty)
        
        Returns:
            BaseModule: New module instance with required data elements set
        """
        pass
    
    @abstractmethod
    def with_optional_elements(self, *args, **kwargs) -> 'BaseModule':
        """Add optional (Type 3) data elements to the module instance.
        
        This method must be implemented by each module subclass to define their
        specific optional elements according to the DICOM standard.
        
        Pattern:
        - Type 3 elements as kwargs with None defaults (optional, may be absent)
        - Method returns self for fluent chaining
        
        Args:
            **kwargs: Optional data elements as keyword arguments
            
        Returns:
            BaseModule: Self for method chaining
        """
        pass
    
    def to_dataset(self) -> pydicom.Dataset:
        """Generate DICOM dataset from current module state.

        Returns:
            Fresh DICOM dataset containing all module data
        """
        return copy.deepcopy(self._dataset)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this module instance against DICOM standard.
        
        This method should be overridden by subclasses to provide module-specific
        validation logic using their corresponding validator classes.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        # Default implementation returns no errors/warnings
        # Subclasses should override this with their specific validator
        return ValidationResult()
    
    def _validate_and_raise(self, 
                           validation_method: Callable[[Union[pydicom.Dataset, 'BaseModule']], ValidationResult],
                           error_context: str) -> ValidationResult:
        """Execute validation and optionally raise exceptions with zero-copy optimization.
        
        Private method - not exposed to end users via IntelliSense.
        Uses self reference instead of to_dataset() for optimal performance.
        
        Args:
            validation_method: Validation function that accepts Dataset or BaseModule
            error_context: Context string for error messages
            
        Returns:
            ValidationResult if validation passes
            
        Raises:
            ValidationError: If validation fails with errors
        """
        result = validation_method(self)  # Zero-copy: pass self instead of self.to_dataset()
        if result.has_errors:
            raise ValidationError(f"{'; '.join(result.errors)}", error_context)
        return result
    
    def _check_element_validity(self, 
                               element_name: str,
                               validation_method: Callable[[Union[pydicom.Dataset, 'BaseModule']], ValidationResult]) -> ValidationResult:
        """Validate specific element with context using zero-copy optimization.
        
        Private method - not exposed to end users via IntelliSense.
        
        Args:
            element_name: Name of the element being validated
            validation_method: Validation function that accepts Dataset or BaseModule
            
        Returns:
            ValidationResult if validation passes
            
        Raises:
            ValidationError: If validation fails with errors
        """
        result = validation_method(self)  # Zero-copy: direct self reference
        if result.has_errors:
            raise ValidationError(f"{'; '.join(result.errors)}", f"Invalid {element_name}")
        return result
    
    def _ensure_valid_state(self) -> None:
        """Validate current state and raise exceptions for any errors.
        
        Private method - can be called by subclasses when strict validation needed.
        Uses zero-copy validation for optimal performance.
        
        Raises:
            ValidationError: If module validation fails with errors
        """
        result = self.validate()  # Already optimized to use self reference
        if result.has_errors:
            raise ValidationError(f"{'; '.join(result.errors)}", "Module validation failed")
    
    @property
    def module_name(self) -> str:
        """Get the name of this module class.
        
        Returns:
            str: Module class name
        """
        return self.__class__.__name__
    
    @property
    def has_data(self) -> bool:
        """Check if this module contains any DICOM data elements.
        
        Returns:
            bool: True if module has any data elements
        """
        return len(self) > 0
    
    def get_element_count(self) -> int:
        """Get the number of DICOM data elements in this module.

        Returns:
            int: Number of data elements
        """
        return len(self)

    def __repr__(self) -> str:
        """String representation of the module.

        Returns:
            str: Module representation with class name and element count
        """
        count = self.get_element_count()
        return f"{self.module_name}({count} attribute{'s' if count != 1 else ''})"
