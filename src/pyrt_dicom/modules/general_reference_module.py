"""
General Reference Module - DICOM PS3.3 C.12.4

The General Reference Module references source and other related Instances
and describes the manner of derivation.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.common_enums import SpatialLocationsPreserved
from ..validators.modules.general_reference_validator import GeneralReferenceValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_string


class GeneralReferenceModule(BaseModule):
    """General Reference Module implementation for DICOM PS3.3 C.12.4.

    Uses composition with internal dataset management for clean separation of concerns.
    References source and other related Instances and describes the manner of derivation.

    All attributes in this module are Type 3 (optional), but some sequences have
    conditional requirements within their items.

    Usage:
        # Create with no required elements (all are Type 3)
        reference = GeneralReferenceModule.from_required_elements()

        # Add optional elements
        reference.with_optional_elements(
            derivation_description="Contrast enhanced image",
            referenced_image_sequence=[
                GeneralReferenceModule.create_referenced_image_item(
                    referenced_sop_class_uid="1.2.840.10008.*******.1.2",
                    referenced_sop_instance_uid="*******.*******.9"
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = reference.to_dataset()

        # Validate
        result = reference.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'GeneralReferenceModule':
        """Create GeneralReferenceModule with no required elements (all are Type 3).
        
        Returns:
            GeneralReferenceModule: New module instance
        """
        return cls()
    
    def with_optional_elements(
        self,
        referenced_image_sequence: list[Dataset] | None = None,
        referenced_instance_sequence: list[Dataset] | None = None,
        derivation_description: str | None = None,
        derivation_code_sequence: list[Dataset] | None = None,
        source_image_sequence: list[Dataset] | None = None,
        source_instance_sequence: list[Dataset] | None = None
    ) -> 'GeneralReferenceModule':
        """Add optional (Type 3) elements.

        Args:
            referenced_image_sequence (list[Dataset] | None): Other images significantly related (0008,1140) Type 3
            referenced_instance_sequence (list[Dataset] | None): Non-image composite SOP Instances (0008,114A) Type 3
                Note: Purpose of Reference Code Sequence is Type 1 within each item
            derivation_description (str | None): Text description of derivation (0008,2111) Type 3
            derivation_code_sequence (list[Dataset] | None): Coded description of derivation (0008,9215) Type 3
            source_image_sequence (list[Dataset] | None): Source images used to derive this image (0008,2112) Type 3
                Note: Patient Orientation is Type 1C if Spatial Locations Preserved is REORIENTED_ONLY
            source_instance_sequence (list[Dataset] | None): Source non-image instances (0042,0013) Type 3

        Returns:
            GeneralReferenceModule: Self for method chaining
        """
        if referenced_image_sequence is not None:
            self.ReferencedImageSequence = referenced_image_sequence
        if referenced_instance_sequence is not None:
            self.ReferencedInstanceSequence = referenced_instance_sequence
        if derivation_description is not None:
            self.DerivationDescription = derivation_description
        if derivation_code_sequence is not None:
            self.DerivationCodeSequence = derivation_code_sequence
        if source_image_sequence is not None:
            self.SourceImageSequence = source_image_sequence
        if source_instance_sequence is not None:
            self.SourceInstanceSequence = source_instance_sequence
        return self
    
    @staticmethod
    def create_referenced_image_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_frame_number: list[int] | None = None,
        purpose_of_reference_code_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create referenced image sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            referenced_frame_number (list[int] | None): Referenced frame numbers
            purpose_of_reference_code_sequence (list[dict] | None): Purpose of reference code

        Returns:
            Dataset: Referenced image sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        if referenced_frame_number is not None:
            item.ReferencedFrameNumber = referenced_frame_number
        if purpose_of_reference_code_sequence is not None:
            item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        return item
    
    @staticmethod
    def create_source_image_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_frame_number: list[int] | None = None,
        purpose_of_reference_code_sequence: list[Dataset] | None = None,
        spatial_locations_preserved: str | SpatialLocationsPreserved | None = None,
        patient_orientation: list[str] | None = None
    ) -> Dataset:
        """Create source image sequence item.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150) - from Image SOP Instance Reference Macro
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155) - from Image SOP Instance Reference Macro
            referenced_frame_number (list[int] | None): Referenced frame numbers (0008,1160) - Type 1C from macro
            purpose_of_reference_code_sequence (list[Dataset] | None): Purpose of reference code (0040,A170) Type 3
                Uses CID 7202 Source Image Purpose of Reference
            spatial_locations_preserved (str | SpatialLocationsPreserved | None): Spatial locations preserved (0028,135A) Type 3
                Enumerated values: YES, NO, REORIENTED_ONLY
            patient_orientation (list[str] | None): Patient orientation (0020,0020) Type 1C
                Required if spatial_locations_preserved is REORIENTED_ONLY

        Returns:
            Dataset: Source image sequence item

        Note:
            Type 1C validation (Patient Orientation required when Spatial Locations Preserved 
            is REORIENTED_ONLY) is handled by the validator, not during item creation.
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid

        if referenced_frame_number is not None:
            item.ReferencedFrameNumber = referenced_frame_number
        if purpose_of_reference_code_sequence is not None:
            item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence

        if spatial_locations_preserved is not None:
            spatial_value = format_enum_string(spatial_locations_preserved)
            item.SpatialLocationsPreserved = spatial_value

        if patient_orientation is not None:
            item.PatientOrientation = patient_orientation

        return item
    
    @staticmethod
    def create_referenced_instance_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        purpose_of_reference_code_sequence: list[Dataset]
    ) -> Dataset:
        """Create referenced instance sequence item with required purpose code.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150) - from SOP Instance Reference Macro
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155) - from SOP Instance Reference Macro
            purpose_of_reference_code_sequence (list[Dataset]): Purpose of reference code (0040,A170) Type 1 (required)
                Uses CID 7004 for waveforms, CID 7022 for RT instances

        Returns:
            Dataset: Referenced instance sequence item

        Note:
            Type 1 validation (Purpose of Reference Code Sequence is required for 
            Referenced Instance Sequence items) is handled by the validator, not during item creation.
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        if purpose_of_reference_code_sequence:
            item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence
        return item
    
    @staticmethod
    def create_source_instance_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        purpose_of_reference_code_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create source instance sequence item for non-image sources.

        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150) - from SOP Instance Reference Macro
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155) - from SOP Instance Reference Macro
            purpose_of_reference_code_sequence (list[Dataset] | None): Purpose of reference code (0040,A170) Type 3
                Uses CID 7013 Non-Image Source Instance Purpose of Reference

        Returns:
            Dataset: Source instance sequence item

        Note:
            Images shall NOT be referenced by Source Instance Sequence per DICOM standard.
            Use Source Image Sequence for image references.
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid

        if purpose_of_reference_code_sequence is not None:
            item.PurposeOfReferenceCodeSequence = purpose_of_reference_code_sequence

        return item

    @property
    def has_referenced_images(self) -> bool:
        """Check if referenced images are present.

        Returns:
            bool: True if referenced image sequence is present
        """
        return 'ReferencedImageSequence' in self

    @property
    def has_derivation_info(self) -> bool:
        """Check if derivation information is present.

        Returns:
            bool: True if derivation description or code sequence is present
        """
        return ('DerivationDescription' in self or
                'DerivationCodeSequence' in self)

    @property
    def has_source_images(self) -> bool:
        """Check if source images are present.

        Returns:
            bool: True if source image sequence is present
        """
        return 'SourceImageSequence' in self

    @property
    def has_referenced_instances(self) -> bool:
        """Check if referenced instances are present.

        Returns:
            bool: True if referenced instance sequence is present
        """
        return 'ReferencedInstanceSequence' in self

    @property
    def has_source_instances(self) -> bool:
        """Check if source instances are present.

        Returns:
            bool: True if source instance sequence is present
        """
        return 'SourceInstanceSequence' in self

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this General Reference Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult object
        """
        return GeneralReferenceValidator.validate(self._dataset, config)
