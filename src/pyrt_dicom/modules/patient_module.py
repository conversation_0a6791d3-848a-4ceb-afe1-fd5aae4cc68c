"""Patient Module - DICOM PS3.3 C.7.1.1

The Patient Module contains attributes of the Patient that are needed for
interpretation of the Composite Instances and are common for all Studies
performed on the Patient. This module contains Attributes of the Patient
that are needed for interpretation of the Composite Instances and are
common for all Studies performed on the Patient.
"""
from datetime import datetime, date
from pydicom import Dataset
from pydicom.valuerep import PersonName
from .base_module import BaseModule
from ..enums.patient_enums import PatientSex, ResponsiblePersonRole, TypeOfPatientID
from ..validators.modules.patient_validator import PatientValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class PatientModule(BaseModule):
    """Patient Module implementation for DICOM PS3.3 C.7.1.1.
    
    Contains attributes of the Patient that are needed for interpretation of
    Composite Instances and are common for all Studies performed on the Patient.
    This module contains Attributes that identify and describe the Patient who
    is the subject of the Study.
    
    Usage:
        # Create with required elements (all Type 2 - required but can be empty)
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        # Add optional elements
        patient.with_optional_elements(
            type_of_patient_id=TypeOfPatientID.TEXT,
            quality_control_subject="NO"
        )
        
        # Add conditional elements for non-human organisms
        patient.with_non_human_organism(
            patient_species_description="Canis lupus familiaris",
            responsible_person="Dr. Smith"
        )
        
        # Generate dataset
        dataset = patient.to_dataset()
        
        # Validate
        result = patient.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        patient_name: str | PersonName = "",
        patient_id: str = "",
        patient_birth_date: str | datetime | date = "",
        patient_sex: str | PatientSex = ""
    ) -> 'PatientModule':
        """Create PatientModule from all required (Type 1 and Type 2) data elements.
        
        All elements in the Patient Module are Type 2 (required but can be empty)
        or conditional types. The core required elements are all Type 2.
        
        Args:
            patient_name (str | PersonName): Patient's full name (0010,0010) Type 2.
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            patient_id (str): Primary identifier for the Patient (0010,0020) Type 2
            patient_birth_date (str | datetime | date): Birth date of the Patient (0010,0030) Type 2.
                Can be a string in YYYYMMDD format, datetime object, or date object.
            patient_sex (str | PatientSex): Sex of the named Patient - M/F/O or PatientSex enum (0010,0040) Type 2
            
        Returns:
            PatientModule: New module instance with required data elements set
        """
        instance = cls()
        instance["PatientName"] = patient_name
        instance["PatientID"] = patient_id
        instance["PatientBirthDate"] = format_date_value(patient_birth_date)
        instance["PatientSex"] = format_enum_string(patient_sex)
        return instance
    
    def with_optional_elements(
        self,
        type_of_patient_id: str | TypeOfPatientID | None = None,
        referenced_patient_photo_sequence: list[Dataset] | None = None,
        quality_control_subject: str | None = None,
        referenced_patient_sequence: list[Dataset] | None = None,
        patient_birth_time: str | datetime | None = None,
        other_patient_ids_sequence: list[Dataset] | None = None,
        other_patient_names: str | PersonName | None = None,
        ethnic_group_code_sequence: list[Dataset] | None = None,
        ethnic_groups: str | None = None,
        patient_comments: str | None = None,
        strain_description: str | None = None,
        strain_nomenclature: str | None = None,
        strain_code_sequence: list[Dataset] | None = None,
        strain_additional_information: str | None = None,
        strain_stock_sequence: list[Dataset] | None = None,
        genetic_modifications_sequence: list[Dataset] | None = None,
        patient_identity_removed: str | None = None
    ) -> 'PatientModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            type_of_patient_id (str | TypeOfPatientID | None): Type of identifier - TEXT/RFID/BARCODE or TypeOfPatientID enum (0010,0022)
            referenced_patient_photo_sequence (list[Dataset] | None): Photo to confirm identity (0010,1100)
            quality_control_subject (str | None): Quality control phantom - YES/NO (0010,0200)
            referenced_patient_sequence (list[Dataset] | None): Reference to a Patient (0008,1120)
            patient_birth_time (str | datetime | None): Birth time of Patient (0010,0032).
                Can be a string in HHMMSS format or datetime object.
                If datetime is provided, only the time portion is used.
            other_patient_ids_sequence (list[Dataset] | None): Additional patient identifiers (0010,1002)
            other_patient_names (str | PersonName | None): Other names for patient (0010,1001).
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            ethnic_group_code_sequence (list[Dataset] | None): Ethnic group codes (0010,2161)
            ethnic_groups (str | None): Ethnic group values (0010,2162)
            patient_comments (str | None): Additional patient information (0010,4000)
            strain_description (str | None): Strain of the Patient (0010,0212)
            strain_nomenclature (str | None): Nomenclature for strain (0010,0213)
            strain_code_sequence (list[Dataset] | None): Coded strain identification (0010,0219)
            strain_additional_information (str | None): Additional strain info (0010,0218)
            strain_stock_sequence (list[Dataset] | None): Strain stock information (0010,0216)
            genetic_modifications_sequence (list[Dataset] | None): Genetic modifications (0010,0221)
            patient_identity_removed (str | None): Identity removal flag - YES/NO (0012,0062)
            
        Returns:
            PatientModule: Self with optional elements added
        """
        if type_of_patient_id is not None:
            self.TypeOfPatientID = format_enum_string(type_of_patient_id)
        if referenced_patient_photo_sequence is not None:
            self.ReferencedPatientPhotoSequence = referenced_patient_photo_sequence
        if quality_control_subject is not None:
            self.QualityControlSubject = quality_control_subject
        if referenced_patient_sequence is not None:
            self.ReferencedPatientSequence = referenced_patient_sequence
        if patient_birth_time is not None:
            self.PatientBirthTime = format_time_value(patient_birth_time)
        if other_patient_ids_sequence is not None:
            self.OtherPatientIDsSequence = other_patient_ids_sequence
        if other_patient_names is not None:
            self.OtherPatientNames = other_patient_names
        if ethnic_group_code_sequence is not None:
            self.EthnicGroupCodeSequence = ethnic_group_code_sequence
        if ethnic_groups is not None:
            self.EthnicGroups = ethnic_groups
        if patient_comments is not None:
            self.PatientComments = patient_comments
        if strain_description is not None:
            self.StrainDescription = strain_description
        if strain_nomenclature is not None:
            self.StrainNomenclature = strain_nomenclature
        if strain_code_sequence is not None:
            self.StrainCodeSequence = strain_code_sequence
        if strain_additional_information is not None:
            self.StrainAdditionalInformation = strain_additional_information
        if strain_stock_sequence is not None:
            self.StrainStockSequence = strain_stock_sequence
        if genetic_modifications_sequence is not None:
            self.GeneticModificationsSequence = genetic_modifications_sequence
        if patient_identity_removed is not None:
            self.PatientIdentityRemoved = patient_identity_removed
        return self
    
    def with_alternative_calendar(
        self,
        patient_alternative_calendar: str,
        patient_birth_date_in_alternative_calendar: str | datetime | date | None = None,
        patient_death_date_in_alternative_calendar: str | datetime | date | None = None
    ) -> 'PatientModule':
        """Add alternative calendar dates with calendar specification.
        
        Note: Type 1C Requirement - Patient's Alternative Calendar (0010,0035) is required if either
        Patient's Birth Date in Alternative Calendar (0010,0033) or Patient's Death Date 
        in Alternative Calendar (0010,0034) is present. This validation is handled by the validator.
        
        Args:
            patient_alternative_calendar (str): Alternative calendar used (0010,0035) Type 1C
            patient_birth_date_in_alternative_calendar (str | datetime | date | None): Birth date in alt calendar (0010,0033) Type 3.
                Can be a string, datetime object, or date object.
            patient_death_date_in_alternative_calendar (str | datetime | date | None): Death date in alt calendar (0010,0034) Type 3.
                Can be a string, datetime object, or date object.
            
        Returns:
            PatientModule: Self with alternative calendar elements added
        """
        self.PatientAlternativeCalendar = patient_alternative_calendar
        if patient_birth_date_in_alternative_calendar is not None:
            self.PatientBirthDateInAlternativeCalendar = format_date_value(patient_birth_date_in_alternative_calendar)
        if patient_death_date_in_alternative_calendar is not None:
            self.PatientDeathDateInAlternativeCalendar = format_date_value(patient_death_date_in_alternative_calendar)
        return self
    
    def with_non_human_organism(
        self,
        patient_species_description: str | None = None,
        patient_species_code_sequence: list[Dataset] | None = None,
        patient_breed_description: str | None = None,
        patient_breed_code_sequence: list[Dataset] | None = None,
        breed_registration_sequence: list[Dataset] | None = None,
        responsible_person: str | PersonName | None = None,
        responsible_organization: str | None = None
    ) -> 'PatientModule':
        """Add non-human organism data with conditional elements.
        
        Note: Type 1C/2C Requirements for non-human organisms per DICOM PS3.3 C.7.1.1:
        - Type 1C: Patient Species Description (0010,2201) OR Patient Species Code Sequence (0010,2202)
        - Type 2C: Patient Breed Code Sequence (0010,2293) - required but may be empty
        - Type 2C: Patient Breed Description (0010,2292) - required if Breed Code Sequence is empty
        - Type 2C: Breed Registration Sequence (0010,2294) - required but may be empty
        - Type 2C: Responsible Person (0010,2297) - required but may be empty
        - Type 2C: Responsible Organization (0010,2299) - required but may be empty
        These requirements are validated by the validator.
        
        Args:
            patient_species_description (str | None): Taxonomic rank value (0010,2201) Type 1C
            patient_species_code_sequence (list[Dataset] | None): Coded taxonomic rank (0010,2202) Type 1C
            patient_breed_description (str | None): Breed description (0010,2292) Type 2C
            patient_breed_code_sequence (list[Dataset] | None): Coded breed (0010,2293) Type 2C
            breed_registration_sequence (list[Dataset] | None): Breed registry info (0010,2294) Type 2C
            responsible_person (str | PersonName | None): Person with authority (0010,2297) Type 2C
            responsible_organization (str | None): Organization with authority (0010,2299) Type 2C
            
        Returns:
            PatientModule: Self with non-human organism elements added
        """
        # Set species information (Type 1C)
        if patient_species_description is not None:
            self.PatientSpeciesDescription = patient_species_description
        if patient_species_code_sequence is not None:
            self.PatientSpeciesCodeSequence = patient_species_code_sequence

        # Set breed information (Type 2C - required for non-human organisms)
        self.PatientBreedCodeSequence = patient_breed_code_sequence or []
        self.PatientBreedDescription = patient_breed_description or ""

        # Set registration and responsibility (Type 2C - required for non-human organisms)
        self.BreedRegistrationSequence = breed_registration_sequence or []
        self.ResponsiblePerson = responsible_person or ""
        self.ResponsibleOrganization = responsible_organization or ""
        
        return self
    
    def with_responsible_person(
        self,
        responsible_person: str | PersonName,
        responsible_person_role: str | ResponsiblePersonRole
    ) -> 'PatientModule':
        """Add responsible person with role specification.
        
        Note: Type 1C Requirement - Responsible Person Role (0010,2298) is required if 
        Responsible Person (0010,2297) is present and has a value. This validation is handled by the validator.
        
        Args:
            responsible_person (str | PersonName): Name of person with authority (0010,2297).
                Can be a string formatted as "Family^Given^Middle^Prefix^Suffix" 
                or a pydicom.valuerep.PersonName object.
            responsible_person_role (str | ResponsiblePersonRole): Role or ResponsiblePersonRole enum (0010,2298) Type 1C
            
        Returns:
            PatientModule: Self with responsible person elements added
        """
        self.ResponsiblePerson = responsible_person
        if responsible_person_role:
            self.ResponsiblePersonRole = format_enum_string(responsible_person_role)
        return self
    
    def with_deidentification(
        self,
        patient_identity_removed: str | None = "YES",
        de_identification_method: str | None = None,
        de_identification_method_code_sequence: list[Dataset] | None = None
    ) -> 'PatientModule':
        """Add patient identity removal with method specification.
        
        Note: Type 1C Requirements per DICOM PS3.3 C.7.1.1:
        - De-identification Method (0012,0063): Required if Patient Identity Removed is "YES" 
          AND De-identification Method Code Sequence is not present
        - De-identification Method Code Sequence (0012,0064): Required if Patient Identity Removed 
          is "YES" AND De-identification Method is not present
        These requirements are validated by the validator.
        
        Args:
            patient_identity_removed (str | None): Identity removal flag - YES/NO (0012,0062) Type 3
            de_identification_method (str | None): Description of removal method (0012,0063) Type 1C
            de_identification_method_code_sequence (list[Dataset] | None): Coded removal method (0012,0064) Type 1C
            
        Returns:
            PatientModule: Self with deidentification elements added
        """
        if patient_identity_removed is not None:
            self.PatientIdentityRemoved = patient_identity_removed
        
        if de_identification_method is not None:
            self.DeIdentificationMethod = de_identification_method
        if de_identification_method_code_sequence is not None:
            self.DeIdentificationMethodCodeSequence = de_identification_method_code_sequence
        
        return self
    
    @staticmethod
    def create_other_patient_id_item(
        patient_id: str,
        type_of_patient_id: str | TypeOfPatientID
    ) -> Dataset:
        """Create an item for Other Patient IDs Sequence (0010,1002).
        
        Args:
            patient_id (str): An identifier for the Patient (0010,0020) Type 1
            type_of_patient_id (str | TypeOfPatientID): Type of identifier - TEXT/RFID/BARCODE or TypeOfPatientID enum (0010,0022) Type 1
            
        Returns:
            Dataset: Sequence item with Patient ID and Type
        """
        item = Dataset()
        item.PatientID = patient_id
        if isinstance(type_of_patient_id, TypeOfPatientID):
            item.TypeOfPatientID = type_of_patient_id.value
        else:
            item.TypeOfPatientID = type_of_patient_id
        return item
    
    @staticmethod
    def create_strain_stock_item(
        strain_stock_number: str,
        strain_source: str,
        strain_source_registry_code: Dataset
    ) -> Dataset:
        """Create an item for Strain Stock Sequence (0010,0216).
        
        Args:
            strain_stock_number (str): Stock number of the strain (0010,0214) Type 1
            strain_source (str): Organization source of the organism (0010,0217) Type 1
            strain_source_registry_code (Dataset): Registry code sequence item (0010,0215) Type 1
            
        Returns:
            Dataset: Sequence item with strain stock information
        """
        item = Dataset()
        item.StrainStockNumber = strain_stock_number
        item.StrainSource = strain_source
        item.StrainSourceRegistryCodeSequence = [strain_source_registry_code]
        return item
    
    @staticmethod
    def create_genetic_modification_item(
        genetic_modifications_description: str,
        genetic_modifications_nomenclature: str,
        genetic_modifications_code_sequence: list[Dataset] | None = None
    ) -> Dataset:
        """Create an item for Genetic Modifications Sequence (0010,0221).
        
        Args:
            genetic_modifications_description (str): Genetic modifications description (0010,0222) Type 1
            genetic_modifications_nomenclature (str): Nomenclature used (0010,0223) Type 1
            genetic_modifications_code_sequence (list[Dataset] | None): Coded identification (0010,0229) Type 3
            
        Returns:
            Dataset: Sequence item with genetic modification information
        """
        item = Dataset()
        item.GeneticModificationsDescription = genetic_modifications_description
        item.GeneticModificationsNomenclature = genetic_modifications_nomenclature
        
        if genetic_modifications_code_sequence is not None:
            item.GeneticModificationsCodeSequence = genetic_modifications_code_sequence
        
        return item
    
    @property
    def is_human(self) -> bool:
        """Check if this patient represents a human organism.
        
        Returns:
            bool: True if no species information indicates human organism
        """
        return not self.is_non_human
    
    @property
    def is_non_human(self) -> bool:
        """Check if this patient represents a non-human organism.
        
        Returns:
            bool: True if species information indicates non-human organism
        """
        return ("PatientSpeciesDescription" in self or 
                "PatientSpeciesCodeSequence" in self)
    
    @property
    def is_deidentified(self) -> bool:
        """Check if patient identity has been removed.
        
        Returns:
            bool: True if PatientIdentityRemoved is "YES"
        """
        if "PatientIdentityRemoved" not in self:
            return False
        return self.PatientIdentityRemoved == "YES"
    
    @property
    def has_alternative_calendar_dates(self) -> bool:
        """Check if alternative calendar dates are present.
        
        Returns:
            bool: True if birth or death date in alternative calendar is present
        """
        return ("PatientBirthDateInAlternativeCalendar" in self or
                "PatientDeathDateInAlternativeCalendar" in self)
    
    @property
    def has_responsible_person_with_role(self) -> bool:
        """Check if responsible person is present with required role.
        
        Returns:
            bool: True if both responsible person and role are present
        """
        has_person = "ResponsiblePerson" in self and bool(str(self.ResponsiblePerson).strip())
        has_role = "ResponsiblePersonRole" in self and bool(str(self.ResponsiblePersonRole).strip())
        return has_person and has_role
    
    @property
    def requires_alternative_calendar(self) -> bool:
        """Check if alternative calendar specification is required (Type 1C).
        
        Returns:
            bool: True if alternative calendar dates are present, requiring calendar specification
        """
        return self.has_alternative_calendar_dates
    
    @property
    def requires_deidentification_method(self) -> bool:
        """Check if deidentification method is required (Type 1C).
        
        Returns:
            bool: True if patient identity is removed, requiring method specification
        """
        return self.is_deidentified
    
    # Public validation convenience methods with zero-copy optimization
    def check_type_2_requirements(self) -> ValidationResult:
        """Check Type 2 required elements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientValidator.validate_type_2_requirements(self)  # Pass self, not self.to_dataset()
    
    def check_conditional_requirements(self) -> ValidationResult:
        """Check Type 1C and 2C conditional requirements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientValidator.validate_conditional_requirements(self)
    
    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.
        
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientValidator.validate_enumerated_values(self)
    
    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.
        
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientValidator.validate_sequence_requirements(self)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientValidator.validate(self, config)  # Direct self reference for performance
    
    # Private methods for strict validation scenarios
    def _ensure_type_2_requirements_valid(self) -> None:
        """Ensure Type 2 required elements are valid, raise exception if not."""
        self._validate_and_raise(
            PatientValidator.validate_type_2_requirements,
            "Type 2 requirements validation failed"
        )
    
    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not."""
        self._validate_and_raise(
            PatientValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )
    
    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enumerated value constraints are valid, raise exception if not."""
        self._validate_and_raise(
            PatientValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )
    
    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence structure requirements are valid, raise exception if not."""
        self._validate_and_raise(
            PatientValidator.validate_sequence_requirements,
            "Sequence requirements validation failed"
        )