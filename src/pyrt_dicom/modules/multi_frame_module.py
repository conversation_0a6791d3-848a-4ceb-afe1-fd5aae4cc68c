"""
Multi-frame Module Implementation - DICOM PS3.3 C.7.6.6

This module implements the DICOM Multi-frame Module as specified in PS3.3 Section C.7.6.6.
The Multi-frame Module enables storage and management of sequential image data where multiple
frames are combined into a single DICOM object, essential for dynamic imaging modalities,
time series analysis, and volumetric reconstruction workflows.

Multi-frame images are fundamental to many medical imaging applications:
- Dynamic studies: Cardiac cine, perfusion imaging, and functional analysis
- Time series: Temporal bone studies, contrast enhancement tracking, and treatment monitoring
- Volumetric data: CT angiography, MR imaging sequences, and reconstruction datasets
- Stereoscopic imaging: 3D visualization and depth perception applications
- Functional imaging: fMRI, cardiac wall motion, and respiratory-gated acquisitions

The module provides comprehensive frame management including temporal sequencing, stereoscopic
pair coordination, and functional group organization. It supports multiple frame increment
strategies for different imaging scenarios and temporal sampling requirements.

Key features include:
- Frame sequencing control through Frame Increment Pointer management
- Stereoscopic pair detection and validation for 3D imaging workflows
- Temporal frame timing through Frame Time and Frame Time Vector support
- Functional group integration for enhanced multi-frame organization
- Comprehensive validation for clinical compatibility and DICOM compliance
"""
from pydicom.tag import Tag, BaseTag
from .base_module import BaseModule
from ..enums.image_enums import StereoPairsPresent, FrameType
from ..validators.modules.multi_frame_validator import MultiFrameValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..utils.dicom_formatters import format_enum_string


class MultiFrameModule(BaseModule):
    """DICOM Multi-frame Module implementation conforming to PS3.3 Section C.7.6.6.
    
    This class provides a comprehensive interface for creating and managing multi-frame DICOM
    images where multiple image frames are combined into a single DICOM object. Multi-frame
    imaging is essential for dynamic studies, temporal analysis, volumetric reconstruction,
    and stereoscopic visualization in modern medical imaging workflows.
    
    Multi-frame images enable advanced clinical applications:
    - Cardiac imaging: Cine loops showing heart wall motion and valve function
    - Perfusion studies: Temporal contrast enhancement tracking for tissue characterization
    - Dynamic imaging: Real-time fluoroscopy, angiography, and interventional guidance
    - Volumetric reconstruction: CT/MR volume datasets for 3D visualization and analysis
    - Stereoscopic imaging: Left/right pairs for depth perception and surgical planning
    - Functional studies: fMRI time series, cardiac strain analysis, and motion tracking
    
    Technical Implementation Features:
    - Frame Increment Pointer management for flexible temporal sequencing strategies
    - Support for Frame Time, Frame Time Vector, and Functional Groups sequencing
    - Stereoscopic pair validation and expected frame count calculation
    - Automatic tag format conversion (string, Tag objects, integers) for user convenience
    - Comprehensive frame timing and sequence validation for clinical compatibility
    
    DICOM Compliance:
    This implementation follows DICOM PS3.3 C.7.6.6 specifications exactly, ensuring
    compatibility with all DICOM viewers, PACS systems, and advanced imaging workstations.
    
    Args:
        None (use factory methods for creation)
    
    Example:
        Cardiac cine loop with frame timing:
        
        >>> # Create cardiac cine with 20 frames using frame time
        >>> cardiac_cine = MultiFrameModule.from_required_elements(
        ...     number_of_frames=20,
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ... )
        
        >>> # Configure for cardiac imaging workflow
        >>> cardiac_cine.with_optional_elements(
        ...     stereo_pairs_present=StereoPairsPresent.NO,
        ...     frame_type=[FrameType.ORIGINAL, FrameType.PRIMARY, FrameType.CARDIAC, FrameType.GATED],
        ...     encapsulated_pixel_data_value_total_length=2048000
        ... )
        
        Dynamic perfusion study with time vector:
        
        >>> # Create perfusion study with variable frame timing
        >>> perfusion_study = MultiFrameModule.from_required_elements(
        ...     number_of_frames=50,
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()
        ... )
        
        Stereoscopic 3D imaging:
        
        >>> # Create stereoscopic dataset with paired frames
        >>> stereo_volume = MultiFrameModule.from_required_elements(
        ...     number_of_frames=100,  # 50 stereo pairs
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_functional_groups()
        ... )
        
        >>> # Enable stereoscopic pair processing
        >>> stereo_volume.with_optional_elements(
        ...     stereo_pairs_present=StereoPairsPresent.YES,
        ...     frame_type=[FrameType.DERIVED, FrameType.SECONDARY, FrameType.STATIC]
        ... )
        
        >>> # Validate stereoscopic configuration
        >>> print(f"Expected stereo pairs: {stereo_volume.expected_stereo_frame_count}")
        Expected stereo pairs: 50
        
        Flexible tag format support:
        
        >>> # Multiple ways to specify frame increment pointer
        >>> # Method 1: Using helper methods (recommended)
        >>> frames1 = MultiFrameModule.from_required_elements(
        ...     number_of_frames=10,
        ...     frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ... )
        
        >>> # Method 2: Using string format
        >>> frames2 = MultiFrameModule.from_required_elements(
        ...     number_of_frames=10,
        ...     frame_increment_pointer=["0018,1063"]  # Frame Time tag
        ... )
        
        >>> # Method 3: Using Tag objects
        >>> from pydicom.tag import Tag
        >>> frames3 = MultiFrameModule.from_required_elements(
        ...     number_of_frames=10,
        ...     frame_increment_pointer=[Tag(0x0018, 0x1063)]
        ... )
        
        Validation and clinical use:
        
        >>> # Always validate before clinical use
        >>> result = cardiac_cine.validate()
        >>> if result.is_valid:
        ...     print("Multi-frame module ready for clinical imaging")
        ...     print(f"Frame sequencing: {cardiac_cine.get_frame_increment_attributes()}")
        ... else:
        ...     print("Validation issues found:", result.errors)
    
    See Also:
        - StereoPairsPresent enum: Defines stereoscopic imaging modes
        - MultiFrameValidator: Validates multi-frame data for DICOM compliance
        - Frame increment strategies: Frame Time, Frame Time Vector, Functional Groups
        - DICOM PS3.3 Section C.7.6.6: Complete specification reference
    """

    @classmethod
    def from_required_elements(
        cls,
        number_of_frames: int,
        frame_increment_pointer: list[int] | list[BaseTag] | list[str]
    ) -> 'MultiFrameModule':
        """Create MultiFrameModule with all required DICOM elements for multi-frame imaging.
        
        Creates a complete multi-frame module with all Type 1 (required) DICOM attributes.
        The Frame Increment Pointer defines how frames are sequenced and timed, enabling
        proper temporal organization for dynamic imaging and volumetric reconstruction.
        
        Args:
            number_of_frames (int): Total number of frames in the multi-frame image (0028,0008) Type 1.
                Must be greater than zero. For stereoscopic imaging, should be even (pairs).
                
            frame_increment_pointer (list[int | Tag | str]): DICOM tags defining frame sequencing (0028,0009) Type 1.
                Specifies which DICOM attributes control frame progression and timing. Supports:
                - int: Combined tag value (e.g., 0x00181063 for Frame Time)
                - Tag: pydicom Tag object (e.g., Tag(0x0018, 0x1063))
                - str: Tag in "GGGG,EEEE" format (e.g., "0018,1063") - automatically converted
            
        Returns:
            MultiFrameModule: Configured module ready for optional elements and validation.
                
        Note:
            No validation is performed during module creation. Use validate() method to check
            for proper DICOM compliance and data integrity.
        """
        from ..validators.modules.multi_frame_validator import MultiFrameValidator
        
        instance = cls()
        instance['NumberOfFrames'] = number_of_frames
        
        # Convert frame increment pointer values using validator (no exceptions raised)
        converted_tags, conversion_result = MultiFrameValidator.convert_frame_increment_pointer(frame_increment_pointer)
        
        # Always set the converted tags, even if there were validation errors
        # If conversion completely failed, create a minimal valid value to prevent pydicom errors
        # Validation errors will be caught when validate() is called
        if converted_tags:
            instance['FrameIncrementPointer'] = converted_tags
        else:
            # If conversion completely failed, use a placeholder value that won't cause pydicom to crash
            # This will be caught by validation later
            from pydicom.tag import Tag
            instance['FrameIncrementPointer'] = [int(Tag(0x0008, 0x0001))]  # Use a minimal valid tag as placeholder
        
        # Store original input and conversion errors for later validation
        instance._original_frame_increment_pointer = frame_increment_pointer
        instance._frame_increment_pointer_conversion_errors = conversion_result
        
        return instance
    
    def with_optional_elements(
        self,
        stereo_pairs_present: str | StereoPairsPresent | None = None,
        frame_type: list[str | FrameType] | None = None,
        encapsulated_pixel_data_value_total_length: int | None = None
    ) -> 'MultiFrameModule':
        """Add optional elements to enhance multi-frame processing and stereoscopic capabilities.
        
        These Type 3 elements provide additional metadata for advanced multi-frame imaging
        workflows, particularly stereoscopic 3D visualization and compressed pixel data
        management.
        
        Args:
            stereo_pairs_present (str | StereoPairsPresent | None): Stereoscopic imaging mode (0022,0028) Type 3.
                Indicates whether the multi-frame dataset contains stereoscopic image pairs.
                StereoPairsPresent.YES for left/right pairs, StereoPairsPresent.NO for sequential frames.

            frame_type (list[str | FrameType] | None): Frame Type classification (0008,9007) Type 3.
                Multi-valued frame classification for enhanced multi-frame images.
                Use FrameType enum: FrameType.ORIGINAL, FrameType.PRIMARY, FrameType.DYNAMIC, etc.
                String values are also accepted for compatibility.

            encapsulated_pixel_data_value_total_length (int | None): Total compressed data size (7FE0,0003) Type 3.
                Specifies the total byte length of pixel data when all compressed fragments
                are combined. Used for memory allocation and transfer optimization.
            
        Returns:
            MultiFrameModule: Self-reference enabling method chaining for fluent interface.
            
        Note:
            When stereoscopic pairs are enabled, ensure the number_of_frames is even and
            that frame data is properly organized as alternating left/right pairs.
        """
        if stereo_pairs_present is not None:
            self.StereoPairsPresent = format_enum_string(stereo_pairs_present)
        if frame_type is not None:
            # Convert enum values to strings while preserving list structure
            formatted_frame_type = [format_enum_string(item) for item in frame_type]
            self.FrameType = formatted_frame_type
        if encapsulated_pixel_data_value_total_length is not None:
            self.EncapsulatedPixelDataValueTotalLength = encapsulated_pixel_data_value_total_length
        return self
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time() -> list[int]:
        """Create frame increment pointer for Frame Time attribute.
        
        Returns:
            list[int]: Frame increment pointer pointing to Frame Time (0018,1063).
        """
        return [int(Tag(0x0018, 0x1063))]
    
    @staticmethod
    def create_frame_increment_pointer_for_frame_time_vector() -> list[int]:
        """Create frame increment pointer for Frame Time Vector attribute.
        
        Returns:
            list[int]: Frame increment pointer pointing to Frame Time Vector (0018,1065).
        """
        return [int(Tag(0x0018, 0x1065))]
    
    @staticmethod
    def create_frame_increment_pointer_for_functional_groups() -> list[int]:
        """Create frame increment pointer for Per-Frame Functional Groups.
        
        Returns:
            list[int]: Frame increment pointer pointing to Per-Frame Functional Groups Sequence (5200,9230).
        """
        return [int(Tag(0x5200, 0x9230))]

    @staticmethod
    def create_frame_type(
        frame_data_characteristic: str | FrameType,
        frame_acquisition_characteristic: str | FrameType,
        *frame_specific: str | FrameType
    ) -> list[str]:
        """Create a properly structured Frame Type list for enhanced multi-frame images.

        Helper method to create Frame Type values following DICOM PS3.3 structure for
        Enhanced Multi-frame images (0008,9007).

        Args:
            frame_data_characteristic (str | FrameType): Value 1 - Frame Data Characteristics.
                Use FrameType.ORIGINAL or FrameType.DERIVED
            frame_acquisition_characteristic (str | FrameType): Value 2 - Frame Acquisition Characteristics.
                Use FrameType.PRIMARY or FrameType.SECONDARY
            *frame_specific (str | FrameType): Value 3+ - Frame-Specific Classifications.
                Use FrameType.DYNAMIC, FrameType.CARDIAC, FrameType.GATED, etc.

        Returns:
            list[str]: Properly formatted Frame Type list for DICOM

        Examples:
            # Dynamic cardiac cine frame
            cardiac_type = MultiFrameModule.create_frame_type(
                FrameType.ORIGINAL, FrameType.PRIMARY, FrameType.CARDIAC, FrameType.GATED
            )

            # Perfusion imaging frame
            perfusion_type = MultiFrameModule.create_frame_type(
                FrameType.DERIVED, FrameType.SECONDARY, FrameType.PERFUSION, FrameType.DYNAMIC
            )

            # Static volumetric frame
            volume_type = MultiFrameModule.create_frame_type(
                FrameType.ORIGINAL, FrameType.PRIMARY, FrameType.STATIC
            )
        """
        frame_type_list = [
            format_enum_string(frame_data_characteristic),
            format_enum_string(frame_acquisition_characteristic)
        ]

        # Add frame-specific values
        for value in frame_specific:
            frame_type_list.append(format_enum_string(value))

        return frame_type_list

    @property
    def is_single_frame(self) -> bool:
        """Check if this is actually a single frame (edge case).
        
        Returns:
            bool: True if number of frames equals 1
        """
        return 'NumberOfFrames' in self and self.NumberOfFrames == 1
    
    @property
    def is_multi_frame(self) -> bool:
        """Check if this is truly multi-frame.
        
        Returns:
            bool: True if number of frames is greater than 1
        """
        return 'NumberOfFrames' in self and self.NumberOfFrames > 1
    
    @property
    def has_stereo_pairs(self) -> bool:
        """Check if stereoscopic pairs are present.
        
        Returns:
            bool: True if stereo pairs present indicator is YES
        """
        return 'StereoPairsPresent' in self and self.StereoPairsPresent == "YES"
    
    @property
    def expected_stereo_frame_count(self) -> int | None:
        """Calculate expected number of stereo frame pairs.
        
        Returns:
            int | None: Number of stereo pairs (total frames / 2), or None if not applicable
        """
        if not self.has_stereo_pairs or 'NumberOfFrames' not in self:
            return None
        # For stereo pairs, frames come in pairs (left/right)
        return self.NumberOfFrames // 2
    
    @property
    def uses_frame_time(self) -> bool:
        """Check if Frame Time is used for frame increment.
        
        Returns:
            bool: True if Frame Time tag is present in frame increment pointer
        """
        if 'FrameIncrementPointer' not in self:
            return False
        frame_time_tag = Tag(0x0018, 0x1063)
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return frame_time_tag in fip
        else:
            return fip == frame_time_tag
    
    @property
    def uses_frame_time_vector(self) -> bool:
        """Check if Frame Time Vector is used for frame increment.
        
        Returns:
            bool: True if Frame Time Vector tag is present in frame increment pointer
        """
        if 'FrameIncrementPointer' not in self:
            return False
        frame_time_vector_tag = Tag(0x0018, 0x1065)
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return frame_time_vector_tag in fip
        else:
            return fip == frame_time_vector_tag
    
    @property
    def uses_functional_groups(self) -> bool:
        """Check if Per-Frame Functional Groups are used for frame increment.
        
        Returns:
            bool: True if Functional Groups tag is present in frame increment pointer
        """
        if 'FrameIncrementPointer' not in self:
            return False
        functional_groups_tag = Tag(0x5200, 0x9230)
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            return functional_groups_tag in fip
        else:
            return fip == functional_groups_tag
    
    def get_frame_increment_attributes(self) -> list[str]:
        """Get list of frame increment attribute names.
        
        Returns:
            list[str]: List of DICOM attribute names referenced by frame increment pointer
        """
        if 'FrameIncrementPointer' not in self:
            return []
        
        # Map common tags to attribute names
        tag_to_attr = {
            Tag(0x0018, 0x1063): "FrameTime",
            Tag(0x0018, 0x1065): "FrameTimeVector", 
            Tag(0x5200, 0x9230): "PerFrameFunctionalGroupsSequence"
        }
        
        fip = self.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            tag_list = fip
        else:
            tag_list = [fip]
        
        attributes = []
        for tag in tag_list:
            if tag in tag_to_attr:
                attributes.append(tag_to_attr[tag])
            else:
                # For unknown tags, convert Tag back to hex format
                if hasattr(tag, 'group') and hasattr(tag, 'element'):
                    attributes.append(f"Tag{tag.group:04X}{tag.element:04X}")
                else:
                    attributes.append(f"UnknownTag_{tag}")
        
        return attributes
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate multi-frame module for DICOM PS3.3 C.7.6.6 compliance and clinical use.
        
        Performs comprehensive validation of multi-frame data to ensure compatibility with
        DICOM viewers and clinical imaging systems. Covers required elements, frame sequencing,
        stereoscopic configuration, and temporal relationships.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration to customize
                validation behavior, error reporting levels, and specific check enablement.
                If None, uses default validation settings appropriate for clinical use.
        
        Returns:
            ValidationResult: Structured validation results containing any errors or warnings.
                Contains errors list, warnings list, and is_valid boolean for overall status.
        """
        return MultiFrameValidator.validate(self, config)
