"""
Pytest configuration and shared fixtures for PyRT-DICOM tests.

This module provides shared fixtures and configuration for testing the IOD/module
architecture, with particular focus on RTDoseIOD validation.
"""

import pytest
import numpy as np
from pydicom import Dataset
from pydicom.uid import generate_uid
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, PixelRepresentation
from pyrt_dicom.modules import (
    PatientModule,
    GeneralStudyModule,
    RTSeriesModule,
    FrameOfReferenceModule,
    GeneralEquipmentModule,
    RTDoseModule,
    SOPCommonModule,
    GeneralImageModule,
    ImagePlaneModule,
    ImagePixelModule,
    MultiFrameModule,
    RTDVHModule,
    ApprovalModule
)
from pyrt_dicom.enums import PatientSex, DoseUnits, DoseType


@pytest.fixture
def sample_patient_module():
    """Create sample PatientModule for testing (M - Mandatory)."""
    return PatientModule.from_required_elements(
        patient_name="Test^Patient^Jr^^",
        patient_id="TEST001",
        patient_birth_date="19900101",
        patient_sex=PatientSex.MALE
    )


@pytest.fixture
def sample_general_study_module():
    """Create sample GeneralStudyModule for testing (M - Mandatory)."""
    return GeneralStudyModule.from_required_elements(
        study_instance_uid=generate_uid(),
        study_date="20240101",
        study_time="120000"
    )


@pytest.fixture
def sample_rt_series_module():
    """Create sample RTSeriesModule for testing (M - Mandatory)."""
    return RTSeriesModule.from_required_elements(
        modality="RTDOSE",
        series_instance_uid=generate_uid()
    ).with_optional_elements(
        series_number="1"
    )


@pytest.fixture
def sample_frame_of_reference_module():
    """Create sample FrameOfReferenceModule for testing (M - Mandatory)."""
    return FrameOfReferenceModule.from_required_elements(
        frame_of_reference_uid=generate_uid()
    )


@pytest.fixture
def sample_general_equipment_module():
    """Create sample GeneralEquipmentModule for testing (M - Mandatory)."""
    return GeneralEquipmentModule.from_required_elements(
        manufacturer="Test Manufacturer"
    )


@pytest.fixture
def sample_sop_common_module():
    """Create sample SOPCommonModule for testing (M - Mandatory)."""
    return SOPCommonModule.from_required_elements(
        sop_class_uid="1.2.840.10008.*******.1.481.2",  # RT Dose Storage
        sop_instance_uid=generate_uid()
    )


@pytest.fixture
def small_dose_array():
    """Create small 3D dose array for unit testing (8x8x4)."""
    # Create realistic dose distribution with hot spot in center
    dose_array = np.zeros((8, 8, 4), dtype=np.uint16)
    center_x, center_y, center_z = 4, 4, 2
    
    for z in range(4):
        for y in range(8):
            for x in range(8):
                # Distance from center
                dist = np.sqrt((x-center_x)**2 + (y-center_y)**2 + (z-center_z)**2)
                # Exponential dose falloff (max ~20 Gy with 0.001 scaling)
                dose = 20000 * np.exp(-dist/2)
                dose_array[x, y, z] = int(np.clip(dose, 0, 65535))
    
    return dose_array


@pytest.fixture
def medium_dose_array():
    """Create medium 3D dose array for integration testing (64x64x32)."""
    dose_array = np.zeros((64, 64, 32), dtype=np.uint16)
    center_x, center_y, center_z = 32, 32, 16
    
    for z in range(32):
        for y in range(64):
            for x in range(64):
                dist = np.sqrt((x-center_x)**2 + (y-center_y)**2 + (z-center_z)**2)
                dose = 50000 * np.exp(-dist/15)  # Max ~50 Gy
                dose_array[x, y, z] = int(np.clip(dose, 0, 65535))
    
    return dose_array


@pytest.fixture
def sample_rt_dose_module(small_dose_array):
    """Create sample RTDoseModule for testing (M - Mandatory)."""
    return RTDoseModule.from_required_elements(
        dose_units=DoseUnits.GY,
        dose_type=DoseType.PHYSICAL,
        dose_summation_type="SUM"
    ).with_pixel_data_elements(
        samples_per_pixel=1,
        photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
        bits_allocated=16,
        bits_stored=16,
        high_bit=15,
        pixel_representation=PixelRepresentation.UNSIGNED,
        dose_grid_scaling=0.001
    )


@pytest.fixture
def sample_general_image_module():
    """Create sample GeneralImageModule for testing (C - Conditional)."""
    return GeneralImageModule.from_required_elements(
        instance_number="1"
    ).with_optional_elements(
        image_type=["ORIGINAL", "PRIMARY", "DOSE"],
    )


@pytest.fixture
def sample_image_plane_module():
    """Create sample ImagePlaneModule for testing (C - Conditional)."""
    return ImagePlaneModule.from_required_elements(
        pixel_spacing=[2.5, 2.5],
        image_orientation_patient=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
        image_position_patient=[-100.0, -100.0, -50.0]
    )


@pytest.fixture
def sample_image_pixel_module(small_dose_array):
    """Create sample ImagePixelModule for testing (C - Conditional)."""
    return ImagePixelModule.from_required_elements(
        rows=small_dose_array.shape[0],
        columns=small_dose_array.shape[1],
        bits_allocated=16,
        bits_stored=16,
        high_bit=15,
        pixel_representation=PixelRepresentation.UNSIGNED,
        samples_per_pixel=1,
        photometric_interpretation=PhotometricInterpretation.MONOCHROME2
    ).with_pixel_data(
        pixel_data=small_dose_array
    )


@pytest.fixture
def sample_multi_frame_module(small_dose_array):
    """Create sample MultiFrameModule for testing (C - Conditional)."""
    return MultiFrameModule.from_required_elements(
        number_of_frames=small_dose_array.shape[2],
        frame_increment_pointer=[]
    )


@pytest.fixture
def sample_rt_dvh_module():
    """Create sample RTDVHModule for testing (U - Optional)."""
    # Create simple DVH data for testing
    dvh_item = Dataset()
    dvh_item.DVHReferencedROISequence = Dataset()
    dvh_item.DVHReferencedROISequence.add_new(
        'ReferencedROINumber', 'IS', 1)
    dvh_item.DVHType = 'DIFFERENTIAL'
    dvh_item.DVHDoseUnits = 'GY'
    dvh_item.DVHVolumeUnits = 'CM3'
    dvh_item.DVHNumberOfBins = 100
    dvh_item.DVHData = list(range(100)) # Simple linear DVH for testing
    dvh_item.DVHMaximumDose = 60.0
    dvh_item.DVHMeanDose = 30.0
    
    dvh_sequence = [dvh_item]
    
    return RTDVHModule.from_required_elements(
        dvh_sequence=dvh_sequence,
        referenced_structure_set_sequence=[]
    )


@pytest.fixture
def minimal_rt_dose_modules(
    sample_patient_module,
    sample_general_study_module,
    sample_rt_series_module,
    sample_frame_of_reference_module,
    sample_general_equipment_module,
    sample_rt_dose_module,
    sample_sop_common_module
):
    """Create minimal set of modules required for RTDoseIOD."""
    return {
        'patient_module': sample_patient_module,
        'general_study_module': sample_general_study_module,
        'rt_series_module': sample_rt_series_module,
        'frame_of_reference_module': sample_frame_of_reference_module,
        'general_equipment_module': sample_general_equipment_module,
        'rt_dose_module': sample_rt_dose_module,
        'sop_common_module': sample_sop_common_module
    }


@pytest.fixture
def conditional_rt_dose_modules(
    minimal_rt_dose_modules,
    sample_general_image_module,
    sample_image_plane_module,
    sample_image_pixel_module,
    sample_multi_frame_module
):
    """Create RTDose modules with conditional modules for grid-based dose."""
    modules = minimal_rt_dose_modules.copy()
    modules.update({
        'general_image_module': sample_general_image_module,
        'image_plane_module': sample_image_plane_module,
        'image_pixel_module': sample_image_pixel_module,
        'multi_frame_module': sample_multi_frame_module
    })
    return modules


@pytest.fixture
def full_rt_dose_modules(
    conditional_rt_dose_modules,
    sample_rt_dvh_module
):
    """Create complete RTDose modules with all optional modules."""
    modules = conditional_rt_dose_modules.copy()
    modules.update({
        'rt_dvh_module': sample_rt_dvh_module,
        'approval_module': ApprovalModule.from_required_elements(
            approval_status="APPROVED"
        )
    })
    return modules


@pytest.fixture
def minimal_rt_dose_iod(minimal_rt_dose_modules):
    """Create minimal RTDoseIOD instance for testing."""
    from pyrt_dicom.iods import RTDoseIOD
    return RTDoseIOD(**minimal_rt_dose_modules)


@pytest.fixture
def conditional_rt_dose_iod(conditional_rt_dose_modules):
    """Create RTDoseIOD with conditional modules for testing."""
    from pyrt_dicom.iods import RTDoseIOD
    return RTDoseIOD(**conditional_rt_dose_modules)


@pytest.fixture
def full_rt_dose_iod(full_rt_dose_modules):
    """Create complete RTDoseIOD with all modules for testing."""
    from pyrt_dicom.iods import RTDoseIOD
    return RTDoseIOD(**full_rt_dose_modules)


def create_test_dose_array(size="small"):
    """Factory function for creating test dose arrays of different sizes."""
    sizes = {
        "small": (8, 8, 4),
        "medium": (64, 64, 32),
        "large": (512, 512, 200),
        "single": (1, 1, 1),
        "empty": (0, 0, 0)
    }
    
    shape = sizes.get(size, sizes["small"])
    if shape == (0, 0, 0):
        return np.array([], dtype=np.uint16)
    
    return np.random.random(shape).astype(np.uint16)


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:
        # Mark tests in unit/ directory as unit tests
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Mark tests in integration/ directory as integration tests
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Mark performance tests
        if "performance" in str(item.fspath) or "performance" in item.name:
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)