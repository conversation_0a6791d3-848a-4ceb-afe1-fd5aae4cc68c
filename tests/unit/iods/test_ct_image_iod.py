"""Unit tests for CT Image IOD implementation."""

import pytest
from pydicom import Dataset
from pydicom.uid import generate_uid

from pyrt_dicom.enums.image_enums import ImageType
from pyrt_dicom.iods.ct_image_iod import CTImageIOD
from pyrt_dicom.enums import (
    PatientSex, Modality, PhotometricInterpretation, 
    PixelRepresentation, ContrastBolusAgent, MultiEnergyCTAcquisition
)
from pyrt_dicom.modules import (
    PatientModule, GeneralStudyModule, GeneralSeriesModule,
    FrameOfReferenceModule, GeneralEquipmentModule, GeneralAcquisitionModule,
    GeneralImageModule, ImagePlaneModule, ImagePixelModule, CTImageModule,
    SOPCommonModule
)


class TestCTImageIODConstruction:
    """Test CT Image IOD construction and basic functionality."""

    def test_valid_construction_minimal(self):
        """Test successful construction with only required modules."""
        # Create required modules
        patient_module = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="TEST001",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        general_study_module = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid()
        )
        
        general_series_module = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid=generate_uid()
        )
        
        frame_of_reference_module = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        
        general_equipment_module = GeneralEquipmentModule.from_required_elements(
            manufacturer="Test Manufacturer"
        )
        
        general_acquisition_module = GeneralAcquisitionModule()
        
        general_image_module = GeneralImageModule.from_required_elements(
            instance_number="1"
        )
        
        image_plane_module = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0]
        )
        
        image_pixel_module = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=512,
            columns=512,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )
        
        ct_image_module = CTImageModule.from_required_elements(
            image_type=ImageType.from_ct_characteristics().value,
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
        )
        
        sop_common_module = SOPCommonModule.from_required_elements(
            sop_class_uid=CTImageIOD.SOP_CLASS_UID,
            sop_instance_uid=generate_uid()
        )
        
        # Create CT IOD
        ct_iod = CTImageIOD(
            patient_module=patient_module,
            general_study_module=general_study_module,
            general_series_module=general_series_module,
            frame_of_reference_module=frame_of_reference_module,
            general_equipment_module=general_equipment_module,
            general_acquisition_module=general_acquisition_module,
            general_image_module=general_image_module,
            image_plane_module=image_plane_module,
            image_pixel_module=image_pixel_module,
            ct_image_module=ct_image_module,
            sop_common_module=sop_common_module
        )
        
        assert ct_iod is not None
        assert len(ct_iod._required_modules) == 11  # 11 required modules
        assert ct_iod.get_module('patient') is not None
        assert ct_iod.get_module('ct_image') is not None

    def test_invalid_modality_raises_error(self):
        """Test that wrong modality raises IODValidationError."""
        # Create modules with wrong modality
        patient_module = PatientModule.from_required_elements(
            patient_name="Test^Patient", patient_id="TEST001",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        # Wrong modality - should be CT
        general_series_module = GeneralSeriesModule.from_required_elements(
            modality=Modality.MR,  # Wrong modality
            series_instance_uid=generate_uid()
        )
        
        # Create other required modules
        modules = self._create_minimal_modules_except_series(patient_module)
        
        # Should raise error for wrong modality
        with pytest.raises(Exception) as exc_info:
            CTImageIOD(
                general_series_module=general_series_module,
                **modules
            )
        
        assert "modality=CT" in str(exc_info.value)

    def test_dataset_generation(self):
        """Test that IOD generates valid DICOM dataset."""
        ct_iod = self._create_minimal_ct_iod()
        
        # Generate dataset
        dataset = ct_iod.to_dataset()
        
        # Verify dataset properties
        assert isinstance(dataset, Dataset)
        assert hasattr(dataset, 'SOPClassUID')
        assert dataset.SOPClassUID == CTImageIOD.SOP_CLASS_UID
        assert hasattr(dataset, 'Modality')
        assert dataset.Modality == 'CT'
        assert hasattr(dataset, 'PatientName')
        assert hasattr(dataset, 'StudyInstanceUID')

    def _create_minimal_modules_except_series(self, patient_module):
        """Helper to create minimal modules except series module."""
        return {
            'patient_module': patient_module,
            'general_study_module': GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid()
            ),
            'frame_of_reference_module': FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid()
            ),
            'general_equipment_module': GeneralEquipmentModule.from_required_elements(
                manufacturer="Test Manufacturer"
            ),
            'general_acquisition_module': GeneralAcquisitionModule(),
            'general_image_module': GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            'image_plane_module': ImagePlaneModule.from_required_elements(
                pixel_spacing=[1.0, 1.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0]
            ),
            'image_pixel_module': ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                rows=512, columns=512, bits_allocated=16,
                bits_stored=16, high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED
            ),
            'ct_image_module': CTImageModule.from_required_elements(
                image_type=ImageType.from_ct_characteristics().value,
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                rescale_intercept=-1024.0,
                rescale_slope=1.0,
                kvp=120.0
            ),
            'sop_common_module': SOPCommonModule.from_required_elements(
                sop_class_uid=CTImageIOD.SOP_CLASS_UID,
                sop_instance_uid=generate_uid()
            )
        }

    def _create_minimal_ct_iod(self):
        """Helper to create minimal CT IOD for testing."""
        patient_module = PatientModule.from_required_elements(
            patient_name="Test^Patient", patient_id="TEST001",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        general_series_module = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT, series_instance_uid=generate_uid()
        )
        
        modules = self._create_minimal_modules_except_series(patient_module)
        
        return CTImageIOD(
            general_series_module=general_series_module,
            **modules
        )


class TestCTImageIODFactoryMethods:
    """Test CT Image IOD factory methods."""

    def test_for_diagnostic_imaging(self):
        """Test diagnostic imaging factory method."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE,
            study_description="Chest CT",
            manufacturer="Siemens Healthineers"
        )
        
        assert ct_iod is not None
        assert ct_iod.get_module('patient') is not None
        assert ct_iod.get_module('general_study') is not None
        assert ct_iod.get_module('general_series').get_element('Modality') == 'CT' # type: ignore
        
        # Verify patient data
        patient_data = ct_iod.get_module('patient')._dataset # type: ignore
        assert patient_data.PatientName == "Doe^John"
        assert patient_data.PatientID == "12345"

    def test_for_treatment_planning(self):
        """Test treatment planning factory method."""
        ct_iod = CTImageIOD.for_treatment_planning(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE,
            study_description="CT for Radiotherapy",
            manufacturer="CT Simulator"
        )
        
        assert ct_iod is not None
        
        # Verify higher resolution for treatment planning
        image_plane = ct_iod.get_module('image_plane')._dataset # type: ignore
        assert image_plane.PixelSpacing == [0.97656, 0.97656]  # High resolution

    def test_for_contrast_enhanced_imaging(self):
        """Test contrast enhanced imaging factory method."""
        ct_iod = CTImageIOD.for_contrast_enhanced_imaging(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE,
            contrast_agent=ContrastBolusAgent.IOHEXOL,
            study_description="Contrast Enhanced CT"
        )
        
        assert ct_iod is not None
        assert ct_iod.has_contrast_enhancement
        assert 'contrast_bolus' in ct_iod._required_modules


class TestCTImageIODFluentAPI:
    """Test CT Image IOD fluent API methods."""

    def test_with_contrast_enhancement(self):
        """Test contrast enhancement fluent API."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        # Add contrast enhancement
        enhanced_ct = ct_iod.with_contrast_enhancement(
            contrast_agent=ContrastBolusAgent.IOHEXOL,
            contrast_bolus_volume=100.0,
            contrast_flow_rate=[3.0, 2.0]
        )
        
        assert enhanced_ct is ct_iod  # Should return self
        assert ct_iod.has_contrast_enhancement
        assert 'contrast_bolus' in ct_iod._required_modules

    def test_with_multi_energy_ct(self):
        """Test multi-energy CT fluent API."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        # Add multi-energy CT
        multi_energy_ct = ct_iod.with_multi_energy_ct(
            multi_energy_acquisition=MultiEnergyCTAcquisition.YES,
            energy_weighting_factor=0.5
        )
        
        assert multi_energy_ct is ct_iod  # Should return self
        assert ct_iod.is_multi_energy_ct
        assert 'multi_energy_ct_image' in ct_iod._required_modules

    def test_with_clinical_trial_context(self):
        """Test clinical trial context fluent API."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        # Add clinical trial context
        trial_ct = ct_iod.with_clinical_trial_context(
            protocol_name="RTOG-1234",
            site_id="Site-001",
            sponsor_name="NCI Clinical Trials Network"
        )
        
        assert trial_ct is ct_iod  # Should return self
        assert ct_iod.has_clinical_trial_data
        assert 'clinical_trial_subject' in ct_iod._required_modules


class TestCTImageIODProperties:
    """Test CT Image IOD property methods."""

    def test_basic_properties(self):
        """Test basic CT IOD properties."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        # Test dimensions
        assert ct_iod.image_dimensions == (512, 512)  # Default from factory
        assert ct_iod.pixel_spacing == [1.0, 1.0]  # Default from factory
        assert ct_iod.slice_thickness == 0.0  # Not set in factory
        
        # Test feature flags
        assert not ct_iod.has_contrast_enhancement
        assert not ct_iod.is_multi_energy_ct
        assert not ct_iod.has_time_synchronization
        assert not ct_iod.has_clinical_trial_data

    def test_ct_parameters(self):
        """Test CT parameter retrieval."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE,
            kvp=120.0, exposure_time=1000
        )
        
        ct_params = ct_iod.get_ct_parameters()
        assert 'kvp' in ct_params
        assert ct_params['kvp'] == 120.0
        assert 'exposure_time' in ct_params
        assert ct_params['exposure_time'] == 1000

    def test_spatial_information(self):
        """Test spatial information retrieval."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        spatial_info = ct_iod.get_spatial_information()
        assert 'frame_of_reference_uid' in spatial_info
        assert 'pixel_spacing' in spatial_info
        assert 'image_orientation_patient' in spatial_info
        assert 'rows' in spatial_info
        assert 'columns' in spatial_info
        assert spatial_info['rows'] == 512
        assert spatial_info['columns'] == 512

    def test_contrast_information(self):
        """Test contrast information retrieval."""
        ct_iod = CTImageIOD.for_contrast_enhanced_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE,
            contrast_agent=ContrastBolusAgent.IOHEXOL
        )
        
        contrast_info = ct_iod.get_contrast_information()
        assert 'contrast_bolus_agent' in contrast_info
        assert contrast_info['contrast_bolus_agent'] == ContrastBolusAgent.IOHEXOL.value


class TestCTImageIODValidation:
    """Test CT Image IOD validation methods."""

    def test_validation_method_exists(self):
        """Test that validation method exists and returns ValidationResult."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        # Test validation method exists
        result = ct_iod.validate()
        assert result is not None
        # Note: ValidationResult testing depends on validator implementation

    def test_to_validated_dataset(self):
        """Test validated dataset generation."""
        ct_iod = CTImageIOD.for_diagnostic_imaging(
            patient_name="Doe^John", patient_id="12345",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        
        # Test validated dataset generation
        dataset, validation_result = ct_iod.to_validated_dataset()
        assert isinstance(dataset, Dataset)
        assert validation_result is not None