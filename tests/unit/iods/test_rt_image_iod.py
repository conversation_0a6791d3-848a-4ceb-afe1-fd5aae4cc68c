"""
Test RTImageIOD (RT Image Information Object Definition) functionality.

RTImageIOD implements DICOM PS3.3 A.17.3 RT Image IOD with module composition
and conditional module dependencies for radiotherapy imaging.
"""

import pytest
from pydicom.uid import generate_uid
from unittest.mock import Mock
from pydicom import Dataset
from datetime import datetime

from pyrt_dicom.iods import RTImageIOD, IODValidationError
from pyrt_dicom.modules import (
    PatientModule, GeneralStudyModule, RTSeriesModule, FrameOfReferenceModule,
    GeneralEquipmentModule, GeneralAcquisitionModule, GeneralImageModule,
    ImagePixelModule, RTImageModule, SOPCommonModule
)
from pyrt_dicom.enums import (
    PatientSex, Modality, PhotometricInterpretation, PixelRepresentation,
    RTImagePlane, ConversionType
)


def create_mock_module(**attributes):
    """Create a mock module with specified attributes."""
    mock = Mock(spec=Dataset)
    mock._dataset = Mock()
    for attr, value in attributes.items():
        setattr(mock._dataset, attr, value)
    return mock


def create_mock_patient_module():
    """Create a mock patient module."""
    return create_mock_module(
        PatientName="Doe^John^^",
        PatientID="12345",
        PatientBirthDate="19900101",
        PatientSex="M"
    )


def create_mock_general_study_module():
    """Create a mock general study module."""
    return create_mock_module(
        StudyInstanceUID=generate_uid(),
        StudyDate="20240101",
        StudyTime="120000"
    )


def create_mock_rt_series_module():
    """Create a mock RT series module."""
    return create_mock_module(
        Modality=Modality.RTIMAGE.value,
        SeriesInstanceUID=generate_uid(),
        SeriesNumber="1"
    )


def create_mock_general_equipment_module():
    """Create a mock general equipment module."""
    return create_mock_module(
        Manufacturer="Test Portal Imager"
    )


def create_mock_general_acquisition_module():
    """Create a mock general acquisition module."""
    return create_mock_module(
        AcquisitionDate="20240101",
        AcquisitionTime="143000"
    )


def create_mock_general_image_module():
    """Create a mock general image module."""
    return create_mock_module(
        InstanceNumber="1",
        PatientOrientation=""
    )


def create_mock_image_pixel_module():
    """Create a mock image pixel module."""
    return create_mock_module(
        SamplesPerPixel=1,
        PhotometricInterpretation=PhotometricInterpretation.MONOCHROME2.value,
        Rows=1024,
        Columns=768,
        BitsAllocated=16,
        BitsStored=16,
        HighBit=15,
        PixelRepresentation=PixelRepresentation.UNSIGNED.value
    )


def create_mock_rt_image_module():
    """Create a mock RT image module."""
    return create_mock_module(
        SamplesPerPixel=1,
        PhotometricInterpretation=PhotometricInterpretation.MONOCHROME2.value,
        BitsAllocated=16,
        BitsStored=16,
        HighBit=15,
        PixelRepresentation=PixelRepresentation.UNSIGNED.value,
        RTImageLabel="Portal Image 1",
        ImageType=["ORIGINAL", "PRIMARY", "PORTAL"],
        ConversionType=ConversionType.DI.value,
        RTImagePlane=RTImagePlane.NORMAL.value
    )


def create_mock_sop_common_module():
    """Create a mock SOP common module."""
    return create_mock_module(
        SOPClassUID="1.2.840.10008.*******.1.481.1",
        SOPInstanceUID=generate_uid()
    )


def create_mock_frame_of_reference_module():
    """Create a mock frame of reference module."""
    return create_mock_module(
        FrameOfReferenceUID=generate_uid()
    )


def create_mock_multi_frame_module():
    """Create a mock multi-frame module."""
    return create_mock_module(
        NumberOfFrames=10,
        FrameIncrementPointer=["0018,1063"]  # Frame Time
    )


def create_mock_contrast_bolus_module():
    """Create a mock contrast/bolus module."""
    return create_mock_module(
        ContrastBolusAgent="Iodine",
        ContrastBolusRoute="IV",
        ContrastBolusVolume=100.0
    )


def create_mock_cine_module():
    """Create a mock cine module."""
    return create_mock_module(
        CineRate=10.0,
        FrameTime=100.0
    )


def create_mock_approval_module():
    """Create a mock approval module."""
    return create_mock_module(
        ApprovalStatus="APPROVED",
        ReviewDate="20240101",
        ReviewTime="120000",
        ReviewerName="Dr. Medical Physicist"
    )


def create_mock_modality_lut_module():
    """Create a mock modality LUT module."""
    return create_mock_module(
        ModalityLUTSequence=[]
    )


def create_mock_voi_lut_module():
    """Create a mock VOI LUT module."""
    return create_mock_module(
        WindowCenter=1024.0,
        WindowWidth=2048.0,
        WindowCenterWidthExplanation="Portal image display"
    )


class TestRTImageIOD:
    """Test RTImageIOD functionality."""
    
    def test_valid_construction_minimal_required_modules(self):
        """Test successful RTImageIOD creation with only required modules."""
        rt_image = RTImageIOD(
            patient_module=PatientModule.from_required_elements(
                patient_name="Doe^John^Jr^^",
                patient_id="12345",
                patient_birth_date="19900101",
                patient_sex=PatientSex.MALE
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="20240101",
                study_time="120000"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality=Modality.RTIMAGE,
                series_instance_uid=generate_uid()
            ).with_optional_elements(
                series_number="1"
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Varian Portal Vision aS1000"
            ),
            general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                rows=1024,
                columns=768,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED
            ),
            rt_image_module=RTImageModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED,
                rt_image_label="Portal Image 1",
                image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
                conversion_type=ConversionType.DI,
                rt_image_plane=RTImagePlane.NORMAL
            ),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.1",
                sop_instance_uid=generate_uid()
            )
        )
        
        # Verify IOD creation
        assert rt_image is not None
        assert rt_image.get_module('patient') is not None
        assert rt_image.get_module('rt_image') is not None
        assert rt_image.SOP_CLASS_UID == "1.2.840.10008.*******.1.481.1"
    
    def test_valid_construction_with_all_modules(self):
        """Test RTImageIOD creation with all optional and conditional modules."""
        rt_image = RTImageIOD(
            # Required modules
            patient_module=PatientModule.from_required_elements(
                patient_name="Smith^Jane^Dr^^",
                patient_id="67890",
                patient_birth_date="19850315",
                patient_sex=PatientSex.FEMALE
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="20240315",
                study_time="143000"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality=Modality.RTIMAGE,
                series_instance_uid=generate_uid()
            ).with_optional_elements(
                series_number="2"
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Elekta iViewGT"
            ),
            general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                rows=512,
                columns=512,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED
            ),
            rt_image_module=RTImageModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED,
                rt_image_label="Verification Image",
                image_type=["ORIGINAL", "PRIMARY", "VERIFICATION"],
                conversion_type=ConversionType.DI,
                rt_image_plane=RTImagePlane.NORMAL
            ),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.1",
                sop_instance_uid=generate_uid()
            ),
            # Optional modules
            frame_of_reference_module=FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid()
            ),
            # Using mocks for complex optional modules
            multi_frame_module=create_mock_multi_frame_module(),
            contrast_bolus_module=create_mock_contrast_bolus_module(),
            cine_module=create_mock_cine_module(),
            approval_module=create_mock_approval_module(),
            modality_lut_module=create_mock_modality_lut_module(),
            voi_lut_module=create_mock_voi_lut_module()
        )
        
        # Verify all modules are present
        assert rt_image.get_module('patient') is not None
        assert rt_image.get_module('rt_image') is not None
        assert rt_image.get_module('frame_of_reference') is not None
        assert rt_image.get_module('multi_frame') is not None
        assert rt_image.get_module('contrast_bolus') is not None
        assert rt_image.get_module('cine') is not None
        assert rt_image.get_module('approval') is not None
        assert rt_image.get_module('modality_lut') is not None
        assert rt_image.get_module('voi_lut') is not None
        
        # Verify IOD properties
        assert rt_image.is_multi_frame
        assert rt_image.has_cine_capability
        assert rt_image.has_contrast_enhancement
        assert rt_image.has_spatial_information
        assert rt_image.has_approval_data
        assert rt_image.has_lut_enhancement
    
    def test_invalid_modality_raises_error(self):
        """Test that incorrect modality raises IODValidationError."""
        with pytest.raises(IODValidationError, match="modality=RTIMAGE"):
            RTImageIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Test^Patient",
                    patient_id="12345",
                    patient_birth_date="",
                    patient_sex=""
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid=generate_uid(),
                    study_date="",
                    study_time=""
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality=Modality.CT,  # Wrong modality
                    series_instance_uid=generate_uid()
                ).with_optional_elements(
                    series_number="1"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Test System"
                ),
                general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
                general_image_module=GeneralImageModule.from_required_elements(
                    instance_number="1"
                ),
                image_pixel_module=ImagePixelModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                    rows=512,
                    columns=512,
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=PixelRepresentation.UNSIGNED
                ),
                rt_image_module=RTImageModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=PixelRepresentation.UNSIGNED,
                    rt_image_label="Test Image",
                    image_type=["ORIGINAL", "PRIMARY"],
                    conversion_type=ConversionType.DI,
                    rt_image_plane=RTImagePlane.NORMAL
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.1",
                    sop_instance_uid=generate_uid()
                )
            )
    
    def test_conditional_dependency_validation_cine_without_multi_frame(self):
        """Test that cine_module requires multi_frame_module."""
        with pytest.raises(IODValidationError, match="cine_module requires multi_frame_module"):
            RTImageIOD(
                patient_module=PatientModule.from_required_elements(
                    patient_name="Test^Patient",
                    patient_id="12345",
                    patient_birth_date="",
                    patient_sex=""
                ),
                general_study_module=GeneralStudyModule.from_required_elements(
                    study_instance_uid=generate_uid(),
                    study_date="",
                    study_time=""
                ),
                rt_series_module=RTSeriesModule.from_required_elements(
                    modality=Modality.RTIMAGE,
                    series_instance_uid=generate_uid()
                ).with_optional_elements(
                    series_number="1"
                ),
                general_equipment_module=GeneralEquipmentModule.from_required_elements(
                    manufacturer="Test System"
                ),
                general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
                general_image_module=GeneralImageModule.from_required_elements(
                    instance_number="1"
                ),
                image_pixel_module=ImagePixelModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                    rows=512,
                    columns=512,
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=PixelRepresentation.UNSIGNED
                ),
                rt_image_module=RTImageModule.from_required_elements(
                    samples_per_pixel=1,
                    photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=PixelRepresentation.UNSIGNED,
                    rt_image_label="Test Image",
                    image_type=["ORIGINAL", "PRIMARY"],
                    conversion_type=ConversionType.DI,
                    rt_image_plane=RTImagePlane.NORMAL
                ),
                sop_common_module=SOPCommonModule.from_required_elements(
                    sop_class_uid="1.2.840.10008.*******.1.481.1",
                    sop_instance_uid=generate_uid()
                ),
                # Missing multi_frame_module but providing cine_module
                cine_module=create_mock_cine_module()
            )
    
    # =====================================================================
    # Property Tests
    # =====================================================================
    
    def test_properties_is_portal_image(self):
        """Test is_portal_image property."""
        # Create RT image with portal image type
        rt_image = self._create_minimal_rt_image_iod(
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        assert rt_image.is_portal_image
        
        # Create RT image with verification image type
        rt_image_verification = self._create_minimal_rt_image_iod(
            image_type=["ORIGINAL", "PRIMARY", "VERIFICATION"]
        )
        assert rt_image_verification.is_portal_image  # Should be True for verification too
        
        # Create RT image with other image type
        rt_image_other = self._create_minimal_rt_image_iod(
            image_type=["ORIGINAL", "PRIMARY", "SIMULATOR"]
        )
        assert not rt_image_other.is_portal_image
    
    def test_properties_is_drr_image(self):
        """Test is_drr_image property."""
        # Create RT image with DRR image type
        rt_image = self._create_minimal_rt_image_iod(
            image_type=["DERIVED", "SECONDARY", "DRR"]
        )
        assert rt_image.is_drr_image
        
        # Create RT image without DRR image type
        rt_image_other = self._create_minimal_rt_image_iod(
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        assert not rt_image_other.is_drr_image
    
    def test_properties_is_simulator_image(self):
        """Test is_simulator_image property."""
        # Create RT image with simulator image type
        rt_image = self._create_minimal_rt_image_iod(
            image_type=["ORIGINAL", "PRIMARY", "SIMULATOR"]
        )
        assert rt_image.is_simulator_image
        
        # Create RT image without simulator image type
        rt_image_other = self._create_minimal_rt_image_iod(
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"]
        )
        assert not rt_image_other.is_simulator_image
    
    def test_properties_is_multi_frame(self):
        """Test is_multi_frame property."""
        # IOD without multi-frame
        rt_image = self._create_minimal_rt_image_iod()
        assert not rt_image.is_multi_frame
        
        # IOD with multi-frame
        rt_image_multi = self._create_minimal_rt_image_iod(
            multi_frame_module=create_mock_multi_frame_module()
        )
        assert rt_image_multi.is_multi_frame
    
    def test_properties_has_cine_capability(self):
        """Test has_cine_capability property."""
        # IOD without cine capability
        rt_image = self._create_minimal_rt_image_iod()
        assert not rt_image.has_cine_capability
        
        # IOD with cine capability
        rt_image_cine = self._create_minimal_rt_image_iod(
            multi_frame_module=create_mock_multi_frame_module(),
            cine_module=create_mock_cine_module()
        )
        assert rt_image_cine.has_cine_capability
    
    def test_properties_has_contrast_enhancement(self):
        """Test has_contrast_enhancement property."""
        # IOD without contrast enhancement
        rt_image = self._create_minimal_rt_image_iod()
        assert not rt_image.has_contrast_enhancement
        
        # IOD with contrast enhancement
        rt_image_contrast = self._create_minimal_rt_image_iod(
            contrast_bolus_module=create_mock_contrast_bolus_module()
        )
        assert rt_image_contrast.has_contrast_enhancement
    
    def test_properties_has_spatial_information(self):
        """Test has_spatial_information property."""
        # IOD without spatial information - create manually to avoid automatic frame of reference
        rt_image = RTImageIOD(
            patient_module=PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="12345",
                patient_birth_date="",
                patient_sex=""
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="",
                study_time=""
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality=Modality.RTIMAGE,
                series_instance_uid=generate_uid()
            ).with_optional_elements(
                series_number="1"
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Test System"
            ),
            general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                rows=512,
                columns=512,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED
            ),
            rt_image_module=RTImageModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED,
                rt_image_label="Test Image",
                image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
                conversion_type=ConversionType.DI,
                rt_image_plane=RTImagePlane.NORMAL
            ),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.1",
                sop_instance_uid=generate_uid()
            )
            # No frame_of_reference_module
        )
        assert not rt_image.has_spatial_information
        
        # IOD with spatial information
        rt_image_spatial = self._create_minimal_rt_image_iod()  # This includes frame of reference
        assert rt_image_spatial.has_spatial_information
    
    def test_properties_has_approval_data(self):
        """Test has_approval_data property."""
        # IOD without approval data
        rt_image = self._create_minimal_rt_image_iod()
        assert not rt_image.has_approval_data
        
        # IOD with approval data
        rt_image_approval = self._create_minimal_rt_image_iod(
            approval_module=create_mock_approval_module()
        )
        assert rt_image_approval.has_approval_data
    
    def test_properties_has_lut_enhancement(self):
        """Test has_lut_enhancement property."""
        # IOD without LUT enhancement
        rt_image = self._create_minimal_rt_image_iod()
        assert not rt_image.has_lut_enhancement
        
        # IOD with modality LUT enhancement
        rt_image_modality_lut = self._create_minimal_rt_image_iod(
            modality_lut_module=create_mock_modality_lut_module()
        )
        assert rt_image_modality_lut.has_lut_enhancement
        
        # IOD with VOI LUT enhancement
        rt_image_voi_lut = self._create_minimal_rt_image_iod(
            voi_lut_module=create_mock_voi_lut_module()
        )
        assert rt_image_voi_lut.has_lut_enhancement
    
    # =====================================================================
    # Summary Method Tests
    # =====================================================================
    
    def test_get_rt_imaging_parameters(self):
        """Test get_rt_imaging_parameters method."""
        rt_image = self._create_minimal_rt_image_iod(
            rt_image_label="Test Portal Image",
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        parameters = rt_image.get_rt_imaging_parameters()
        
        # Verify basic parameters are present (may be None for mock data)
        assert 'image_label' in parameters
        assert 'image_plane' in parameters
        assert 'rt_image_sid' in parameters
        assert 'radiation_machine_name' in parameters
        assert 'conversion_type' in parameters
        assert 'image_type' in parameters
    
    def test_get_image_dimensions(self):
        """Test get_image_dimensions method."""
        rt_image = self._create_minimal_rt_image_iod()
        
        dimensions = rt_image.get_image_dimensions()
        
        # Verify dimension parameters are present
        assert 'rows' in dimensions
        assert 'columns' in dimensions
        assert 'samples_per_pixel' in dimensions
        assert 'bits_allocated' in dimensions
        assert 'photometric_interpretation' in dimensions
    
    def test_get_spatial_information(self):
        """Test get_spatial_information method."""
        frame_uid = generate_uid()
        rt_image = self._create_minimal_rt_image_iod(
            frame_of_reference_uid=frame_uid
        )
        
        spatial_info = rt_image.get_spatial_information()
        
        # Verify spatial information structure
        assert 'frame_of_reference_uid' in spatial_info
        assert 'rt_image_position' in spatial_info
        assert 'rt_image_sid' in spatial_info
        assert 'gantry_angle' in spatial_info
    
    def test_get_acquisition_summary(self):
        """Test get_acquisition_summary method."""
        rt_image = self._create_minimal_rt_image_iod()
        
        summary = rt_image.get_acquisition_summary()
        
        # Verify acquisition summary structure - keys are present even if values are None
        assert 'acquisition_date' in summary
        assert 'acquisition_time' in summary
        assert 'manufacturer' in summary
        assert 'radiation_machine_name' in summary
        
        # Verify expected values for test setup
        assert summary['manufacturer'] == 'Test System'  # From _create_minimal_rt_image_iod
        # acquisition_date and acquisition_time may be None since GeneralAcquisitionModule.from_required_elements() doesn't set them
    
    # =====================================================================
    # Validation Tests
    # =====================================================================
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        rt_image = self._create_minimal_rt_image_iod()
        
        assert hasattr(rt_image, 'validate')
        assert callable(rt_image.validate)
        
        # Test validation returns ValidationResult object
        result = rt_image.validate()
        from pyrt_dicom.validators import ValidationResult
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert hasattr(result, 'has_errors')
        assert hasattr(result, 'has_warnings')
    
    def test_to_dataset_method_exists(self):
        """Test that to_dataset method exists and is callable."""
        rt_image = self._create_minimal_rt_image_iod()
        
        assert hasattr(rt_image, 'to_dataset')
        assert callable(rt_image.to_dataset)
    
    def test_get_module_method(self):
        """Test get_module method for accessing individual modules."""
        rt_image = self._create_minimal_rt_image_iod()
        
        # Test accessing required modules
        patient_module = rt_image.get_module('patient')
        assert patient_module is not None
        
        rt_image_module = rt_image.get_module('rt_image')
        assert rt_image_module is not None
        
        # Test accessing non-existent module
        missing_module = rt_image.get_module('non_existent')
        assert missing_module is None
    
    # =====================================================================
    # Factory Method Tests
    # =====================================================================
    
    def test_for_portal_imaging_basic(self):
        """Test for_portal_imaging factory method with basic parameters."""
        rt_image = RTImageIOD.for_portal_imaging(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        # Verify IOD creation
        assert rt_image is not None
        assert rt_image.SOP_CLASS_UID == "1.2.840.10008.*******.1.481.1"
        
        # Verify required modules are present
        assert rt_image.get_module('patient') is not None
        assert rt_image.get_module('general_study') is not None
        assert rt_image.get_module('rt_series') is not None
        assert rt_image.get_module('general_equipment') is not None
        assert rt_image.get_module('rt_image') is not None
        assert rt_image.get_module('sop_common') is not None
        
        # Verify portal imaging defaults
        assert rt_image.is_portal_image
        assert rt_image.has_spatial_information  # Frame of reference included
    
    def test_for_portal_imaging_with_custom_parameters(self):
        """Test for_portal_imaging with custom parameters."""
        custom_study_uid = generate_uid()
        custom_series_uid = generate_uid()
        
        rt_image = RTImageIOD.for_portal_imaging(
            patient_name="Smith^Jane^Dr",
            patient_id="54321",
            patient_birth_date=datetime(1985, 3, 15),
            patient_sex=PatientSex.FEMALE,
            study_description="Prostate IMRT Portal Images",
            manufacturer="Elekta iViewGT v2.0",
            rt_image_label="Prostate Portal AP",
            image_plane=RTImagePlane.NORMAL,
            referring_physician="Dr. Radiation Oncologist",
            study_instance_uid=custom_study_uid,
            series_instance_uid=custom_series_uid
        )
        
        # Verify custom parameters
        study_module = rt_image.get_module('general_study')
        assert study_module is not None
        study_dataset = study_module.to_dataset()
        assert study_dataset.StudyInstanceUID == custom_study_uid
        assert study_dataset.StudyDescription == "Prostate IMRT Portal Images"
        
        equipment_module = rt_image.get_module('general_equipment')
        assert equipment_module is not None
        equipment_dataset = equipment_module.to_dataset()
        assert equipment_dataset.Manufacturer == "Elekta iViewGT v2.0"
    
    def test_for_verification_imaging_basic(self):
        """Test for_verification_imaging factory method."""
        rt_image = RTImageIOD.for_verification_imaging(
            patient_name="Test^Patient",
            patient_id="VERIFY-001",
            patient_birth_date="20200101",
            patient_sex=PatientSex.MALE
        )
        
        # Verify IOD creation
        assert rt_image is not None
        
        # Verify verification-specific defaults
        study_module = rt_image.get_module('general_study')
        assert study_module is not None
        study_dataset = study_module.to_dataset()
        assert "Treatment Verification" in study_dataset.StudyDescription
        
        series_module = rt_image.get_module('rt_series')
        assert series_module is not None
        series_dataset = series_module.to_dataset()
        assert series_dataset.SeriesDescription == "Treatment Verification Images"
    
    def test_for_simulator_imaging_basic(self):
        """Test for_simulator_imaging factory method."""
        rt_image = RTImageIOD.for_simulator_imaging(
            patient_name="Sim^Patient",
            patient_id="SIM-001",
            patient_birth_date="19800101",
            patient_sex=PatientSex.FEMALE
        )
        
        # Verify IOD creation
        assert rt_image is not None
        
        # Verify simulator-specific defaults
        study_module = rt_image.get_module('general_study')
        assert study_module is not None
        study_dataset = study_module.to_dataset()
        assert "Simulator Imaging" in study_dataset.StudyDescription
        
        series_module = rt_image.get_module('rt_series')
        assert series_module is not None
        series_dataset = series_module.to_dataset()
        assert series_dataset.SeriesDescription == "Simulator Images"
    
    def test_for_drr_imaging_basic(self):
        """Test for_drr_imaging factory method."""
        rt_image = RTImageIOD.for_drr_imaging(
            patient_name="DRR^Patient",
            patient_id="DRR-001",
            patient_birth_date="19750101",
            patient_sex=PatientSex.MALE
        )
        
        # Verify IOD creation
        assert rt_image is not None
        
        # Verify DRR-specific defaults
        study_module = rt_image.get_module('general_study')
        assert study_module is not None
        study_dataset = study_module.to_dataset()
        assert "DRR Generation" in study_dataset.StudyDescription
        
        series_module = rt_image.get_module('rt_series')
        assert series_module is not None
        series_dataset = series_module.to_dataset()
        assert series_dataset.SeriesDescription == "Digitally Reconstructed Radiographs"
        
        # Verify DRR image type
        rt_image_module = rt_image.get_module('rt_image')
        assert rt_image_module is not None
        rt_image_dataset = rt_image_module.to_dataset()
        assert rt_image_dataset.ImageType == ["DERIVED", "SECONDARY", "DRR"]
        assert rt_image_dataset.ConversionType == ConversionType.WSD.value
    
    def test_factory_methods_generate_unique_uids(self):
        """Test that factory methods generate unique UIDs when not provided."""
        # Create multiple instances
        rt_image1 = RTImageIOD.for_portal_imaging(
            patient_name="Patient1", patient_id="001",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE
        )
        rt_image2 = RTImageIOD.for_portal_imaging(
            patient_name="Patient2", patient_id="002",
            patient_birth_date="19900101", patient_sex=PatientSex.FEMALE
        )
        
        # Verify unique UIDs
        study1 = rt_image1.get_module('general_study')
        assert study1 is not None
        study2 = rt_image2.get_module('general_study')
        assert study2 is not None
        study1 = study1.to_dataset()
        study2 = study2.to_dataset()
        assert study1.StudyInstanceUID != study2.StudyInstanceUID
        
        series1 = rt_image1.get_module('rt_series')
        assert series1 is not None
        series2 = rt_image2.get_module('rt_series')
        assert series2 is not None
        series1 = series1.to_dataset()
        series2 = series2.to_dataset()
        assert series1.SeriesInstanceUID != series2.SeriesInstanceUID
        
        sop1 = rt_image1.get_module('sop_common')
        assert sop1 is not None
        sop2 = rt_image2.get_module('sop_common')
        assert sop2 is not None
        sop1 = sop1.to_dataset()
        sop2 = sop2.to_dataset()
        assert sop1.SOPInstanceUID != sop2.SOPInstanceUID
    
    def test_factory_methods_preserve_custom_uids(self):
        """Test that factory methods preserve provided UIDs."""
        custom_study_uid = generate_uid()
        custom_series_uid = generate_uid()
        custom_frame_ref_uid = generate_uid()
        custom_sop_uid = generate_uid()
        
        rt_image = RTImageIOD.for_portal_imaging(
            patient_name="Patient", patient_id="001",
            patient_birth_date="19900101", patient_sex=PatientSex.MALE,
            study_instance_uid=custom_study_uid,
            series_instance_uid=custom_series_uid,
            frame_of_reference_uid=custom_frame_ref_uid,
            sop_instance_uid=custom_sop_uid
        )
        
        # Verify custom UIDs are preserved
        study_module = rt_image.get_module('general_study')
        assert study_module is not None
        study_dataset = study_module.to_dataset()
        assert study_dataset.StudyInstanceUID == custom_study_uid
        
        series_module = rt_image.get_module('rt_series')
        assert series_module is not None
        series_dataset = series_module.to_dataset()
        assert series_dataset.SeriesInstanceUID == custom_series_uid
        
        frame_ref_module = rt_image.get_module('frame_of_reference')
        assert frame_ref_module is not None
        frame_ref_dataset = frame_ref_module.to_dataset()
        assert frame_ref_dataset.FrameOfReferenceUID == custom_frame_ref_uid
        
        sop_module = rt_image.get_module('sop_common')
        assert sop_module is not None
        sop_dataset = sop_module.to_dataset()
        assert sop_dataset.SOPInstanceUID == custom_sop_uid
    
    # =====================================================================
    # Fluent API Tests  
    # =====================================================================
    
    def test_with_multi_frame_capabilities(self):
        """Test with_multi_frame_capabilities fluent API method."""
        rt_image = self._create_minimal_rt_image_iod()
        
        # Add multi-frame capabilities
        enhanced_rt_image = rt_image.with_multi_frame_capabilities(
            number_of_frames=30,
            frame_increment_pointer=["0018,1063"],
            cine_rate=10.0,
            frame_time=100.0
        )
        
        # Verify method returns self for chaining
        assert enhanced_rt_image is rt_image
        
        # Verify multi-frame capabilities added
        assert rt_image.is_multi_frame
        assert rt_image.has_cine_capability
        
        multi_frame_module = rt_image.get_module('multi_frame')
        assert multi_frame_module is not None
        
        cine_module = rt_image.get_module('cine')
        assert cine_module is not None
    
    def test_with_contrast_enhancement(self):
        """Test with_contrast_enhancement fluent API method."""
        rt_image = self._create_minimal_rt_image_iod()
        
        # Add contrast enhancement
        enhanced_rt_image = rt_image.with_contrast_enhancement(
            contrast_bolus_agent="Iodine",
            contrast_bolus_route="IV",
            contrast_bolus_volume=100.0,
            contrast_bolus_start_time="120000",
            contrast_bolus_stop_time="121000"
        )
        
        # Verify method returns self for chaining
        assert enhanced_rt_image is rt_image
        
        # Verify contrast enhancement added
        assert rt_image.has_contrast_enhancement
        
        contrast_module = rt_image.get_module('contrast_bolus')
        assert contrast_module is not None
    
    def test_with_clinical_trial_context(self):
        """Test with_clinical_trial_context fluent API method."""
        rt_image = self._create_minimal_rt_image_iod()
        
        # Add clinical trial context
        enhanced_rt_image = rt_image.with_clinical_trial_context(
            protocol_name="RTOG-1234",
            site_id="Site-001",
            sponsor_name="NCI Clinical Trials Network",
            subject_reading_id="READING-001",
            subject_id="SUBJECT-001"
        )
        
        # Verify method returns self for chaining
        assert enhanced_rt_image is rt_image
        
        # Verify clinical trial modules added
        assert rt_image.get_module('clinical_trial_subject') is not None
        assert rt_image.get_module('clinical_trial_study') is not None
        assert rt_image.get_module('clinical_trial_series') is not None
    
    def test_with_image_approval(self):
        """Test with_image_approval fluent API method."""
        rt_image = self._create_minimal_rt_image_iod()
        
        # Add image approval
        enhanced_rt_image = rt_image.with_image_approval(
            approval_status="APPROVED",
            approval_date="20240101",
            reviewer_name="Dr. Medical Physicist"
        )
        
        # Verify method returns self for chaining
        assert enhanced_rt_image is rt_image
        
        # Verify approval added
        assert rt_image.has_approval_data
        
        approval_module = rt_image.get_module('approval')
        assert approval_module is not None
    
    def test_with_lut_enhancement(self):
        """Test with_lut_enhancement fluent API method."""
        rt_image = self._create_minimal_rt_image_iod()
        
        # Add LUT enhancement with modality LUT sequence
        enhanced_rt_image = rt_image.with_lut_enhancement(
            modality_lut_sequence=[self._create_sample_lut_item()]
        )
        
        # Verify method returns self for chaining
        assert enhanced_rt_image is rt_image
        
        # Verify LUT enhancement added
        assert rt_image.has_lut_enhancement
        
        modality_lut_module = rt_image.get_module('modality_lut')
        assert modality_lut_module is not None
    
    def test_fluent_api_chaining(self):
        """Test that fluent API methods can be chained together."""
        rt_image = self._create_minimal_rt_image_iod()
        
        # Chain multiple fluent API methods
        enhanced_rt_image = rt_image \
            .with_multi_frame_capabilities(
                number_of_frames=20,
                cine_rate=5.0
            ) \
            .with_contrast_enhancement(
                contrast_bolus_agent="Gadolinium"
            ) \
            .with_image_approval(
                approval_status="APPROVED",
                reviewer_name="Dr. QA Physicist"
            ) \
            .with_lut_enhancement(
                modality_lut_sequence=[self._create_sample_lut_item()]
            )
        
        # Verify chaining returns the same object
        assert enhanced_rt_image is rt_image
        
        # Verify all enhancements are present
        assert rt_image.is_multi_frame
        assert rt_image.has_cine_capability
        assert rt_image.has_contrast_enhancement
        assert rt_image.has_approval_data
        assert rt_image.has_lut_enhancement
    
    # =====================================================================
    # Comprehensive Integration Tests
    # =====================================================================
    
    def test_comprehensive_rt_image_workflow(self):
        """Test comprehensive RT image workflow with all features."""
        # Create RT Image IOD with complete configuration
        rt_image = RTImageIOD.for_portal_imaging(
            patient_name="Wilson^Robert^M^^",
            patient_id="RT001234",
            patient_birth_date="19700515",
            patient_sex=PatientSex.MALE,
            study_description="Prostate IMRT Portal Verification",
            manufacturer="Varian Portal Vision aS1200",
            rt_image_label="Prostate Portal AP",
            referring_physician="Dr. Radiation Oncologist"
        ).with_multi_frame_capabilities(
            number_of_frames=10,
            cine_rate=2.0,
            frame_time=500.0
        ).with_clinical_trial_context(
            protocol_name="RTOG-0815",
            site_id="Site-123",
            sponsor_name="NRG Oncology"
        ).with_image_approval(
            approval_status="APPROVED",
            approval_date="20240320",
            reviewer_name="Dr. Medical Physicist"
        ).with_lut_enhancement(
            modality_lut_sequence=[self._create_sample_lut_item()]
        )
        
        # Verify comprehensive IOD
        assert rt_image.is_portal_image
        assert rt_image.is_multi_frame
        assert rt_image.has_cine_capability
        assert rt_image.has_spatial_information
        assert rt_image.has_approval_data
        assert rt_image.has_lut_enhancement
        
        # Verify summary methods work
        imaging_params = rt_image.get_rt_imaging_parameters()
        assert imaging_params is not None
        assert 'image_label' in imaging_params
        
        dimensions = rt_image.get_image_dimensions()
        assert dimensions is not None
        assert 'rows' in dimensions
        assert 'columns' in dimensions
        
        spatial_info = rt_image.get_spatial_information()
        assert spatial_info is not None
        assert 'frame_of_reference_uid' in spatial_info
        
        acquisition_summary = rt_image.get_acquisition_summary()
        assert acquisition_summary is not None
        assert 'manufacturer' in acquisition_summary
    
    def test_docstring_usage_example(self):
        """Test that the RTImageIOD docstring usage example works correctly."""
        # Create modules using composition-based builder patterns (from docstring)
        patient_module = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345",
            patient_birth_date="19900101",
            patient_sex=PatientSex.MALE
        )
        
        rt_image_module = RTImageModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            rt_image_label="Portal Image 1",
            image_type=["ORIGINAL", "PRIMARY", "PORTAL"],
            conversion_type=ConversionType.DI,
            rt_image_plane=RTImagePlane.NORMAL
        )
        
        # Create RT Image IOD with portal imaging (from docstring)
        rt_image = RTImageIOD(
            patient_module=patient_module,
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid="*******.5",
                study_date="20240101",
                study_time="120000"
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality=Modality.RTIMAGE,
                series_instance_uid="*******.6"
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Varian Medical Systems"
            ),
            general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                rows=1024,
                columns=768,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED
            ),
            rt_image_module=rt_image_module,
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.1",
                sop_instance_uid="*******.8"
            )
        )
        
        # Verify IOD was created successfully
        assert rt_image is not None
        assert rt_image.get_module('patient') is not None
        assert rt_image.get_module('rt_image') is not None
        
        # Access RT imaging parameters (from docstring)
        image_params = rt_image.get_rt_imaging_parameters()
        assert image_params is not None
        
        # Generate dataset for export (from docstring)
        dataset = rt_image.to_dataset()
        assert dataset is not None
        
        # Verify key DICOM attributes are present
        assert hasattr(dataset, 'PatientName')
        assert hasattr(dataset, 'SOPClassUID')
        assert hasattr(dataset, 'SOPInstanceUID')
        assert hasattr(dataset, 'Modality')
        assert dataset.Modality == "RTIMAGE"
        assert dataset.SOPClassUID == "1.2.840.10008.*******.1.481.1"
        
        # Note: We don't actually save the file in tests as shown in docstring
        # dataset.save_as("rt_image.dcm")  # Skip file I/O in tests
    
    # =====================================================================
    # Helper Methods
    # =====================================================================
    
    def _create_sample_lut_item(self):
        """Create a sample modality LUT item for testing."""
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 16]  # [number_of_entries, first_input_value, bits_per_entry]
        lut_item.LUTData = list(range(256))  # Sample LUT data
        return lut_item
    
    def _create_minimal_rt_image_iod(self,
                                   image_type=None,
                                   rt_image_label="Test Image",
                                   rt_image_plane=RTImagePlane.NORMAL,
                                   frame_of_reference_uid=None,
                                   **optional_modules):
        """Helper method to create minimal RTImageIOD for testing."""
        if frame_of_reference_uid is None:
            frame_of_reference_uid = generate_uid()
        
        if image_type is None:
            image_type = ["ORIGINAL", "PRIMARY", "PORTAL"]
        
        rt_image = RTImageIOD(
            patient_module=PatientModule.from_required_elements(
                patient_name="Test^Patient",
                patient_id="12345",
                patient_birth_date="",
                patient_sex=""
            ),
            general_study_module=GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="",
                study_time=""
            ),
            rt_series_module=RTSeriesModule.from_required_elements(
                modality=Modality.RTIMAGE,
                series_instance_uid=generate_uid()
            ).with_optional_elements(
                series_number="1"
            ),
            general_equipment_module=GeneralEquipmentModule.from_required_elements(
                manufacturer="Test System"
            ),
            general_acquisition_module=GeneralAcquisitionModule.from_required_elements(),
            general_image_module=GeneralImageModule.from_required_elements(
                instance_number="1"
            ),
            image_pixel_module=ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                rows=512,
                columns=512,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED
            ),
            rt_image_module=RTImageModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=PixelRepresentation.UNSIGNED,
                rt_image_label=rt_image_label,
                image_type=image_type,
                conversion_type=ConversionType.DI,
                rt_image_plane=rt_image_plane
            ),
            sop_common_module=SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.1",
                sop_instance_uid=generate_uid()
            ),
            **optional_modules
        )
        
        # Add frame of reference if specified
        if frame_of_reference_uid:
            frame_ref_module = FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=frame_of_reference_uid
            )
            rt_image._required_modules['frame_of_reference'] = frame_ref_module
        
        return rt_image