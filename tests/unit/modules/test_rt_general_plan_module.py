"""
Test RTGeneralPlanModule functionality.

RTGeneralPlanModule implements DICOM PS3.3 C.8.8.9 RT General Plan Module.
Contains general information about the RT Plan.
"""

from pyrt_dicom.modules import RTGeneralPlanModule
from pyrt_dicom.enums import PlanIntent, RTPlanGeometry, RTPlanRelationship
from pyrt_dicom.validators import ValidationResult


class TestRTGeneralPlanModule:
    """Test RTGeneralPlanModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Treatment Plan 1",
            rt_plan_date="20240101",
            rt_plan_time="120000",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )

        dataset = plan.to_dataset()
        assert dataset.RTPlanLabel == "Treatment Plan 1"
        assert dataset.RTPlanDate == "20240101"
        assert dataset.RTPlanTime == "120000"
        assert dataset.RTPlanGeometry == "PATIENT"
    
    def test_required_elements_with_defaults(self):
        """Test creation with default empty values for Type 2 elements."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan"
        )

        dataset = plan.to_dataset()
        # Type 1 element should be set
        assert dataset.RTPlanLabel == "Test Plan"

        # Type 2 elements can be empty
        assert dataset.RTPlanDate == ""
        assert dataset.RTPlanTime == ""
        assert dataset.RTPlanGeometry == ""
    
    def test_geometry_enum_values(self):
        """Test RT Plan Geometry enum values."""
        # Test PATIENT geometry
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Patient Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )
        dataset = plan.to_dataset()
        assert dataset.RTPlanGeometry == "PATIENT"
        assert plan.is_patient_based is True

        # Test TREATMENT_DEVICE geometry
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Device Plan",
            rt_plan_geometry=RTPlanGeometry.TREATMENT_DEVICE
        )
        dataset = plan.to_dataset()
        assert dataset.RTPlanGeometry == "TREATMENT_DEVICE"
        assert plan.is_patient_based is False
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        ).with_optional_elements(
            rt_plan_name="Primary Treatment Plan",
            rt_plan_description="Curative treatment for lung cancer",
            instance_number="1",
            treatment_protocols="RTOG 0617",
            plan_intent=PlanIntent.CURATIVE,
            treatment_site="Lung"
        )

        dataset = plan.to_dataset()
        assert hasattr(dataset, 'RTPlanName')
        assert hasattr(dataset, 'RTPlanDescription')
        assert hasattr(dataset, 'InstanceNumber')
        assert hasattr(dataset, 'TreatmentProtocols')
        assert hasattr(dataset, 'PlanIntent')
        assert hasattr(dataset, 'TreatmentSite')

        assert dataset.RTPlanName == "Primary Treatment Plan"
        assert dataset.RTPlanDescription == "Curative treatment for lung cancer"
        assert dataset.InstanceNumber == "1"
        assert dataset.TreatmentProtocols == "RTOG 0617"
        assert dataset.PlanIntent == "CURATIVE"
        assert dataset.TreatmentSite == "Lung"
    
    def test_plan_intent_enum_values(self):
        """Test all valid plan intent enum values."""
        intent_values = [
            PlanIntent.CURATIVE,
            PlanIntent.PALLIATIVE,
            PlanIntent.PROPHYLACTIC,
            PlanIntent.VERIFICATION
        ]

        for intent in intent_values:
            plan = RTGeneralPlanModule.from_required_elements(
                rt_plan_label="Test Plan"
            ).with_optional_elements(
                plan_intent=intent
            )
            dataset = plan.to_dataset()
            assert dataset.PlanIntent == intent.value
    
    def test_structure_set_reference(self):
        """Test structure set reference for patient-based plans."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Patient Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )

        # Add structure set reference
        plan.with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )

        assert plan.has_structure_set_reference is True
        dataset = plan.to_dataset()
        assert hasattr(dataset, 'ReferencedStructureSetSequence')
        assert len(dataset.ReferencedStructureSetSequence) == 1

        ref_item = dataset.ReferencedStructureSetSequence[0]
        assert ref_item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.3"
        assert ref_item.ReferencedSOPInstanceUID == "*******.*******.9"
    
    def test_treatment_site_code_creation(self):
        """Test treatment site code sequence item creation."""
        code_item = RTGeneralPlanModule.create_treatment_site_code_item(
            code_value="39607008",
            coding_scheme_designator="SCT",
            code_meaning="Lung structure"
        )

        assert code_item.CodeValue == "39607008"
        assert code_item.CodingSchemeDesignator == "SCT"
        assert code_item.CodeMeaning == "Lung structure"
    
    def test_referenced_rt_plan_creation(self):
        """Test referenced RT plan sequence item creation."""
        ref_plan = RTGeneralPlanModule.create_referenced_rt_plan_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.5",
            referenced_sop_instance_uid="*******.*******.10",
            rt_plan_relationship=RTPlanRelationship.PRIOR
        )

        assert ref_plan.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.5"
        assert ref_plan.ReferencedSOPInstanceUID == "*******.*******.10"
        assert ref_plan.RTPlanRelationship == "PRIOR"
    
    def test_property_methods(self):
        """Test utility property methods."""
        # Test patient-based plan
        patient_plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Patient Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )
        assert patient_plan.is_patient_based is True
        assert patient_plan.has_structure_set_reference is False
        assert patient_plan.has_treatment_site_info is False
        
        # Test treatment device plan
        device_plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Device Plan",
            rt_plan_geometry=RTPlanGeometry.TREATMENT_DEVICE
        )
        assert device_plan.is_patient_based is False
        
        # Test with treatment site info
        plan_with_site = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Site Plan"
        ).with_optional_elements(
            treatment_site="Lung"
        )
        assert plan_with_site.has_treatment_site_info is True
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )
        
        assert hasattr(plan, 'validate')
        assert callable(plan.validate)
        
        # Test validation result structure
        validation_result = plan.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
        
    def test_method_chaining(self):
        """Test that methods support chaining."""
        plan = (RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Chained Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )
        .with_optional_elements(
            rt_plan_name="Chained Name",
            plan_intent=PlanIntent.CURATIVE
        )
        .with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        ))

        dataset = plan.to_dataset()
        assert dataset.RTPlanLabel == "Chained Plan"
        assert dataset.RTPlanName == "Chained Name"
        assert dataset.PlanIntent == "CURATIVE"
        assert plan.has_structure_set_reference is True
    
    def test_validation_result_functionality(self):
        """Test that validator returns ValidationResult with correct functionality."""
        from pyrt_dicom.validators.modules.rt_general_plan_validator import RTGeneralPlanValidator
        from pyrt_dicom.validators import ValidationResult

        # Create test data
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )

        # Test direct validator with dataset
        dataset = plan.to_dataset()
        validator_result = RTGeneralPlanValidator.validate(dataset)
        assert isinstance(validator_result, ValidationResult)

        # Test module validate method
        assert hasattr(plan, 'validate')
        assert callable(plan.validate)

        # Test validation result structure
        validation_result = plan.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_dataset_generation(self):
        """Test that to_dataset() generates valid pydicom Dataset."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",  # Shorter to avoid VR SH length warning
            rt_plan_date="20240101",
            rt_plan_time="120000",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        ).with_optional_elements(
            rt_plan_name="Test Name",
            plan_intent=PlanIntent.CURATIVE
        )

        dataset = plan.to_dataset()

        # Test dataset type and basic properties
        import pydicom
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0

        # Test that all attributes are accessible through dataset
        assert dataset.RTPlanLabel == "Test Plan"
        assert dataset.RTPlanDate == "20240101"
        assert dataset.RTPlanTime == "120000"
        assert dataset.RTPlanGeometry == "PATIENT"
        assert dataset.RTPlanName == "Test Name"
        assert dataset.PlanIntent == "CURATIVE"

    def test_conditional_validation_logic(self):
        """Test conditional validation for structure set reference."""
        # Test that structure set reference is required for PATIENT geometry
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Patient Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )

        # Should be able to add structure set reference
        plan.with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )
        assert plan.has_structure_set_reference is True


    def test_is_configured_property(self):
        """Test is_configured property."""
        # Test unconfigured module
        empty_plan = RTGeneralPlanModule()
        assert empty_plan.is_configured is False

        # Test partially configured module (geometry is empty string)
        partial_plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan"
        )
        # Note: from_required_elements sets geometry to empty string (Type 2 default)
        # so is_configured should check for meaningful values, not just presence
        dataset = partial_plan.to_dataset()
        assert dataset.RTPlanGeometry == ""  # Empty string default
        # The current implementation checks for presence, not meaningful values
        # This is actually correct behavior since empty string is valid for Type 2
        assert partial_plan.is_configured is True

        # Test fully configured module
        full_plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTPlanGeometry.PATIENT
        )
        assert full_plan.is_configured is True
    