"""
Test ImagePixelModule functionality.

ImagePixelModule implements DICOM PS3.3 C.7.6.3 Image Pixel Module.
Tests the composition-based architecture with internal dataset management.
"""

import pytest
import pydicom
import numpy as np
from pyrt_dicom.modules import ImagePixelModule
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, PlanarConfiguration, PixelRepresentation
from pyrt_dicom.validators import ValidationResult


class TestImagePixelModule:
    """Test ImagePixelModule composition-based architecture."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=128,
            columns=128,
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0
        )

        # Test internal dataset access
        dataset = pixel.to_dataset()
        assert dataset.SamplesPerPixel == 1
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        assert dataset.Rows == 128
        assert dataset.Columns == 128
        assert dataset.BitsAllocated == 32
        assert dataset.BitsStored == 32
        assert dataset.HighBit == 31
        assert dataset.PixelRepresentation in [0, "0"]

        # Test module properties
        assert pixel.is_monochrome
        assert not pixel.is_color
        assert pixel.is_unsigned
        assert not pixel.is_signed
    
    def test_to_dataset_method(self):
        """Test to_dataset() method returns proper pydicom Dataset."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )

        dataset = pixel.to_dataset()

        # Verify it's a proper pydicom Dataset
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) == 8  # All 8 required elements

        # Verify all required elements are present
        assert hasattr(dataset, 'SamplesPerPixel')
        assert hasattr(dataset, 'PhotometricInterpretation')
        assert hasattr(dataset, 'Rows')
        assert hasattr(dataset, 'Columns')
        assert hasattr(dataset, 'BitsAllocated')
        assert hasattr(dataset, 'BitsStored')
        assert hasattr(dataset, 'HighBit')
        assert hasattr(dataset, 'PixelRepresentation')

    def test_enum_parameter_handling(self):
        """Test that enum parameters are properly handled."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )

        dataset = pixel.to_dataset()
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        assert dataset.PixelRepresentation == 0
    
    def test_various_matrix_sizes(self):
        """Test various image matrix sizes."""
        matrix_sizes = [
            (32, 32),    # Small grid
            (64, 64),    # Standard grid
            (128, 128),  # High resolution
            (256, 256),  # Very high resolution
            (64, 128),   # Non-square
            (100, 80)    # Irregular size
        ]

        for rows, cols in matrix_sizes:
            pixel = ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=rows,
                columns=cols,
                bits_allocated=32,
                bits_stored=32,
                high_bit=31,
                pixel_representation=0
            )
            dataset = pixel.to_dataset()
            assert dataset.Rows == rows
            assert dataset.Columns == cols
            assert pixel.total_pixels == rows * cols
    
    def test_bits_configuration_validation(self):
        """Test various bits allocated/stored configurations."""
        bits_configs = [
            (16, 16, 15),  # 16-bit
            (32, 32, 31),  # 32-bit
            (16, 12, 11),  # 12-bit stored in 16-bit
            (32, 24, 23)   # 24-bit stored in 32-bit
        ]

        for allocated, stored, high_bit in bits_configs:
            pixel = ImagePixelModule.from_required_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                rows=64,
                columns=64,
                bits_allocated=allocated,
                bits_stored=stored,
                high_bit=high_bit,
                pixel_representation=0
            )
            dataset = pixel.to_dataset()
            assert dataset.BitsAllocated == allocated
            assert dataset.BitsStored == stored
            assert dataset.HighBit == high_bit
            assert pixel.bytes_per_pixel == (allocated + 7) // 8
    
    def test_photometric_interpretation_validation(self):
        """Test various photometric interpretation values."""
        interpretations = [
            ("MONOCHROME1", True, False),  # (value, is_monochrome, is_color)
            ("MONOCHROME2", True, False),
            ("RGB", False, True),
            ("PALETTE COLOR", False, True),
            ("YBR_FULL", False, True)
        ]

        for interpretation, expected_mono, expected_color in interpretations:
            samples = 1 if interpretation in ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"] else 3
            pixel = ImagePixelModule.from_required_elements(
                samples_per_pixel=samples,
                photometric_interpretation=interpretation,
                rows=64,
                columns=64,
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=0
            )
            dataset = pixel.to_dataset()
            assert dataset.PhotometricInterpretation == interpretation
            assert pixel.is_monochrome == expected_mono
            assert pixel.is_color == expected_color
    
    def test_with_pixel_data(self):
        """Test adding pixel data (Type 1C)."""
        pixel_data = np.ones((64, 64), dtype=np.uint16).tobytes()

        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_pixel_data(pixel_data=pixel_data)

        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PixelData')
        assert dataset.PixelData == pixel_data
        assert pixel.has_pixel_data

    def test_with_pixel_data_provider_url(self):
        """Test adding pixel data provider URL (Type 1C)."""
        url = "http://example.com/pixel_data"

        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_pixel_data(pixel_data_provider_url=url)

        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PixelDataProviderURL')
        assert dataset.PixelDataProviderURL == url
        assert pixel.has_pixel_data

    def test_pixel_data_no_validation(self):
        """Test that pixel data methods don't perform validation (validation handled by validator)."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )

        # Test providing both parameters - since if/elif logic, only first will be set
        pixel.with_pixel_data(
            pixel_data=b"test_data",
            pixel_data_provider_url="http://example.com"
        )
        
        # Only pixel_data should be set due to if/elif logic
        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PixelData')
        assert not hasattr(dataset, 'PixelDataProviderURL')

        # Test providing neither - should not raise error
        pixel2 = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )
        pixel2.with_pixel_data()  # Should not raise error
        
        dataset2 = pixel2.to_dataset()
        assert not hasattr(dataset2, 'PixelData')
        assert not hasattr(dataset2, 'PixelDataProviderURL')

        # Test manually setting both to verify validator catches this
        pixel3 = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )
        pixel3.PixelData = b"test_data"
        pixel3.PixelDataProviderURL = "http://example.com"
        
        dataset3 = pixel3.to_dataset()
        assert hasattr(dataset3, 'PixelData')
        assert hasattr(dataset3, 'PixelDataProviderURL')
    
    def test_with_color_configuration(self):
        """Test adding color configuration for multi-sample images (Type 1C)."""
        # Test RGB image requiring planar configuration
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=3,
            photometric_interpretation="RGB",
            rows=64,
            columns=64,
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=0
        ).with_color_configuration(planar_configuration=PlanarConfiguration.COLOR_BY_PIXEL)

        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PlanarConfiguration')
        assert dataset.PlanarConfiguration == 0
        assert pixel.requires_planar_configuration

    def test_color_configuration_for_single_sample(self):
        """Test that planar configuration can be set for single-sample images (validation handled by validator)."""
        # Test that planar configuration can be set for single-sample images (validator will catch the issue)
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )

        # Should not raise error (validation moved to validator)
        pixel.with_color_configuration(planar_configuration=0)
        
        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PlanarConfiguration')
        assert dataset.PlanarConfiguration == 0
    
    def test_with_pixel_aspect_ratio(self):
        """Test adding pixel aspect ratio (Type 1C)."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_pixel_aspect_ratio(pixel_aspect_ratio=[6, 5])  # 0.30mm/0.25mm

        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PixelAspectRatio')
        assert dataset.PixelAspectRatio == [6, 5]

    def test_pixel_aspect_ratio_format_acceptance(self):
        """Test that pixel aspect ratio accepts various formats without validation."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )

        # Test 3-element list - module accepts it (validator will catch format issue)
        pixel.with_pixel_aspect_ratio(pixel_aspect_ratio=[1, 2, 3])
        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PixelAspectRatio')
        assert dataset.PixelAspectRatio == [1, 2, 3]

        # Test valid 2-element list
        pixel2 = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )
        pixel2.with_pixel_aspect_ratio(pixel_aspect_ratio=[1, 1])
        dataset2 = pixel2.to_dataset()
        assert hasattr(dataset2, 'PixelAspectRatio')
        assert dataset2.PixelAspectRatio == [1, 1]
    
    def test_with_palette_color(self):
        """Test adding palette color lookup tables (Type 1C)."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="PALETTE COLOR",
            rows=64,
            columns=64,
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=0
        ).with_palette_color(
            red_palette_descriptor=[256, 0, 16],
            green_palette_descriptor=[256, 0, 16],
            blue_palette_descriptor=[256, 0, 16],
            red_palette_data=b'\x00' * 512,
            green_palette_data=b'\x00' * 512,
            blue_palette_data=b'\x00' * 512
        )

        dataset = pixel.to_dataset()
        assert pixel.is_palette_color
        assert pixel.requires_palette_color_tables
        assert pixel.has_palette_color_tables
        assert hasattr(dataset, 'RedPaletteColorLookupTableDescriptor')
        assert hasattr(dataset, 'GreenPaletteColorLookupTableDescriptor')
        assert hasattr(dataset, 'BluePaletteColorLookupTableDescriptor')
        assert hasattr(dataset, 'RedPaletteColorLookupTableData')
        assert hasattr(dataset, 'GreenPaletteColorLookupTableData')
        assert hasattr(dataset, 'BluePaletteColorLookupTableData')

    def test_palette_color_any_photometric(self):
        """Test that palette color can be set for any photometric interpretation (validation handled by validator)."""
        # Test that palette color can be set for non-PALETTE COLOR photometric interpretation
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=0
        )

        # Should not raise error (validation moved to validator)
        pixel.with_palette_color(
            red_palette_descriptor=[256, 0, 16],
            green_palette_descriptor=[256, 0, 16],
            blue_palette_descriptor=[256, 0, 16],
            red_palette_data=b'\x00' * 512,
            green_palette_data=b'\x00' * 512,
            blue_palette_data=b'\x00' * 512
        )
        
        dataset = pixel.to_dataset()
        assert pixel.has_palette_color_tables

        # Test invalid descriptor format - should not raise error
        pixel_palette = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="PALETTE COLOR",
            rows=64,
            columns=64,
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=0
        )

        # Should not raise error (validation moved to validator)
        pixel_palette.with_palette_color(
            red_palette_descriptor=[256, 0],  # Invalid format but module accepts it
            green_palette_descriptor=[256, 0, 16],
            blue_palette_descriptor=[256, 0, 16],
            red_palette_data=b'\x00' * 512,
            green_palette_data=b'\x00' * 512,
            blue_palette_data=b'\x00' * 512
        )
        
        dataset_palette = pixel_palette.to_dataset()
        assert hasattr(dataset_palette, 'RedPaletteColorLookupTableDescriptor')
        assert dataset_palette.RedPaletteColorLookupTableDescriptor == [256, 0]
    
    def test_with_extended_offset_table(self):
        """Test adding extended offset table (Type 3/1C)."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_extended_offset_table(
            extended_offset_table=b'\x00\x00\x00\x00\x00\x00\x00\x00',
            extended_offset_table_lengths=b'\x00\x10\x00\x00'
        )

        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'ExtendedOffsetTable')
        assert hasattr(dataset, 'ExtendedOffsetTableLengths')

    def test_with_pixel_padding_range_limit(self):
        """Test adding pixel padding range limit (Type 1C)."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_pixel_padding_range_limit(pixel_padding_range_limit=65535)

        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'PixelPaddingRangeLimit')
        assert dataset.PixelPaddingRangeLimit == 65535

    def test_with_optional_elements(self):
        """Test adding optional (Type 3) elements."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_optional_elements(
            smallest_image_pixel_value=0,
            largest_image_pixel_value=65535,
            icc_profile=b'\x00' * 100,
            color_space="RGB"
        )

        dataset = pixel.to_dataset()
        assert hasattr(dataset, 'SmallestImagePixelValue')
        assert hasattr(dataset, 'LargestImagePixelValue')
        assert hasattr(dataset, 'ICCProfile')
        assert hasattr(dataset, 'ColorSpace')
        assert dataset.SmallestImagePixelValue == 0
        assert dataset.LargestImagePixelValue == 65535
    
    def test_property_calculations(self):
        """Test property calculations for pixel characteristics."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=12,
            high_bit=11,
            pixel_representation=0
        )

        # Test calculated properties
        assert pixel.bytes_per_pixel == 2  # (16 + 7) // 8
        assert pixel.total_pixels == 64 * 64
        assert pixel.expected_pixel_data_size == 64 * 64 * 1 * 2  # rows * cols * samples * bytes_per_pixel

        # Test bit relationship validation
        dataset = pixel.to_dataset()
        assert dataset.HighBit == dataset.BitsStored - 1
        assert dataset.BitsStored <= dataset.BitsAllocated
    
    def test_ybr_full_422_special_case(self):
        """Test YBR_FULL_422 special pixel data size calculation."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=3,
            photometric_interpretation="YBR_FULL_422",
            rows=64,
            columns=64,  # Must be even for horizontal subsampling
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=0
        )

        # YBR_FULL_422 has special pixel data size calculation
        # 2 Y values per CB/CR pair, so effective samples = 2
        expected_size = 64 * 64 * 2 * 1  # rows * cols * 2 * bytes_per_pixel
        assert pixel.expected_pixel_data_size == expected_size
    
    def test_signed_vs_unsigned_pixel_representation(self):
        """Test signed vs unsigned pixel representation."""
        # Test unsigned
        pixel_unsigned = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED
        )

        assert pixel_unsigned.is_unsigned
        assert not pixel_unsigned.is_signed

        # Test signed
        pixel_signed = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.SIGNED
        )

        assert pixel_signed.is_signed
        assert not pixel_signed.is_unsigned
    
    def test_method_chaining(self):
        """Test that all builder methods support method chaining."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=3,
            photometric_interpretation="RGB",
            rows=64,
            columns=64,
            bits_allocated=8,
            bits_stored=8,
            high_bit=7,
            pixel_representation=0
        ).with_pixel_data(
            pixel_data=b'\x00' * (64 * 64 * 3)
        ).with_color_configuration(
            planar_configuration=PlanarConfiguration.COLOR_BY_PIXEL
        ).with_pixel_aspect_ratio(
            pixel_aspect_ratio=[1, 1]
        ).with_optional_elements(
            smallest_image_pixel_value=0,
            largest_image_pixel_value=255
        )

        # Verify all elements were set through chaining
        dataset = pixel.to_dataset()
        assert len(dataset) == 13  # 8 required + 5 additional elements (pixel data, planar config, pixel aspect ratio, smallest/largest pixel values)
        assert pixel.has_pixel_data
        assert pixel.requires_planar_configuration
    
    def test_validation_method_exists(self):
        """Test that validation method exists and works with internal dataset."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        ).with_pixel_data(pixel_data=b'\x00' * (64 * 64 * 2))

        assert hasattr(pixel, 'validate')
        assert callable(pixel.validate)

        # Test validation result structure
        validation_result = pixel.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

        # Should have no errors for valid module
        assert len(validation_result.errors) == 0
    
    def test_module_properties(self):
        """Test module base properties."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )

        # Test base module properties
        assert pixel.module_name == "ImagePixelModule"
        assert pixel.has_data
        assert pixel.get_element_count() == 8  # 8 required elements

        # Test string representation
        repr_str = repr(pixel)
        assert "ImagePixelModule" in repr_str
        assert "8 attributes" in repr_str

    def test_dataset_independence(self):
        """Test that to_dataset() returns independent copy."""
        pixel = ImagePixelModule.from_required_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            rows=64,
            columns=64,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0
        )

        dataset1 = pixel.to_dataset()
        dataset2 = pixel.to_dataset()

        # Should be independent copies
        assert dataset1 is not dataset2
        assert dataset1.Rows == dataset2.Rows

        # Modifying one shouldn't affect the other
        dataset1.Rows = 128
        assert dataset2.Rows == 64