"""
Test CTImageModule functionality.

CTImageModule implements DICOM PS3.3 C.8.2.1 CT Image Module.
Contains attributes that describe CT images including pixel characteristics,
acquisition parameters, and reconstruction details.
"""

from pydicom import Dataset
from pyrt_dicom.modules import CTImageModule
from pyrt_dicom.enums.image_enums import (
    MultiEnergyCTAcquisition,
    RotationDirection,
    ExposureModulationType,
    CTImageTypeValue3,
    CTImageTypeValue4,
    CTSamplesPerPixel,
    CTBitsAllocated,
    CTBitsStored,
    RescaleType,
    FilterMaterial,
    ScanOptions,
    FilterType,
    CTImageTypeValue1,
    CTImageTypeValue2,
)
from pyrt_dicom.enums.image_enums import PhotometricInterpretation, ImageType
from pyrt_dicom.validators import ValidationResult


class TestCTImageModule:
    """Test CTImageModule functionality."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=CTSamplesPerPixel.ONE.value,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2.value,
            bits_allocated=CTBitsAllocated.SIXTEEN.value,
            bits_stored=CTBitsStored.SIXTEEN.value,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )

        # Test using to_dataset() method
        dataset = ct_image.to_dataset()
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY", "AXIAL"]
        assert dataset.SamplesPerPixel == 1
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        assert dataset.BitsAllocated == 16
        assert dataset.BitsStored == 16
        assert dataset.HighBit == 15
        assert dataset.RescaleIntercept == -1024.0
        assert dataset.RescaleSlope == 1.0
        assert dataset.KVP == 120.0
        assert dataset.AcquisitionNumber == 1

    def test_type_1_elements_with_enums(self):
        """Test Type 1 elements with enum values."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.from_ct_characteristics(
                is_original=True, is_primary=True, is_axial=False
            ),
            samples_per_pixel=CTSamplesPerPixel.ONE,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=CTBitsAllocated.SIXTEEN,
            bits_stored=CTBitsStored.TWELVE,
            high_bit=11,
            rescale_intercept=0.0,
            rescale_slope=1.0,
            kvp=110.0,
            acquisition_number=5,
        )

        dataset = ct_image.to_dataset()
        assert dataset.ImageType == ["ORIGINAL", "PRIMARY", "LOCALIZER"]
        assert dataset.BitsStored == 12
        assert dataset.HighBit == 11

    def test_type_2_elements_empty_values(self):
        """Test Type 2 elements can have empty values."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp="",  # Type 2 can be empty
            acquisition_number="",  # Type 2 can be empty
        )

        dataset = ct_image.to_dataset()
        assert dataset.KVP == ""
        assert dataset.AcquisitionNumber == ""

    def test_with_optional_elements(self):
        """Test adding optional Type 3 elements."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.NO,
            scan_options=ScanOptions.HELICAL_CT,
            data_collection_diameter=500.0,
            convolution_kernel="STANDARD",
            revolution_time=0.5,
            filter_type=FilterType.ALUMINUM,
            filter_material=[FilterMaterial.ALUMINUM, FilterMaterial.COPPER],
            rotation_direction=RotationDirection.CW,
            exposure_modulation_type=ExposureModulationType.NONE,
        )

        dataset = ct_image.to_dataset()
        assert dataset.MultienergyCTAcquisition == "NO"
        assert dataset.ScanOptions == "HELICAL_CT"
        assert dataset.DataCollectionDiameter == 500.0
        assert dataset.ConvolutionKernel == "STANDARD"
        assert dataset.RevolutionTime == 0.5
        assert dataset.FilterType == "ALUMINUM"
        assert dataset.FilterMaterial == ["AL", "CU"]
        assert dataset.RotationDirection == "CW"
        assert dataset.ExposureModulationType == "NONE"

    def test_rescale_type_conditional_required(self):
        """Test rescale type conditional requirement."""
        ct_image = (
            CTImageModule.from_required_elements(
                image_type=ImageType.DEFAULT,
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                rescale_intercept=-1024.0,
                rescale_slope=1.0,
                kvp=120.0,
                acquisition_number=1,
            )
            .with_optional_elements(
                multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
            )
            .with_rescale_type_conditional(rescale_type=RescaleType.HU)
        )

        dataset = ct_image.to_dataset()
        assert dataset.MultienergyCTAcquisition == "YES"
        assert dataset.RescaleType == "HU"

    def test_energy_weighting_conditional(self):
        """Test energy weighting factor conditional logic."""
        # Create derivation code sequence with multi-energy proportional weighting
        derivation_item = Dataset()
        derivation_item.CodeValue = "113097"
        derivation_item.CodingSchemeDesignator = "DCM"
        derivation_item.CodeMeaning = "Multi-energy proportional weighting"
        derivation_sequence = [derivation_item]

        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_energy_weighting_conditional(
            energy_weighting_factor=0.5, derivation_code_sequence=derivation_sequence
        )

        dataset = ct_image.to_dataset()
        assert dataset.EnergyWeightingFactor == 0.5

    def test_water_equivalent_diameter_conditional(self):
        """Test water equivalent diameter calculation method conditional."""
        # Create Dataset object for sequence
        code_item = Dataset()
        code_item.CodeValue = "113821"
        code_item.CodingSchemeDesignator = "DCM"
        code_item.CodeMeaning = "Equivalent diameter"

        ct_image = (
            CTImageModule.from_required_elements(
                image_type=ImageType.DEFAULT,
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                rescale_intercept=-1024.0,
                rescale_slope=1.0,
                kvp=120.0,
                acquisition_number=1,
            )
            .with_optional_elements(water_equivalent_diameter=350.0)
            .with_water_equivalent_diameter_conditional(
                water_equivalent_diameter_calculation_method_code_sequence=[code_item]
            )
        )

        dataset = ct_image.to_dataset()
        assert dataset.WaterEquivalentDiameter == 350.0
        assert hasattr(dataset, "WaterEquivalentDiameterCalculationMethodCodeSequence")

    def test_create_ctdi_phantom_type_code_item(self):
        """Test CTDI phantom type code item creation."""
        item = CTImageModule.create_ctdi_phantom_type_code_item(
            code_value="113681", code_meaning="IEC Body Dosimetry Phantom"
        )

        assert isinstance(item, Dataset)
        assert item.CodeValue == "113681"
        assert item.CodingSchemeDesignator == "DCM"
        assert item.CodeMeaning == "IEC Body Dosimetry Phantom"

    def test_create_ct_additional_x_ray_source_item(self):
        """Test CT additional X-Ray source sequence item creation."""
        item = CTImageModule.create_ct_additional_x_ray_source_item(
            kvp=140.0,
            x_ray_tube_current_in_ma=200.0,
            data_collection_diameter=500.0,
            focal_spots=[0.6, 1.2],
            filter_type="ALUMINUM",
            filter_material=["AL", "CU"],
            exposure_in_mas=100.0,
            energy_weighting_factor=0.3,
        )

        assert isinstance(item, Dataset)
        assert item.KVP == 140.0
        assert item.XRayTubeCurrentInmA == 200.0
        assert item.DataCollectionDiameter == 500.0
        assert item.FocalSpots == [0.6, 1.2]
        assert item.FilterType == "ALUMINUM"
        assert item.FilterMaterial == ["AL", "CU"]
        assert item.ExposureInmAs == 100.0
        assert item.EnergyWeightingFactor == 0.3

    def test_property_is_multi_energy(self):
        """Test is_multi_energy property."""
        # Single energy CT
        single_energy = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.NO
        )

        assert not single_energy.is_multi_energy

        # Multi-energy CT
        multi_energy = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
        )

        assert multi_energy.is_multi_energy

    def test_property_is_axial_image(self):
        """Test is_axial_image property."""
        axial_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )

        assert axial_image.is_axial_image
        assert not axial_image.is_localizer_image

    def test_property_is_localizer_image(self):
        """Test is_localizer_image property."""
        localizer_image = CTImageModule.from_required_elements(
            image_type=ImageType.from_ct_characteristics(
                is_original=True, is_primary=True, is_axial=False
            ),
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )

        assert localizer_image.is_localizer_image
        assert not localizer_image.is_axial_image

    def test_property_has_rescale_type(self):
        """Test has_rescale_type property."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )

        assert not ct_image.has_rescale_type

        ct_image.with_rescale_type_conditional(rescale_type=RescaleType.HU)
        assert ct_image.has_rescale_type

    def test_property_has_water_equivalent_diameter(self):
        """Test has_water_equivalent_diameter property."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )

        assert not ct_image.has_water_equivalent_diameter

        ct_image.with_optional_elements(water_equivalent_diameter=350.0)
        assert ct_image.has_water_equivalent_diameter

    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )

        assert hasattr(ct_image, "validate")
        assert callable(ct_image.validate)

        # Test validation result structure
        validation_result = ct_image.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, "errors")
        assert hasattr(validation_result, "warnings")
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_multi_energy_image_type_value_4(self):
        """Test multi-energy CT with Image Type Value 4."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.from_enumerations(
                original_or_derived=CTImageTypeValue1.DERIVED,
                primary_or_secondary=CTImageTypeValue2.SECONDARY,
                axial_or_localizer=CTImageTypeValue3.AXIAL,
                multi_energy_ct_type=CTImageTypeValue4.VMI,
            ),
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
        )

        dataset = ct_image.to_dataset()
        assert len(dataset.ImageType) == 4
        assert dataset.ImageType[3] == "VMI"
        assert ct_image.is_multi_energy

    def test_method_chaining(self):
        """Test that method chaining works correctly."""
        ct_image = (
            CTImageModule.from_required_elements(
                image_type=ImageType.DEFAULT,
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                rescale_intercept=-1024.0,
                rescale_slope=1.0,
                kvp=120.0,
                acquisition_number=1,
            )
            .with_optional_elements(
                scan_options=ScanOptions.HELICAL_CT, convolution_kernel="STANDARD"
            )
            .with_rescale_type_conditional(rescale_type=RescaleType.HU)
        )

        dataset = ct_image.to_dataset()
        assert dataset.ScanOptions == "HELICAL_CT"
        assert dataset.ConvolutionKernel == "STANDARD"
        assert dataset.RescaleType == "HU"

    def test_rotation_direction_and_exposure_modulation_enums(self):
        """Test RotationDirection and ExposureModulationType enum usage."""
        # Test with clockwise rotation and no exposure modulation
        ct_image_cw = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            rotation_direction=RotationDirection.CW,
            exposure_modulation_type=ExposureModulationType.NONE,
        )

        dataset_cw = ct_image_cw.to_dataset()
        assert dataset_cw.RotationDirection == "CW"
        assert dataset_cw.ExposureModulationType == "NONE"

        # Test with counter-clockwise rotation
        ct_image_cc = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=2,
        ).with_optional_elements(rotation_direction=RotationDirection.CC)

        dataset_cc = ct_image_cc.to_dataset()
        assert dataset_cc.RotationDirection == "CC"

    def test_to_dataset_method(self):
        """Test that to_dataset() method works correctly."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(convolution_kernel="STANDARD")

        # Test that to_dataset() returns a pydicom Dataset
        dataset = ct_image.to_dataset()
        assert isinstance(dataset, Dataset)

        # Test that dataset contains expected attributes
        assert hasattr(dataset, "ImageType")
        assert hasattr(dataset, "SamplesPerPixel")
        assert hasattr(dataset, "ConvolutionKernel")

        # Test that dataset is a copy (not the same object)
        dataset2 = ct_image.to_dataset()
        assert dataset is not dataset2
        assert dataset.ImageType == dataset2.ImageType

    def test_check_required_elements_valid(self):
        """Test check_required_elements method with valid data."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        result = ct_image.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
    def test_check_required_elements_missing(self):
        """Test check_required_elements method with missing data."""
        ct_image = CTImageModule()  # Empty module
        
        result = ct_image.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert result.error_count == 8  # All required elements missing

    def test_check_conditional_requirements_valid(self):
        """Test check_conditional_requirements method with valid data."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
        ).with_rescale_type_conditional(rescale_type=RescaleType.HU)
        
        result = ct_image.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid

    def test_check_conditional_requirements_missing(self):
        """Test check_conditional_requirements method with missing conditional data."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES  # This requires RescaleType
        )
        
        result = ct_image.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert "RescaleType" in result.errors[0]

    def test_check_enum_constraints_valid(self):
        """Test check_enum_constraints method with valid enum values."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.NO,
            rotation_direction=RotationDirection.CW
        )
        
        result = ct_image.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid

    def test_check_enum_constraints_invalid(self):
        """Test check_enum_constraints method with invalid enum values."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=2,  # Should be 1 for CT
            photometric_interpretation="RGB",  # Invalid for CT
            bits_allocated=8,  # Should be 16 for CT
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        result = ct_image.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert result.error_count >= 3  # Multiple enum violations

    def test_check_pixel_data_consistency_valid(self):
        """Test check_pixel_data_consistency method with valid data."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,  # Should be bits_stored - 1
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        result = ct_image.check_pixel_data_consistency()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid

    def test_check_pixel_data_consistency_invalid(self):
        """Test check_pixel_data_consistency method with invalid data."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=14,  # Should be 15 (bits_stored - 1)
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        result = ct_image.check_pixel_data_consistency()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert "High Bit" in result.errors[0]

    def test_check_sequence_structures_valid(self):
        """Test check_sequence_structures method with valid sequences."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            ctdi_phantom_type_code_sequence=[
                CTImageModule.create_ctdi_phantom_type_code_item(
                    code_value="113681", code_meaning="IEC Body Dosimetry Phantom"
                )
            ]
        )
        
        result = ct_image.check_sequence_structures()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid

    def test_validate_zero_copy_integration(self):
        """Test that validate method uses zero-copy optimization."""
        ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        # Test main validate method
        result = ct_image.validate()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
        # Test all specific validation methods return ValidationResult
        assert isinstance(ct_image.check_required_elements(), ValidationResult)
        assert isinstance(ct_image.check_conditional_requirements(), ValidationResult)
        assert isinstance(ct_image.check_enum_constraints(), ValidationResult)
        assert isinstance(ct_image.check_pixel_data_consistency(), ValidationResult)
        assert isinstance(ct_image.check_sequence_structures(), ValidationResult)

    def test_private_validation_methods_raise_exceptions(self):
        """Test that private validation methods raise ValidationError on failure."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        # Create invalid module (missing required elements)
        ct_image = CTImageModule()
        
        # Test private methods raise ValidationError
        try:
            ct_image._ensure_required_elements_valid()
            assert False, "Should have raised ValidationError"
        except ValidationError as e:
            assert "Required elements validation failed" in str(e)
            
        # Test valid module passes private validation
        valid_ct_image = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        # Should not raise any exception
        valid_ct_image._ensure_required_elements_valid()
        valid_ct_image._ensure_conditional_requirements_valid()
        valid_ct_image._ensure_enum_constraints_valid()
        valid_ct_image._ensure_pixel_data_consistency_valid()
        valid_ct_image._ensure_sequence_structures_valid()
