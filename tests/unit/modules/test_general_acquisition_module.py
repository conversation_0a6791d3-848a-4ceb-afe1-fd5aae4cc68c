"""
Test GeneralAcquisitionModule functionality.

GeneralAcquisitionModule implements DICOM PS3.3 C.7.10.1 General Acquisition Module.
All elements are Type 3 (optional).
"""

from datetime import datetime, date
from pyrt_dicom.modules import GeneralAcquisitionModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralAcquisitionModule:
    """Test GeneralAcquisitionModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements (all Type 3)."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Should be able to create empty instance since all elements are Type 3
        assert acquisition is not None
        assert isinstance(acquisition, GeneralAcquisitionModule)
    
    def test_with_optional_elements_basic(self):
        """Test adding basic optional elements."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.**********",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000"
        )
        
        # Test via to_dataset() method
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionUID')
        assert hasattr(dataset, 'AcquisitionNumber')
        assert hasattr(dataset, 'AcquisitionDate')
        assert hasattr(dataset, 'AcquisitionTime')
        
        assert dataset.AcquisitionUID == "*******.*******.**********"
        assert dataset.AcquisitionNumber == 1
        assert dataset.AcquisitionDate == "20240101"
        assert dataset.AcquisitionTime == "120000"
    
    def test_with_optional_elements_all(self):
        """Test adding all optional elements."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.**********",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000.000000",
            acquisition_duration=30.5,
            images_in_acquisition=100,
            irradiation_event_uid="*******.*******.9.10.11.13"
        )
        
        # Test via to_dataset() method
        dataset = acquisition.to_dataset()
        assert dataset.AcquisitionUID == "*******.*******.**********"
        assert dataset.AcquisitionNumber == 1
        assert dataset.AcquisitionDate == "20240101"
        assert dataset.AcquisitionTime == "120000"
        assert dataset.AcquisitionDateTime == "20240101120000.000000"
        assert dataset.AcquisitionDuration == 30.5
        assert dataset.ImagesInAcquisition == 100
        assert dataset.IrradiationEventUID == "*******.*******.9.10.11.13"
    
    def test_datetime_formatting(self):
        """Test datetime formatting functionality."""
        now = datetime(2024, 1, 1, 12, 0, 0, 123456)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_datetime=now
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionDateTime')
        assert "20240101120000.123456" in dataset.AcquisitionDateTime
    
    def test_date_formatting(self):
        """Test date formatting functionality."""
        test_date = date(2024, 1, 1)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_date=test_date
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionDate')
        assert dataset.AcquisitionDate == "20240101"
    
    def test_time_formatting(self):
        """Test time formatting functionality."""
        test_time = datetime(2024, 1, 1, 14, 30, 45)
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_time=test_time
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'AcquisitionTime')
        assert dataset.AcquisitionTime == "143045"
    
    def test_property_methods_empty(self):
        """Test property methods with empty module."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Test has_* properties
        assert acquisition.has_acquisition_identification is False
        assert acquisition.has_timing_information is False
        assert acquisition.has_irradiation_event is False
        assert acquisition.has_image_count is False
        
        # Test value properties
        assert acquisition.acquisition_uid_value is None
        assert acquisition.acquisition_number_value is None
        assert acquisition.acquisition_date_value is None
        assert acquisition.acquisition_time_value is None
        assert acquisition.acquisition_datetime_value is None
        assert acquisition.acquisition_duration_value is None
        assert acquisition.images_in_acquisition_value is None
        assert acquisition.irradiation_event_uid_value is None
    
    def test_property_methods_populated(self):
        """Test property methods with populated module."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.5",
            acquisition_number=5,
            acquisition_date="20240101",
            acquisition_duration=45.0,
            images_in_acquisition=200,
            irradiation_event_uid="*******.6"
        )
        
        # Test has_* properties
        assert acquisition.has_acquisition_identification is True
        assert acquisition.has_timing_information is True
        assert acquisition.has_irradiation_event is True
        assert acquisition.has_image_count is True
        
        # Test value properties
        assert acquisition.acquisition_uid_value == "*******.5"
        assert acquisition.acquisition_number_value == 5
        assert acquisition.acquisition_date_value == "20240101"
        assert acquisition.acquisition_duration_value == 45.0
        assert acquisition.images_in_acquisition_value == 200
        assert acquisition.irradiation_event_uid_value == "*******.6"
    
    def test_acquisition_summary_empty(self):
        """Test acquisition summary with empty module."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements()
        summary = acquisition.get_acquisition_summary()
        
        assert isinstance(summary, Dataset)
        assert len(summary) == 0
    
    def test_acquisition_summary_populated(self):
        """Test acquisition summary with populated module."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.5",
            acquisition_number=10,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_duration=60.0,
            images_in_acquisition=150,
            irradiation_event_uid="*******.6"
        )
        
        summary = acquisition.get_acquisition_summary()
        
        assert isinstance(summary, Dataset)
        assert hasattr(summary, 'Identification')
        assert hasattr(summary, 'Timing')
        assert hasattr(summary, 'ImagesInAcquisition')
        assert hasattr(summary, 'IrradiationEventUID')
        
        # Check identification dataset
        assert hasattr(summary.Identification, 'AcquisitionUID')
        assert hasattr(summary.Identification, 'AcquisitionNumber')
        assert summary.Identification.AcquisitionUID == "*******.5"
        assert summary.Identification.AcquisitionNumber == 10
        
        # Check timing dataset
        assert hasattr(summary.Timing, 'AcquisitionDate')
        assert hasattr(summary.Timing, 'AcquisitionTime')
        assert hasattr(summary.Timing, 'AcquisitionDuration')
        assert summary.Timing.AcquisitionDate == "20240101"
        assert summary.Timing.AcquisitionTime == "120000"
        assert summary.Timing.AcquisitionDuration == 60.0
        
        # Check direct attributes
        assert summary.ImagesInAcquisition == 150
        assert summary.IrradiationEventUID == "*******.6"
    
    def test_is_synchronized_with_external_clock(self):
        """Test external clock synchronization method."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # This method should always return False as documented
        assert acquisition.is_synchronized_with_external_clock() is False
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        acquisition = GeneralAcquisitionModule.from_required_elements()
        
        assert hasattr(acquisition, 'validate')
        assert callable(acquisition.validate)
        
        # Test validation result structure
        validation_result = acquisition.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid=None,
            acquisition_number=None,
            acquisition_date=None
        )
        
        # None values should not create attributes in the internal dataset
        dataset = acquisition.to_dataset()
        assert not hasattr(dataset, 'AcquisitionUID')
        assert not hasattr(dataset, 'AcquisitionNumber')
        assert not hasattr(dataset, 'AcquisitionDate')
    
    def test_multiple_irradiation_event_uids(self):
        """Test VM=1-n support for multiple irradiation event UIDs."""
        multiple_uids = [
            "*******.*******.9.10",
            "*******.*******.9.11",
            "*******.*******.9.12"
        ]
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            irradiation_event_uid=multiple_uids
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'IrradiationEventUID')
        assert acquisition.irradiation_event_uid_value == multiple_uids
        assert acquisition.has_irradiation_event is True
    
    def test_single_irradiation_event_uid(self):
        """Test VM=1-n support for single irradiation event UID."""
        single_uid = "*******.*******.9.10"
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            irradiation_event_uid=single_uid
        )
        
        dataset = acquisition.to_dataset()
        assert hasattr(dataset, 'IrradiationEventUID')
        assert acquisition.irradiation_event_uid_value == single_uid
        assert acquisition.has_irradiation_event is True
    
    def test_to_dataset_method(self):
        """Test that to_dataset() method returns proper pydicom Dataset."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.*******.**********",
            acquisition_number=42,
            acquisition_date="20240101",
            acquisition_time="120000"
        )
        
        dataset = acquisition.to_dataset()
        
        # Verify dataset type
        assert isinstance(dataset, Dataset)
        
        # Verify dataset contains expected attributes
        assert hasattr(dataset, 'AcquisitionUID')
        assert hasattr(dataset, 'AcquisitionNumber')
        assert hasattr(dataset, 'AcquisitionDate')
        assert hasattr(dataset, 'AcquisitionTime')
        
        # Verify dataset values are correct
        assert dataset.AcquisitionUID == "*******.*******.**********"
        assert dataset.AcquisitionNumber == 42
        assert dataset.AcquisitionDate == "20240101"
        assert dataset.AcquisitionTime == "120000"
        
        # Verify dataset length
        assert len(dataset) == 4
    
    def test_to_dataset_empty_module(self):
        """Test to_dataset() method with empty module."""
        from pydicom import Dataset
        
        acquisition = GeneralAcquisitionModule.from_required_elements()
        dataset = acquisition.to_dataset()
        
        # Should return empty dataset
        assert isinstance(dataset, Dataset)
        assert len(dataset) == 0
    
    def test_acquisition_summary_with_multiple_irradiation_uids(self):
        """Test acquisition summary with multiple irradiation event UIDs."""
        from pydicom import Dataset
        
        multiple_uids = [
            "*******.*******.9.10",
            "*******.*******.9.11"
        ]
        
        acquisition = GeneralAcquisitionModule.from_required_elements().with_optional_elements(
            acquisition_uid="*******.5",
            irradiation_event_uid=multiple_uids
        )
        
        summary = acquisition.get_acquisition_summary()
        
        assert isinstance(summary, Dataset)
        assert hasattr(summary, 'IrradiationEventUID')
        assert summary.IrradiationEventUID == multiple_uids
    
    def test_property_methods_with_multiple_irradiation_uids(self):
        """Test property methods with multiple irradiation event UIDs."""
        multiple_uids = [
            "*******.*******.9.10",
            "*******.*******.9.11"
        ]
        
        acquisition = GeneralAcquisitionModule\
            .from_required_elements()\
            .with_optional_elements(
                irradiation_event_uid=multiple_uids
            )
        
        assert acquisition.has_irradiation_event is True
        uid_value = acquisition.irradiation_event_uid_value
        assert uid_value is not None, "irradiation_event_uid_value should not be None"
        assert uid_value == multiple_uids
        # Verify it's iterable and has the correct length (could be list or MultiValue)
        assert hasattr(uid_value, '__iter__')
        assert len(uid_value) == 2
        # Verify individual values match
        assert list(uid_value) == multiple_uids
