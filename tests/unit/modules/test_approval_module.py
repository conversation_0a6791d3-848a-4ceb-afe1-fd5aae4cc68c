"""
Test ApprovalModule (U - Optional) functionality.

ApprovalModule implements DICOM PS3.3 C.8.8.16 Approval Module.
Optional module for dose approval and authorization tracking.
"""

import pytest
from datetime import datetime
from pyrt_dicom.modules import ApprovalModule
from pyrt_dicom.enums import ApprovalStatus
from pyrt_dicom.validators import ValidationResult


class TestApprovalModule:
    """Test ApprovalModule (U - Optional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        dataset = approval.to_dataset()
        assert dataset.ApprovalStatus == ApprovalStatus.APPROVED.value
    
    def test_approval_status_validation(self):
        """Test approval status enumeration validation."""
        for status in ApprovalStatus:
            approval = ApprovalModule.from_required_elements(
                approval_status=status
            )
            dataset = approval.to_dataset()
            assert dataset.ApprovalStatus == status.value
    
    def test_with_review_information(self):
        """Test adding review information."""
        current_time = datetime.now()
        review_date = current_time.strftime("%Y%m%d")
        review_time = current_time.strftime("%H%M%S.%f")[:-3]
        
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date=review_date,
            review_time=review_time,
            reviewer_name="Smith^John^MD^^"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewDate == review_date
        assert dataset.ReviewTime == review_time
        assert dataset.ReviewerName == "Smith^John^MD^^"
    
    def test_to_dataset_generation(self):
        """Test that to_dataset() generates correct DICOM datasets."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",
            review_time="120000",
            reviewer_name="Dr. Smith"
        )
        
        dataset = approval.to_dataset()
        
        # Verify dataset type and contents
        from pydicom import Dataset
        assert isinstance(dataset, Dataset)
        assert len(dataset) > 0
        
        # Verify all required elements are present
        assert hasattr(dataset, 'ApprovalStatus')
        assert hasattr(dataset, 'ReviewDate')
        assert hasattr(dataset, 'ReviewTime')
        assert hasattr(dataset, 'ReviewerName')
        
        # Verify values are correct
        assert dataset.ApprovalStatus == ApprovalStatus.APPROVED.value
        assert dataset.ReviewDate == "20240101"
        assert dataset.ReviewTime == "120000"
        assert dataset.ReviewerName == "Dr. Smith"
    
    def test_review_info_validation(self):
        """Test validation of review information."""
        # Should not allow review info for UNAPPROVED status
        with pytest.raises(ValueError):
            ApprovalModule.from_required_elements(
                approval_status=ApprovalStatus.UNAPPROVED
            ).with_review_information(
                review_date="20230101",
                review_time="120000",
                reviewer_name="Smith^John"
            )
    
    def test_reviewer_name_formatting(self):
        """Test reviewer name DICOM Person Name formatting."""
        reviewer_names = [
            "Smith^John^MD^^",
            "Johnson^Mary^PhD^Dr.^",
            "Brown^Robert^^^III",
            "Wilson^Sarah^RO^^",
        ]
        
        for name in reviewer_names:
            approval = ApprovalModule.from_required_elements(
                approval_status=ApprovalStatus.APPROVED
            ).with_review_information(
                review_date="20230101",
                review_time="120000",
                reviewer_name=name
            )
            dataset = approval.to_dataset()
            assert dataset.ReviewerName == name
    
    def test_approval_workflow_statuses(self):
        """Test different approval workflow statuses and their properties."""
        workflow_scenarios = [
            (ApprovalStatus.APPROVED, "Final approval for treatment"),
            (ApprovalStatus.REJECTED, "Dose distribution needs revision"),
            (ApprovalStatus.UNAPPROVED, "Not yet submitted for approval")
        ]
        
        for status, comment in workflow_scenarios:
            approval = ApprovalModule.from_required_elements(
                approval_status=status
            )
            
            dataset = approval.to_dataset()
            assert dataset.ApprovalStatus == status.value, comment
            
            # Test property getters
            if status == ApprovalStatus.APPROVED:
                assert approval.is_approved, "Final approval for treatment"
                assert not approval.is_rejected, "Final approval for treatment"
                assert not approval.is_unapproved, "Final approval for treatment"
                assert approval.requires_review_information, "Final approval for treatment"
            elif status == ApprovalStatus.REJECTED:
                assert not approval.is_approved, "Dose distribution needs revision"
                assert approval.is_rejected, "Dose distribution needs revision"
                assert not approval.is_unapproved, "Dose distribution needs revision"
                assert approval.requires_review_information, "Dose distribution needs revision"
            else:  # UNAPPROVED
                assert not approval.is_approved, "Not yet submitted for approval"
                assert not approval.is_rejected, "Not yet submitted for approval"
                assert approval.is_unapproved, "Not yet submitted for approval"
                assert not approval.requires_review_information, "Not yet submitted for approval"
    
    def test_review_date_time_validation(self):
        """Test review date and time validation with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240315",
            review_time="143022.123456",
            reviewer_name="System^Test"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewDate == "20240315"
        assert dataset.ReviewTime == "143022.123456"
        assert dataset.ReviewerName == "System^Test"
        assert approval.has_review_information
        assert approval.is_configured
    
    def test_approval_authority_information(self):
        """Test approval authority and institutional information with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20230101",
            review_time="120000",
            reviewer_name="Wilson^James^MD^Dr.^"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewerName == "Wilson^James^MD^Dr.^"
        assert approval.has_review_information
        assert approval.requires_review_information
    
    def test_dose_specific_approval_comments(self):
        """Test basic approval functionality."""
        # This test verifies basic approval functionality
        # Note: The module doesn't store comments directly, but we can test the approval workflow
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        assert approval.is_configured
    
    def test_rejection_with_feedback(self):
        """Test rejection status with detailed feedback and property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.REJECTED
        ).with_review_information(
            review_date="20240320",
            review_time="091500",
            reviewer_name="Thompson^Lisa^MD^Dr.^"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ApprovalStatus == ApprovalStatus.REJECTED.value
        assert dataset.ReviewerName == "Thompson^Lisa^MD^Dr.^"
        assert approval.is_rejected
        assert approval.requires_review_information
        assert approval.has_review_information
    
    def test_unapproved_workflow(self):
        """Test unapproved workflow status and properties."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        dataset = approval.to_dataset()
        assert dataset.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
        assert not approval.requires_review_information
        assert not approval.has_review_information
        assert approval.is_configured
        assert approval.is_unapproved
        assert not approval.is_approved
        assert not approval.is_rejected
    
    def test_approval_sequence_tracking(self):
        """Test approval with review information and property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240320",
            review_time="100000",
            reviewer_name="Smith^John^MD^^"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewerName == "Smith^John^MD^^"
        assert dataset.ReviewDate == "20240320"
        assert dataset.ReviewTime == "100000"
        assert approval.has_review_information
        assert approval.is_configured
    
    def test_approval_digital_signature(self):
        """Test approval with digital signature information."""
        # Note: Digital signature handling would typically be separate from basic approval
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240320",
            review_time="120000",
            reviewer_name="System^Automated^Sig^^"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewerName == "System^Automated^Sig^^"
        assert approval.is_approved
        assert approval.requires_review_information
    
    def test_approval_version_tracking(self):
        """Test approval with version and modification tracking."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240320",
            review_time="143000.000",
            reviewer_name="System^Version^1.0"
        )
        
        assert approval.is_configured
        assert approval.has_review_information
        dataset = approval.to_dataset()
        assert dataset.ReviewerName == "System^Version^1.0"
    
    def test_qa_approval_workflow(self):
        """Test quality assurance approval workflow with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20230101",
            review_time="150000",
            reviewer_name="Davis^Michael^PhD^Prof.^"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewerName == "Davis^Michael^PhD^Prof.^"
        assert approval.is_approved
        assert approval.requires_review_information
        assert approval.has_review_information
    
    def test_unapproved_initial_state(self):
        """Test unapproved initial state for new dose calculations with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        dataset = approval.to_dataset()
        assert dataset.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
        assert not approval.requires_review_information
        assert not approval.has_review_information
        assert approval.is_configured
        assert approval.is_unapproved
        assert not approval.is_approved
        assert not approval.is_rejected
    
    def test_approval_audit_trail(self):
        """Test approval audit trail elements with property checks."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240322",
            review_time="101500",
            reviewer_name="Smith^John^MD^^"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewerName == "Smith^John^MD^^"
        assert dataset.ReviewDate == "20240322"
        assert dataset.ReviewTime == "101500"
        assert approval.has_review_information
        assert approval.is_configured
        assert approval.is_approved
    
    def test_optional_module_behavior(self):
        """Test that ApprovalModule behaves as optional enhancement."""
        # Approval module should not be required for basic dose functionality
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        # Module should function independently
        assert approval.is_configured
        assert approval.is_unapproved
        assert not approval.requires_review_information
        dataset = approval.to_dataset()
        assert dataset.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        assert hasattr(approval, 'validate')
        assert callable(approval.validate)
        
        # Test validation result structure
        validation_result = approval.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_check_required_elements_success(self):
        """Test check_required_elements with valid data."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        result = approval.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_check_required_elements_failure(self):
        """Test check_required_elements with missing required element."""
        approval = ApprovalModule()  # Empty module, missing ApprovalStatus
        
        result = approval.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Missing required Type 1 element: ApprovalStatus (300E,0002)" in result.errors[0]
    
    def test_check_conditional_requirements_approved_with_review_info(self):
        """Test check_conditional_requirements for APPROVED status with review info."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",
            review_time="120000",
            reviewer_name="Dr. Smith"
        )
        
        result = approval.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_check_conditional_requirements_approved_missing_review_info(self):
        """Test check_conditional_requirements for APPROVED status missing review info."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        result = approval.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "Missing required Type 2C elements" in result.errors[0]
        assert "ReviewDate (300E,0004)" in result.errors[0]
        assert "ReviewTime (300E,0005)" in result.errors[0]
        assert "ReviewerName (300E,0008)" in result.errors[0]
    
    def test_check_conditional_requirements_unapproved_without_review_info(self):
        """Test check_conditional_requirements for UNAPPROVED status without review info."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        result = approval.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_check_conditional_requirements_unapproved_with_review_info(self):
        """Test check_conditional_requirements for UNAPPROVED status with review info (error)."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        # Manually add review information that shouldn't be there
        approval.ReviewDate = "20240101"
        approval.ReviewTime = "120000"
        approval.ReviewerName = "Dr. Smith"
        
        result = approval.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "Type 2C elements present for UNAPPROVED status" in result.errors[0]
    
    def test_check_enum_constraints_valid(self):
        """Test check_enum_constraints with valid enumerated value."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        result = approval.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_warnings
    
    def test_check_enum_constraints_invalid(self):
        """Test check_enum_constraints with invalid enumerated value."""
        approval = ApprovalModule()
        approval.ApprovalStatus = "INVALID_STATUS"
        
        result = approval.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Warnings don't make it invalid
        assert result.has_warnings
        assert "ApprovalStatus (300E,0002) value 'INVALID_STATUS'" in result.warnings[0]
    
    def test_check_sequence_requirements(self):
        """Test check_sequence_requirements (Approval Module has no sequences)."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        result = approval.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        assert not result.has_warnings
    
    def test_check_date_time_formats_valid(self):
        """Test check_date_time_formats with valid date/time formats."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",
            review_time="120000.123456",
            reviewer_name="Dr. Smith"
        )
        
        result = approval.check_date_time_formats()
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_check_date_time_formats_invalid_date(self):
        """Test check_date_time_formats with invalid date format."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        approval.ReviewDate = "2024-01-01"  # Invalid format
        approval.ReviewTime = "120000"
        approval.ReviewerName = "Dr. Smith"
        
        result = approval.check_date_time_formats()
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "ReviewDate (300E,0004)" in result.errors[0]
        assert "DICOM DA format" in result.errors[0]
    
    def test_check_date_time_formats_invalid_time(self):
        """Test check_date_time_formats with invalid time format."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        approval.ReviewDate = "20240101"
        approval.ReviewTime = "12:00:00"  # Invalid format
        approval.ReviewerName = "Dr. Smith"
        
        result = approval.check_date_time_formats()
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "ReviewTime (300E,0005)" in result.errors[0]
        assert "HHMMSS" in result.errors[0]
    
    def test_private_validation_methods_success(self):
        """Test private validation methods with valid data (no exceptions)."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",
            review_time="120000",
            reviewer_name="Dr. Smith"
        )
        
        # These should not raise exceptions
        approval._ensure_required_elements_valid()
        approval._ensure_conditional_requirements_valid()
        approval._ensure_enum_constraints_valid()
        approval._ensure_sequence_requirements_valid()
        approval._ensure_date_time_formats_valid()
    
    def test_private_validation_methods_failure(self):
        """Test private validation methods with invalid data (should raise ValidationError)."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        approval = ApprovalModule()  # Empty module
        
        # Should raise ValidationError for missing required element
        with pytest.raises(ValidationError) as exc_info:
            approval._ensure_required_elements_valid()
        assert "Required elements validation failed" in str(exc_info.value)
    
    def test_private_validation_conditional_requirements_failure(self):
        """Test private conditional validation method with invalid data."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        # Missing required review information
        
        with pytest.raises(ValidationError) as exc_info:
            approval._ensure_conditional_requirements_valid()
        assert "Conditional requirements validation failed" in str(exc_info.value)
    
    def test_approval_with_multiple_reviewers(self):
        """Test approval workflow with multiple reviewers."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            reviewer_name="Wilson^James^MD^Dr.^",
            review_date="20240322",
            review_time="101500"
        )
        
        dataset = approval.to_dataset()
        assert dataset.ReviewerName == "Wilson^James^MD^Dr.^"
    
    def test_treatment_machine_approval(self):
        """Test approval specific to treatment machine compatibility."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        dataset = approval.to_dataset()
        assert dataset.ApprovalStatus == ApprovalStatus.APPROVED.value
        
