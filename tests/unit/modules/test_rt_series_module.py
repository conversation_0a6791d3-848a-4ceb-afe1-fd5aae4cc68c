"""
Test RTSeriesModule (M - Mandatory) functionality.

RTSeriesModule implements DICOM PS3.3 C.8.8.1 RT Series Module.
Uses composition-based architecture with internal dataset management.
"""

import pydicom
from pydicom.uid import generate_uid
from pyrt_dicom.modules import RTSeriesModule
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.series_enums import Modality


class TestRTSeriesModule:
    """Test RTSeriesModule (M - Mandatory) functionality."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        series_uid = generate_uid()
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=series_uid,
            operators_name="Tech^John"
        )

        dataset = series.to_dataset()
        assert dataset.Modality == "RTDOSE"
        assert dataset.SeriesInstanceUID == series_uid
        assert dataset.OperatorsName == "Tech^John"
    
    def test_modality_validation(self):
        """Test modality validation for RT Series."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        )

        dataset = series.to_dataset()
        assert dataset.Modality == "RTDOSE"

    def test_operators_name_required(self):
        """Test operators name is required (Type 2)."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name=""  # Empty but present
        )

        dataset = series.to_dataset()
        assert hasattr(dataset, 'OperatorsName')
        assert dataset.OperatorsName == ""

    def test_various_rt_modalities(self):
        """Test various RT modality values."""
        rt_modalities = ["RTDOSE", "RTPLAN", "RTSTRUCT", "RTIMAGE", "RTRECORD"]

        for modality in rt_modalities:
            series = RTSeriesModule.from_required_elements(
                modality=modality,
                series_instance_uid=generate_uid(),
                operators_name="Tech^John"
            )
            dataset = series.to_dataset()
            assert dataset.Modality == modality
    
    def test_series_uid_uniqueness(self):
        """Test that series UIDs are unique."""
        series1 = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        )
        series2 = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^Jane"
        )

        dataset1 = series1.to_dataset()
        dataset2 = series2.to_dataset()
        assert dataset1.SeriesInstanceUID != dataset2.SeriesInstanceUID

    def test_with_optional_elements(self):
        """Test adding optional series information."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        ).with_optional_elements(
            series_number=1,
            series_description="RT Dose Distribution",
            series_date="20240101",
            series_time="120000"
        )

        dataset = series.to_dataset()
        assert hasattr(dataset, 'SeriesNumber')
        assert hasattr(dataset, 'SeriesDescription')
        assert hasattr(dataset, 'SeriesDate')
        assert hasattr(dataset, 'SeriesTime')
        assert dataset.SeriesNumber == 1
        assert dataset.SeriesDescription == "RT Dose Distribution"
    
    def test_series_number_optional_values(self):
        """Test various valid series number values as optional elements."""
        valid_series_numbers = ["1", "001", "123", "999"]

        for number in valid_series_numbers:
            series = RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid=generate_uid(),
                operators_name="Tech^John"
            ).with_optional_elements(
                series_number=number
            )
            dataset = series.to_dataset()
            assert dataset.SeriesNumber == number

    def test_enum_modality_support(self):
        """Test that enum modality values are supported."""
        series = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE,
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        )

        dataset = series.to_dataset()
        assert dataset.Modality == "RTDOSE"
    
    def test_operator_identification_sequence(self):
        """Test adding operator identification sequence."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        ).with_operator_identification(
            operator_identification_sequence=[
                RTSeriesModule.create_operator_identification_item(
                    person_address="123 Main St",
                    person_telephone_numbers=["555-1234"],
                    institution_name="General Hospital"
                )
            ]
        )

        dataset = series.to_dataset()
        assert hasattr(dataset, 'OperatorIdentificationSequence')
        assert len(dataset.OperatorIdentificationSequence) == 1
        assert dataset.OperatorIdentificationSequence[0].PersonAddress == "123 Main St"

    def test_rt_specific_elements(self):
        """Test adding RT-specific elements."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        ).with_rt_specific_elements(
            treatment_session_uid="*******.*******.9.12"
        )

        dataset = series.to_dataset()
        assert hasattr(dataset, 'TreatmentSessionUID')
        assert dataset.TreatmentSessionUID == "*******.*******.9.12"
    
    def test_dataset_generation(self):
        """Test that to_dataset() generates valid pydicom Dataset."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        )

        dataset = series.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        assert hasattr(dataset, 'Modality')
        assert hasattr(dataset, 'SeriesInstanceUID')
        assert hasattr(dataset, 'OperatorsName')

    def test_properties(self):
        """Test module properties."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        )

        # Test basic properties
        assert series.has_operator_info is True
        assert series.is_configured is True
        assert series.Modality == "RTDOSE"
        assert series.modality_type == "RTDOSE"

        # Test with RT-specific elements
        series.with_rt_specific_elements(treatment_session_uid="*******.5")
        assert series.has_rt_specific_info is True

    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        )

        assert hasattr(series, 'validate')
        assert callable(series.validate)

        # Test validation result structure
        validation_result = series.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_series_description_code_sequence(self):
        """Test series description code sequence creation."""
        code_item = RTSeriesModule.create_series_description_code_item(
            code_value="113014",
            coding_scheme_designator="DCM",
            code_meaning="Dose"
        )

        assert isinstance(code_item, pydicom.Dataset)
        assert code_item.CodeValue == "113014"
        assert code_item.CodingSchemeDesignator == "DCM"
        assert code_item.CodeMeaning == "Dose"

        # Test with series
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            operators_name="Tech^John"
        ).with_optional_elements(
            series_description_code_sequence=[code_item]
        )

        dataset = series.to_dataset()
        assert hasattr(dataset, 'SeriesDescriptionCodeSequence')
        assert len(dataset.SeriesDescriptionCodeSequence) == 1
