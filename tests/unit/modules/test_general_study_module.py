"""
Test GeneralStudyModule (M - Mandatory) functionality.

GeneralStudyModule implements DICOM PS3.3 C.7.2.1 General Study Module.
Required for all RTDoseIOD instances.
"""

from pydicom import Dataset
from pydicom.uid import generate_uid
from pyrt_dicom.modules import GeneralStudyModule
from pyrt_dicom.validators import ValidationResult


class TestGeneralStudyModule:
    """Test GeneralStudyModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        study_uid = generate_uid()
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_uid,
            study_date="20240101",
            study_time="120000"
        )

        # Test using to_dataset() method to access module data
        dataset = study.to_dataset()
        assert dataset.StudyInstanceUID == study_uid
        assert dataset.StudyDate == "20240101"
        assert dataset.StudyTime == "120000"
    
    def test_study_uid_validation(self):
        """Test study instance UID validation."""
        # Valid UID format
        valid_uid = "1.2.3.4.5.6.7.8.9"
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=valid_uid,
            study_date="",
            study_time=""
        )

        dataset = study.to_dataset()
        assert dataset.StudyInstanceUID == valid_uid
    
    def test_date_time_validation(self):
        """Test DICOM date/time format validation."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",  # YYYYMMDD format
            study_time="123045.123456"  # HHMMSS.FFFFFF format
        )

        dataset = study.to_dataset()
        assert dataset.StudyDate == "20240101"
        assert dataset.StudyTime == "123045.123456"
    
    def test_generated_uid_uniqueness(self):
        """Test that generated UIDs are unique."""
        study1 = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",
            study_time=""
        )
        study2 = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",
            study_time=""
        )

        dataset1 = study1.to_dataset()
        dataset2 = study2.to_dataset()
        assert dataset1.StudyInstanceUID != dataset2.StudyInstanceUID
    
    def test_with_optional_elements(self):
        """Test adding optional study information."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000",
            study_id="STUDY001",
            accession_number="ACC12345"
        ).with_optional_elements(
            study_description="RT Treatment Planning Study"
        )

        dataset = study.to_dataset()
        assert hasattr(dataset, 'StudyDescription')
        assert hasattr(dataset, 'StudyID')
        assert hasattr(dataset, 'AccessionNumber')
        assert dataset.StudyDescription == "RT Treatment Planning Study"
    
    def test_empty_date_time_allowed(self):
        """Test that empty date/time are allowed (Type 2)."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",  # Empty but not None
            study_time=""   # Empty but not None
        )

        dataset = study.to_dataset()
        assert dataset.StudyDate == ""
        assert dataset.StudyTime == ""
    
    def test_various_date_formats(self):
        """Test various valid DICOM date formats."""
        valid_dates = [
            "20240101",     # Standard date
            "20240229",     # Leap year
            "20231231",     # End of year
            "19700101"      # Historical date
        ]

        for date in valid_dates:
            study = GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date=date,
                study_time=""
            )
            dataset = study.to_dataset()
            assert dataset.StudyDate == date
    
    def test_various_time_formats(self):
        """Test various valid DICOM time formats."""
        valid_times = [
            "120000",           # HHMMSS
            "120000.000",       # HHMMSS.FFF
            "120000.123456",    # HHMMSS.FFFFFF
            "000000",           # Midnight
            "235959"            # End of day
        ]

        for time in valid_times:
            study = GeneralStudyModule.from_required_elements(
                study_instance_uid=generate_uid(),
                study_date="",
                study_time=time
            )
            dataset = study.to_dataset()
            assert dataset.StudyTime == time
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="",
            study_time=""
        )
        
        assert hasattr(study, 'validate')
        assert callable(study.validate)
        
        # Test validation result structure
        validation_result = study.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_to_dataset_method(self):
        """Test that to_dataset() returns a proper pydicom Dataset."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        dataset = study.to_dataset()
        assert hasattr(dataset, 'StudyInstanceUID')
        assert hasattr(dataset, 'StudyDate')
        assert hasattr(dataset, 'StudyTime')

        # Verify it's a copy, not a reference
        original_uid = dataset.StudyInstanceUID
        dataset.StudyInstanceUID = "modified"
        new_dataset = study.to_dataset()
        assert new_dataset.StudyInstanceUID == original_uid

    def test_with_physician_identification(self):
        """Test adding physician identification sequences."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        ).with_physician_identification(
            consulting_physician_name="Smith^John\\Jones^Mary"
        )

        dataset = study.to_dataset()
        assert hasattr(dataset, 'ConsultingPhysicianName')
        # DICOM automatically parses backslash-separated values into a list for multi-value fields
        assert len(dataset.ConsultingPhysicianName) == 2
        assert dataset.ConsultingPhysicianName[0] == "Smith^John"
        assert dataset.ConsultingPhysicianName[1] == "Jones^Mary"

    def test_create_person_identification_item(self):
        """Test creating person identification items."""
        item = GeneralStudyModule.create_person_identification_item(
            code_value="12345",
            coding_scheme_designator="LOCAL",
            code_meaning="Dr. Smith",
            institution_name="General Hospital"
        )

        assert hasattr(item, 'PersonIdentificationCodeSequence')
        assert len(item.PersonIdentificationCodeSequence) == 1
        assert item.PersonIdentificationCodeSequence[0].CodeValue == "12345"
        assert item.PersonIdentificationCodeSequence[0].CodingSchemeDesignator == "LOCAL"
        assert item.PersonIdentificationCodeSequence[0].CodeMeaning == "Dr. Smith"
        assert item.InstitutionName == "General Hospital"

    def test_create_person_identification_item_validation_handled_by_validator(self):
        """Test that person identification items are created without exceptions, validation handled by validator."""
        # Previously this would have raised an exception, now it creates the item
        # and validation is handled separately by the validator
        item = GeneralStudyModule.create_person_identification_item(
            code_value="",  # Empty code value
            coding_scheme_designator="LOCAL",
            code_meaning="Dr. Smith"
            # No institution_name or institution_code_sequence
        )

        # Item should be created but may be invalid according to validator
        assert hasattr(item, 'PersonIdentificationCodeSequence')
        assert len(item.PersonIdentificationCodeSequence) == 1
        assert item.PersonIdentificationCodeSequence[0].CodeMeaning == "Dr. Smith"
        # Note: Validation of Type 1C requirements is now handled by the validator

    def test_create_referenced_study_item(self):
        """Test creating referenced study items."""
        item = GeneralStudyModule.create_referenced_study_item(
            study_instance_uid="1.2.3.4.5",
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="1.2.3.4.5.6"
        )

        assert item.StudyInstanceUID == "1.2.3.4.5"
        assert item.ReferencedSOPClassUID == "1.2.840.10008.5.1.4.1.1.2"
        assert item.ReferencedSOPInstanceUID == "1.2.3.4.5.6"

    def test_create_procedure_code_item(self):
        """Test creating procedure code items."""
        item = GeneralStudyModule.create_procedure_code_item(
            code_value="77301",
            coding_scheme_designator="CPT",
            code_meaning="Intensity modulated radiotherapy plan"
        )

        assert item.CodeValue == "77301"
        assert item.CodingSchemeDesignator == "CPT" 
        assert item.CodeMeaning == "Intensity modulated radiotherapy plan"

    def test_create_procedure_code_item_validation_handled_by_validator(self):
        """Test that procedure code items are created without exceptions, validation handled by validator."""
        # Previously this would have raised an exception, now it creates the item
        # and validation is handled separately by the validator
        item = GeneralStudyModule.create_procedure_code_item(
            code_value="",  # Empty code value
            coding_scheme_designator="CPT",
            code_meaning="Some procedure"
            # No long_code_value or urn_code_value either
        )

        # Item should be created but may be invalid according to validator
        assert item.CodeMeaning == "Some procedure"
        # Note: Validation of Type 1C requirements is now handled by the validator

    def test_property_methods(self):
        """Test property methods for checking module state."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        # Initially no optional elements
        assert not study.has_physician_identification
        assert not study.has_procedure_info
        assert not study.has_requesting_service_info

        # Add some optional elements
        study.with_optional_elements(
            requesting_service="Radiology",
            procedure_code_sequence=[Dataset()]
        )

        assert study.has_requesting_service_info
        assert study.has_procedure_info

    def test_check_required_elements(self):
        """Test check_required_elements method returns ValidationResult."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        result = study.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')

    def test_check_conditional_requirements(self):
        """Test check_conditional_requirements method returns ValidationResult."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        result = study.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        # General Study Module has no conditional requirements, so should be empty
        assert len(result.errors) == 0

    def test_check_enum_constraints(self):
        """Test check_enum_constraints method returns ValidationResult."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        result = study.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        # General Study Module has no enumerated constraints, so should be empty
        assert len(result.errors) == 0

    def test_check_sequence_requirements(self):
        """Test check_sequence_requirements method returns ValidationResult."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        result = study.check_sequence_requirements()
        assert isinstance(result, ValidationResult)

    def test_zero_copy_validation(self):
        """Test that validation methods use zero-copy optimization (pass self)."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        # These should all work without creating Dataset copies
        result1 = study.check_required_elements()
        result2 = study.check_conditional_requirements()
        result3 = study.check_enum_constraints()
        result4 = study.check_sequence_requirements()
        result5 = study.validate()

        # All should return ValidationResult instances
        for result in [result1, result2, result3, result4, result5]:
            assert isinstance(result, ValidationResult)

    def test_private_validation_methods_exist(self):
        """Test that private validation methods exist and are callable."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        # Check that private methods exist
        assert hasattr(study, '_ensure_required_elements_valid')
        assert hasattr(study, '_ensure_conditional_requirements_valid')
        assert hasattr(study, '_ensure_enum_constraints_valid')
        assert hasattr(study, '_ensure_sequence_requirements_valid')

        # Check they are callable
        assert callable(study._ensure_required_elements_valid)
        assert callable(study._ensure_conditional_requirements_valid)
        assert callable(study._ensure_enum_constraints_valid)
        assert callable(study._ensure_sequence_requirements_valid)

    def test_private_validation_methods_with_valid_data(self):
        """Test private validation methods with valid data (should not raise exceptions)."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000",
            referring_physicians_name="Smith^John",
            study_id="STUDY001",
            accession_number="ACC123"
        )

        # These should not raise exceptions with valid data
        try:
            study._ensure_required_elements_valid()
            study._ensure_conditional_requirements_valid()
            study._ensure_enum_constraints_valid()
            study._ensure_sequence_requirements_valid()
        except Exception as e:
            assert False, f"Private validation methods should not raise exceptions with valid data: {e}"

    def test_private_validation_methods_with_missing_required_elements(self):
        """Test private validation methods raise ValidationError with missing required elements."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with missing required elements
        study = GeneralStudyModule()

        # Should raise ValidationError for missing required elements
        try:
            study._ensure_required_elements_valid()
            assert False, "Should have raised ValidationError for missing required elements"
        except ValidationError as e:
            assert "Study Instance UID" in str(e)

    def test_validation_result_structure_consistency(self):
        """Test that all validation methods return consistent ValidationResult structure."""
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=generate_uid(),
            study_date="20240101",
            study_time="120000"
        )

        validation_methods = [
            study.check_required_elements,
            study.check_conditional_requirements,
            study.check_enum_constraints,
            study.check_sequence_requirements,
            study.validate
        ]

        for method in validation_methods:
            result = method()
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')
            assert hasattr(result, 'has_errors')
            assert hasattr(result, 'is_valid')
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)
            assert isinstance(result.has_errors, bool)
            assert isinstance(result.is_valid, bool)