"""Tests for BaseModule class.

This module tests the base module functionality including dataset abstraction
methods and basic DICOM element operations.
"""

import pytest
import pydicom
from pydicom.tag import Tag

from pyrt_dicom.modules.base_module import BaseModule
from pyrt_dicom.validators.validation_result import ValidationResult


class ConcreteModule(BaseModule):
    """Concrete implementation of BaseModule for testing."""
    
    @classmethod
    def from_required_elements(cls, test_value: str = "test") -> 'ConcreteModule':
        """Create module with test elements."""
        instance = cls()
        # Use proper DICOM tags instead of camel case
        instance[(0x0008, 0x0070)] = test_value  # Manufacturer
        return instance
    
    def with_optional_elements(self, optional_value: str | None = None) -> 'ConcreteModule':
        """Add optional elements."""
        if optional_value is not None:
            # Use proper DICOM tag
            self[(0x0008, 0x0080)] = optional_value  # Institution Name
        return self


class TestBaseModuleInitialization:
    """Test BaseModule initialization and basic properties."""
    
    def test_init_creates_empty_dataset(self):
        """Test that BaseModule creates empty pydicom Dataset."""
        module = ConcreteModule()
        assert hasattr(module, '_dataset')
        assert isinstance(module._dataset, pydicom.Dataset)
        assert len(module._dataset) == 0
    
    def test_module_name_property(self):
        """Test module_name property returns class name."""
        module = ConcreteModule()
        assert module.module_name == "ConcreteModule"
    
    def test_has_data_property_empty(self):
        """Test has_data property with empty module."""
        module = ConcreteModule()
        assert not module.has_data
    
    def test_has_data_property_with_data(self):
        """Test has_data property with data."""
        module = ConcreteModule.from_required_elements("test_value")
        assert module.has_data
    
    def test_get_element_count_empty(self):
        """Test get_element_count with empty module."""
        module = ConcreteModule()
        assert module.get_element_count() == 0
    
    def test_get_element_count_with_data(self):
        """Test get_element_count with data."""
        module = ConcreteModule.from_required_elements("test_value")
        assert module.get_element_count() == 1


class TestBaseModuleDataAccess:
    """Test BaseModule data access methods."""
    
    def setup_method(self):
        """Set up test module with sample data."""
        self.module = ConcreteModule()
        # Use proper DICOM tags
        self.module[(0x0010, 0x0010)] = "Doe^John"  # PatientName
        self.module[(0x0010, 0x0020)] = "12345"     # PatientID
        self.module[(0x0008, 0x0020)] = "20240101"  # StudyDate
    
    def test_get_element_existing_key(self):
        """Test get_element with existing key."""
        assert self.module[(0x0010, 0x0010)].value == "Doe^John"
        assert self.module[(0x0010, 0x0020)].value == "12345"
    
    def test_get_element_missing_key_with_default(self):
        """Test get_element with missing key and default value."""
        with pytest.raises(KeyError):
            self.module[(0x0099, 0x0099)]
    
    def test_get_element_missing_key_no_default(self):
        """Test get_element with missing key and no default."""
        with pytest.raises(KeyError):
            self.module[(0x0099, 0x0099)]
    
    def test_get_element_with_tag_object(self):
        """Test get_element with pydicom Tag object."""
        tag = Tag((0x0010, 0x0010))  # PatientName
        assert self.module[tag].value == "Doe^John"
    
    def test_contains_operator_existing(self):
        """Test __contains__ with existing elements."""
        assert (0x0010, 0x0010) in self.module  # PatientName
        assert (0x0010, 0x0020) in self.module  # PatientID
    
    def test_contains_operator_missing(self):
        """Test __contains__ with missing elements."""
        assert (0x0099, 0x0099) not in self.module  # Non-existent tag
    
    def test_getitem_operator_existing(self):
        """Test __getitem__ with existing elements."""
        assert self.module[(0x0010, 0x0010)].value == "Doe^John"
        assert self.module[(0x0010, 0x0020)].value == "12345"
    
    def test_getitem_operator_missing(self):
        """Test __getitem__ with missing element raises KeyError."""
        with pytest.raises(KeyError):
            _ = self.module[(0x0099, 0x0099)]  # Non-existent tag
    
    def test_get_method_existing_key(self):
        """Test get method with existing key (tag tuples return DataElements)."""
        assert self.module.get((0x0010, 0x0010), "default").value == "Doe^John"
        assert self.module.get((0x0010, 0x0020), "default").value == "12345"
    
    def test_get_method_missing_key_with_default(self):
        """Test get method with missing key returns default value."""
        default_value = "default_result"
        result = self.module.get((0x0099, 0x0099), default_value)
        assert result == default_value
    
    def test_get_method_missing_key_no_default(self):
        """Test get method with missing key and no default returns None."""
        result = self.module.get((0x0099, 0x0099), None)
        assert result is None
    
    def test_get_method_with_tag_object(self):
        """Test get method with pydicom Tag object (returns DataElement)."""
        tag = Tag((0x0010, 0x0010))  # PatientName
        assert self.module.get(tag, "default").value == "Doe^John"
    
    def test_get_method_with_string_key(self):
        """Test get method with string key (keyword returns value directly)."""
        # Add element with string key for testing
        self.module._dataset.PatientBirthDate = "19800101"
        assert self.module.get("PatientBirthDate", "default") == "19800101"
        
        # Test missing string key
        result = self.module.get("NonExistentAttribute", "default_value")
        assert result == "default_value"
    
    def test_access_patterns_comparison(self):
        """Test different access patterns for the same element."""
        # Set up element that can be accessed both ways
        self.module._dataset.PatientBirthDate = "19900515"
        
        # Dict-style access with string key returns DataElement
        birth_date_element = self.module["PatientBirthDate"]
        assert hasattr(birth_date_element, 'value')
        assert birth_date_element.value == "19900515"
        
        # get() method with string key returns value directly  
        birth_date_value = self.module.get("PatientBirthDate", "default")
        assert birth_date_value == "19900515"
        
        # Dict-style access with tag tuple returns DataElement
        patient_name_element = self.module[(0x0010, 0x0010)]
        assert hasattr(patient_name_element, 'value')
        assert patient_name_element.value == "Doe^John"
        
        # get() method with tag tuple also returns DataElement
        patient_name_element_get = self.module.get((0x0010, 0x0010), "default")
        assert hasattr(patient_name_element_get, 'value')
        assert patient_name_element_get.value == "Doe^John"
    
    def test_setitem_operator_new_element(self):
        """Test __setitem__ with new element."""
        self.module[(0x0008, 0x0080)] = "new_value"  # InstitutionName
        assert self.module[(0x0008, 0x0080)].value == "new_value"
        assert (0x0008, 0x0080) in self.module
    
    def test_setitem_operator_existing_element(self):
        """Test __setitem__ with existing element."""
        self.module[(0x0010, 0x0010)] = "Smith^Jane"
        assert self.module[(0x0010, 0x0010)].value == "Smith^Jane"
    
    def test_delitem_operator_existing(self):
        """Test __delitem__ with existing element."""
        assert (0x0010, 0x0010) in self.module
        del self.module[(0x0010, 0x0010)]
        assert (0x0010, 0x0010) not in self.module
    
    def test_delitem_operator_missing(self):
        """Test __delitem__ with missing element raises KeyError."""
        with pytest.raises(KeyError):
            del self.module[(0x0099, 0x0099)]  # Non-existent tag
    
    def test_len_operator(self):
        """Test __len__ returns correct count."""
        assert len(self.module) == 3  # PatientName, PatientID, StudyDate
        
        # Add element and verify count increases
        self.module[(0x0008, 0x0080)] = "value"  # InstitutionName
        assert len(self.module) == 4
        
        # Remove element and verify count decreases
        del self.module[(0x0008, 0x0080)]
        assert len(self.module) == 3


class TestBaseModuleElementManagement:
    """Test BaseModule element management methods."""
    
    def setup_method(self):
        """Set up test module."""
        self.module = ConcreteModule()
    
    def test_set_element_new(self):
        """Test set_element adds new element with explicit VR."""
        self.module[(0x0010, 0x0010)] = "Doe^John"
        assert self.module[(0x0010, 0x0010)].value == "Doe^John"
        assert (0x0010, 0x0010) in self.module
    
    def test_set_element_with_tag_tuple(self):
        """Test set_element with tag tuple."""
        self.module[(0x0010, 0x0010)] = "Doe^John"
        assert self.module[(0x0010, 0x0010)].value == "Doe^John"
    
    def test_set_element_with_tag_object(self):
        """Test set_element with Tag object."""
        tag = Tag((0x0010, 0x0010))
        self.module[tag] = "Doe^John"
        assert self.module[(0x0010, 0x0010)].value == "Doe^John"
        
    def test_set_method_new_element(self):
        """Test set method adds new element."""
        self.module.set((0x0010, 0x0010), "Smith^Jane")
        assert self.module[(0x0010, 0x0010)].value == "Smith^Jane"
        assert (0x0010, 0x0010) in self.module
    
    def test_set_method_existing_element(self):
        """Test set method updates existing element."""
        # Add initial element
        self.module[(0x0010, 0x0010)] = "Initial^Name"
        assert self.module[(0x0010, 0x0010)].value == "Initial^Name"
        
        # Update using set method
        self.module.set((0x0010, 0x0010), "Updated^Name")
        assert self.module[(0x0010, 0x0010)].value == "Updated^Name"
    
    def test_set_method_with_tag_object(self):
        """Test set method with Tag object."""
        tag = Tag((0x0010, 0x0020))  # PatientID
        self.module.set(tag, "12345")
        assert self.module[tag].value == "12345"
        assert (0x0010, 0x0020) in self.module
    
    def test_set_method_with_string_key(self):
        """Test set method with string key."""
        self.module.set("PatientBirthDate", "19850615")
        assert self.module._dataset.PatientBirthDate == "19850615"
        assert self.module.get("PatientBirthDate", "default") == "19850615"
    
    def test_set_method_with_different_value_types(self):
        """Test set method with different value types."""
        # String value
        self.module.set((0x0008, 0x0070), "Test Manufacturer")
        assert self.module[(0x0008, 0x0070)].value == "Test Manufacturer"
        
        # Integer value  
        self.module.set((0x0020, 0x0013), 42)  # Instance Number
        assert self.module[(0x0020, 0x0013)].value == 42
        
        # Float value
        self.module.set((0x0018, 0x0050), 2.5)  # Slice Thickness
        assert self.module[(0x0018, 0x0050)].value == 2.5
    
    def test_clear_removes_all_elements(self):
        """Test clear removes all elements."""
        self.module[(0x0010, 0x0010)] = "value1"  # PatientName
        self.module[(0x0010, 0x0020)] = "value2"  # PatientID
        assert len(self.module) == 2
        
        self.module.clear()
        assert len(self.module) == 0
        assert not self.module.has_data
    
    def test_copy_returns_dataset_copy(self):
        """Test copy returns a copy of internal dataset."""
        self.module[(0x0010, 0x0010)] = "Doe^John"
        self.module[(0x0010, 0x0020)] = "12345"
        
        copied_dataset = self.module.copy()
        
        # Verify it's a pydicom Dataset
        assert isinstance(copied_dataset, pydicom.Dataset)
        
        # Verify it has the same data (copy returns raw dataset with DataElements)
        assert copied_dataset[(0x0010, 0x0010)].value == "Doe^John"
        assert copied_dataset[(0x0010, 0x0020)].value == "12345"
        
        # Verify it's a separate object (changes don't affect original)
        # For direct dataset manipulation, we need to create a proper DataElement
        from pydicom.dataelem import DataElement
        copied_dataset[(0x0010, 0x0010)] = DataElement((0x0010, 0x0010), "PN", "Smith^Jane")
        assert self.module[(0x0010, 0x0010)].value == "Doe^John"  # Original unchanged


class TestBaseModuleIteration:
    """Test BaseModule iteration methods."""
    
    def setup_method(self):
        """Set up test module with sample data."""
        self.module = ConcreteModule()
        # Use proper DICOM tags
        self.module[(0x0010, 0x0010)] = "Doe^John"  # PatientName
        self.module[(0x0010, 0x0020)] = "12345"     # PatientID
        self.module[(0x0008, 0x0020)] = "20240101"  # StudyDate
    
    def test_keys_returns_iterator(self):
        """Test keys() returns iterator of element keys."""
        keys = list(self.module.keys())
        assert len(keys) == 3
        # Check that the tags are present as strings
        key_strs = [str(k) for k in keys]
        assert "(0010,0010)" in key_strs
        assert "(0010,0020)" in key_strs
        assert "(0008,0020)" in key_strs
    
    def test_values_returns_iterator(self):
        """Test values() returns iterator of element values."""
        values = list(self.module.values())
        assert len(values) == 3
        # values() returns DataElements from pydicom dataset
        # Extract actual values from DataElements
        actual_values = []
        for v in values:
            if hasattr(v, 'value'):
                actual_values.append(v.value)
            else:
                actual_values.append(v)
        assert "Doe^John" in actual_values
        assert "12345" in actual_values
        assert "20240101" in actual_values
    
    def test_items_returns_iterator(self):
        """Test items() returns iterator of (tag, element) pairs."""
        items = list(self.module.items())
        assert len(items) == 3
        
        # Verify we get tuples of (tag, element)
        for _, element in items:
            assert hasattr(element, 'value')  # DICOM DataElement has value attribute


class TestBaseModuleDatasetGeneration:
    """Test BaseModule dataset generation methods."""
    
    def test_to_dataset_returns_deep_copy(self):
        """Test to_dataset returns deep copy of internal dataset."""
        module = ConcreteModule()
        module["PatientName"] = "Doe^John"
        module["PatientID"] = "12345"
        
        dataset = module.to_dataset()
        
        # Verify it's a pydicom Dataset
        assert isinstance(dataset, pydicom.Dataset)
        
        # Verify it has the same data
        assert dataset.PatientName == "Doe^John"
        assert dataset.PatientID == "12345"
        
        # Verify it's a separate object (changes don't affect original)
        dataset.PatientName = "Smith^Jane"
        assert module.get("PatientName", "default") == "Doe^John"  # Original unchanged


class TestBaseModuleValidation:
    """Test BaseModule validation methods."""
    
    def test_default_validate_returns_empty_result(self):
        """Test default validate method returns empty ValidationResult."""
        module = ConcreteModule()
        result = module.validate()
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        assert not result.has_warnings
        assert result.error_count == 0
        assert result.warning_count == 0


class TestBaseModuleStringRepresentation:
    """Test BaseModule string representation."""
    
    def test_repr_empty_module(self):
        """Test __repr__ with empty module."""
        module = ConcreteModule()
        repr_str = repr(module)
        assert "ConcreteModule(0 attributes)" == repr_str
    
    def test_repr_module_with_data(self):
        """Test __repr__ with module containing data."""
        module = ConcreteModule()
        module[(0x0010, 0x0010)] = "value1"  # PatientName
        module[(0x0010, 0x0020)] = "value2"  # PatientID
        
        repr_str = repr(module)
        assert "ConcreteModule(2 attributes)" == repr_str


class TestBaseModuleFactoryPattern:
    """Test BaseModule factory pattern methods."""
    
    def test_from_required_elements_creates_instance(self):
        """Test from_required_elements class method."""
        module = ConcreteModule.from_required_elements("test_value")
        
        assert isinstance(module, ConcreteModule)
        assert module[(0x0008, 0x0070)].value == "test_value"  # Manufacturer
        assert len(module) == 1
    
    def test_with_optional_elements_returns_self(self):
        """Test with_optional_elements returns self for chaining."""
        module = ConcreteModule.from_required_elements("test_value")
        result = module.with_optional_elements("optional_value")
        
        assert result is module  # Should return same instance
        assert module[(0x0008, 0x0080)].value == "optional_value"  # Institution Name
        assert len(module) == 2


class TestBaseModuleIntegration:
    """Integration tests for BaseModule functionality."""
    
    def test_complete_workflow(self):
        """Test complete workflow with all BaseModule functionality."""
        # Create module with factory pattern
        module = ConcreteModule.from_required_elements("initial_value")
        
        # Add optional elements
        module.with_optional_elements("optional_value")
        
        # Use various access methods
        assert (0x0008, 0x0070) in module
        assert module[(0x0008, 0x0070)].value == "initial_value"
        assert module[(0x0008, 0x0080)].value == "optional_value"
        
        # Modify elements
        module[(0x0008, 0x0070)] = "modified_value"
        module[(0x0008, 0x1030)] = "new_element_value"  # StudyDescription
        
        # Verify state
        assert len(module) == 3
        assert module.has_data
        assert module.get_element_count() == 3
        
        # Generate dataset
        dataset = module.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        # to_dataset() returns raw pydicom dataset with DataElements
        assert dataset[(0x0008, 0x0070)].value == "modified_value"
        assert dataset[(0x0008, 0x0080)].value == "optional_value"
        assert dataset[(0x0008, 0x1030)].value == "new_element_value"
        
        # Validate
        result = module.validate()
        assert result.is_valid


class TestBaseModulePrivateValidationMethods:
    """Test BaseModule private validation integration methods."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.module = ConcreteModule()
        
        # Mock validation method that returns successful result
        def mock_validator_success(data):
            """Mock validator that returns successful validation."""
            result = ValidationResult()
            return result
        
        # Mock validation method that returns errors
        def mock_validator_with_errors(data):
            """Mock validator that returns validation errors."""
            result = ValidationResult()
            result.add_error("Test validation error")
            result.add_error("Another validation error")
            return result
        
        # Mock validation method that returns warnings
        def mock_validator_with_warnings(data):
            """Mock validator that returns validation warnings."""
            result = ValidationResult()
            result.add_warning("Test validation warning")
            return result
        
        self.mock_validator_success = mock_validator_success
        self.mock_validator_with_errors = mock_validator_with_errors
        self.mock_validator_with_warnings = mock_validator_with_warnings
    
    def test_validate_and_raise_success(self):
        """Test _validate_and_raise method with successful validation."""
        result = self.module._validate_and_raise(
            self.mock_validator_success,
            "Test context"
        )
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_validate_and_raise_with_errors(self):
        """Test _validate_and_raise method raises exception on validation errors."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        with pytest.raises(ValidationError) as exc_info:
            self.module._validate_and_raise(
                self.mock_validator_with_errors,
                "Test context"
            )
        
        assert exc_info.value.context == "Test context"
        assert "Test validation error" in str(exc_info.value)
        assert "Another validation error" in str(exc_info.value)
    
    def test_validate_and_raise_with_warnings_only(self):
        """Test _validate_and_raise method succeeds with warnings only."""
        result = self.module._validate_and_raise(
            self.mock_validator_with_warnings,
            "Test context"
        )
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Valid because no errors
        assert result.has_warnings
        assert result.warning_count == 1
    
    def test_validate_and_raise_zero_copy_optimization(self):
        """Test _validate_and_raise uses zero-copy optimization by passing self."""
        def validator_that_checks_parameter(data):
            """Validator that verifies it receives the module instance."""
            result = ValidationResult()
            # Verify that we received the module instance directly
            assert data is self.module
            assert hasattr(data, '_dataset')
            return result
        
        result = self.module._validate_and_raise(
            validator_that_checks_parameter,
            "Test context"
        )
        
        assert result.is_valid
    
    def test_check_element_validity_success(self):
        """Test _check_element_validity method with successful validation."""
        result = self.module._check_element_validity(
            "TestElement",
            self.mock_validator_success
        )
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_check_element_validity_with_errors(self):
        """Test _check_element_validity method raises exception on validation errors."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        with pytest.raises(ValidationError) as exc_info:
            self.module._check_element_validity(
                "TestElement",
                self.mock_validator_with_errors
            )
        
        assert exc_info.value.context == "Invalid TestElement"
        assert "Test validation error" in str(exc_info.value)
        assert "Another validation error" in str(exc_info.value)
    
    def test_check_element_validity_with_warnings_only(self):
        """Test _check_element_validity method succeeds with warnings only."""
        result = self.module._check_element_validity(
            "TestElement",
            self.mock_validator_with_warnings
        )
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Valid because no errors
        assert result.has_warnings
        assert result.warning_count == 1
    
    def test_check_element_validity_zero_copy_optimization(self):
        """Test _check_element_validity uses zero-copy optimization by passing self."""
        def validator_that_checks_parameter(data):
            """Validator that verifies it receives the module instance."""
            result = ValidationResult()
            # Verify that we received the module instance directly
            assert data is self.module
            assert hasattr(data, '_dataset')
            return result
        
        result = self.module._check_element_validity(
            "TestElement",
            validator_that_checks_parameter
        )
        
        assert result.is_valid
    
    def test_ensure_valid_state_success(self):
        """Test _ensure_valid_state method with valid state."""
        # The default validate() method returns empty ValidationResult (no errors)
        # This should not raise an exception
        self.module._ensure_valid_state()  # Should not raise
    
    def test_ensure_valid_state_with_errors(self):
        """Test _ensure_valid_state method raises exception when validation fails."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        # Override the validate method to return errors using a bound method
        def mock_validate_with_errors(config=None):
            result = ValidationResult()
            result.add_error("Module validation error")
            result.add_error("Another module error")
            return result
        
        # Temporarily replace the validate method using __class__ approach
        import types
        original_validate = self.module.__class__.validate
        self.module.__class__.validate = mock_validate_with_errors
        
        try:
            with pytest.raises(ValidationError) as exc_info:
                self.module._ensure_valid_state()
            
            assert exc_info.value.context == "Module validation failed"
            assert "Module validation error" in str(exc_info.value)
            assert "Another module error" in str(exc_info.value)
        finally:
            # Restore original validate method
            self.module.__class__.validate = original_validate
    
    def test_ensure_valid_state_with_warnings_only(self):
        """Test _ensure_valid_state method succeeds with warnings only."""
        # Override the validate method to return warnings only
        def mock_validate_with_warnings(config=None):
            result = ValidationResult()
            result.add_warning("Module validation warning")
            return result
        
        # Temporarily replace the validate method using __class__ approach
        original_validate = self.module.__class__.validate
        self.module.__class__.validate = mock_validate_with_warnings
        
        try:
            # Should not raise an exception (warnings are not errors)
            self.module._ensure_valid_state()  # Should not raise
        finally:
            # Restore original validate method
            self.module.__class__.validate = original_validate
    
    def test_private_methods_not_visible_in_public_api(self):
        """Test that private validation methods are not exposed in public API."""
        # These methods should exist but be marked as private (underscore prefix)
        assert hasattr(self.module, '_validate_and_raise')
        assert hasattr(self.module, '_check_element_validity')
        assert hasattr(self.module, '_ensure_valid_state')
        
        # Verify they are callable
        assert callable(getattr(self.module, '_validate_and_raise'))
        assert callable(getattr(self.module, '_check_element_validity'))
        assert callable(getattr(self.module, '_ensure_valid_state'))
        
        # These methods should not appear in dir() by default (they are private)
        public_methods = [attr for attr in dir(self.module) if not attr.startswith('_')]
        assert '_validate_and_raise' not in public_methods
        assert '_check_element_validity' not in public_methods
        assert '_ensure_valid_state' not in public_methods
    
    def test_all_private_methods_use_modern_type_hints(self):
        """Test that all private validation methods use modern type hints."""
        import inspect
        
        # Get method signatures
        validate_and_raise_sig = inspect.signature(self.module._validate_and_raise)
        check_element_validity_sig = inspect.signature(self.module._check_element_validity)
        ensure_valid_state_sig = inspect.signature(self.module._ensure_valid_state)
        
        # _validate_and_raise should have parameters with proper type hints
        params = validate_and_raise_sig.parameters
        assert 'validation_method' in params
        assert 'error_context' in params
        
        # _check_element_validity should have parameters with proper type hints
        params = check_element_validity_sig.parameters
        assert 'element_name' in params
        assert 'validation_method' in params
        
        # _ensure_valid_state should have no parameters (except self)
        params = list(ensure_valid_state_sig.parameters.keys())
        assert params == []  # Only self parameter, which is not included in signature.parameters