"""
Test OverlayPlaneModule (U - Optional) functionality.

OverlayPlaneModule implements DICOM PS3.3 C.9.2 Overlay Plane Module.
Optional module for graphical overlays on dose distributions.

BREAKING CHANGES: Tests updated for composition-based architecture with internal dataset management.
"""

import numpy as np
import pydicom
from pyrt_dicom.modules import OverlayPlaneModule
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.image_enums import OverlayType, OverlaySubtype


class TestOverlayPlaneModule:
    """Test OverlayPlaneModule (U - Optional) functionality with composition-based architecture."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements and dataset generation."""
        overlay_data = np.zeros((64, 64), dtype=np.uint8)
        overlay_data[20:40, 20:40] = 1  # Create overlay region

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=64,
            overlay_columns=64,
            overlay_type="G",  # Graphics overlay
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )

        # Test dataset generation
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0

        # Test tag-based access works
        assert overlay[(0x6000, 0x0010)].value == 64  # Overlay Rows
        assert overlay[(0x6000, 0x0011)].value == 64  # Overlay Columns
        assert overlay[(0x6000, 0x0040)].value == "G"  # Overlay Type
        assert overlay[(0x6000, 0x0050)].value == [1, 1]  # Overlay Origin
        assert overlay[(0x6000, 0x0100)].value == 1  # Overlay Bits Allocated
        assert overlay[(0x6000, 0x0102)].value == 0  # Overlay Bit Position

        # Test that data is stored in internal dataset
        assert hasattr(overlay, '_dataset')
        assert len(overlay._dataset) > 0
    
    def test_overlay_type_validation(self):
        """Test overlay type validation for different overlay purposes with dataset generation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        overlay_types = ["G", "R"]  # Graphics, ROI

        for overlay_type in overlay_types:
            overlay = OverlayPlaneModule.from_required_elements(
                overlay_rows=32,
                overlay_columns=32,
                overlay_type=overlay_type,
                overlay_origin=[1, 1],
                overlay_data=overlay_data.tobytes()
            )

            # Test dataset generation
            dataset = overlay.to_dataset()
            assert isinstance(dataset, pydicom.Dataset)

            # Test compatibility property
            assert overlay[(0x6000, 0x0040)].value == overlay_type  # Overlay Type

            # Test logical properties
            if overlay_type == "G":
                assert overlay.is_graphics_overlay
                assert not overlay.is_roi_overlay
            else:  # overlay_type == "R"
                assert overlay.is_roi_overlay
                assert not overlay.is_graphics_overlay
    
    def test_various_overlay_sizes(self):
        """Test various overlay plane sizes matching dose grids with dataset generation."""
        overlay_sizes = [
            (64, 64),    # Standard dose grid
            (128, 128),  # High-resolution grid
            (256, 256),  # Very high resolution
            (100, 80),   # Non-square
            (32, 32)     # Small overlay
        ]

        for rows, cols in overlay_sizes:
            overlay_data = np.zeros((rows, cols), dtype=np.uint8)

            overlay = OverlayPlaneModule.from_required_elements(
                overlay_rows=rows,
                overlay_columns=cols,
                overlay_type="G",
                overlay_origin=[1, 1],
                overlay_data=overlay_data.tobytes()
            )

            # Test dataset generation
            dataset = overlay.to_dataset()
            assert isinstance(dataset, pydicom.Dataset)

            # Test compatibility properties
            assert overlay[(0x6000, 0x0010)].value == rows  # Overlay Rows
            assert overlay[(0x6000, 0x0011)].value == cols  # Overlay Columns

            # Test calculated properties
            assert overlay.overlay_pixel_count == rows * cols
            expected_size = overlay.calculate_overlay_data_size(rows, cols)
            assert overlay.expected_data_size == expected_size
    
    def test_overlay_origin_validation(self):
        """Test overlay origin positioning with dataset generation."""
        overlay_data = np.zeros((50, 50), dtype=np.uint8)

        # Various origin positions
        origins = [
            [1, 1],      # Standard DICOM origin
            [0, 0],      # Zero-based origin
            [10, 10],    # Offset origin
            [-5, -5],    # Negative offset
            [100, 50]    # Large offset
        ]

        for origin in origins:
            overlay = OverlayPlaneModule.from_required_elements(
                overlay_rows=50,
                overlay_columns=50,
                overlay_type="G",
                overlay_origin=origin,
                overlay_data=overlay_data.tobytes()
            )

            # Test dataset generation
            dataset = overlay.to_dataset()
            assert isinstance(dataset, pydicom.Dataset)

            # Test compatibility property
            assert overlay[(0x6000, 0x0050)].value == origin  # Overlay Origin

            # Test coordinate helper method
            coords = overlay.get_overlay_origin_coordinates()
            assert coords == (origin[0], origin[1])
    
    def test_overlay_bits_configuration(self):
        """Test overlay bits allocation and positioning are set correctly with dataset generation."""
        overlay_data = np.zeros((40, 40), dtype=np.uint8)

        # DICOM overlays are always 1-bit allocated at position 0
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=40,
            overlay_columns=40,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )

        # Test dataset generation
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

        # Verify that overlay automatically sets correct DICOM standard values
        assert overlay[(0x6000, 0x0100)].value == 1   # Overlay Bits Allocated (always 1)
        assert overlay[(0x6000, 0x0102)].value == 0     # Overlay Bit Position (always 0)

        # Test data size calculations
        assert overlay.actual_data_size == len(overlay_data.tobytes())
        assert overlay.expected_data_size is not None
    
    def test_with_optional_elements(self):
        """Test adding optional overlay elements with dataset generation."""
        overlay_data = np.zeros((60, 60), dtype=np.uint8)
        overlay_data[10:50, 10:50] = 1  # Create overlay pattern

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=60,
            overlay_columns=60,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_description="Dose isoline overlay",
            overlay_label="50% Isodose",
            overlay_subtype=OverlaySubtype.USER
        )

        # Test dataset generation
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

        # Test optional elements exist in dataset
        assert (0x6000, 0x0022) in overlay  # Overlay Description
        assert (0x6000, 0x1500) in overlay  # Overlay Label
        assert (0x6000, 0x0045) in overlay  # Overlay Subtype

        # Test values
        assert overlay[(0x6000, 0x0022)].value == "Dose isoline overlay"  # Overlay Description
        assert overlay[(0x6000, 0x1500)].value == "50% Isodose"  # Overlay Label
        assert overlay[(0x6000, 0x0045)].value == OverlaySubtype.USER.value  # Overlay Subtype

    def test_multi_frame_overlay_functionality(self):
        """Test multi-frame overlay functionality with conditional logic."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        overlay_data[10:22, 10:22] = 1  # Create overlay pattern

        # Test overlay without multi-frame elements (applies to all frames)
        overlay_all_frames = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )

        # Test conditional logic properties
        assert not overlay_all_frames.is_multi_frame_overlay
        assert overlay_all_frames.applies_to_all_frames

        # Test overlay with multi-frame elements (applies to specific frames)
        overlay_specific_frames = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_multi_frame_overlay(
            number_of_frames_in_overlay=5,
            image_frame_origin=[1, 3, 5, 7, 9]
        )

        # Test conditional logic properties
        assert overlay_specific_frames.is_multi_frame_overlay
        assert not overlay_specific_frames.applies_to_all_frames

        # Test dataset generation
        dataset = overlay_specific_frames.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

    def test_dose_isoline_overlays(self):
        """Test overlay patterns for dose isolines with dataset generation."""
        # Create isoline pattern overlay
        overlay_data = np.zeros((80, 80), dtype=np.uint8)

        # Create concentric isoline patterns
        center = (40, 40)
        for radius in [10, 20, 30]:
            y, x = np.ogrid[:80, :80]
            mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            overlay_data[mask] = 1

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=80,
            overlay_columns=80,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_description="Dose isoline contours",
            overlay_subtype=OverlaySubtype.AUTOMATED
        )

        # Test dataset generation
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

        # Test values
        assert overlay[(0x6000, 0x0022)].value == "Dose isoline contours"  # Overlay Description
        assert overlay[(0x6000, 0x0045)].value == OverlaySubtype.AUTOMATED.value  # Overlay Subtype

        # Test logical properties
        assert overlay.is_graphics_overlay
        assert not overlay.is_roi_overlay
    
    def test_roi_overlay_patterns(self):
        """Test overlay patterns for ROI visualization with dataset generation."""
        overlay_data = np.zeros((64, 64), dtype=np.uint8)

        # Create ROI boundary pattern
        overlay_data[15:50, 15:50] = 1  # ROI outline
        overlay_data[20:45, 20:45] = 0  # Hollow center

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=64,
            overlay_columns=64,
            overlay_type="R",  # ROI overlay
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_description="Target volume outline",
            overlay_label="PTV"
        )

        # Add ROI statistics using separate method
        overlay.with_roi_statistics(
            roi_area=1250,  # ROI area in pixels
            roi_mean=128,   # Mean pixel value in ROI
            roi_standard_deviation=25
        )

        # Test dataset generation
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

        # Test values
        assert overlay[(0x6000, 0x0040)].value == "R"  # Overlay Type
        assert overlay[(0x6000, 0x0022)].value == "Target volume outline"  # Overlay Description
        assert (0x6000, 0x1301) in overlay  # ROI Area
        assert overlay[(0x6000, 0x1301)].value == "1250"  # ROI Area (stored as string)

        # Test logical properties
        assert overlay.is_roi_overlay
        assert not overlay.is_graphics_overlay
        assert overlay.has_roi_statistics
    
    def test_overlay_subtype_validation(self):
        """Test overlay subtype validation for different purposes."""
        overlay_data = np.zeros((40, 40), dtype=np.uint8)
        
        subtypes = [
            "ISOLINE",     # Dose isolines
            "HISTOGRAM",   # Histogram overlay
            "TEXT",        # Text annotations
            "CURVE",       # Curve overlays
            "GRAPHICS"     # General graphics
        ]
        
        for subtype in subtypes:
            overlay = OverlayPlaneModule.from_required_elements(
                overlay_rows=40,
                overlay_columns=40,
                overlay_type="G",
                overlay_origin=[1, 1],
                overlay_data=overlay_data.tobytes()
            ).with_optional_elements(
                overlay_subtype=subtype
            )
            assert overlay[(0x6000, 0x0045)].value == subtype  # Overlay Subtype
    
    def test_multi_frame_overlay_support(self):
        """Test multi-frame overlay support for dose sequences."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        # Multi-frame overlay functionality would require additional implementation
        # For now, just verify the basic overlay was created successfully
        assert overlay[(0x6000, 0x0010)].value == 32  # Overlay Rows
        assert overlay[(0x6000, 0x0011)].value == 32  # Overlay Columns
    
    def test_overlay_magnification_and_smoothing(self):
        """Test overlay magnification and smoothing parameters."""
        overlay_data = np.zeros((50, 50), dtype=np.uint8)
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=50,
            overlay_columns=50,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        # Magnification and smoothing functionality would require additional implementation
        # For now, just verify the basic overlay was created successfully
        assert overlay[(0x6000, 0x0010)].value == 50  # Overlay Rows
        assert overlay[(0x6000, 0x0011)].value == 50  # Overlay Columns
    
    def test_overlay_data_packing(self):
        """Test overlay data bit packing validation."""
        # Test different data packing scenarios
        overlay_sizes = [(8, 8), (16, 16), (32, 32)]
        
        for rows, cols in overlay_sizes:
            overlay_data = np.random.randint(0, 2, (rows, cols), dtype=np.uint8)
            
            overlay = OverlayPlaneModule.from_required_elements(
                overlay_rows=rows,
                overlay_columns=cols,
                overlay_type="G",
                overlay_origin=[1, 1],
                overlay_data=overlay_data.tobytes()
            )
            
            # Verify data length matches expected
            expected_length = rows * cols
            assert len(overlay[(0x6000, 0x3000)].value) == expected_length  # Overlay Data
    
    def test_overlay_coordinate_alignment(self):
        """Test overlay coordinate alignment with dose grid."""
        # Overlay should align with dose grid coordinates
        overlay_data = np.zeros((64, 64), dtype=np.uint8)
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=64,
            overlay_columns=64,
            overlay_type="G",
            overlay_origin=[1, 1],  # DICOM pixel coordinates
            overlay_data=overlay_data.tobytes()
        )
        
        # Pixel spacing functionality would require additional implementation
        # For now, just verify the basic overlay was created successfully
        assert overlay[(0x6000, 0x0010)].value == 64  # Overlay Rows
        assert overlay[(0x6000, 0x0011)].value == 64  # Overlay Columns
    
    def test_empty_overlay_data(self):
        """Test handling of empty overlay data."""
        # Minimal overlay with no visible content
        overlay_data = np.zeros((10, 10), dtype=np.uint8)
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=10,
            overlay_columns=10,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        # Verify empty overlay is valid
        assert overlay[(0x6000, 0x0010)].value == 10  # Overlay Rows
        assert overlay[(0x6000, 0x0011)].value == 10  # Overlay Columns
        # Note: OverlayData is bytes, not individual bits
        assert len(overlay[(0x6000, 0x3000)].value) == 10 * 10  # Overlay Data
    
    def test_optional_module_behavior(self):
        """Test that OverlayPlaneModule behaves as optional enhancement."""
        # Overlay module should not be required for basic dose functionality
        overlay_data = np.ones((30, 30), dtype=np.uint8)
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=30,
            overlay_columns=30,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        # Module should function independently
        assert overlay[(0x6000, 0x0010)].value == 30  # Overlay Rows
        assert overlay[(0x6000, 0x0011)].value == 30  # Overlay Columns
    
    def test_validation_method_exists(self):
        """Test that validation method exists and works with internal dataset."""
        overlay_data = np.zeros((20, 20), dtype=np.uint8)

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=20,
            overlay_columns=20,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )

        assert hasattr(overlay, 'validate')
        assert callable(overlay.validate)

        # Test validation result structure
        validation_result = overlay.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

        # Test that validation passes for valid overlay
        assert len(validation_result.errors) == 0

        # Test dataset generation
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

    def test_dataset_generation_comprehensive(self):
        """Test comprehensive dataset generation with all elements."""
        overlay_data = np.zeros((64, 64), dtype=np.uint8)
        overlay_data[20:44, 20:44] = 1  # Create ROI pattern

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=64,
            overlay_columns=64,
            overlay_type=OverlayType.ROI,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_description="Test ROI for validation",
            overlay_label="TEST_ROI",
            overlay_subtype=OverlaySubtype.USER
        ).with_roi_statistics(
            roi_area=576,  # 24x24 ROI
            roi_mean=150.5,
            roi_standard_deviation=12.3
        ).with_multi_frame_overlay(
            number_of_frames_in_overlay=3,
            image_frame_origin=[1, 5, 10]
        )

        # Test dataset generation
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0

        # Test that all elements are present in dataset
        # Note: We can't directly test DICOM tags due to group-specific nature
        # but we can test that the dataset contains the expected number of elements
        assert len(dataset) >= 10  # Should have at least 10 DICOM elements

        # Test module properties still work
        assert overlay.is_roi_overlay
        assert overlay.has_roi_statistics
        assert overlay.is_multi_frame_overlay
        assert not overlay.applies_to_all_frames

        # Test validation passes
        validation_result = overlay.validate()
        assert len(validation_result.errors) == 0

    def test_large_overlay_handling(self):
        """Test handling of large overlay planes with dataset generation."""
        # Large overlay matching high-resolution dose grid
        large_size = 512
        overlay_data = np.zeros((large_size, large_size), dtype=np.uint8)

        # Create sparse overlay pattern for memory efficiency
        overlay_data[::50, ::50] = 1  # Sparse grid pattern

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=large_size,
            overlay_columns=large_size,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )

        # Test dataset generation with large data
        dataset = overlay.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

        # Verify large overlay handling
        assert overlay[(0x6000, 0x0010)].value == large_size  # Overlay Rows
        assert overlay[(0x6000, 0x0011)].value == large_size  # Overlay Columns
        # Note: OverlayData length in bytes, not pixels
        assert len(overlay[(0x6000, 0x3000)].value) == large_size * large_size  # Overlay Data

        # Test calculated properties
        assert overlay.overlay_pixel_count == large_size * large_size
        assert overlay.actual_data_size == large_size * large_size

        # Test validation passes even for large overlays
        validation_result = overlay.validate()
        assert isinstance(validation_result, ValidationResult)

    def test_overlay_group_creation_without_validation(self):
        """Test that overlay groups can be created without validation in the module."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Test that valid overlay groups work as before
        overlay_valid = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes(),
            overlay_group=0x6000  # Valid group
        )
        assert overlay_valid._overlay_group == 0x6000

        # Test that invalid overlay groups can be created (validation deferred to validator)
        overlay_invalid = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes(),
            overlay_group=0x5FFF  # Invalid group (below range)
        )
        assert overlay_invalid._overlay_group == 0x5FFF

        # Test that odd overlay groups can be created (validation deferred to validator)
        overlay_odd = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type="G",
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes(),
            overlay_group=0x6001  # Invalid group (odd number)
        )
        assert overlay_odd._overlay_group == 0x6001

        # Validation should catch these issues
        invalid_result = overlay_invalid.validate()
        assert invalid_result.has_errors
        assert any("0x5FFF" in error for error in invalid_result.errors)

        odd_result = overlay_odd.validate()
        assert odd_result.has_errors
        assert any("0x6001" in error for error in odd_result.errors)