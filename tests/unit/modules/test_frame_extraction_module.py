"""
Test FrameExtractionModule (U - Optional) functionality.

FrameExtractionModule implements DICOM PS3.3 C.12.3 Frame Extraction Module.
Optional module for describing frames extracted from multi-frame DICOM objects.
"""

from pydicom import Dataset
from pyrt_dicom.modules import FrameExtractionModule
from pyrt_dicom.validators import ValidationR<PERSON>ult
from pyrt_dicom.validators.validation_error import ValidationError
import pytest


class TestFrameExtractionModule:
    """Test FrameExtractionModule (U - Optional) functionality."""
    
    def test_from_required_elements_simple_frame_list(self):
        """Test successful creation with simple frame list."""
        frame_extraction_sequence = [
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.10',
                simple_frame_list=[1, 3, 5, 7, 9]
            )
        ]
        
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=frame_extraction_sequence
        )
        
        # Test internal dataset has the data
        assert 'FrameExtractionSequence' in extraction
        assert len(extraction.FrameExtractionSequence)
        
        # Test dataset generation
        dataset = extraction.to_dataset()
        assert hasattr(dataset, 'FrameExtractionSequence')
        assert len(dataset.FrameExtractionSequence) == 1
        assert dataset.FrameExtractionSequence[0].MultiFrameSourceSOPInstanceUID == '*******.*******.9.10'
        assert dataset.FrameExtractionSequence[0].SimpleFrameList == [1, 3, 5, 7, 9]
    
    def test_from_required_elements_calculated_frame_list(self):
        """Test creation with calculated frame list."""
        frame_extraction_sequence = [
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.11',
                calculated_frame_list=[1, 10, 2]  # start, end, increment
            )
        ]
        
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=frame_extraction_sequence
        )
        
        # Test dataset generation and access
        dataset = extraction.to_dataset()
        assert dataset.FrameExtractionSequence[0].CalculatedFrameList == [1, 10, 2]
    
    def test_from_required_elements_time_range(self):
        """Test creation with time range."""
        frame_extraction_sequence = [
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.12',
                time_range=[120000.0, 130000.0]
            )
        ]
        
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=frame_extraction_sequence
        )
        
        # Test dataset generation and access
        dataset = extraction.to_dataset()
        assert dataset.FrameExtractionSequence[0].TimeRange == [120000.0, 130000.0]
    
    def test_create_frame_extraction_item_simple_frame_list(self):
        """Test creating frame extraction item with simple frame list."""
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.13',
            simple_frame_list=[2, 4, 6, 8]
        )
        
        assert item.MultiFrameSourceSOPInstanceUID == '*******.*******.9.13'
        assert item.SimpleFrameList == [2, 4, 6, 8]
        assert not hasattr(item, 'CalculatedFrameList')
        assert not hasattr(item, 'TimeRange')
    
    def test_create_frame_extraction_item_calculated_frame_list(self):
        """Test creating frame extraction item with calculated frame list."""
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.14',
            calculated_frame_list=[5, 20, 3]  # start=5, end=20, increment=3
        )
        
        assert item.MultiFrameSourceSOPInstanceUID == '*******.*******.9.14'
        assert item.CalculatedFrameList == [5, 20, 3]
        assert not hasattr(item, 'SimpleFrameList')
        assert not hasattr(item, 'TimeRange')
    
    def test_create_frame_extraction_item_time_range(self):
        """Test creating frame extraction item with time range."""
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.15',
            time_range=[140000.0, 150000.0]
        )
        
        assert item.MultiFrameSourceSOPInstanceUID == '*******.*******.9.15'
        assert item.TimeRange == [140000.0, 150000.0]
        assert not hasattr(item, 'SimpleFrameList')
        assert not hasattr(item, 'CalculatedFrameList')
    
    def test_create_frame_extraction_item_without_validation_errors(self):
        """Test that create_frame_extraction_item does not raise exceptions."""
        # Test no frame specification provided - should not raise exception
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.16'
        )
        assert item.MultiFrameSourceSOPInstanceUID == '*******.*******.9.16'
        assert not hasattr(item, 'SimpleFrameList')
        assert not hasattr(item, 'CalculatedFrameList')
        assert not hasattr(item, 'TimeRange')
        
        # Test multiple frame specifications provided - should not raise exception
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.17',
            simple_frame_list=[1, 2, 3],
            calculated_frame_list=[1, 10, 2]
        )
        assert item.MultiFrameSourceSOPInstanceUID == '*******.*******.9.17'
        # Only first provided specification should be set (simple_frame_list)
        assert item.SimpleFrameList == [1, 2, 3]
        assert not hasattr(item, 'CalculatedFrameList')
        
        # Test invalid calculated frame list (not triplets) - should not raise exception
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.18',
            calculated_frame_list=[1, 10]  # Missing increment
        )
        assert item.MultiFrameSourceSOPInstanceUID == '*******.*******.9.18'
        assert item.CalculatedFrameList == [1, 10]
        
        # Test invalid time range (not exactly 2 values) - should not raise exception
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.19',
            time_range=[0]  # Missing end time
        )
        assert item.MultiFrameSourceSOPInstanceUID == '*******.*******.9.19'
        # pydicom converts single-element lists to scalar values
        assert item.TimeRange == 0 or item.TimeRange == [0]
    
    def test_multiple_frame_extraction_sequence(self):
        """Test extraction sequence with multiple items."""
        frame_extraction_sequence = [
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.20',
                simple_frame_list=[1, 5, 10]
            ),
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.21',
                calculated_frame_list=[2, 8, 2]
            ),
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.22',
                time_range=[100000, 110000]
            )
        ]
        
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=frame_extraction_sequence
        )
        
        # Test internal dataset structure
        assert 'FrameExtractionSequence' in extraction
        assert len(extraction.FrameExtractionSequence) == 3
        
        # Test dataset generation
        dataset = extraction.to_dataset()
        assert len(dataset.FrameExtractionSequence) == 3
        
        # Test properties
        assert extraction.extraction_count == 3
        assert extraction.has_simple_frame_lists
        assert extraction.has_calculated_frame_lists
        assert extraction.has_time_ranges
    
    def test_add_frame_extraction_item(self):
        """Test adding frame extraction items dynamically."""
        # Start with one item
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid='*******.*******.9.23',
                    simple_frame_list=[1, 2, 3]
                )
            ]
        )
        
        # Add another item
        extraction.add_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.24',
            calculated_frame_list=[5, 15, 5]
        )
        
        # Test internal dataset structure
        assert 'FrameExtractionSequence' in extraction
        assert len(extraction.FrameExtractionSequence) == 2
        assert extraction.extraction_count == 2
        
        # Test dataset generation
        dataset = extraction.to_dataset()
        assert len(dataset.FrameExtractionSequence) == 2
        assert dataset.FrameExtractionSequence[1].MultiFrameSourceSOPInstanceUID == '*******.*******.9.24'
        assert dataset.FrameExtractionSequence[1].CalculatedFrameList == [5, 15, 5]
    
    def test_with_optional_elements_no_parameters(self):
        """Test that with_optional_elements accepts no parameters."""
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid='*******.*******.9.25',
                    simple_frame_list=[1]
                )
            ]
        )
        
        # Should work with no parameters
        result = extraction.with_optional_elements()
        assert result is extraction  # Should return self
    
    def test_with_optional_elements_no_validation_error(self):
        """Test that with_optional_elements does not raise error for unsupported parameters."""
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid='*******.*******.9.26',
                    simple_frame_list=[1]
                )
            ]
        )
        
        # Should not raise error with unsupported parameters - validation is done by validator
        result = extraction.with_optional_elements()  # kwargs removed from this method
        assert result is extraction  # Should return self for method chaining
    
    def test_property_has_simple_frame_lists(self):
        """Test has_simple_frame_lists property."""
        # Test with simple frame list
        extraction_with_simple = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid='*******.*******.9.27',
                    simple_frame_list=[1, 2, 3]
                )
            ]
        )
        assert extraction_with_simple.has_simple_frame_lists
        
        # Test without simple frame list
        extraction_without_simple = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[
                FrameExtractionModule.create_frame_extraction_item(
                    multi_frame_source_sop_instance_uid='*******.*******.9.28',
                    calculated_frame_list=[1, 10, 2]
                )
            ]
        )
        assert not extraction_without_simple.has_simple_frame_lists
    
    def test_property_has_calculated_frame_lists(self):
        """Test has_calculated_frame_lists property."""
        # Test with calculated frame list
        item_with_calculated = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.29',
            calculated_frame_list=[1, 20, 3]
        )
        extraction_with_calculated = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item_with_calculated]
        )
        assert extraction_with_calculated.has_calculated_frame_lists
        
        # Test without calculated frame list
        item_without_calculated = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.30',
            simple_frame_list=[5, 10, 15]
        )
        extraction_without_calculated = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item_without_calculated]
        )
        assert not extraction_without_calculated.has_calculated_frame_lists
    
    def test_property_has_time_ranges(self):
        """Test has_time_ranges property."""
        # Test with time range
        item_with_time = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.31',
            time_range=[80000, 90000]
        )
        extraction_with_time = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item_with_time]
        )
        assert extraction_with_time.has_time_ranges
        
        # Test without time range
        item_without_time = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.32',
            simple_frame_list=[1, 2]
        )
        extraction_without_time = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item_without_time]
        )
        assert not extraction_without_time.has_time_ranges
    
    def test_property_is_configured(self):
        """Test is_configured property."""
        # Test configured module
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.33',
            simple_frame_list=[1]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item]
        )
        assert extraction.is_configured
        
        # Test empty module
        empty_extraction = FrameExtractionModule()
        assert not empty_extraction.is_configured
    
    def test_empty_frame_extraction_sequence_no_error(self):
        """Test that empty frame extraction sequence does not raise error."""
        # Should not raise exception - validation is handled by validator
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[]
        )
        
        # Module should be created but not configured
        assert extraction is not None
        assert not extraction.is_configured
        assert extraction.extraction_count == 0
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.34',
            simple_frame_list=[1, 2, 3]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item]
        )
        
        assert hasattr(extraction, 'validate')
        assert callable(extraction.validate)
        
        # Test validation result structure
        validation_result = extraction.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_complex_calculated_frame_list(self):
        """Test complex calculated frame list with multiple triplets."""
        # Multiple triplets: frames 1-10 step 2, and frames 20-30 step 1
        calculated_list = [1, 10, 2, 20, 30, 1]
        
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.35',
            calculated_frame_list=calculated_list
        )
        
        assert item.CalculatedFrameList == calculated_list
        
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item]
        )
        
        assert extraction.has_calculated_frame_lists
        assert extraction['FrameExtractionSequence'][0].CalculatedFrameList == calculated_list
    
    def test_mixed_frame_extraction_types(self):
        """Test module with mixed frame extraction types in sequence."""
        mixed_sequence = [
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.36',
                simple_frame_list=[1, 3, 5, 7]
            ),
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.37',
                calculated_frame_list=[10, 20, 2]
            ),
            FrameExtractionModule.create_frame_extraction_item(
                multi_frame_source_sop_instance_uid='*******.*******.9.38',
                time_range=[160000, 170000]
            )
        ]
        
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=mixed_sequence
        )
        
        # Verify all types are detected
        assert extraction.has_simple_frame_lists
        assert extraction.has_calculated_frame_lists
        assert extraction.has_time_ranges
        assert extraction.extraction_count == 3
        assert extraction.is_configured
    
    def test_large_simple_frame_list(self):
        """Test handling of large simple frame lists."""
        large_frame_list = list(range(1, 101))  # Frames 1-100
        
        item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.39',
            simple_frame_list=large_frame_list
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[item]
        )
        
        # Test dataset generation and access
        dataset = extraction.to_dataset()
        assert len(dataset.FrameExtractionSequence[0].SimpleFrameList) == 100
        assert dataset.FrameExtractionSequence[0].SimpleFrameList[0] == 1
        assert dataset.FrameExtractionSequence[0].SimpleFrameList[-1] == 100
    
    def test_composition_based_architecture(self):
        """Test new composition-based architecture with internal dataset management."""
        # Create module with frame extraction sequence
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.100',
            simple_frame_list=[1, 2, 3, 4, 5]
        )
        
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )
        
        # Verify module has internal dataset
        assert hasattr(extraction, '_dataset')
        assert isinstance(extraction._dataset, Dataset)
        
        # Verify data is stored in internal dataset, NOT on module directly
        assert 'FrameExtractionSequence' in extraction
        assert 'FrameExtractionSequence' not in dir(extraction)  # Should NOT be directly on module
        
        # Verify to_dataset() generates proper dataset
        dataset = extraction.to_dataset()
        assert isinstance(dataset, Dataset)
        assert hasattr(dataset, 'FrameExtractionSequence')
        assert len(dataset.FrameExtractionSequence) == 1
        
        # Verify dataset is independent copy
        dataset.FrameExtractionSequence.append(frame_item)  # Modify copy
        original_dataset = extraction.to_dataset()  # Get fresh copy
        assert len(original_dataset.FrameExtractionSequence) == 1  # Original unchanged
        
        # Verify module metadata
        assert extraction.module_name == 'FrameExtractionModule'
        assert extraction.has_data is True
        assert extraction.get_element_count() == 1  # One sequence element
        
        # Verify properties work with internal dataset
        assert extraction.is_configured is True
        assert extraction.extraction_count == 1
        assert extraction.has_simple_frame_lists is True
        assert extraction.has_calculated_frame_lists is False
        assert extraction.has_time_ranges is False

    # Phase 2 Validation Integration Tests

    def test_check_required_elements_zero_copy(self):
        """Test check_required_elements method with zero-copy optimization."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.100',
            simple_frame_list=[1, 2, 3]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        # Test that validation method works directly with the module instance (zero-copy)
        result = extraction.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass validation

        # Test with empty module (should have errors)
        empty_extraction = FrameExtractionModule()
        result = empty_extraction.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0  # Should fail validation

    def test_check_conditional_requirements_zero_copy(self):
        """Test check_conditional_requirements method with zero-copy optimization."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.101',
            calculated_frame_list=[1, 10, 2]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        result = extraction.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass validation

    def test_check_enum_constraints_zero_copy(self):
        """Test check_enum_constraints method with zero-copy optimization."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.102',
            time_range=[120000.0, 130000.0]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        result = extraction.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Frame Extraction Module has no enum constraints

    def test_check_sequence_requirements_zero_copy(self):
        """Test check_sequence_requirements method with zero-copy optimization."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.103',
            simple_frame_list=[5, 10, 15]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        result = extraction.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass validation

    def test_validate_zero_copy_optimization(self):
        """Test that validate method uses zero-copy optimization (passes self)."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.104',
            simple_frame_list=[1, 3, 5]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        # Test that validation works directly with the module instance
        result = extraction.validate()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass validation

    def test_private_ensure_required_elements_valid(self):
        """Test private _ensure_required_elements_valid method."""
        # Test with valid module (should not raise)
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.105',
            simple_frame_list=[1, 2]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        # Should not raise exception
        extraction._ensure_required_elements_valid()

        # Test with invalid module (should raise ValidationError)
        empty_extraction = FrameExtractionModule()
        with pytest.raises(ValidationError) as exc_info:
            empty_extraction._ensure_required_elements_valid()

        assert "Required elements validation failed" in str(exc_info.value)

    def test_private_ensure_conditional_requirements_valid(self):
        """Test private _ensure_conditional_requirements_valid method."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.106',
            calculated_frame_list=[2, 8, 2]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        # Should not raise exception for valid module
        extraction._ensure_conditional_requirements_valid()

    def test_private_ensure_enum_constraints_valid(self):
        """Test private _ensure_enum_constraints_valid method."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.107',
            time_range=[100000.0, 200000.0]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        # Should not raise exception (Frame Extraction Module has no enum constraints)
        extraction._ensure_enum_constraints_valid()

    def test_private_ensure_sequence_requirements_valid(self):
        """Test private _ensure_sequence_requirements_valid method."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.108',
            simple_frame_list=[10, 20, 30]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        # Should not raise exception for valid module
        extraction._ensure_sequence_requirements_valid()

    def test_validation_methods_return_validation_result(self):
        """Test that all new validation methods return ValidationResult objects."""
        frame_item = FrameExtractionModule.create_frame_extraction_item(
            multi_frame_source_sop_instance_uid='*******.*******.9.109',
            simple_frame_list=[1]
        )
        extraction = FrameExtractionModule.from_required_elements(
            frame_extraction_sequence=[frame_item]
        )

        # Test all public validation methods return ValidationResult
        methods_to_test = [
            extraction.check_required_elements,
            extraction.check_conditional_requirements,
            extraction.check_enum_constraints,
            extraction.check_sequence_requirements,
            extraction.validate
        ]

        for method in methods_to_test:
            result = method()
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)