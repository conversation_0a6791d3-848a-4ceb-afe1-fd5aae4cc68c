"""
Test ClinicalTrialSeriesModule functionality.

ClinicalTrialSeriesModule implements DICOM PS3.3 C.7.3.2 Clinical Trial Series Module.
- Clinical Trial Coordinating Center Name is Type 2 (required but may be empty)
- Other attributes are Type 3 (optional)
"""

import pytest
from pyrt_dicom.modules.clinical_trial_series_module import ClinicalTrialSeriesModule
from pyrt_dicom.validators import ValidationResult


class TestClinicalTrialSeriesModule:
    """Test ClinicalTrialSeriesModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required Type 2 element."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Medical Center"
        )
        
        # Module should be created successfully with required element
        assert trial_series is not None
        assert isinstance(trial_series, ClinicalTrialSeriesModule)
        
        dataset = trial_series.to_dataset()
        assert dataset.ClinicalTrialCoordinatingCenterName == "Research Medical Center"
    
    def test_with_optional_elements_coordinating_center(self):
        """Test that coordinating center is set during construction."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center A"
        )
        
        dataset = trial_series.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialCoordinatingCenterName')
        assert dataset.ClinicalTrialCoordinatingCenterName == "Research Center A"
    
    def test_with_optional_elements_series_id(self):
        """Test adding series identification information."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            clinical_trial_series_description="Baseline CT imaging"
        )
        
        dataset = trial_series.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert hasattr(dataset, 'ClinicalTrialSeriesDescription')
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
        assert dataset.ClinicalTrialSeriesDescription == "Baseline CT imaging"
    
    def test_with_optional_elements_issuer_info(self):
        """Test adding issuer information."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="HOSPITAL_A"
        )
        
        dataset = trial_series.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert hasattr(dataset, 'IssuerOfClinicalTrialSeriesID')
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
        assert dataset.IssuerOfClinicalTrialSeriesID == "HOSPITAL_A"
    
    def test_with_optional_elements_all_fields(self):
        """Test adding all optional elements."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="National Research Center"
        ).with_optional_elements(
            clinical_trial_series_id="TRIAL_ABC_SERIES_001",
            issuer_of_clinical_trial_series_id="NRC_IMAGING",
            clinical_trial_series_description="Pre-treatment baseline imaging series"
        )
        
        dataset = trial_series.to_dataset()
        assert dataset.ClinicalTrialCoordinatingCenterName == "National Research Center"
        assert dataset.ClinicalTrialSeriesID == "TRIAL_ABC_SERIES_001"
        assert dataset.IssuerOfClinicalTrialSeriesID == "NRC_IMAGING"
        assert dataset.ClinicalTrialSeriesDescription == "Pre-treatment baseline imaging series"
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id=None,
            clinical_trial_series_description=None
        )
        
        # Only non-None values should be set
        dataset = trial_series.to_dataset()
        # Coordinating center name should be empty string by default (Type 2)
        assert hasattr(dataset, 'ClinicalTrialCoordinatingCenterName')
        assert dataset.ClinicalTrialCoordinatingCenterName == ""
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert not hasattr(dataset, 'IssuerOfClinicalTrialSeriesID')
        assert not hasattr(dataset, 'ClinicalTrialSeriesDescription')
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
    
    def test_has_coordinating_center_info_property(self):
        """Test has_coordinating_center_info property."""
        # Test with empty coordinating center name (default)
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        assert not trial_series.has_coordinating_center_info
        
        # Test with actual coordinating center name
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        assert trial_series.has_coordinating_center_info
    
    def test_has_series_identification_property(self):
        """Test has_series_identification property."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Initially should be False
        assert not trial_series.has_series_identification
        
        # After adding series ID
        trial_series.with_optional_elements(clinical_trial_series_id="SERIES001")
        assert trial_series.has_series_identification
        
        # Reset and test with description only
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(clinical_trial_series_description="Test description")
        assert trial_series.has_series_identification
    
    def test_has_issuer_info_property(self):
        """Test has_issuer_info property."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Initially should be False
        assert not trial_series.has_issuer_info
        
        # After adding issuer information
        trial_series.with_optional_elements(
            issuer_of_clinical_trial_series_id="TEST_ISSUER"
        )
        assert trial_series.has_issuer_info
    
    def test_method_chaining(self):
        """Test that with_optional_elements returns self for method chaining."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001"
        )
        
        # Should return self
        assert result is trial_series
        
        dataset = trial_series.to_dataset()
        assert dataset.ClinicalTrialCoordinatingCenterName == "Test Center"
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        assert hasattr(trial_series, 'validate')
        assert callable(trial_series.validate)
        
        # Test that validation returns ValidationResult - actual validation logic tested in validator tests
        validation_result = trial_series.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
    
    # Tests for new public validation convenience methods
    def test_check_required_elements_method(self):
        """Test check_required_elements method returns ValidationResult."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Should pass with required elements present
    
    def test_check_conditional_requirements_method(self):
        """Test check_conditional_requirements method returns ValidationResult."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No conditional requirements in this module
    
    def test_check_enum_constraints_method(self):
        """Test check_enum_constraints method returns ValidationResult."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No enum constraints in this module
    
    def test_check_sequence_requirements_method(self):
        """Test check_sequence_requirements method returns ValidationResult."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No sequence requirements in this module
    
    def test_check_vr_constraints_method(self):
        """Test check_vr_constraints method returns ValidationResult."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.check_vr_constraints()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Should pass with valid strings
    
    def test_check_semantic_relationships_method(self):
        """Test check_semantic_relationships method returns ValidationResult."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        result = trial_series.check_semantic_relationships()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Should pass without series ID
        assert not result.has_warnings
    
    def test_check_semantic_relationships_with_series_id_warning(self):
        """Test semantic validation generates warning when series ID present without issuer."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001"
            # No issuer provided
        )
        
        result = trial_series.check_semantic_relationships()
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Warnings don't affect validity
        assert result.has_warnings
        assert "Issuer of Clinical Trial Series ID" in result.warnings[0]
    
    def test_check_vr_constraints_with_long_values(self):
        """Test VR constraint validation with values exceeding 64 characters."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="A" * 65  # Too long
        )
        
        result = trial_series.check_vr_constraints()
        assert isinstance(result, ValidationResult)
        assert not result.is_valid  # Should fail due to length
        assert result.has_errors
        assert "exceeds maximum length" in result.errors[0]
    
    # Tests for private validation methods that raise ValidationError
    def test_ensure_required_elements_valid_success(self):
        """Test _ensure_required_elements_valid passes with valid elements."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        # Should not raise exception
        trial_series._ensure_required_elements_valid()
    
    def test_ensure_required_elements_valid_raises_error(self):
        """Test _ensure_required_elements_valid raises ValidationError with missing elements."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        trial_series = ClinicalTrialSeriesModule()  # Missing required element
        
        with pytest.raises(ValidationError) as exc_info:
            trial_series._ensure_required_elements_valid()
        
        assert "Required elements validation failed" in str(exc_info.value)
        assert "Clinical Trial Coordinating Center Name" in str(exc_info.value)
    
    def test_ensure_conditional_requirements_valid_success(self):
        """Test _ensure_conditional_requirements_valid passes (no conditionals in this module)."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        # Should not raise exception
        trial_series._ensure_conditional_requirements_valid()
    
    def test_ensure_vr_constraints_valid_success(self):
        """Test _ensure_vr_constraints_valid passes with valid VR constraints."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        # Should not raise exception
        trial_series._ensure_vr_constraints_valid()
    
    def test_ensure_vr_constraints_valid_raises_error(self):
        """Test _ensure_vr_constraints_valid raises ValidationError with VR violations."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="A" * 65  # Too long
        )
        
        with pytest.raises(ValidationError) as exc_info:
            trial_series._ensure_vr_constraints_valid()
        
        assert "VR constraint validation failed" in str(exc_info.value)
        assert "exceeds maximum length" in str(exc_info.value)
    
    def test_ensure_semantic_relationships_valid_success(self):
        """Test _ensure_semantic_relationships_valid passes without warnings."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        # Should not raise exception (no warnings)
        trial_series._ensure_semantic_relationships_valid()
    
    def test_ensure_semantic_relationships_valid_with_warnings(self):
        """Test _ensure_semantic_relationships_valid raises ValidationError for warnings."""
        from pyrt_dicom.validators.validation_error import ValidationError
        
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001"
            # No issuer - will generate warning
        )
        
        with pytest.raises(ValidationError) as exc_info:
            trial_series._ensure_semantic_relationships_valid()
        
        assert "Semantic relationship validation failed" in str(exc_info.value)
        assert "Issuer of Clinical Trial Series ID" in str(exc_info.value)
    
    # Test zero-copy validation
    def test_validation_uses_zero_copy(self):
        """Test that validation methods pass self directly (zero-copy optimization)."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        # All these methods should work without creating dataset copies
        result1 = trial_series.check_required_elements()
        result2 = trial_series.check_vr_constraints()
        result3 = trial_series.validate()
        
        assert isinstance(result1, ValidationResult)
        assert isinstance(result2, ValidationResult)
        assert isinstance(result3, ValidationResult)
        
        # Should all be valid
        assert result1.is_valid
        assert result2.is_valid
        assert result3.is_valid
    
    def test_to_dataset_generation(self):
        """Test that to_dataset() generates correct DICOM datasets."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Test Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="TEST_ISSUER",
            clinical_trial_series_description="Test series description"
        )
        
        dataset = trial_series.to_dataset()
        
        # Verify dataset type and contents
        from pydicom import Dataset
        assert isinstance(dataset, Dataset)
        assert len(dataset) > 0
        
        # Verify all elements are present
        assert hasattr(dataset, 'ClinicalTrialCoordinatingCenterName')
        assert hasattr(dataset, 'ClinicalTrialSeriesID')
        assert hasattr(dataset, 'IssuerOfClinicalTrialSeriesID')
        assert hasattr(dataset, 'ClinicalTrialSeriesDescription')
        
        # Verify values are correct
        assert dataset.ClinicalTrialCoordinatingCenterName == "Test Center"
        assert dataset.ClinicalTrialSeriesID == "SERIES001"
        assert dataset.IssuerOfClinicalTrialSeriesID == "TEST_ISSUER"
        assert dataset.ClinicalTrialSeriesDescription == "Test series description"