"""
Test SynchronizationModule functionality.

SynchronizationModule implements DICOM PS3.3 C.7.4.2 Synchronization Module.
Contains attributes necessary to uniquely identify a Frame of Reference that 
establishes the temporal relationship of SOP Instances.

Tests the composition-based architecture with internal dataset management.
"""
import pydicom
from pyrt_dicom.modules import SynchronizationModule
from pyrt_dicom.enums.synchronization_enums import (
    SynchronizationTrigger,
    AcquisitionTimeSynchronized,
    TimeDistributionProtocol
)
from pyrt_dicom.validators import ValidationResult


class TestSynchronizationModule:
    """Test SynchronizationModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Test using to_dataset() method for attribute access
        dataset = sync.to_dataset()
        assert dataset.SynchronizationFrameOfReferenceUID == "*******.*******.9.10"
        assert dataset.SynchronizationTrigger == "SOURCE"
        assert dataset.AcquisitionTimeSynchronized == "Y"
        
        # Test module properties
        assert sync.is_source_synchronized
        assert sync.is_time_synchronized
        assert sync.is_configured
    
    def test_required_elements_with_string_values(self):
        """Test creation with string enum values."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger="EXTERNAL",
            acquisition_time_synchronized="N"
        )
        
        # Test using to_dataset() method for attribute access
        dataset = sync.to_dataset()
        assert dataset.SynchronizationFrameOfReferenceUID == "*******.*******.9.10"
        assert dataset.SynchronizationTrigger == "EXTERNAL"
        assert dataset.AcquisitionTimeSynchronized == "N"
        
        # Test module properties
        assert sync.is_externally_synchronized
        assert not sync.is_time_synchronized
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        ).with_optional_elements(
            trigger_source_or_type="LINAC_001",
            time_source="NTP_SERVER_001",
            time_distribution_protocol=TimeDistributionProtocol.NTP,
            ntp_source_address="*************"
        )
        
        # Test using to_dataset() method for attribute access
        dataset = sync.to_dataset()
        assert hasattr(dataset, 'TriggerSourceOrType')
        assert hasattr(dataset, 'TimeSource')
        assert hasattr(dataset, 'TimeDistributionProtocol')
        assert hasattr(dataset, 'NTPSourceAddress')
        assert dataset.TriggerSourceOrType == "LINAC_001"
        assert dataset.TimeSource == "NTP_SERVER_001"
        assert dataset.TimeDistributionProtocol == "NTP"
        assert dataset.NTPSourceAddress == "*************"
        
        # Test module properties
        assert sync.uses_ntp_protocol
        assert not sync.uses_precision_time_protocol
    
    def test_with_synchronization_channel(self):
        """Test adding synchronization channel."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        ).with_synchronization_channel(
            synchronization_channel=[1, 2]
        )
        
        # Test using to_dataset() method for attribute access
        dataset = sync.to_dataset()
        assert hasattr(dataset, 'SynchronizationChannel')
        assert dataset.SynchronizationChannel == [1, 2]
        
        # Test module property
        assert sync.has_synchronization_channel
    
    def test_synchronization_channel_validation(self):
        """Test synchronization channel validation."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Test invalid channel specification (wrong number of values)
        try:
            sync.with_synchronization_channel([1])  # Should be [M,C] pair
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "exactly 2 values" in str(e)
            assert "(0018,106C)" in str(e)
            assert "Waveform Sequence" in str(e)
        
        # Test valid channel specification
        sync.with_synchronization_channel([3, 5])
        dataset = sync.to_dataset()
        assert dataset.SynchronizationChannel == [3, 5]
    
    def test_synchronization_trigger_properties(self):
        """Test synchronization trigger property methods."""
        # Test SOURCE trigger
        sync_source = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert sync_source.is_source_synchronized
        assert not sync_source.is_externally_synchronized
        assert not sync_source.is_passthrough_synchronized
        assert not sync_source.has_no_trigger
        
        # Test EXTERNAL trigger
        sync_external = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert not sync_external.is_source_synchronized
        assert sync_external.is_externally_synchronized
        assert not sync_external.is_passthrough_synchronized
        assert not sync_external.has_no_trigger
        
        # Test PASSTHRU trigger
        sync_passthru = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.PASSTHRU,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert not sync_passthru.is_source_synchronized
        assert not sync_passthru.is_externally_synchronized
        assert sync_passthru.is_passthrough_synchronized
        assert not sync_passthru.has_no_trigger
        
        # Test NO_TRIGGER
        sync_no_trigger = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.NO_TRIGGER,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert not sync_no_trigger.is_source_synchronized
        assert not sync_no_trigger.is_externally_synchronized
        assert not sync_no_trigger.is_passthrough_synchronized
        assert sync_no_trigger.has_no_trigger
    
    def test_time_synchronization_properties(self):
        """Test time synchronization property methods."""
        # Test time synchronized
        sync_time_yes = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        assert sync_time_yes.is_time_synchronized
        
        # Test time not synchronized
        sync_time_no = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.NO
        )
        assert not sync_time_no.is_time_synchronized
    
    def test_protocol_properties(self):
        """Test time distribution protocol properties."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Test NTP protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.NTP)
        assert sync.uses_ntp_protocol
        assert not sync.uses_precision_time_protocol
        assert not sync.uses_gps_protocol
        assert not sync.uses_irig_protocol
        
        # Test PTP protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.PTP)
        assert not sync.uses_ntp_protocol
        assert sync.uses_precision_time_protocol
        assert not sync.uses_gps_protocol
        
        # Test SNTP protocol (should be considered NTP)
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.SNTP)
        assert sync.uses_ntp_protocol
        assert not sync.uses_precision_time_protocol
        
        # Test GPS protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.GPS)
        assert sync.uses_gps_protocol
        assert not sync.uses_ntp_protocol
        
        # Test IRIG protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.IRIG)
        assert sync.uses_irig_protocol
        assert not sync.uses_ntp_protocol
    
    def test_synchronization_channel_property(self):
        """Test synchronization channel property."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.EXTERNAL,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Initially no synchronization channel
        assert not sync.has_synchronization_channel
        assert not sync.requires_synchronization_channel  # Cannot be determined without waveform context
        
        # Add synchronization channel
        sync.with_synchronization_channel([3, 4])
        assert sync.has_synchronization_channel
        
        # Verify dataset content
        dataset = sync.to_dataset()
        assert dataset.SynchronizationChannel == [3, 4]
    
    def test_all_synchronization_trigger_enum_values(self):
        """Test all valid synchronization trigger enum values."""
        trigger_values = [
            SynchronizationTrigger.SOURCE,
            SynchronizationTrigger.EXTERNAL,
            SynchronizationTrigger.PASSTHRU,
            SynchronizationTrigger.NO_TRIGGER
        ]
        
        for trigger in trigger_values:
            sync = SynchronizationModule.from_required_elements(
                synchronization_frame_of_reference_uid="*******.*******.9.10",
                synchronization_trigger=trigger,
                acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
            )
            # Test using to_dataset() method for attribute access
            dataset = sync.to_dataset()
            assert dataset.SynchronizationTrigger == trigger.value
    
    def test_all_time_distribution_protocol_enum_values(self):
        """Test all valid time distribution protocol enum values."""
        protocol_values = [
            TimeDistributionProtocol.NTP,
            TimeDistributionProtocol.IRIG,
            TimeDistributionProtocol.GPS,
            TimeDistributionProtocol.SNTP,
            TimeDistributionProtocol.PTP
        ]
        
        for protocol in protocol_values:
            sync = SynchronizationModule.from_required_elements(
                synchronization_frame_of_reference_uid="*******.*******.9.10",
                synchronization_trigger=SynchronizationTrigger.SOURCE,
                acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
            ).with_optional_elements(time_distribution_protocol=protocol)
            
            # Test using to_dataset() method for attribute access
            dataset = sync.to_dataset()
            assert dataset.TimeDistributionProtocol == protocol.value
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        assert hasattr(sync, 'validate')
        assert callable(sync.validate)
        
        # Test validation result structure
        validation_result = sync.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_to_dataset_method(self):
        """Test to_dataset() method functionality."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        ).with_optional_elements(
            trigger_source_or_type="LINAC_001",
            time_distribution_protocol=TimeDistributionProtocol.NTP
        )
        
        # Test dataset generation
        dataset = sync.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        
        # Test that all attributes are in the dataset
        assert hasattr(dataset, 'SynchronizationFrameOfReferenceUID')
        assert hasattr(dataset, 'SynchronizationTrigger')
        assert hasattr(dataset, 'AcquisitionTimeSynchronized')
        assert hasattr(dataset, 'TriggerSourceOrType')
        assert hasattr(dataset, 'TimeDistributionProtocol')
        
        # Test values
        assert dataset.SynchronizationFrameOfReferenceUID == "*******.*******.9.10"
        assert dataset.SynchronizationTrigger == "SOURCE"
        assert dataset.AcquisitionTimeSynchronized == "Y"
        assert dataset.TriggerSourceOrType == "LINAC_001"
        assert dataset.TimeDistributionProtocol == "NTP"
        
        # Test dataset is a copy (deep copy)
        dataset2 = sync.to_dataset()
        assert dataset is not dataset2
        assert dataset.SynchronizationFrameOfReferenceUID == dataset2.SynchronizationFrameOfReferenceUID
    
    def test_module_composition_properties(self):
        """Test BaseModule composition properties."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Test BaseModule properties
        assert sync.module_name == "SynchronizationModule"
        assert sync.has_data
        assert sync.get_element_count() == 3  # 3 required elements
        assert "SynchronizationModule" in str(sync)
        assert "3 attributes" in str(sync)
        
        # Test is_configured property
        assert sync.is_configured
        
        # Test with empty module
        empty_sync = SynchronizationModule()
        assert not empty_sync.has_data
        assert empty_sync.get_element_count() == 0
        assert not empty_sync.is_configured
    
    def test_new_protocol_properties(self):
        """Test new protocol properties added in refactoring."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # Test GPS protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.GPS)
        assert sync.uses_gps_protocol
        assert not sync.uses_irig_protocol
        assert not sync.uses_ntp_protocol
        assert not sync.uses_precision_time_protocol
        
        # Test IRIG protocol
        sync.with_optional_elements(time_distribution_protocol=TimeDistributionProtocol.IRIG)
        assert sync.uses_irig_protocol
        assert not sync.uses_gps_protocol
        assert not sync.uses_ntp_protocol
        assert not sync.uses_precision_time_protocol
    
    def test_conditional_logic_property(self):
        """Test requires_synchronization_channel property logic."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        )
        
        # This property cannot determine if synchronization channel is required
        # without waveform context, so it always returns False
        assert not sync.requires_synchronization_channel
        
        # Even with synchronization channel present
        sync.with_synchronization_channel([1, 2])
        assert not sync.requires_synchronization_channel
    
    def test_optional_elements_with_none_values(self):
        """Test optional elements method with None values."""
        sync = SynchronizationModule.from_required_elements(
            synchronization_frame_of_reference_uid="*******.*******.9.10",
            synchronization_trigger=SynchronizationTrigger.SOURCE,
            acquisition_time_synchronized=AcquisitionTimeSynchronized.YES
        ).with_optional_elements(
            trigger_source_or_type=None,  # Should be ignored
            time_source="NTP_SERVER_001",
            time_distribution_protocol=None,  # Should be ignored  
            ntp_source_address=None  # Should be ignored
        )
        
        # Test that only non-None values were set
        dataset = sync.to_dataset()
        assert not hasattr(dataset, 'TriggerSourceOrType')
        assert hasattr(dataset, 'TimeSource')
        assert not hasattr(dataset, 'TimeDistributionProtocol')
        assert not hasattr(dataset, 'NTPSourceAddress')
        assert dataset.TimeSource == "NTP_SERVER_001"
    