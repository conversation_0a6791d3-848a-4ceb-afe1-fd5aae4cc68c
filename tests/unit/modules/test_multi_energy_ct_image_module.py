"""
Test MultiEnergyCTImageModule functionality.

MultiEnergyCTImageModule implements DICOM PS3.3 C.8.2.2 Multi-energy CT Image Module.
Contains attributes that describe a Multi-energy CT image with complex nested sequences.
"""

from datetime import datetime
from pydicom import Dataset
from pyrt_dicom.modules import MultiEnergyCTImageModule
from pyrt_dicom.enums.image_enums import MultiEnergySourceTechnique, MultiEnergyDetectorType
from pyrt_dicom.validators import ValidationResult


class TestMultiEnergyCTImageModule:
    """Test MultiEnergyCTImageModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create a complete acquisition sequence
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000",
                        switching_phase_number=1
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                        nominal_max_energy=120.0,
                        nominal_min_energy=20.0
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        # Test that module is properly created
        assert module.has_data
        assert module.acquisition_sequence_count == 1
        
        # Test dataset generation
        dataset = module.to_dataset()
        assert (0x0018, 0x9362) in dataset  # MultiEnergyCTAcquisitionSequence
        assert len(dataset[(0x0018, 0x9362)].value) == 1
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        # Create acquisition item with optional acquisition description
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ],
                multi_energy_acquisition_description="Dual-energy CT acquisition"
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        ).with_optional_elements(
            multi_energy_acquisition_description="This parameter is for API consistency"
        )
        
        # Test that acquisition description is present in the sequence items
        assert module.has_acquisition_description
        
        # Verify through dataset generation
        dataset = module.to_dataset()
        acq_item = dataset[(0x0018, 0x9362)].value[0]  # MultiEnergyCTAcquisitionSequence
        assert (0x0018, 0x937B) in acq_item  # MultiEnergyAcquisitionDescription
        assert acq_item[(0x0018, 0x937B)].value == "Dual-energy CT acquisition"
    
    def test_dataset_generation(self):
        """Test that to_dataset() generates proper pydicom Dataset."""
        # Create a complete module
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000",
                        switching_phase_number=1
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                        nominal_max_energy=120.0,
                        nominal_min_energy=20.0
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        # Test dataset generation
        dataset = module.to_dataset()
        assert isinstance(dataset, Dataset)
        assert len(dataset) > 0
        
        # Test that DICOM attributes are properly accessible
        assert (0x0018, 0x9362) in dataset  # MultiEnergyCTAcquisitionSequence
        assert len(dataset[(0x0018, 0x9362)].value) == 1
        
        # Test nested sequence structure
        acq_item = dataset[(0x0018, 0x9362)].value[0]
        assert (0x0018, 0x9365) in acq_item  # MultiEnergyCTXRaySourceSequence
        assert (0x0018, 0x936F) in acq_item  # MultiEnergyCTXRayDetectorSequence
        assert (0x0018, 0x9379) in acq_item  # MultiEnergyCTPathSequence
        
        # Test that generated dataset contains all expected data
        source_item = acq_item[(0x0018, 0x9365)].value[0]  # MultiEnergyCTXRaySourceSequence
        assert source_item[(0x0018, 0x9366)].value == 1  # XRaySourceIndex
        assert source_item[(0x0018, 0x9368)].value == "SWITCHING_SOURCE"  # MultiEnergySourceTechnique
        assert (0x0018, 0x936B) in source_item  # SwitchingPhaseNumber
        
        detector_item = acq_item[(0x0018, 0x936F)].value[0]  # MultiEnergyCTXRayDetectorSequence
        assert detector_item[(0x0018, 0x9372)].value == "PHOTON_COUNTING"  # MultiEnergyDetectorType
        assert (0x0018, 0x9374) in detector_item  # NominalMaxEnergy
        assert (0x0018, 0x9375) in detector_item  # NominalMinEnergy
    
    def test_create_x_ray_source_item_switching_source(self):
        """Test X-Ray source item creation with switching source technique."""
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
            source_start_datetime="20240101120000.000000",
            source_end_datetime="20240101120030.000000",
            switching_phase_number=1,
            switching_phase_nominal_duration=0.5,
            switching_phase_transition_duration=0.1,
            generator_power=100.0
        )
        
        assert isinstance(source_item, Dataset)
        assert source_item[(0x0018, 0x9366)].value == 1  # XRaySourceIndex
        assert source_item[(0x0018, 0x9367)].value == "SOURCE_001"  # XRaySourceID
        assert source_item[(0x0018, 0x9368)].value == "SWITCHING_SOURCE"  # MultiEnergySourceTechnique
        assert source_item[(0x0018, 0x9369)].value == "20240101120000.000000"  # SourceStartDateTime
        assert source_item[(0x0018, 0x936A)].value == "20240101120030.000000"  # SourceEndDateTime
        assert source_item[(0x0018, 0x936B)].value == 1  # SwitchingPhaseNumber
        assert source_item[(0x0018, 0x936C)].value == 0.5  # SwitchingPhaseNominalDuration
        assert source_item[(0x0018, 0x936D)].value == 0.1  # SwitchingPhaseTransitionDuration
        assert source_item[(0x0018, 0x1170)].value == 100  # GeneratorPower
    
    def test_create_x_ray_source_item_constant_source(self):
        """Test X-Ray source item creation with constant source technique."""
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
            source_start_datetime="20240101120000.000000",
            source_end_datetime="20240101120030.000000"
        )
        
        assert isinstance(source_item, Dataset)
        assert source_item[(0x0018, 0x9366)].value == 1  # XRaySourceIndex
        assert source_item[(0x0018, 0x9368)].value == "CONSTANT_SOURCE"  # MultiEnergySourceTechnique
        # Switching phase number should not be present for constant source
        assert (0x0018, 0x936B) not in source_item  # SwitchingPhaseNumber
    
    def test_create_x_ray_detector_item_photon_counting(self):
        """Test X-Ray detector item creation with photon counting type."""
        detector_item = MultiEnergyCTImageModule.create_x_ray_detector_item(
            x_ray_detector_index=1,
            x_ray_detector_id="DETECTOR_001",
            multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
            nominal_max_energy=120.0,
            nominal_min_energy=20.0,
            x_ray_detector_label="Main Detector",
            effective_bin_energy=70.0
        )
        
        assert isinstance(detector_item, Dataset)
        assert detector_item[(0x0018, 0x9370)].value == 1  # XRayDetectorIndex
        assert detector_item[(0x0018, 0x9371)].value == "DETECTOR_001"  # XRayDetectorID
        assert detector_item[(0x0018, 0x9372)].value == "PHOTON_COUNTING"  # MultiEnergyDetectorType
        assert detector_item[(0x0018, 0x9374)].value == 120.0  # NominalMaxEnergy
        assert detector_item[(0x0018, 0x9375)].value == 20.0  # NominalMinEnergy
        assert detector_item[(0x0018, 0x9373)].value == "Main Detector"  # XRayDetectorLabel
        assert detector_item[(0x0018, 0x936E)].value == 70.0  # EffectiveBinEnergy
    
    def test_create_x_ray_detector_item_integrating(self):
        """Test X-Ray detector item creation with integrating type."""
        detector_item = MultiEnergyCTImageModule.create_x_ray_detector_item(
            x_ray_detector_index=1,
            x_ray_detector_id="DETECTOR_001",
            multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
        )
        
        assert isinstance(detector_item, Dataset)
        assert detector_item[(0x0018, 0x9370)].value == 1  # XRayDetectorIndex
        assert detector_item[(0x0018, 0x9372)].value == "INTEGRATING"  # MultiEnergyDetectorType
        # Energy values may or may not be present for integrating detectors
    
    def test_create_path_item(self):
        """Test path item creation."""
        path_item = MultiEnergyCTImageModule.create_path_item(
            path_index=1,
            referenced_x_ray_source_index=1,
            referenced_x_ray_detector_index=1
        )
        
        assert isinstance(path_item, Dataset)
        assert path_item[(0x0018, 0x937A)].value == 1  # MultiEnergyCTPathIndex
        assert path_item[(0x0018, 0x9377)].value == 1  # ReferencedXRaySourceIndex
        assert path_item[(0x0018, 0x9376)].value == 1  # ReferencedXRayDetectorIndex
    
    def test_datetime_formatting(self):
        """Test datetime object handling in source creation."""
        start_datetime = datetime(2024, 1, 1, 12, 0, 0)
        end_datetime = datetime(2024, 1, 1, 12, 0, 30)
        
        source_item = MultiEnergyCTImageModule.create_x_ray_source_item(
            x_ray_source_index=1,
            x_ray_source_id="SOURCE_001",
            multi_energy_source_technique="CONSTANT_SOURCE",
            source_start_datetime=start_datetime,
            source_end_datetime=end_datetime
        )
        
        # Should format datetime objects as DICOM DT strings
        assert source_item[(0x0018, 0x9369)].value.startswith("20240101120000")  # SourceStartDateTime
        assert source_item[(0x0018, 0x936A)].value.startswith("20240101120030")  # SourceEndDateTime
    
    def test_property_methods(self):
        """Test property and helper methods."""
        # Create module with switching sources and photon counting detectors
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.SWITCHING_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000",
                        switching_phase_number=1
                    ),
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=2,
                        x_ray_source_id="SOURCE_002",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.PHOTON_COUNTING,
                        nominal_max_energy=120.0,
                        nominal_min_energy=20.0
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    ),
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=2,
                        referenced_x_ray_source_index=2,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        # Test property methods
        assert module.acquisition_sequence_count == 1
        assert module.has_switching_sources is True
        assert module.has_photon_counting_detectors is True
        assert module.get_source_count() == 2
        assert module.get_detector_count() == 1
        assert module.get_path_count() == 2
        assert module.has_acquisition_description is False
    
    def test_validate_method_exists(self):
        """Test validation method exists and returns proper structure."""
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        ct_image_module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        assert hasattr(ct_image_module, 'validate')
        assert callable(ct_image_module.validate)
        
        # Test validation result structure
        validation_result = ct_image_module.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_empty_properties(self):
        """Test property methods with minimal module."""
        # Create module without complex features
        acquisition_sequence = [
            MultiEnergyCTImageModule.create_multi_energy_acquisition_item(
                x_ray_source_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_source_item(
                        x_ray_source_index=1,
                        x_ray_source_id="SOURCE_001",
                        multi_energy_source_technique=MultiEnergySourceTechnique.CONSTANT_SOURCE,
                        source_start_datetime="20240101120000.000000",
                        source_end_datetime="20240101120030.000000"
                    )
                ],
                x_ray_detector_sequence=[
                    MultiEnergyCTImageModule.create_x_ray_detector_item(
                        x_ray_detector_index=1,
                        x_ray_detector_id="DETECTOR_001",
                        multi_energy_detector_type=MultiEnergyDetectorType.INTEGRATING
                    )
                ],
                path_sequence=[
                    MultiEnergyCTImageModule.create_path_item(
                        path_index=1,
                        referenced_x_ray_source_index=1,
                        referenced_x_ray_detector_index=1
                    )
                ]
            )
        ]
        
        module = MultiEnergyCTImageModule.from_required_elements(
            multi_energy_ct_acquisition_sequence=acquisition_sequence
        )
        
        # Test properties with no switching sources or photon counting detectors
        assert module.has_switching_sources is False
        assert module.has_photon_counting_detectors is False
