"""
Test RTFractionSchemeModule functionality.

RTFractionSchemeModule implements DICOM PS3.3 C.8.8.13 RT Fraction Scheme Module.
Contains information describing the fractionation of the treatment plan.

Tests the new composition-based architecture with internal dataset management.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTFractionSchemeModule
from pyrt_dicom.enums.rt_enums import BeamDoseMeaning
from pyrt_dicom.enums.dose_enums import DoseType
from pyrt_dicom.validators import ValidationResult


class TestRTFractionSchemeModule:
    """Test RTFractionSchemeModule functionality with composition-based architecture."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements using composition architecture."""
        # Create basic fraction group using Dataset directly
        fraction_group = Dataset()
        fraction_group.FractionGroupNumber = 1
        fraction_group.NumberOfFractionsPlanned = 25
        fraction_group.NumberOfBeams = 0
        fraction_group.NumberOfBrachyApplicationSetups = 0

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[fraction_group]
        )

        # Test internal dataset management
        assert fraction_scheme.has_data
        assert fraction_scheme.fraction_group_count == 1

        # Test dataset generation
        dataset = fraction_scheme.to_dataset()
        assert hasattr(dataset, 'FractionGroupSequence')
        assert len(dataset.FractionGroupSequence) == 1
        assert dataset.FractionGroupSequence[0].FractionGroupNumber == 1
        assert dataset.FractionGroupSequence[0].NumberOfFractionsPlanned == 25

        # Verify internal dataset is not directly accessible
        assert 'FractionGroupSequence' not in dir(fraction_scheme)

    def test_from_required_elements_empty_sequence_fails(self):
        """Test that empty fraction group sequence raises ValueError."""
        with pytest.raises(ValueError, match="Fraction Group Sequence is required and cannot be empty"):
            RTFractionSchemeModule.from_required_elements(fraction_group_sequence=[])

        with pytest.raises(ValueError, match="Fraction Group Sequence is required and cannot be empty"):
            RTFractionSchemeModule.from_required_elements(fraction_group_sequence=None) # type: ignore
    
    def test_create_fraction_group_item_basic(self):
        """Test creation of basic fraction group item."""
        fraction_group = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=30,
            number_of_beams=0,  # Set to 0 to avoid conditional validation
            number_of_brachy_application_setups=0
        )

        # Verify all required Type 1 and Type 2 elements are present
        assert fraction_group.FractionGroupNumber == 1
        assert fraction_group.NumberOfFractionsPlanned == 30
        assert fraction_group.NumberOfBeams == 0
        assert fraction_group.NumberOfBrachyApplicationSetups == 0

        # Verify optional elements are not present when not provided
        assert not hasattr(fraction_group, 'FractionGroupDescription')
        assert not hasattr(fraction_group, 'BeamDoseMeaning')
        assert not hasattr(fraction_group, 'ReferencedBeamSequence')
        assert not hasattr(fraction_group, 'ReferencedBrachyApplicationSetupSequence')

    def test_create_fraction_group_item_mutual_exclusivity_violation(self):
        """Test that mutual exclusivity of beams and brachy setups is enforced."""
        with pytest.raises(ValueError, match="Number of Beams and Number of Brachy Application Setups cannot both be greater than zero"):
            RTFractionSchemeModule.create_fraction_group_item(
                fraction_group_number=1,
                number_of_fractions_planned=25,
                number_of_beams=1,  # Both > 0 should fail
                number_of_brachy_application_setups=1
            )
    
    def test_create_fraction_group_item_with_beams(self):
        """Test creation of fraction group item with referenced beams."""
        referenced_beam_sequence = [
            RTFractionSchemeModule.create_referenced_beam_item(
                referenced_beam_number=1,
                beam_dose=180.0,
                beam_meterset=100.0
            )
        ]

        fraction_group = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=25,
            number_of_beams=1,
            number_of_brachy_application_setups=0,
            referenced_beam_sequence=referenced_beam_sequence
        )

        # Verify Type 1C conditional requirement is satisfied
        assert hasattr(fraction_group, 'ReferencedBeamSequence')
        assert len(fraction_group.ReferencedBeamSequence) == 1
        assert fraction_group.ReferencedBeamSequence[0].ReferencedBeamNumber == 1
        assert fraction_group.ReferencedBeamSequence[0].BeamDose == 180.0
        assert fraction_group.ReferencedBeamSequence[0].BeamMeterset == 100.0

        # Verify brachy sequence is not present
        assert not hasattr(fraction_group, 'ReferencedBrachyApplicationSetupSequence')

    def test_create_fraction_group_item_beams_without_sequence_fails(self):
        """Test that beams > 0 without Referenced Beam Sequence fails."""
        with pytest.raises(ValueError, match="Referenced Beam Sequence.*is required when Number of Beams > 0"):
            RTFractionSchemeModule.create_fraction_group_item(
                fraction_group_number=1,
                number_of_fractions_planned=25,
                number_of_beams=1,  # > 0 but no sequence provided
                number_of_brachy_application_setups=0
            )

    def test_create_fraction_group_item_brachy_without_sequence_fails(self):
        """Test that brachy setups > 0 without Referenced Brachy Application Setup Sequence fails."""
        with pytest.raises(ValueError, match="Referenced Brachy Application Setup Sequence.*is required when"):
            RTFractionSchemeModule.create_fraction_group_item(
                fraction_group_number=1,
                number_of_fractions_planned=25,
                number_of_beams=0,
                number_of_brachy_application_setups=1  # > 0 but no sequence provided
            )
    
    def test_create_referenced_beam_item_basic(self):
        """Test creation of basic referenced beam item."""
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(
            referenced_beam_number=5
        )

        # Verify Type 1 element is present
        assert beam_item.ReferencedBeamNumber == 5

        # Verify Type 3 optional elements are not present when not provided
        assert not hasattr(beam_item, 'BeamDose')
        assert not hasattr(beam_item, 'BeamMeterset')
        assert not hasattr(beam_item, 'BeamDoseType')
        assert not hasattr(beam_item, 'AlternateBeamDose')
        assert not hasattr(beam_item, 'AlternateBeamDoseType')

    def test_create_referenced_beam_item_with_optional_elements(self):
        """Test creation of referenced beam item with optional elements."""
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(
            referenced_beam_number=3,
            referenced_dose_reference_uid="*******.5",
            beam_dose=250.5,
            beam_meterset=120.0,
            beam_delivery_duration_limit=300.0,
            dose_calibration_conditions_verified_flag="NO"
        )

        # Verify all provided elements are present
        assert beam_item.ReferencedBeamNumber == 3
        assert beam_item.ReferencedDoseReferenceUID == "*******.5"
        assert beam_item.BeamDose == 250.5
        assert beam_item.BeamMeterset == 120.0
        assert beam_item.BeamDeliveryDurationLimit == 300.0
        assert beam_item.DoseCalibrationConditionsVerifiedFlag == "NO"

        # Verify Type 1C elements are not present when not required
        assert not hasattr(beam_item, 'BeamDoseType')
        assert not hasattr(beam_item, 'AlternateBeamDose')
        assert not hasattr(beam_item, 'AlternateBeamDoseType')

    def test_create_referenced_beam_item_alternate_dose_validation(self):
        """Test Type 1C validation for alternate beam dose."""
        # Test that alternate dose requires both dose types
        with pytest.raises(ValueError, match="Beam Dose Type.*is required when Alternate Beam Dose is present"):
            RTFractionSchemeModule.create_referenced_beam_item(
                referenced_beam_number=1,
                alternate_beam_dose=100.0
                # Missing beam_dose_type and alternate_beam_dose_type
            )

        with pytest.raises(ValueError, match="Alternate Beam Dose Type.*is required when Alternate Beam Dose is present"):
            RTFractionSchemeModule.create_referenced_beam_item(
                referenced_beam_number=1,
                alternate_beam_dose=100.0,
                beam_dose_type=DoseType.PHYSICAL
                # Missing alternate_beam_dose_type
            )

        # Test that dose types cannot be the same
        with pytest.raises(ValueError, match="Beam Dose Type and Alternate Beam Dose Type shall not have the same value"):
            RTFractionSchemeModule.create_referenced_beam_item(
                referenced_beam_number=1,
                alternate_beam_dose=100.0,
                beam_dose_type=DoseType.PHYSICAL,
                alternate_beam_dose_type=DoseType.PHYSICAL  # Same as beam_dose_type
            )

        # Test successful creation with different dose types
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(
            referenced_beam_number=1,
            alternate_beam_dose=100.0,
            beam_dose_type=DoseType.PHYSICAL,
            alternate_beam_dose_type=DoseType.EFFECTIVE
        )

        assert beam_item.AlternateBeamDose == 100.0
        assert beam_item.BeamDoseType == DoseType.PHYSICAL.value
        assert beam_item.AlternateBeamDoseType == DoseType.EFFECTIVE.value
    
    def test_create_referenced_brachy_application_setup_item(self):
        """Test creation of referenced brachy application setup item."""
        brachy_item = RTFractionSchemeModule.create_referenced_brachy_application_setup_item(
            referenced_brachy_application_setup_number=1,
            brachy_application_setup_dose=100.0,
            brachy_application_setup_dose_specification_point=[5.0, 10.0, 15.0],
            referenced_dose_reference_uid="*******.5"
        )

        # Verify Type 1 element
        assert brachy_item.ReferencedBrachyApplicationSetupNumber == 1

        # Verify Type 3 optional elements
        assert brachy_item.BrachyApplicationSetupDose == 100.0
        assert brachy_item.BrachyApplicationSetupDoseSpecificationPoint == [5.0, 10.0, 15.0]
        assert brachy_item.ReferencedDoseReferenceUID == "*******.5"

    def test_create_referenced_dose_reference_item_with_constraints(self):
        """Test creation of referenced dose reference item with dose constraints."""
        dose_ref_item = RTFractionSchemeModule.create_referenced_dose_reference_item(
            referenced_dose_reference_number=3,
            constraint_weight=0.8,
            delivery_warning_dose=50.0,
            delivery_maximum_dose=60.0,
            target_prescription_dose=45.0,
            target_minimum_dose=40.0,
            target_maximum_dose=50.0
        )

        # Verify Type 1 element
        assert dose_ref_item.ReferencedDoseReferenceNumber == 3

        # Verify Type 3 constraint elements
        assert dose_ref_item.ConstraintWeight == 0.8
        assert dose_ref_item.DeliveryWarningDose == 50.0
        assert dose_ref_item.DeliveryMaximumDose == 60.0
        assert dose_ref_item.TargetPrescriptionDose == 45.0
        assert dose_ref_item.TargetMinimumDose == 40.0
        assert dose_ref_item.TargetMaximumDose == 50.0

    def test_create_dose_calibration_conditions_item(self):
        """Test creation of dose calibration conditions item."""
        calibration_item = RTFractionSchemeModule.create_dose_calibration_conditions_item(
            absorbed_dose_to_meterset_ratio=1.0,
            delineated_radiation_field_size=[100.0, 100.0],
            calibration_reference_point_depth=50.0,
            source_to_surface_distance=1000.0,
            calibration_datetime="20240101120000"
        )

        # Verify Type 1 elements
        assert calibration_item.AbsorbedDoseToMetersetRatio == 1.0
        assert calibration_item.DelineatedRadiationFieldSize == [100.0, 100.0]
        assert calibration_item.CalibrationReferencePointDepth == 50.0
        assert calibration_item.SourceToSurfaceDistance == 1000.0

        # Verify Type 2 element
        assert calibration_item.CalibrationDateTime == "20240101120000"
    
    def test_fraction_pattern_validation(self):
        """Test fraction pattern validation logic."""
        # Test invalid pattern length
        with pytest.raises(ValueError, match="Fraction Pattern length.*must equal"):
            RTFractionSchemeModule.create_fraction_group_item(
                fraction_group_number=1,
                number_of_fractions_planned=25,
                number_of_beams=0,
                number_of_brachy_application_setups=0,
                number_of_fraction_pattern_digits_per_day=1,
                repeat_fraction_cycle_length=1,
                fraction_pattern="111110"  # Should be 7 characters for 7×1×1
            )

        # Test invalid pattern characters
        with pytest.raises(ValueError, match="Fraction Pattern must contain only '0' and '1' characters"):
            RTFractionSchemeModule.create_fraction_group_item(
                fraction_group_number=1,
                number_of_fractions_planned=25,
                number_of_beams=0,
                number_of_brachy_application_setups=0,
                number_of_fraction_pattern_digits_per_day=1,
                repeat_fraction_cycle_length=1,
                fraction_pattern="111X100"  # Invalid character 'X'
            )

        # Test valid pattern
        fraction_group = RTFractionSchemeModule.create_fraction_group_item(
            fraction_group_number=1,
            number_of_fractions_planned=25,
            number_of_beams=0,
            number_of_brachy_application_setups=0,
            number_of_fraction_pattern_digits_per_day=1,
            repeat_fraction_cycle_length=1,
            fraction_pattern="1111100"  # Monday-Friday treatment
        )

        assert fraction_group.FractionPattern == "1111100"
        assert fraction_group.NumberOfFractionPatternDigitsPerDay == 1
        assert fraction_group.RepeatFractionCycleLength == 1
    
    def test_with_optional_elements(self):
        """Test with_optional_elements method."""
        fraction_group = Dataset()
        fraction_group.FractionGroupNumber = 1
        fraction_group.NumberOfFractionsPlanned = 25
        fraction_group.NumberOfBeams = 0
        fraction_group.NumberOfBrachyApplicationSetups = 0

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[fraction_group]
        ).with_optional_elements()

        # Should return self for method chaining
        assert isinstance(fraction_scheme, RTFractionSchemeModule)

    def test_with_fraction_pattern_method(self):
        """Test with_fraction_pattern method for adding fractionation patterns."""
        fraction_group = Dataset()
        fraction_group.FractionGroupNumber = 1
        fraction_group.NumberOfFractionsPlanned = 25
        fraction_group.NumberOfBeams = 0
        fraction_group.NumberOfBrachyApplicationSetups = 0

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[fraction_group]
        ).with_fraction_pattern(
            fraction_group_number=1,
            number_of_fraction_pattern_digits_per_day=1,
            repeat_fraction_cycle_length=1,
            fraction_pattern="1111100"  # Monday-Friday treatment
        )

        # Verify pattern was added to internal dataset
        dataset = fraction_scheme.to_dataset()
        group = dataset.FractionGroupSequence[0]
        assert group.FractionPattern == "1111100"
        assert group.NumberOfFractionPatternDigitsPerDay == 1
        assert group.RepeatFractionCycleLength == 1

        # Test method chaining
        assert isinstance(fraction_scheme, RTFractionSchemeModule)

    def test_with_beam_dose_meaning_method(self):
        """Test with_beam_dose_meaning method."""
        fraction_group = Dataset()
        fraction_group.FractionGroupNumber = 1
        fraction_group.NumberOfFractionsPlanned = 25
        fraction_group.NumberOfBeams = 0
        fraction_group.NumberOfBrachyApplicationSetups = 0

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[fraction_group]
        ).with_beam_dose_meaning(
            fraction_group_number=1,
            beam_dose_meaning=BeamDoseMeaning.BEAM_LEVEL
        )

        # Verify beam dose meaning was added to internal dataset
        dataset = fraction_scheme.to_dataset()
        group = dataset.FractionGroupSequence[0]
        assert group.BeamDoseMeaning == BeamDoseMeaning.BEAM_LEVEL.value

        # Test method chaining
        assert isinstance(fraction_scheme, RTFractionSchemeModule)
    
    def test_properties_with_internal_dataset_access(self):
        """Test all properties access internal dataset correctly."""
        # Create beam-based fraction group
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(referenced_beam_number=1)
        beam_group = Dataset()
        beam_group.FractionGroupNumber = 1
        beam_group.NumberOfFractionsPlanned = 20
        beam_group.NumberOfBeams = 1
        beam_group.NumberOfBrachyApplicationSetups = 0
        beam_group.ReferencedBeamSequence = [beam_item]

        # Create brachy-based fraction group
        brachy_item = RTFractionSchemeModule.create_referenced_brachy_application_setup_item(
            referenced_brachy_application_setup_number=1
        )
        brachy_group = Dataset()
        brachy_group.FractionGroupNumber = 2
        brachy_group.NumberOfFractionsPlanned = 10
        brachy_group.NumberOfBeams = 0
        brachy_group.NumberOfBrachyApplicationSetups = 1
        brachy_group.ReferencedBrachyApplicationSetupSequence = [brachy_item]
        brachy_group.FractionPattern = "1111100"

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[beam_group, brachy_group]
        )

        # Test all properties
        assert fraction_scheme.has_fraction_groups is True
        assert fraction_scheme.fraction_group_count == 2
        assert fraction_scheme.has_beam_based_groups is True
        assert fraction_scheme.has_brachy_based_groups is True
        assert fraction_scheme.has_fraction_patterns is True
        assert fraction_scheme.get_total_fractions_planned() == 30
        assert fraction_scheme.get_fraction_group_numbers() == [1, 2]

        # Test get_fraction_group_by_number
        found_group = fraction_scheme.get_fraction_group_by_number(1)
        assert found_group is not None
        assert found_group.FractionGroupNumber == 1

        not_found_group = fraction_scheme.get_fraction_group_by_number(99)
        assert not_found_group is None

        # Test empty module properties
        empty_module = RTFractionSchemeModule()
        assert empty_module.has_fraction_groups is False
        assert empty_module.fraction_group_count == 0
        assert empty_module.has_beam_based_groups is False
        assert empty_module.has_brachy_based_groups is False
        assert empty_module.has_fraction_patterns is False
        assert empty_module.get_total_fractions_planned() == 0
        assert empty_module.get_fraction_group_numbers() == []
    
    def test_to_dataset_method(self):
        """Test to_dataset method returns proper pydicom Dataset."""
        # Create fraction group with various elements
        beam_item = RTFractionSchemeModule.create_referenced_beam_item(
            referenced_beam_number=1,
            beam_dose=200.0
        )

        fraction_group = Dataset()
        fraction_group.FractionGroupNumber = 1
        fraction_group.NumberOfFractionsPlanned = 25
        fraction_group.NumberOfBeams = 1
        fraction_group.NumberOfBrachyApplicationSetups = 0
        fraction_group.ReferencedBeamSequence = [beam_item]

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[fraction_group]
        )

        # Test dataset generation
        dataset = fraction_scheme.to_dataset()

        # Verify it's a proper Dataset
        assert isinstance(dataset, Dataset)

        # Verify all data is present
        assert hasattr(dataset, 'FractionGroupSequence')
        assert len(dataset.FractionGroupSequence) == 1

        group = dataset.FractionGroupSequence[0]
        assert group.FractionGroupNumber == 1
        assert group.NumberOfFractionsPlanned == 25
        assert group.NumberOfBeams == 1
        assert group.NumberOfBrachyApplicationSetups == 0
        assert len(group.ReferencedBeamSequence) == 1
        assert group.ReferencedBeamSequence[0].ReferencedBeamNumber == 1
        assert group.ReferencedBeamSequence[0].BeamDose == 200.0

        # Verify dataset is a copy (not reference to internal dataset)
        dataset.FractionGroupSequence[0].FractionGroupNumber = 999
        original_dataset = fraction_scheme.to_dataset()
        assert original_dataset.FractionGroupSequence[0].FractionGroupNumber == 1  # Unchanged

    def test_validation_with_internal_dataset(self):
        """Test that validation method uses internal dataset correctly."""
        # Create valid fraction group
        group = Dataset()
        group.FractionGroupNumber = 1
        group.NumberOfFractionsPlanned = 25
        group.NumberOfBeams = 0
        group.NumberOfBrachyApplicationSetups = 0

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[group]
        )

        # Test validation method exists and is callable
        assert hasattr(fraction_scheme, 'validate')
        assert callable(fraction_scheme.validate)

        # Test validation result structure
        validation_result = fraction_scheme.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)

        # Test ValidationResult functionality
        assert hasattr(validation_result, 'is_valid')
        assert hasattr(validation_result, 'has_errors')
        assert hasattr(validation_result, 'has_warnings')
        assert hasattr(validation_result, 'error_count')
        assert hasattr(validation_result, 'warning_count')
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')

    def test_module_base_properties(self):
        """Test BaseModule properties work correctly."""
        # Test empty module
        empty_module = RTFractionSchemeModule()
        assert empty_module.module_name == "RTFractionSchemeModule"
        assert empty_module.has_data is False
        assert empty_module.get_element_count() == 0
        assert "RTFractionSchemeModule(0 attributes)" in str(empty_module)

        # Test module with data
        group = Dataset()
        group.FractionGroupNumber = 1
        group.NumberOfFractionsPlanned = 25
        group.NumberOfBeams = 0
        group.NumberOfBrachyApplicationSetups = 0

        fraction_scheme = RTFractionSchemeModule.from_required_elements(
            fraction_group_sequence=[group]
        )

        assert fraction_scheme.module_name == "RTFractionSchemeModule"
        assert fraction_scheme.has_data is True
        assert fraction_scheme.get_element_count() == 1  # FractionGroupSequence
        assert "RTFractionSchemeModule(1 attribute)" in str(fraction_scheme)