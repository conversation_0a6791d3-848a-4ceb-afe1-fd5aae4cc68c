"""
Test MultiFrameModule (C - Conditional) functionality.

MultiFrameModule implements DICOM PS3.3 C.7.6.6 Multi-frame Module.
Required when dose data spans multiple frames/slices in a single instance.
"""

import pydicom
from pydicom.tag import Tag
from pyrt_dicom.modules import MultiFrameModule
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.image_enums import StereoPairsPresent


class TestMultiFrameModule:
    """Test MultiFrameModule (C - Conditional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        # Verify creation succeeded
        assert multi_frame.has_data
        
        # Verify data through dataset generation
        dataset = multi_frame.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert dataset.NumberOfFrames == 10
    
    def test_number_of_frames_validation(self):
        """Test number of frames validation for dose volumes."""
        frame_counts = [1, 5, 10, 20, 50, 100, 200]
        
        for frame_count in frame_counts:
            multi_frame = MultiFrameModule.from_required_elements(
                number_of_frames=frame_count,
                frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
            )
            dataset = multi_frame.to_dataset()
            assert dataset.NumberOfFrames == frame_count
    
    def test_single_frame_handling(self):
        """Test single frame case (edge case for multi-frame)."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=1,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        # Even single frame can use multi-frame module
        dataset = multi_frame.to_dataset()
        assert dataset.NumberOfFrames == 1
        
        # Test single frame property
        assert multi_frame.is_single_frame
        assert not multi_frame.is_multi_frame
    
    def test_with_optional_elements(self):
        """Test adding optional multi-frame elements."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=20,
            frame_increment_pointer=["3004,000C"]  # Grid Frame Offset Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = multi_frame.to_dataset()
        assert hasattr(dataset, 'FrameIncrementPointer')
        assert hasattr(dataset, 'StereoPairsPresent')
        assert dataset.StereoPairsPresent == "NO"
    
    def test_frame_increment_pointer_dose_specific(self):
        """Test frame increment pointer for dose-specific tags."""
        # Common dose-related frame increment pointers
        dose_frame_pointers = [
            "(3004,000C)",  # Grid Frame Offset Vector
            "(0020,0032)",  # Image Position Patient
            "(0020,1041)",  # Slice Location
            "(0018,0050)"   # Slice Thickness
        ]
        
        for pointer in dose_frame_pointers:
            # Convert pointer format from "(GGGG,EEEE)" to "GGGG,EEEE"
            clean_pointer = pointer.strip("()")
            multi_frame = MultiFrameModule.from_required_elements(
                number_of_frames=10,
                frame_increment_pointer=[clean_pointer]
            )
            # Convert clean_pointer to Tag for comparison
            expected_tag = Tag(*[int(x, 16) for x in clean_pointer.split(',')])
            dataset = multi_frame.to_dataset()
            fip = dataset.FrameIncrementPointer
            # Handle both single tag and list of tags
            if isinstance(fip, (list, tuple)):
                assert expected_tag in fip
            else:
                assert fip == expected_tag
    
    def test_frame_dimension_pointer_validation(self):
        """Test frame dimension pointer for spatial organization."""
        dimension_pointers = [
            "(0020,0032)",  # Image Position Patient
            "(0020,1041)",  # Slice Location
            "(0054,0080)",  # Slice Vector
            "(0020,0037)"   # Image Orientation Patient
        ]
        
        for pointer in dimension_pointers:
            # Convert pointer format from "(GGGG,EEEE)" to "GGGG,EEEE"
            clean_pointer = pointer.strip("()")
            multi_frame = MultiFrameModule.from_required_elements(
                number_of_frames=15,
                frame_increment_pointer=[clean_pointer]
            )
            # Convert clean_pointer to Tag for comparison
            expected_tag = Tag(*[int(x, 16) for x in clean_pointer.split(',')])
            dataset = multi_frame.to_dataset()
            fip = dataset.FrameIncrementPointer
            # Handle both single tag and list of tags
            if isinstance(fip, (list, tuple)):
                assert expected_tag in fip
            else:
                assert fip == expected_tag
    
    def test_rt_dose_multi_frame_scenarios(self):
        """Test RT dose specific multi-frame scenarios."""
        # Scenario 1: Multi-slice dose volume
        dose_volume = MultiFrameModule.from_required_elements(
            number_of_frames=30,  # 30 axial slices
            frame_increment_pointer=["3004,000C"]  # Grid Frame Offset Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = dose_volume.to_dataset()
        assert dataset.NumberOfFrames == 30
        assert dataset.StereoPairsPresent == "NO"
        
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x3004, 0x000C)
        fip = dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_large_frame_count_handling(self):
        """Test handling of large frame counts."""
        # Large dose volume (e.g., 4D dose or high-resolution)
        large_frame_count = 500
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=large_frame_count,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        dataset = multi_frame.to_dataset()
        assert dataset.NumberOfFrames == large_frame_count
    
    def test_frame_time_vector(self):
        """Test frame time vector for temporal dose sequences."""
        # Time-based dose sequences (4D dose)
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()  # Frame Time Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = multi_frame.to_dataset()
        assert hasattr(dataset, 'FrameIncrementPointer')
        assert dataset.StereoPairsPresent == "NO"
        
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x0018, 0x1065)
        fip = dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_frame_reference_time(self):
        """Test frame reference time for dose calculations."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=5,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()  # Frame Time Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = multi_frame.to_dataset()
        assert hasattr(dataset, 'FrameIncrementPointer')
        assert dataset.StereoPairsPresent == "NO"
        
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x0018, 0x1065)
        fip = dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_nominal_interval_validation(self):
        """Test nominal interval for regular frame spacing."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=20,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = multi_frame.to_dataset()
        assert hasattr(dataset, 'FrameIncrementPointer')
        assert dataset.StereoPairsPresent == "NO"
    
    def test_beat_rejection_flag(self):
        """Test beat rejection flag for cardiac-gated dose."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=8,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = multi_frame.to_dataset()
        assert hasattr(dataset, 'FrameIncrementPointer')
        assert dataset.StereoPairsPresent == "NO"
    
    def test_frame_acquisition_sequence(self):
        """Test frame acquisition sequence for dose metadata."""
        # Test creating a multi-frame with acquisition sequence
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=2,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = multi_frame.to_dataset()
        assert hasattr(dataset, 'FrameIncrementPointer')
        assert dataset.StereoPairsPresent == "NO"
        # Check if FrameIncrementPointer has expected number of elements
        dataset = multi_frame.to_dataset()
        fip = dataset.FrameIncrementPointer
        if isinstance(fip, (list, tuple)):
            assert len(fip) == 1
        else:
            # Single tag case - count as 1 element
            assert 1 == 1  # We have a single tag
    
    def test_dataset_generation(self):
        """Test dataset generation functionality."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=5,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ).with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.NO,
            encapsulated_pixel_data_value_total_length=1024000
        )
        
        # Test dataset generation
        dataset = multi_frame.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        
        # Verify all attributes are present in dataset
        assert dataset.NumberOfFrames == 5
        assert dataset.StereoPairsPresent == "NO"
        assert dataset.EncapsulatedPixelDataValueTotalLength == 1024000
        assert hasattr(dataset, 'FrameIncrementPointer')
    
    def test_stereo_pairs_functionality(self):
        """Test stereoscopic pairs functionality."""
        # Test stereo pairs enabled
        stereo_multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=20,  # 10 stereo pairs
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_functional_groups()
        ).with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.YES
        )
        
        # Test properties
        assert stereo_multi_frame.has_stereo_pairs
        assert stereo_multi_frame.expected_stereo_frame_count == 10
        assert stereo_multi_frame.is_multi_frame
        assert not stereo_multi_frame.is_single_frame
        
        # Verify dataset
        dataset = stereo_multi_frame.to_dataset()
        assert dataset.StereoPairsPresent == "YES"
        assert dataset.NumberOfFrames == 20
    
    def test_frame_increment_properties(self):
        """Test frame increment pointer properties."""
        # Test Frame Time usage
        frame_time_module = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        )
        
        assert frame_time_module.uses_frame_time
        assert not frame_time_module.uses_frame_time_vector
        assert not frame_time_module.uses_functional_groups
        
        # Test Frame Time Vector usage
        frame_time_vector_module = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()
        )
        
        assert not frame_time_vector_module.uses_frame_time
        assert frame_time_vector_module.uses_frame_time_vector
        assert not frame_time_vector_module.uses_functional_groups
        
        # Test Functional Groups usage
        functional_groups_module = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_functional_groups()
        )
        
        assert not functional_groups_module.uses_frame_time
        assert not functional_groups_module.uses_frame_time_vector
        assert functional_groups_module.uses_functional_groups
    
    def test_frame_increment_attribute_names(self):
        """Test frame increment attribute name retrieval."""
        # Test with different frame increment strategies
        test_cases = [
            (MultiFrameModule.create_frame_increment_pointer_for_frame_time(), ["FrameTime"]),
            (MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector(), ["FrameTimeVector"]),
            (MultiFrameModule.create_frame_increment_pointer_for_functional_groups(), ["PerFrameFunctionalGroupsSequence"])
        ]
        
        for pointer, expected_names in test_cases:
            multi_frame = MultiFrameModule.from_required_elements(
                number_of_frames=5,
                frame_increment_pointer=pointer
            )
            
            attribute_names = multi_frame.get_frame_increment_attributes()
            assert attribute_names == expected_names
    
    def test_dependency_on_general_image_module(self):
        """Test that MultiFrameModule depends on GeneralImageModule."""
        # This dependency should be validated at IOD level
        # Here we test that MultiFrameModule can be created independently
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        # Module should be valid on its own
        dataset = multi_frame.to_dataset()
        assert dataset.NumberOfFrames == 10
    
    def test_frame_vector_consistency(self):
        """Test consistency between frame count and vector lengths."""
        frame_count = 5
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=frame_count,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time_vector()
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        # Frame increment pointer should be set correctly
        dataset = multi_frame.to_dataset()
        expected_tag = Tag(0x0018, 0x1065)
        fip = dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_zero_frames_edge_case(self):
        """Test edge case of zero frames."""
        # Zero frames is technically invalid, but test module creation
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=0,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        dataset = multi_frame.to_dataset()
        assert dataset.NumberOfFrames == 0
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()  # Frame Time
        )
        
        assert hasattr(multi_frame, 'validate')
        assert callable(multi_frame.validate)
        
        # Test validation result structure
        validation_result = multi_frame.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_multi_frame_dose_grid_organization(self):
        """Test multi-frame organization for dose grid volumes."""
        # 3D dose volume organized as multi-frame
        slice_count = 64  # 64 axial slices
        
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=slice_count,
            frame_increment_pointer=["3004,000C"]  # Grid Frame Offset Vector
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        # Verify dose volume organization through dataset
        dataset = multi_frame.to_dataset()
        assert dataset.NumberOfFrames == slice_count
        assert dataset.StereoPairsPresent == "NO"
        
        # Convert string tag to Tag object for comparison
        expected_tag = Tag(0x3004, 0x000C)
        fip = dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            assert expected_tag in fip
        else:
            assert fip == expected_tag
    
    def test_frame_content_sequence(self):
        """Test frame content sequence for dose frame metadata."""
        # Test creating a multi-frame with frame content
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=2,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ).with_optional_elements(
            stereo_pairs_present="NO"
        )
        
        dataset = multi_frame.to_dataset()
        assert hasattr(dataset, 'FrameIncrementPointer')
        assert dataset.StereoPairsPresent == "NO"
        # Check if FrameIncrementPointer has expected number of elements
        dataset = multi_frame.to_dataset()
        fip = dataset.FrameIncrementPointer
        if isinstance(fip, (list, tuple)):
            assert len(fip) == 1
        else:
            # Single tag case - count as 1 element
            assert 1 == 1  # We have a single tag
