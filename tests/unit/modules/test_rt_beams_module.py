"""
Test RTBeamsModule functionality.

RTBeamsModule implements DICOM PS3.3 C.8.8.14 RT Beams Module.
Contains information defining equipment parameters for external radiation beams.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTBeamsModule
from pyrt_dicom.enums import BeamType, RadiationType
from pyrt_dicom.enums.rt_enums import RTBeamLimitingDeviceType, PrimaryDosimeterUnit, EnhancedRTBeamLimitingDeviceDefinitionFlag
from pyrt_dicom.validators import ValidationResult


class TestRTBeamsModule:
    """Test RTBeamsModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create a simple beam sequence with required elements
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            treatment_machine_name="TrueBeam",
            radiation_type=RadiationType.PHOTON,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beams = RTBeamsModule.from_required_elements(
            beam_sequence=[beam_item]
        )
        
        assert beams.has_beams
        assert beams.beam_count == 1
        assert len(beams.BeamSequence) == 1
        assert beams.BeamSequence[0].BeamNumber == 1
        assert beams.BeamSequence[0].BeamType == "STATIC"
        assert beams.BeamSequence[0].TreatmentMachineName == "TrueBeam"
        assert beams.BeamSequence[0].RadiationType == "PHOTON"
    
    def test_create_beam_item_with_enum_values(self):
        """Test beam item creation with enum values."""
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=2,
            beam_type=BeamType.DYNAMIC,
            treatment_machine_name="Versa HD",
            radiation_type=RadiationType.ELECTRON,
            primary_dosimeter_unit=PrimaryDosimeterUnit.MU,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.Y,
                    number_of_leaf_jaw_pairs=2
                )
            ]
        )
        
        assert beam_item.BeamNumber == 2
        assert beam_item.BeamType == "DYNAMIC"
        assert beam_item.TreatmentMachineName == "Versa HD"
        assert beam_item.RadiationType == "ELECTRON"
        assert beam_item.PrimaryDosimeterUnit == "MU"
    
    def test_create_beam_item_with_string_values(self):
        """Test beam item creation with string values."""
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=3,
            beam_type="STATIC",
            treatment_machine_name="Clinac",
            radiation_type="PHOTON",
            primary_dosimeter_unit="MU",
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type="X",
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        assert beam_item.BeamNumber == 3
        assert beam_item.BeamType == "STATIC"
        assert beam_item.TreatmentMachineName == "Clinac"
        assert beam_item.RadiationType == "PHOTON"
        assert beam_item.PrimaryDosimeterUnit == "MU"
    
    def test_create_beam_limiting_device_item(self):
        """Test beam limiting device item creation."""
        device_item = RTBeamsModule.create_beam_limiting_device_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.ASYMX,
            number_of_leaf_jaw_pairs=40,
            source_to_beam_limiting_device_distance=1000.0
        )
        
        assert device_item.RTBeamLimitingDeviceType == 'ASYMX'
        assert device_item.NumberOfLeafJawPairs == 40
        assert device_item.SourceToBeamLimitingDeviceDistance == 1000.0
    
    def test_create_beam_limiting_device_item_mlc_creation(self):
        """Test beam limiting device item creation for MLC types."""
        # Should now create item without validation (validation moved to validator)
        device_item = RTBeamsModule.create_beam_limiting_device_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.MLCX,
            number_of_leaf_jaw_pairs=60
        )
        
        assert device_item.RTBeamLimitingDeviceType == "MLCX"
        assert device_item.NumberOfLeafJawPairs == 60
        # Note: LeafPositionBoundaries validation is now handled by validator
        
        # Should work with leaf position boundaries
        leaf_boundaries = [-200.0, -195.0, -190.0, -185.0, 185.0, 190.0, 195.0, 200.0]
        device_item = RTBeamsModule.create_beam_limiting_device_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.MLCX,
            number_of_leaf_jaw_pairs=4,
            leaf_position_boundaries=leaf_boundaries
        )
        
        assert device_item.RTBeamLimitingDeviceType == "MLCX"
        assert device_item.NumberOfLeafJawPairs == 4
        assert device_item.LeafPositionBoundaries == leaf_boundaries
    
    def test_enhanced_device_flag_creation(self):
        """Test enhanced RT beam limiting device definition flag creation."""
        # Should create beam item without validation (validation moved to validator)
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            enhanced_rt_beam_limiting_device_definition_flag=EnhancedRTBeamLimitingDeviceDefinitionFlag.NO
        )
        
        assert beam_item.BeamNumber == 1
        assert beam_item.BeamType == "STATIC"
        assert beam_item.EnhancedRTBeamLimitingDeviceDefinitionFlag == "NO"
        # Note: Enhanced device flag validation is now handled by validator
        
        # Should create beam item with YES flag
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            enhanced_rt_beam_limiting_device_definition_flag=EnhancedRTBeamLimitingDeviceDefinitionFlag.YES
        )
        
        assert beam_item.BeamNumber == 1
        assert beam_item.BeamType == "STATIC"
        assert beam_item.EnhancedRTBeamLimitingDeviceDefinitionFlag == "YES"
    
    def test_create_control_point_item(self):
        """Test control point item creation."""
        control_point = RTBeamsModule.create_control_point_item(
            control_point_index=0,
            cumulative_meterset_weight=0.0,
            gantry_angle=0.0,
            beam_limiting_device_angle=0.0,
            patient_support_angle=0.0,
            nominal_beam_energy=6.0,
            dose_rate_set=600.0
        )
        
        assert control_point.ControlPointIndex == 0
        assert control_point.CumulativeMetersetWeight == 0.0
        assert control_point.GantryAngle == 0.0
        assert control_point.BeamLimitingDeviceAngle == 0.0
        assert control_point.PatientSupportAngle == 0.0
        assert control_point.NominalBeamEnergy == 6.0
        assert control_point.DoseRateSet == 600.0
    
    def test_create_beam_limiting_device_position_item(self):
        """Test beam limiting device position item creation."""
        position_item = RTBeamsModule.create_beam_limiting_device_position_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
            leaf_jaw_positions=[-50.0, 50.0]
        )
        
        assert position_item.RTBeamLimitingDeviceType == "X"
        assert position_item.LeafJawPositions == [-50.0, 50.0]
    
    def test_create_wedge_item(self):
        """Test wedge item creation."""
        wedge_item = RTBeamsModule.create_wedge_item(
            wedge_number=1,
            wedge_type="DYNAMIC",
            wedge_angle=15.0,
            wedge_factor=0.75,
            wedge_orientation=90.0,
            wedge_id="EDW15"
        )
        
        assert wedge_item.WedgeNumber == 1
        assert wedge_item.WedgeType == "DYNAMIC"
        assert wedge_item.WedgeAngle == 15.0
        assert wedge_item.WedgeFactor == 0.75
        assert wedge_item.WedgeOrientation == 90.0
        assert wedge_item.WedgeID == "EDW15"
    
    def test_beam_helper_properties(self):
        """Test beam helper properties and methods."""
        # Create control points for beams
        control_points = [
            RTBeamsModule.create_control_point_item(
                control_point_index=0,
                cumulative_meterset_weight=0.0
            ),
            RTBeamsModule.create_control_point_item(
                control_point_index=1,
                cumulative_meterset_weight=1.0
            )
        ]
        
        # Create beams module with multiple beams
        beam1 = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            radiation_type=RadiationType.PHOTON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beam2 = RTBeamsModule.create_beam_item(
            beam_number=2,
            beam_type=BeamType.DYNAMIC,
            radiation_type=RadiationType.ELECTRON,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.Y,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beams = RTBeamsModule.from_required_elements(
            beam_sequence=[beam1, beam2]
        )
        
        # Test properties
        assert beams.has_beams
        assert beams.beam_count == 2
        assert beams.is_configured
        
        # Test beam numbers
        beam_numbers = beams.get_beam_numbers()
        assert beam_numbers == [1, 2]
        
        # Test get beam by number
        found_beam = beams.get_beam_by_number(1)
        assert found_beam is not None
        assert found_beam.BeamNumber == 1
        
        not_found_beam = beams.get_beam_by_number(99)
        assert not_found_beam is None
        
        # Test radiation types
        radiation_types = beams.get_radiation_types()
        assert 'PHOTON' in radiation_types
        assert 'ELECTRON' in radiation_types
        assert len(radiation_types) == 2
    
    def test_with_optional_elements(self):
        """Test with_optional_elements method."""
        beams = RTBeamsModule.from_required_elements(
            beam_sequence=[]
        ).with_optional_elements()
        
        # Should return self for method chaining
        assert isinstance(beams, RTBeamsModule)
        assert beams.beam_count == 0
        assert not beams.has_beams
        assert beams.is_configured  # is_configured just checks if BeamSequence exists (even if empty)
    
    def test_validation_method(self):
        """Test validation method exists and can be called."""
        beams = RTBeamsModule.from_required_elements(
            beam_sequence=[]
        )
        
        assert hasattr(beams, 'validate')
        assert callable(beams.validate)
        
        # Test validation result structure
        validation_result = beams.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_to_dataset_generation(self):
        """Test dataset generation method."""
        control_points = [
            RTBeamsModule.create_control_point_item(
                control_point_index=0,
                cumulative_meterset_weight=0.0
            )
        ]
        
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            number_of_wedges=0,
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=1,
            control_point_sequence=control_points,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
        )
        
        beams = RTBeamsModule.from_required_elements(
            beam_sequence=[beam_item]
        )
        
        # Test dataset generation
        dataset = beams.to_dataset()
        assert isinstance(dataset, Dataset)
        assert hasattr(dataset, 'BeamSequence')
        assert len(dataset.BeamSequence) == 1
        
        # Test that dataset is independent (deep copy)
        dataset.BeamSequence[0].BeamNumber = 999
        original_dataset = beams.to_dataset()
        assert original_dataset.BeamSequence[0].BeamNumber == 1
    
    def test_conditional_sequence_creation(self):
        """Test conditional sequence requirements creation."""
        control_points = [
            RTBeamsModule.create_control_point_item(
                control_point_index=0,
                cumulative_meterset_weight=0.0
            ),
            RTBeamsModule.create_control_point_item(
                control_point_index=1,
                cumulative_meterset_weight=1.0
            )
        ]
        
        # Should create beam item without validation (validation moved to validator)
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            number_of_wedges=2,  # Non-zero requires wedge sequence - but validation moved to validator
            number_of_compensators=0,
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
            # Note: Missing wedge_sequence validation is now handled by validator
        )
        
        assert beam_item.BeamNumber == 1
        assert beam_item.NumberOfWedges == 2
        
        # Should create beam item without compensator sequence validation
        beam_item = RTBeamsModule.create_beam_item(
            beam_number=1,
            beam_type=BeamType.STATIC,
            number_of_wedges=0,
            number_of_compensators=1,  # Non-zero requires compensator sequence - but validation moved to validator
            number_of_boli=0,
            number_of_blocks=0,
            number_of_control_points=2,
            control_point_sequence=control_points,
            final_cumulative_meterset_weight=1.0,
            beam_limiting_device_sequence=[
                RTBeamsModule.create_beam_limiting_device_item(
                    rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
                    number_of_leaf_jaw_pairs=1
                )
            ]
            # Note: Missing compensator_sequence validation is now handled by validator
        )
        
        assert beam_item.BeamNumber == 1
        assert beam_item.NumberOfCompensators == 1
    
    def test_new_helper_methods(self):
        """Test new helper methods for creating sequence items."""
        # Test compensator item creation
        compensator_item = RTBeamsModule.create_compensator_item(
            compensator_number=1,
            material_id='LEAD',
            compensator_rows=10,
            compensator_columns=10,
            compensator_thickness_data=[1.0, 2.0, 3.0]
        )
        
        assert compensator_item.CompensatorNumber == 1
        assert compensator_item.MaterialID == 'LEAD'
        assert compensator_item.CompensatorRows == 10
        assert compensator_item.CompensatorColumns == 10
        assert compensator_item.CompensatorThicknessData == [1.0, 2.0, 3.0]
        
        # Test block item creation
        block_item = RTBeamsModule.create_block_item(
            block_number=1,
            block_type='SHIELDING',
            block_divergence='PRESENT',
            material_id='LEAD',
            block_number_of_points=4,
            block_data=[-10.0, -10.0, 10.0, -10.0, 10.0, 10.0, -10.0, 10.0],
            block_thickness=5.0
        )
        
        assert block_item.BlockNumber == 1
        assert block_item.BlockType == 'SHIELDING'
        assert block_item.BlockDivergence == 'PRESENT'
        assert block_item.MaterialID == 'LEAD'
        assert block_item.BlockNumberOfPoints == 4
        assert block_item.BlockThickness == 5.0
        
        # Test applicator item creation
        applicator_item = RTBeamsModule.create_applicator_item(
            applicator_id='APPL01',
            applicator_type='ELECTRON_SQUARE'
        )
        
        assert applicator_item.ApplicatorID == 'APPL01'
        assert applicator_item.ApplicatorType == 'ELECTRON_SQUARE'
        
        # Test general accessory item creation
        accessory_item = RTBeamsModule.create_general_accessory_item(
            general_accessory_number=1,
            general_accessory_id='GRATICULE01',
            general_accessory_type='GRATICULE'
        )
        
        assert accessory_item.GeneralAccessoryNumber == 1
        assert accessory_item.GeneralAccessoryID == 'GRATICULE01'
        assert accessory_item.GeneralAccessoryType == 'GRATICULE'
    
    def test_empty_beam_sequence_properties(self):
        """Test properties with empty beam sequence."""
        beams = RTBeamsModule.from_required_elements(
            beam_sequence=[]
        )
        
        assert not beams.has_beams
        assert beams.beam_count == 0
        assert beams.is_configured
        assert beams.get_beam_numbers() == []
        assert beams.get_beam_by_number(1) is None
        assert beams.get_radiation_types() == []