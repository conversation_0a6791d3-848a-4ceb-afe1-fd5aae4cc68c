"""
Test ROIContourModule functionality.

ROIContourModule implements DICOM PS3.3 C.8.8.6 ROI Contour Module.
Defines ROIs as a set of contours with geometric types.
"""

import pydicom
from pyrt_dicom.modules import ROIContourModule
from pyrt_dicom.enums.rt_enums import ContourGeometricType
from pyrt_dicom.validators.modules.roi_contour_validator import ROIContourValidator
from pyrt_dicom.validators import ValidationResult


class TestROIContourModule:
    """Test ROIContourModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create basic contour item
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 20.0, 20.0, 0.0, 10.0, 20.0, 0.0]
        )
        
        # Create ROI contour item
        roi_contour_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        # Create module with required elements
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_contour_item]
        )
        
        # Test dataset generation
        dataset = roi_contour.to_dataset()
        assert hasattr(dataset, 'ROIContourSequence')
        assert len(dataset.ROIContourSequence) == 1
        assert dataset.ROIContourSequence[0].ReferencedROINumber == 1
        assert dataset.ROIContourSequence[0].ROIDisplayColor == [255, 0, 0]
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        # Create basic module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )
        
        roi_contour_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[0, 255, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_contour_item]
        ).with_optional_elements()
        
        # Should return self for method chaining
        assert roi_contour is not None
        
        # Test dataset generation
        dataset = roi_contour.to_dataset()
        assert hasattr(dataset, 'ROIContourSequence')
    
    def test_create_contour_item_basic(self):
        """Test basic contour item creation."""
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=3,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        assert contour_item.ContourGeometricType == "CLOSED_PLANAR"
        assert contour_item.NumberOfContourPoints == 3
        assert len(contour_item.ContourData) == 9
    
    def test_create_contour_item_with_optional_elements(self):
        """Test contour item creation with optional elements."""
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.OPEN_PLANAR,
            number_of_contour_points=2,
            contour_data=[0.0, 0.0, 0.0, 1.0, 1.0, 1.0],
            contour_number=1,
            contour_slab_thickness=2.5,
            contour_offset_vector=[0.0, 0.0, 1.0]
        )
        
        assert contour_item.ContourGeometricType == "OPEN_PLANAR"
        assert contour_item.NumberOfContourPoints == 2
        assert contour_item.ContourNumber == 1
        assert contour_item.ContourSlabThickness == 2.5
        assert contour_item.ContourOffsetVector == [0.0, 0.0, 1.0]
    
    def test_create_roi_contour_item_basic(self):
        """Test basic ROI contour item creation."""
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=2,
            roi_display_color=[0, 0, 255]
        )
        
        assert roi_item.ReferencedROINumber == 2
        assert roi_item.ROIDisplayColor == [0, 0, 255]
        assert not hasattr(roi_item, 'ContourSequence')  # Optional, not provided
    
    def test_create_roi_contour_item_with_optional_elements(self):
        """Test ROI contour item creation with optional elements."""
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=3,
            roi_display_color=[128, 128, 128],
            recommended_display_grayscale_value=128
        )
        
        assert roi_item.ReferencedROINumber == 3
        assert roi_item.ROIDisplayColor == [128, 128, 128]
        assert roi_item.RecommendedDisplayGrayscaleValue == 128
    
    def test_module_properties(self):
        """Test module convenience properties."""
        # Create module without contours
        empty_module = ROIContourModule.from_required_elements(roi_contour_sequence=[])
        
        assert empty_module.has_contours  # has_contours only checks if ROIContourSequence exists, not if it's empty
        assert empty_module.roi_count == 0
        assert empty_module.total_contour_count == 0
        assert empty_module.get_contour_geometric_types() == []
        
        # Create module with contours
        contour_item1 = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=3,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        contour_item2 = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[5.0, 5.0, 5.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item1, contour_item2]
        )
        
        module_with_contours = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        assert module_with_contours.has_contours
        assert module_with_contours.roi_count == 1
        assert module_with_contours.total_contour_count == 2
        geometric_types = module_with_contours.get_contour_geometric_types()
        assert "CLOSED_PLANAR" in geometric_types
        assert "POINT" in geometric_types
    
    def test_validation_basic(self):
        """Test basic validation functionality."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        validation_result = roi_contour.validate()
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
        
        # Basic validation should pass for well-formed module
        assert len(validation_result.errors) == 0
    
    def test_dataset_generation(self):
        """Test that modules generate valid pydicom Dataset objects."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        # Test dataset generation
        dataset = roi_contour.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        
        # Test that DICOM attributes are accessible through generated dataset
        assert hasattr(dataset, 'ROIContourSequence')
        assert dataset.ROIContourSequence[0].ReferencedROINumber == 1
        assert dataset.ROIContourSequence[0].ROIDisplayColor == [255, 0, 0]
    
    def test_helper_methods(self):
        """Test static helper methods for creating sub-items."""
        # Test contour image item creation
        contour_image_item = ROIContourModule.create_contour_image_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        
        assert contour_image_item.ReferencedSOPClassUID == "1.2.840.10008.5.1.4.1.1.2"
        assert contour_image_item.ReferencedSOPInstanceUID == "1.2.3.4.5.6.7.8.9"
        
        # Test source series item creation
        source_series_item = ROIContourModule.create_source_series_item(
            series_instance_uid="1.2.3.4.5.6.7.8.10"
        )
        
        assert source_series_item.SeriesInstanceUID == "1.2.3.4.5.6.7.8.10"
        
        # Test source pixel planes characteristics item creation
        pixel_item = ROIContourModule.create_source_pixel_planes_characteristics_item(
            pixel_spacing=[1.0, 1.0],
            spacing_between_slices=2.5,
            image_orientation_patient=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            image_position_patient=[0.0, 0.0, 0.0],
            number_of_frames=100,
            rows=512,
            columns=512
        )
        
        assert pixel_item.PixelSpacing == [1.0, 1.0]
        assert pixel_item.SpacingBetweenSlices == 2.5
        assert pixel_item.NumberOfFrames == 100
        assert pixel_item.Rows == 512
        assert pixel_item.Columns == 512
    
    def test_validation_result_functionality(self):
        """Test that validator returns ValidationResult with correct functionality."""
        # Create valid test data
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        # Test direct validator call returns ValidationResult
        dataset = roi_contour.to_dataset()
        validator_result = ROIContourValidator.validate(dataset)
        assert isinstance(validator_result, ValidationResult)
        
        # Test module validate returns dict (backward compatibility)
        assert hasattr(roi_contour, 'validate')
        assert callable(roi_contour.validate)
        
        # Test validation result structure
        validation_result = roi_contour.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)