"""
Test SOPCommonModule (M - Mandatory) functionality.

SOPCommonModule implements DICOM PS3.3 C.12.1 SOP Common Module.
Required for all RTDoseIOD instances.
"""

from datetime import datetime
from pydicom import Dataset
from pydicom.uid import generate_uid
from pyrt_dicom.modules import SOPCommonModule
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.common_enums import (
    SyntheticData, SOPInstanceStatus, QueryRetrieveView,
    ContentQualification, LongitudinalTemporalInformationModified
)


class TestSOPCommonModule:
    """Test SOPCommonModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        sop_instance_uid = generate_uid()
        sop_class_uid = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class_uid,
            sop_instance_uid=sop_instance_uid
        )
        
        # Test through dataset generation - composition architecture pattern
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == sop_class_uid
        assert dataset.SOPInstanceUID == sop_instance_uid
    
    def test_rt_dose_sop_class_uid(self):
        """Test RT Dose specific SOP Class UID."""
        rt_dose_sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=rt_dose_sop_class,
            sop_instance_uid=generate_uid()
        )
        
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == rt_dose_sop_class
    
    def test_sop_instance_uid_uniqueness(self):
        """Test that SOP Instance UIDs are unique."""
        sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop1 = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class,
            sop_instance_uid=generate_uid()
        )
        sop2 = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class,
            sop_instance_uid=generate_uid()
        )
        
        dataset1 = sop1.to_dataset()
        dataset2 = sop2.to_dataset()
        assert dataset1.SOPInstanceUID != dataset2.SOPInstanceUID
    
    def test_various_sop_class_uids(self):
        """Test various RT-related SOP Class UIDs."""
        rt_sop_classes = [
            "1.2.840.10008.*******.1.481.2",   # RT Dose Storage
            "1.2.840.10008.*******.1.481.5",   # RT Plan Storage
            "1.2.840.10008.*******.1.481.3",   # RT Structure Set Storage
            "1.2.840.10008.*******.1.481.4"    # RT Beams Treatment Record Storage
        ]
        
        for sop_class in rt_sop_classes:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid=sop_class,
                sop_instance_uid=generate_uid()
            )
            dataset = sop.to_dataset()
            assert dataset.SOPClassUID == sop_class
    
    def test_with_optional_elements(self):
        """Test adding optional SOP Common elements."""
        current_time = datetime.now()
        instance_creation_date = current_time.strftime("%Y%m%d")
        instance_creation_time = current_time.strftime("%H%M%S.%f")[:-3]  # Microseconds to milliseconds
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_specific_character_set(
            "ISO_IR 100"
        ).with_optional_elements(
            instance_creation_date=instance_creation_date,
            instance_creation_time=instance_creation_time,
            instance_creator_uid=generate_uid(),
            instance_number=1,
            sop_instance_status=SOPInstanceStatus.AC
        )
        
        # Test through dataset generation - composition architecture pattern
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SpecificCharacterSet')
        assert hasattr(dataset, 'InstanceCreationDate')
        assert hasattr(dataset, 'InstanceCreationTime')
        assert hasattr(dataset, 'InstanceCreatorUID')
        assert hasattr(dataset, 'InstanceNumber')
    
    def test_instance_creation_datetime(self):
        """Test instance creation date and time handling."""
        creation_date = "20240101"
        creation_time = "120000.123"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creation_date=creation_date,
            instance_creation_time=creation_time
        )
        
        dataset = sop.to_dataset()
        assert dataset.InstanceCreationDate == creation_date
        assert dataset.InstanceCreationTime == creation_time
    
    def test_specific_character_set_values(self):
        """Test various specific character set values."""
        character_sets = [
            "ISO_IR 100",    # Latin alphabet No. 1
            "ISO_IR 101",    # Latin alphabet No. 2
            "ISO_IR 109",    # Latin alphabet No. 3
            "ISO_IR 110",    # Latin alphabet No. 4
            "ISO_IR 144",    # Cyrillic
            "ISO_IR 127",    # Arabic
            "ISO_IR 126",    # Greek
            "ISO_IR 138",    # Hebrew
            "ISO_IR 148",    # Latin alphabet No. 5
            "ISO_IR 192"     # UTF-8
        ]
        
        for charset in character_sets:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_specific_character_set(
                charset
            )
            dataset = sop.to_dataset()
            assert dataset.SpecificCharacterSet == charset
    
    def test_instance_number_formats(self):
        """Test various instance number formats."""
        # DICOM Instance Number (IS VR) must be valid integers
        instance_numbers = [1, 123, 999]
        
        for instance_num in instance_numbers:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_optional_elements(
                instance_number=instance_num
            )
            dataset = sop.to_dataset()
            assert dataset.InstanceNumber == instance_num
    
    def test_instance_creator_uid_validation(self):
        """Test instance creator UID validation."""
        creator_uid = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creator_uid=creator_uid
        )
        
        dataset = sop.to_dataset()
        assert dataset.InstanceCreatorUID == creator_uid
    
    def test_sop_authorization_elements(self):
        """Test SOP authorization related elements."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            sop_authorization_comment="Authorized for clinical use",
            authorization_equipment_certification_number="CERT123456"
        )
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SOPAuthorizationComment')
        assert hasattr(dataset, 'AuthorizationEquipmentCertificationNumber')
    
    def test_timezone_offset_from_utc(self):
        """Test timezone offset from UTC handling."""
        timezone_offsets = ["+0000", "-0500", "+0900", "+0530", "-0800"]
        
        for offset in timezone_offsets:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_optional_elements(
                timezone_offset_from_utc=offset
            )
            dataset = sop.to_dataset()
            assert dataset.TimezoneOffsetFromUTC == offset
    
    def test_digital_signatures_elements(self):
        """Test digital signature related elements."""
        # Digital signatures are not implemented in the current SOPCommonModule
        # This test verifies the module can be created and validated without these elements
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        # Verify basic SOP Common elements are present through dataset
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SOPClassUID')
        assert hasattr(dataset, 'SOPInstanceUID')
        
        # Validate the module
        result = sop.validate()
        assert result is not None
    
    def test_rt_dose_sop_requirements(self):
        """Test RT Dose specific SOP Common requirements."""
        rt_dose_sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=rt_dose_sop_class,
            sop_instance_uid=generate_uid()
        )
        
        # Verify RT Dose specific SOP requirements through dataset
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == rt_dose_sop_class
        assert dataset.SOPInstanceUID is not None
        assert len(dataset.SOPInstanceUID) > 0
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        assert hasattr(sop, 'validate')
        assert callable(sop.validate)
        
        # Test validation result structure
        validation_result = sop.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_sop_class_uid_validation(self):
        """Test SOP Class UID format validation."""
        # Test valid UID format
        valid_uid = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=valid_uid,
            sop_instance_uid=generate_uid()
        )
        
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == valid_uid
    
    def test_sop_instance_uid_format(self):
        """Test SOP Instance UID format requirements."""
        # Generate UID and verify it follows DICOM UID rules
        instance_uid = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=instance_uid
        )
        
        # Verify UID characteristics through dataset
        dataset = sop.to_dataset()
        assert dataset.SOPInstanceUID == instance_uid
        assert "." in dataset.SOPInstanceUID  # UIDs contain dots
        assert len(dataset.SOPInstanceUID) <= 64  # Max UID length

    def test_conditional_query_retrieve_view(self):
        """Test conditional query/retrieve view (Type 1C)."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_query_retrieve_view(QueryRetrieveView.ENHANCED)
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'QueryRetrieveView')
        assert dataset.QueryRetrieveView == QueryRetrieveView.ENHANCED.value
        assert sop.is_converted_instance  # Property test

    def test_conditional_encrypted_attributes(self):
        """Test conditional encrypted attributes sequence (Type 1C)."""
        # Create a dummy encrypted attributes item
        encrypted_item = Dataset()
        encrypted_item.EncryptedContentTransferSyntaxUID = "1.2.840.10008.1.2"
        encrypted_item.EncryptedContent = b"dummy_encrypted_content"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_encrypted_attributes_sequence([encrypted_item])
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'EncryptedAttributesSequence')
        assert len(dataset.EncryptedAttributesSequence) == 1
        assert sop.is_encrypted  # Property test

    def test_conditional_conversion_source(self):
        """Test conditional conversion source attributes sequence (Type 1C)."""
        # Create a dummy conversion source item
        conversion_item = Dataset()
        conversion_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.2"
        conversion_item.ReferencedSOPInstanceUID = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_conversion_source_attributes_sequence([conversion_item])
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'ConversionSourceAttributesSequence')
        assert len(dataset.ConversionSourceAttributesSequence) == 1
        assert sop.is_converted_instance  # Property test

    def test_conditional_hl7_document_reference(self):
        """Test conditional HL7 structured document reference sequence (Type 1C)."""
        # Create a dummy HL7 document reference item
        hl7_item = Dataset()
        hl7_item.ReferencedSOPClassUID = "2.16.840.1.113883.1.7.2"  # CDA Release 2
        hl7_item.ReferencedSOPInstanceUID = generate_uid()
        hl7_item.HL7InstanceIdentifier = "1.2.3.4.5^extension"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_hl7_structured_document_reference_sequence([hl7_item])
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'HL7StructuredDocumentReferenceSequence')
        assert len(dataset.HL7StructuredDocumentReferenceSequence) == 1
        assert sop.has_hl7_documents  # Property test

    def test_enhanced_optional_elements(self):
        """Test enhanced optional elements (Type 3)."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            synthetic_data=SyntheticData.YES,
            longitudinal_temporal_information_modified=LongitudinalTemporalInformationModified.MODIFIED,
            content_qualification=ContentQualification.RESEARCH,
            instance_origin_status="LOCAL",
            barcode_value="ABC123456"
        )
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SyntheticData')
        assert dataset.SyntheticData == SyntheticData.YES.value
        assert hasattr(dataset, 'LongitudinalTemporalInformationModified')
        assert dataset.LongitudinalTemporalInformationModified == LongitudinalTemporalInformationModified.MODIFIED.value
        assert hasattr(dataset, 'ContentQualification')
        assert dataset.ContentQualification == ContentQualification.RESEARCH.value
        assert hasattr(dataset, 'InstanceOriginStatus')
        assert dataset.InstanceOriginStatus == "LOCAL"
        assert hasattr(dataset, 'BarcodeValue')
        assert dataset.BarcodeValue == "ABC123456"
        
        # Test properties
        assert sop.is_synthetic

    def test_helper_methods_coding_scheme_identification(self):
        """Test coding scheme identification sequence item creation."""
        item = SOPCommonModule.create_coding_scheme_identification_item(
            coding_scheme_designator="DCM",
            coding_scheme_registry="HL7",
            coding_scheme_uid="1.2.840.10008.2.16.4",
            coding_scheme_name="DICOM Controlled Terminology"
        )
        
        assert isinstance(item, Dataset)
        assert item.CodingSchemeDesignator == "DCM"
        assert item.CodingSchemeRegistry == "HL7"
        assert item.CodingSchemeUID == "1.2.840.10008.2.16.4"
        assert item.CodingSchemeName == "DICOM Controlled Terminology"

    def test_helper_methods_context_group_identification(self):
        """Test context group identification sequence item creation."""
        item = SOPCommonModule.create_context_group_identification_item(
            context_identifier="CID_100",
            mapping_resource="DCMR",
            context_group_version="20240101",
            context_uid="1.2.3.4.5"
        )
        
        assert isinstance(item, Dataset)
        assert item.ContextIdentifier == "CID_100"
        assert item.MappingResource == "DCMR"
        assert item.ContextGroupVersion == "20240101"
        assert item.ContextUID == "1.2.3.4.5"

    def test_helper_methods_private_data_characteristics(self):
        """Test private data element characteristics sequence item creation."""
        item = SOPCommonModule.create_private_data_element_characteristics_item(
            private_group_reference=0x0011,
            private_creator_reference="MY_PRIVATE_CREATOR",
            block_identifying_information_status="SAFE"
        )
        
        assert isinstance(item, Dataset)
        assert item.PrivateGroupReference == 0x0011
        assert item.PrivateCreatorReference == "MY_PRIVATE_CREATOR"
        assert item.BlockIdentifyingInformationStatus == "SAFE"

    def test_dataset_generation_and_integrity(self):
        """Test dataset generation preserves all data."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creation_date="20240101",
            instance_creation_time="120000",
            synthetic_data=SyntheticData.NO,
            instance_number=1
        ).with_specific_character_set("ISO_IR 100")
        
        dataset = sop.to_dataset()
        
        # Verify all elements are preserved
        assert isinstance(dataset, Dataset)
        assert hasattr(dataset, 'SOPClassUID')
        assert hasattr(dataset, 'SOPInstanceUID')
        assert hasattr(dataset, 'InstanceCreationDate')
        assert hasattr(dataset, 'InstanceCreationTime')
        assert hasattr(dataset, 'SyntheticData')
        assert hasattr(dataset, 'InstanceNumber')
        assert hasattr(dataset, 'SpecificCharacterSet')
        
        # Verify values
        assert dataset.SyntheticData == "NO"
        assert dataset.InstanceNumber == 1
        assert dataset.SpecificCharacterSet == "ISO_IR 100"

    def test_module_properties(self):
        """Test module properties with various combinations."""
        # Test empty module
        sop_empty = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        assert not sop_empty.has_creation_info
        assert not sop_empty.is_synthetic
        assert not sop_empty.has_authorization_info
        assert not sop_empty.is_encrypted
        assert not sop_empty.has_character_set
        assert not sop_empty.is_converted_instance
        assert not sop_empty.has_hl7_documents
        assert not sop_empty.has_private_data_characteristics
        assert not sop_empty.has_contributing_equipment
        
        # Test populated module
        contrib_item = Dataset()
        contrib_item.PurposeOfReferenceCodeSequence = []
        contrib_item.Manufacturer = "Test Manufacturer"
        
        sop_full = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creation_date="20240101",
            synthetic_data=SyntheticData.YES,
            contributing_equipment_sequence=[contrib_item]
        ).with_specific_character_set("ISO_IR 100")
        
        assert sop_full.has_creation_info
        assert sop_full.is_synthetic
        assert sop_full.has_character_set
        assert sop_full.has_contributing_equipment