"""Tests for Patient Study-related DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.study_enums import SmokingStatus, PregnancyStatus, PatientSexNeutered


class TestPatientStudyEnums:
    """Test patient study enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(SmokingStatus))
    def test_smoking_status_enum_values(self, enum_value: SmokingStatus):
        """Test that SmokingStatus enum values can be set to Dataset.SmokingStatus without error.
        
        Args:
            enum_value: SmokingStatus enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.SmokingStatus = enum_value.value
        assert dataset.SmokingStatus == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PregnancyStatus))
    def test_pregnancy_status_enum_values(self, enum_value: PregnancyStatus):
        """Test that PregnancyStatus enum values can be set to Dataset.PregnancyStatus without error.
        
        Args:
            enum_value: PregnancyStatus enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.PregnancyStatus = enum_value.value
        assert dataset.PregnancyStatus == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PatientSexNeutered))
    def test_patient_sex_neutered_enum_values(self, enum_value: PatientSexNeutered):
        """Test that PatientSexNeutered enum values can be set to Dataset.PatientSexNeutered without error.
        
        Args:
            enum_value: PatientSexNeutered enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.PatientSexNeutered = enum_value.value
        assert dataset.PatientSexNeutered == enum_value.value

    def test_smoking_status_coverage(self):
        """Test that all expected SmokingStatus values are present."""
        expected_values = {"YES", "NO", "UNKNOWN"}
        actual_values = {member.value for member in SmokingStatus}
        assert actual_values == expected_values

    def test_pregnancy_status_coverage(self):
        """Test that all expected PregnancyStatus values are present."""
        expected_values = {1, 2, 3, 4}
        actual_values = {member.value for member in PregnancyStatus}
        assert actual_values == expected_values

    def test_patient_sex_neutered_coverage(self):
        """Test that all expected PatientSexNeutered values are present."""
        expected_values = {"ALTERED", "UNALTERED"}
        actual_values = {member.value for member in PatientSexNeutered}
        assert actual_values == expected_values