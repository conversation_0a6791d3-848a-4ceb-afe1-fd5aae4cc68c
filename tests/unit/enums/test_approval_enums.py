"""Unit tests for approval enums compatibility with pydicom Dataset.

This module tests that ApprovalStatus enum values can be properly set
as attributes on pydicom Dataset objects without errors.
"""

from pydicom import Dataset
from pyrt_dicom.enums.approval_enums import ApprovalStatus


class TestApprovalStatusEnum:
    """Test ApprovalStatus enum compatibility with pydicom Dataset."""
    
    def test_approval_status_values_exist(self):
        """Test that all expected ApprovalStatus values exist."""
        assert ApprovalStatus.APPROVED.value == "APPROVED"
        assert ApprovalStatus.UNAPPROVED.value == "UNAPPROVED"
        assert ApprovalStatus.REJECTED.value == "REJECTED"
    
    def test_approval_status_approved_can_be_set_to_dataset(self):
        """Test that ApprovalStatus.APPROVED can be set to Dataset without error."""
        ds = Dataset()
        ds.ApprovalStatus = ApprovalStatus.APPROVED.value
        
        assert ds.ApprovalStatus == "APPROVED"
        assert hasattr(ds, 'ApprovalStatus')
    
    def test_approval_status_unapproved_can_be_set_to_dataset(self):
        """Test that ApprovalStatus.UNAPPROVED can be set to Dataset without error."""
        ds = Dataset()
        ds.ApprovalStatus = ApprovalStatus.UNAPPROVED.value
        
        assert ds.ApprovalStatus == "UNAPPROVED"
        assert hasattr(ds, 'ApprovalStatus')
    
    def test_approval_status_rejected_can_be_set_to_dataset(self):
        """Test that ApprovalStatus.REJECTED can be set to Dataset without error."""
        ds = Dataset()
        ds.ApprovalStatus = ApprovalStatus.REJECTED.value
        
        assert ds.ApprovalStatus == "REJECTED"
        assert hasattr(ds, 'ApprovalStatus')
    
    def test_all_approval_status_values_can_be_set_to_dataset(self):
        """Test that all ApprovalStatus values can be set to Dataset without error."""
        for status in ApprovalStatus:
            ds = Dataset()
            # Should not raise any exception
            ds.ApprovalStatus = status.value
            
            # Verify the value was set correctly
            assert ds.ApprovalStatus == status.value
            assert hasattr(ds, 'ApprovalStatus')
    
    def test_approval_status_enum_directly_can_be_set_to_dataset(self):
        """Test that ApprovalStatus enum instances can be used directly with Dataset."""
        for status in ApprovalStatus:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.ApprovalStatus = status
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.ApprovalStatus).endswith(status.value)
            assert hasattr(ds, 'ApprovalStatus')
    
    def test_approval_status_multiple_assignments(self):
        """Test that ApprovalStatus can be reassigned multiple times."""
        ds = Dataset()
        
        # Set to UNAPPROVED
        ds.ApprovalStatus = ApprovalStatus.UNAPPROVED.value
        assert ds.ApprovalStatus == "UNAPPROVED"
        
        # Change to APPROVED
        ds.ApprovalStatus = ApprovalStatus.APPROVED.value
        assert ds.ApprovalStatus == "APPROVED"
        
        # Change to REJECTED
        ds.ApprovalStatus = ApprovalStatus.REJECTED.value
        assert ds.ApprovalStatus == "REJECTED"
    
    def test_approval_status_with_dicom_tag(self):
        """Test that ApprovalStatus can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # First set the element using attribute notation
        ds.ApprovalStatus = ApprovalStatus.APPROVED.value
        
        # Then verify it can be accessed via tag notation
        assert ds[0x300E, 0x0002].value == "APPROVED"
        assert ds.ApprovalStatus == "APPROVED"
        
        # Test setting via DataElement creation
        ds2 = Dataset()
        ds2[0x300E, 0x0002] = DataElement(0x300E0002, 'CS', ApprovalStatus.REJECTED.value)
        assert ds2.ApprovalStatus == "REJECTED"
    
    def test_approval_status_preserves_dicom_vr(self):
        """Test that ApprovalStatus maintains proper DICOM VR (CS - Code String)."""
        ds = Dataset()
        ds.ApprovalStatus = ApprovalStatus.APPROVED.value
        
        # Verify the data element was created correctly
        data_element = ds[0x300E, 0x0002]
        assert data_element.value == "APPROVED"
        # Note: VR might not be automatically set without explicit DICOM dictionary