"""Tests for RT Dose-related DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.dose_enums import DoseUnits, DoseType


class TestDoseEnums:
    """Test dose enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(DoseUnits))
    def test_dose_units_enum_values(self, enum_value: DoseUnits):
        """Test that DoseUnits enum values can be set to Dataset.DoseUnits without error.
        
        Args:
            enum_value: DoseUnits enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.DoseUnits = enum_value.value
        assert dataset.DoseUnits == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DoseType))
    def test_dose_type_enum_values(self, enum_value: DoseType):
        """Test that DoseType enum values can be set to Dataset.DoseType without error.
        
        Args:
            enum_value: DoseType enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.DoseType = enum_value.value
        assert dataset.DoseType == enum_value.value

    def test_dose_units_coverage(self):
        """Test that all expected DoseUnits values are present."""
        expected_values = {"GY", "RELATIVE"}
        actual_values = {member.value for member in DoseUnits}
        assert actual_values == expected_values

    def test_dose_type_coverage(self):
        """Test that all expected DoseType values are present."""
        expected_values = {"PHYSICAL", "EFFECTIVE", "BIOLOGICAL", "ERROR"}
        actual_values = {member.value for member in DoseType}
        assert actual_values == expected_values