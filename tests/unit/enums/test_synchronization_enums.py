"""Tests for Synchronization Module DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.synchronization_enums import (
    SynchronizationTrigger,
    AcquisitionTimeSynchronized,
    TimeDistributionProtocol,
)


class TestSynchronizationEnums:
    """Test synchronization enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(SynchronizationTrigger))
    def test_synchronization_trigger_enum_values(self, enum_value: SynchronizationTrigger):
        """Test that SynchronizationTrigger enum values can be set to Dataset.SynchronizationTrigger without error.
        
        Args:
            enum_value: SynchronizationTrigger enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.SynchronizationTrigger = enum_value.value
        assert dataset.SynchronizationTrigger == enum_value.value

    @pytest.mark.parametrize("enum_value", list(AcquisitionTimeSynchronized))
    def test_acquisition_time_synchronized_enum_values(self, enum_value: AcquisitionTimeSynchronized):
        """Test that AcquisitionTimeSynchronized enum values can be set to Dataset.AcquisitionTimeSynchronized without error.
        
        Args:
            enum_value: AcquisitionTimeSynchronized enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.AcquisitionTimeSynchronized = enum_value.value
        assert dataset.AcquisitionTimeSynchronized == enum_value.value

    @pytest.mark.parametrize("enum_value", list(TimeDistributionProtocol))
    def test_time_distribution_protocol_enum_values(self, enum_value: TimeDistributionProtocol):
        """Test that TimeDistributionProtocol enum values can be set to Dataset.TimeDistributionProtocol without error.
        
        Args:
            enum_value: TimeDistributionProtocol enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.TimeDistributionProtocol = enum_value.value
        assert dataset.TimeDistributionProtocol == enum_value.value

    def test_synchronization_trigger_coverage(self):
        """Test that all expected SynchronizationTrigger values are present."""
        expected_values = {"SOURCE", "EXTERNAL", "PASSTHRU", "NO TRIGGER"}
        actual_values = {member.value for member in SynchronizationTrigger}
        assert actual_values == expected_values

    def test_acquisition_time_synchronized_coverage(self):
        """Test that all expected AcquisitionTimeSynchronized values are present."""
        expected_values = {"Y", "N"}
        actual_values = {member.value for member in AcquisitionTimeSynchronized}
        assert actual_values == expected_values

    def test_time_distribution_protocol_coverage(self):
        """Test that all expected TimeDistributionProtocol values are present."""
        expected_values = {"NTP", "IRIG", "GPS", "SNTP", "PTP"}
        actual_values = {member.value for member in TimeDistributionProtocol}
        assert actual_values == expected_values