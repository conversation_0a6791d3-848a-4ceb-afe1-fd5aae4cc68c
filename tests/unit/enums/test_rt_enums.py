"""Tests for Radiotherapy-specific DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.rt_enums import (
    # Image and Conversion
    ConversionType,
    ReportedValuesOrigin,
    RTImagePlane,
    PixelIntensityRelationshipSign,
    FluenceMode,
    FluenceDataSource,
    RTImageTypeValue3,
    EnhancedRTBeamLimitingDeviceDefinitionFlag,
    
    # Beam and Radiation
    BeamType,
    RadiationType,
    TreatmentDeliveryType,
    PrimaryDosimeterUnit,
    RTBeamLimitingDeviceType,
    ApplicatorType,
    ApplicatorApertureShape,
    GeneralAccessoryType,
    BlockType,
    BlockDivergence,
    BlockMountingPosition,
    BeamDoseMeaning,
    
    # Dose and DVH
    SpatialTransformOfDose,
    DoseSummationType,
    TissueHeterogeneityCorrection,
    DVHType,
    DVHROIContributionType,
    DVHVolumeUnits,
    DoseReferenceStructureType,
    DoseReferenceType,
    DoseValuePurpose,
    DoseValueInterpretation,
    DoseCalibrationConditionsVerifiedFlag,
    
    # ROI and Structure
    ROIGenerationAlgorithm,
    ContourGeometricType,
    RTROIInterpretedType,
    RTROIRelationship,
    ROIPhysicalProperty,
    
    # Plan and Planning
    PlanIntent,
    RTPlanGeometry,
    RTPlanRelationship,
    RTPlanStatus,
    RTPrescriptionStatus,
    
    # Setup and Patient Positioning
    FixationDeviceType,
    ShieldingDeviceType,
    SetupTechnique,
    SetupDeviceType,
    
    # Respiratory and Motion
    RespiratoryMotionCompensationTechnique,
    RespiratorySignalSource,
    
    # Brachytherapy
    BrachyTreatmentTechnique,
    BrachyTreatmentType,
    ApplicationSetupType,
    SourceType,
    SourceMovementType,
    BrachyAccessoryDeviceType,
    SourceApplicatorType,
    SourceStrengthUnits,
)


class TestRTEnums:
    """Test radiotherapy enumeration values can be set on pydicom Dataset without error."""
    
    # ==========================================
    # IMAGE AND CONVERSION ENUMS
    # ==========================================
    
    @pytest.mark.parametrize("enum_value", list(ConversionType))
    def test_conversion_type_enum_values(self, enum_value: ConversionType):
        """Test that ConversionType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ConversionType = enum_value.value
        assert dataset.ConversionType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ReportedValuesOrigin))
    def test_reported_values_origin_enum_values(self, enum_value: ReportedValuesOrigin):
        """Test that ReportedValuesOrigin enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ReportedValuesOrigin = enum_value.value
        assert dataset.ReportedValuesOrigin == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTImagePlane))
    def test_rt_image_plane_enum_values(self, enum_value: RTImagePlane):
        """Test that RTImagePlane enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTImagePlane = enum_value.value
        assert dataset.RTImagePlane == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PixelIntensityRelationshipSign))
    def test_pixel_intensity_relationship_sign_enum_values(self, enum_value: PixelIntensityRelationshipSign):
        """Test that PixelIntensityRelationshipSign enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PixelIntensityRelationshipSign = enum_value.value
        assert dataset.PixelIntensityRelationshipSign == enum_value.value

    @pytest.mark.parametrize("enum_value", list(FluenceMode))
    def test_fluence_mode_enum_values(self, enum_value: FluenceMode):
        """Test that FluenceMode enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.FluenceMode = enum_value.value
        assert dataset.FluenceMode == enum_value.value

    @pytest.mark.parametrize("enum_value", list(FluenceDataSource))
    def test_fluence_data_source_enum_values(self, enum_value: FluenceDataSource):
        """Test that FluenceDataSource enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.FluenceDataSource = enum_value.value
        assert dataset.FluenceDataSource == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTImageTypeValue3))
    def test_rt_image_type_value3_enum_values(self, enum_value: RTImageTypeValue3):
        """Test that RTImageTypeValue3 enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ImageType = enum_value.value  # Third value of ImageType
        assert dataset.ImageType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(EnhancedRTBeamLimitingDeviceDefinitionFlag))
    def test_enhanced_rt_beam_limiting_device_definition_flag_enum_values(self, enum_value: EnhancedRTBeamLimitingDeviceDefinitionFlag):
        """Test that EnhancedRTBeamLimitingDeviceDefinitionFlag enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.EnhancedRTBeamLimitingDeviceDefinitionFlag = enum_value.value
        assert dataset.EnhancedRTBeamLimitingDeviceDefinitionFlag == enum_value.value

    # ==========================================
    # BEAM AND RADIATION ENUMS
    # ==========================================

    @pytest.mark.parametrize("enum_value", list(BeamType))
    def test_beam_type_enum_values(self, enum_value: BeamType):
        """Test that BeamType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BeamType = enum_value.value
        assert dataset.BeamType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RadiationType))
    def test_radiation_type_enum_values(self, enum_value: RadiationType):
        """Test that RadiationType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RadiationType = enum_value.value
        assert dataset.RadiationType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(TreatmentDeliveryType))
    def test_treatment_delivery_type_enum_values(self, enum_value: TreatmentDeliveryType):
        """Test that TreatmentDeliveryType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.TreatmentDeliveryType = enum_value.value
        assert dataset.TreatmentDeliveryType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PrimaryDosimeterUnit))
    def test_primary_dosimeter_unit_enum_values(self, enum_value: PrimaryDosimeterUnit):
        """Test that PrimaryDosimeterUnit enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PrimaryDosimeterUnit = enum_value.value
        assert dataset.PrimaryDosimeterUnit == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTBeamLimitingDeviceType))
    def test_rt_beam_limiting_device_type_enum_values(self, enum_value: RTBeamLimitingDeviceType):
        """Test that RTBeamLimitingDeviceType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTBeamLimitingDeviceType = enum_value.value
        assert dataset.RTBeamLimitingDeviceType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ApplicatorType))
    def test_applicator_type_enum_values(self, enum_value: ApplicatorType):
        """Test that ApplicatorType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ApplicatorType = enum_value.value
        assert dataset.ApplicatorType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ApplicatorApertureShape))
    def test_applicator_aperture_shape_enum_values(self, enum_value: ApplicatorApertureShape):
        """Test that ApplicatorApertureShape enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ApplicatorApertureShape = enum_value.value
        assert dataset.ApplicatorApertureShape == enum_value.value

    @pytest.mark.parametrize("enum_value", list(GeneralAccessoryType))
    def test_general_accessory_type_enum_values(self, enum_value: GeneralAccessoryType):
        """Test that GeneralAccessoryType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.GeneralAccessoryType = enum_value.value
        assert dataset.GeneralAccessoryType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(BlockType))
    def test_block_type_enum_values(self, enum_value: BlockType):
        """Test that BlockType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BlockType = enum_value.value
        assert dataset.BlockType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(BlockDivergence))
    def test_block_divergence_enum_values(self, enum_value: BlockDivergence):
        """Test that BlockDivergence enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BlockDivergence = enum_value.value
        assert dataset.BlockDivergence == enum_value.value

    @pytest.mark.parametrize("enum_value", list(BlockMountingPosition))
    def test_block_mounting_position_enum_values(self, enum_value: BlockMountingPosition):
        """Test that BlockMountingPosition enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BlockMountingPosition = enum_value.value
        assert dataset.BlockMountingPosition == enum_value.value

    @pytest.mark.parametrize("enum_value", list(BeamDoseMeaning))
    def test_beam_dose_meaning_enum_values(self, enum_value: BeamDoseMeaning):
        """Test that BeamDoseMeaning enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BeamDoseMeaning = enum_value.value
        assert dataset.BeamDoseMeaning == enum_value.value

    # ==========================================
    # DOSE AND DVH ENUMS
    # ==========================================

    @pytest.mark.parametrize("enum_value", list(SpatialTransformOfDose))
    def test_spatial_transform_of_dose_enum_values(self, enum_value: SpatialTransformOfDose):
        """Test that SpatialTransformOfDose enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.SpatialTransformOfDose = enum_value.value
        assert dataset.SpatialTransformOfDose == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DoseSummationType))
    def test_dose_summation_type_enum_values(self, enum_value: DoseSummationType):
        """Test that DoseSummationType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DoseSummationType = enum_value.value
        assert dataset.DoseSummationType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(TissueHeterogeneityCorrection))
    def test_tissue_heterogeneity_correction_enum_values(self, enum_value: TissueHeterogeneityCorrection):
        """Test that TissueHeterogeneityCorrection enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.TissueHeterogeneityCorrection = enum_value.value
        assert dataset.TissueHeterogeneityCorrection == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DVHType))
    def test_dvh_type_enum_values(self, enum_value: DVHType):
        """Test that DVHType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DVHType = enum_value.value
        assert dataset.DVHType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DVHROIContributionType))
    def test_dvh_roi_contribution_type_enum_values(self, enum_value: DVHROIContributionType):
        """Test that DVHROIContributionType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DVHROIContributionType = enum_value.value
        assert dataset.DVHROIContributionType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DVHVolumeUnits))
    def test_dvh_volume_units_enum_values(self, enum_value: DVHVolumeUnits):
        """Test that DVHVolumeUnits enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DVHVolumeUnits = enum_value.value
        assert dataset.DVHVolumeUnits == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DoseReferenceStructureType))
    def test_dose_reference_structure_type_enum_values(self, enum_value: DoseReferenceStructureType):
        """Test that DoseReferenceStructureType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DoseReferenceStructureType = enum_value.value
        assert dataset.DoseReferenceStructureType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DoseReferenceType))
    def test_dose_reference_type_enum_values(self, enum_value: DoseReferenceType):
        """Test that DoseReferenceType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DoseReferenceType = enum_value.value
        assert dataset.DoseReferenceType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DoseValuePurpose))
    def test_dose_value_purpose_enum_values(self, enum_value: DoseValuePurpose):
        """Test that DoseValuePurpose enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DoseValuePurpose = enum_value.value
        assert dataset.DoseValuePurpose == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DoseValueInterpretation))
    def test_dose_value_interpretation_enum_values(self, enum_value: DoseValueInterpretation):
        """Test that DoseValueInterpretation enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DoseValueInterpretation = enum_value.value
        assert dataset.DoseValueInterpretation == enum_value.value

    @pytest.mark.parametrize("enum_value", list(DoseCalibrationConditionsVerifiedFlag))
    def test_dose_calibration_conditions_verified_flag_enum_values(self, enum_value: DoseCalibrationConditionsVerifiedFlag):
        """Test that DoseCalibrationConditionsVerifiedFlag enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.DoseCalibrationConditionsVerifiedFlag = enum_value.value
        assert dataset.DoseCalibrationConditionsVerifiedFlag == enum_value.value

    # ==========================================
    # ROI AND STRUCTURE ENUMS
    # ==========================================

    @pytest.mark.parametrize("enum_value", list(ROIGenerationAlgorithm))
    def test_roi_generation_algorithm_enum_values(self, enum_value: ROIGenerationAlgorithm):
        """Test that ROIGenerationAlgorithm enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ROIGenerationAlgorithm = enum_value.value
        assert dataset.ROIGenerationAlgorithm == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ContourGeometricType))
    def test_contour_geometric_type_enum_values(self, enum_value: ContourGeometricType):
        """Test that ContourGeometricType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ContourGeometricType = enum_value.value
        assert dataset.ContourGeometricType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTROIInterpretedType))
    def test_rt_roi_interpreted_type_enum_values(self, enum_value: RTROIInterpretedType):
        """Test that RTROIInterpretedType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTROIInterpretedType = enum_value.value
        assert dataset.RTROIInterpretedType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTROIRelationship))
    def test_rt_roi_relationship_enum_values(self, enum_value: RTROIRelationship):
        """Test that RTROIRelationship enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTROIRelationship = enum_value.value
        assert dataset.RTROIRelationship == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ROIPhysicalProperty))
    def test_roi_physical_property_enum_values(self, enum_value: ROIPhysicalProperty):
        """Test that ROIPhysicalProperty enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ROIPhysicalProperty = enum_value.value
        assert dataset.ROIPhysicalProperty == enum_value.value

    # ==========================================
    # PLAN AND PLANNING ENUMS
    # ==========================================

    @pytest.mark.parametrize("enum_value", list(PlanIntent))
    def test_plan_intent_enum_values(self, enum_value: PlanIntent):
        """Test that PlanIntent enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PlanIntent = enum_value.value
        assert dataset.PlanIntent == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTPlanGeometry))
    def test_rt_plan_geometry_enum_values(self, enum_value: RTPlanGeometry):
        """Test that RTPlanGeometry enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTPlanGeometry = enum_value.value
        assert dataset.RTPlanGeometry == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTPlanRelationship))
    def test_rt_plan_relationship_enum_values(self, enum_value: RTPlanRelationship):
        """Test that RTPlanRelationship enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTPlanRelationship = enum_value.value
        assert dataset.RTPlanRelationship == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTPlanStatus))
    def test_rt_plan_status_enum_values(self, enum_value: RTPlanStatus):
        """Test that RTPlanStatus enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTPlanStatus = enum_value.value
        assert dataset.RTPlanStatus == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RTPrescriptionStatus))
    def test_rt_prescription_status_enum_values(self, enum_value: RTPrescriptionStatus):
        """Test that RTPrescriptionStatus enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RTPrescriptionStatus = enum_value.value
        assert dataset.RTPrescriptionStatus == enum_value.value

    # ==========================================
    # SETUP AND PATIENT POSITIONING ENUMS
    # ==========================================

    @pytest.mark.parametrize("enum_value", list(FixationDeviceType))
    def test_fixation_device_type_enum_values(self, enum_value: FixationDeviceType):
        """Test that FixationDeviceType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.FixationDeviceType = enum_value.value
        assert dataset.FixationDeviceType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ShieldingDeviceType))
    def test_shielding_device_type_enum_values(self, enum_value: ShieldingDeviceType):
        """Test that ShieldingDeviceType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ShieldingDeviceType = enum_value.value
        assert dataset.ShieldingDeviceType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(SetupTechnique))
    def test_setup_technique_enum_values(self, enum_value: SetupTechnique):
        """Test that SetupTechnique enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.SetupTechnique = enum_value.value
        assert dataset.SetupTechnique == enum_value.value

    @pytest.mark.parametrize("enum_value", list(SetupDeviceType))
    def test_setup_device_type_enum_values(self, enum_value: SetupDeviceType):
        """Test that SetupDeviceType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.SetupDeviceType = enum_value.value
        assert dataset.SetupDeviceType == enum_value.value

    # ==========================================
    # RESPIRATORY AND MOTION ENUMS
    # ==========================================

    @pytest.mark.parametrize("enum_value", list(RespiratoryMotionCompensationTechnique))
    def test_respiratory_motion_compensation_technique_enum_values(self, enum_value: RespiratoryMotionCompensationTechnique):
        """Test that RespiratoryMotionCompensationTechnique enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RespiratoryMotionCompensationTechnique = enum_value.value
        assert dataset.RespiratoryMotionCompensationTechnique == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RespiratorySignalSource))
    def test_respiratory_signal_source_enum_values(self, enum_value: RespiratorySignalSource):
        """Test that RespiratorySignalSource enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RespiratorySignalSource = enum_value.value
        assert dataset.RespiratorySignalSource == enum_value.value

    # ==========================================
    # BRACHYTHERAPY ENUMS
    # ==========================================

    @pytest.mark.parametrize("enum_value", list(BrachyTreatmentTechnique))
    def test_brachy_treatment_technique_enum_values(self, enum_value: BrachyTreatmentTechnique):
        """Test that BrachyTreatmentTechnique enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BrachyTreatmentTechnique = enum_value.value
        assert dataset.BrachyTreatmentTechnique == enum_value.value

    @pytest.mark.parametrize("enum_value", list(BrachyTreatmentType))
    def test_brachy_treatment_type_enum_values(self, enum_value: BrachyTreatmentType):
        """Test that BrachyTreatmentType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BrachyTreatmentType = enum_value.value
        assert dataset.BrachyTreatmentType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ApplicationSetupType))
    def test_application_setup_type_enum_values(self, enum_value: ApplicationSetupType):
        """Test that ApplicationSetupType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ApplicationSetupType = enum_value.value
        assert dataset.ApplicationSetupType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(SourceType))
    def test_source_type_enum_values(self, enum_value: SourceType):
        """Test that SourceType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.SourceType = enum_value.value
        assert dataset.SourceType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(SourceMovementType))
    def test_source_movement_type_enum_values(self, enum_value: SourceMovementType):
        """Test that SourceMovementType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.SourceMovementType = enum_value.value
        assert dataset.SourceMovementType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(BrachyAccessoryDeviceType))
    def test_brachy_accessory_device_type_enum_values(self, enum_value: BrachyAccessoryDeviceType):
        """Test that BrachyAccessoryDeviceType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BrachyAccessoryDeviceType = enum_value.value
        assert dataset.BrachyAccessoryDeviceType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(SourceApplicatorType))
    def test_source_applicator_type_enum_values(self, enum_value: SourceApplicatorType):
        """Test that SourceApplicatorType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.SourceApplicatorType = enum_value.value
        assert dataset.SourceApplicatorType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(SourceStrengthUnits))
    def test_source_strength_units_enum_values(self, enum_value: SourceStrengthUnits):
        """Test that SourceStrengthUnits enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.SourceStrengthUnits = enum_value.value
        assert dataset.SourceStrengthUnits == enum_value.value

    # ==========================================
    # COVERAGE TESTS
    # ==========================================

    def test_conversion_type_coverage(self):
        """Test that all expected ConversionType values are present."""
        expected_values = {"DV", "DI", "DF", "WSD"}
        actual_values = {member.value for member in ConversionType}
        assert actual_values == expected_values

    def test_beam_type_coverage(self):
        """Test that all expected BeamType values are present."""
        expected_values = {"STATIC", "DYNAMIC"}
        actual_values = {member.value for member in BeamType}
        assert actual_values == expected_values

    def test_radiation_type_coverage(self):
        """Test that all expected RadiationType values are present."""
        expected_values = {"PHOTON", "ELECTRON", "NEUTRON", "PROTON", "ION"}
        actual_values = {member.value for member in RadiationType}
        assert actual_values == expected_values

    def test_pixel_intensity_relationship_sign_coverage(self):
        """Test that all expected PixelIntensityRelationshipSign values are present."""
        expected_values = {1, -1}
        actual_values = {member.value for member in PixelIntensityRelationshipSign}
        assert actual_values == expected_values

    def test_dvh_type_coverage(self):
        """Test that all expected DVHType values are present."""
        expected_values = {"DIFFERENTIAL", "CUMULATIVE", "NATURAL"}
        actual_values = {member.value for member in DVHType}
        assert actual_values == expected_values

    def test_roi_generation_algorithm_coverage(self):
        """Test that all expected ROIGenerationAlgorithm values are present."""
        expected_values = {"AUTOMATIC", "SEMIAUTOMATIC", "MANUAL"}
        actual_values = {member.value for member in ROIGenerationAlgorithm}
        assert actual_values == expected_values

    def test_plan_intent_coverage(self):
        """Test that all expected PlanIntent values are present."""
        expected_values = {
            "CURATIVE", "PALLIATIVE", "PROPHYLACTIC", "VERIFICATION",
            "MACHINE_QA", "RESEARCH", "SERVICE"
        }
        actual_values = {member.value for member in PlanIntent}
        assert actual_values == expected_values

    def test_rt_roi_interpreted_type_coverage(self):
        """Test that all expected RTROIInterpretedType values are present."""
        expected_values = {
            "EXTERNAL", "PTV", "CTV", "GTV", "TREATED_VOLUME", "IRRAD_VOLUME", "OAR",
            "BOLUS", "AVOIDANCE", "ORGAN", "MARKER", "REGISTRATION", "ISOCENTER",
            "CONTRAST_AGENT", "CAVITY", "BRACHY_CHANNEL", "BRACHY_ACCESSORY",
            "BRACHY_SRC_APP", "BRACHY_CHNL_SHLD", "SUPPORT", "FIXATION",
            "DOSE_REGION", "CONTROL", "DOSE_MEASUREMENT", "DEVICE"
        }
        actual_values = {member.value for member in RTROIInterpretedType}
        assert actual_values == expected_values

    def test_respiratory_motion_compensation_technique_coverage(self):
        """Test that all expected RespiratoryMotionCompensationTechnique values are present."""
        expected_values = {
            "NONE", "BREATH_HOLD", "REALTIME", "GATING", "TRACKING",
            "PHASE_ORDERING", "PHASE_RESCANNING", "RETROSPECTIVE", "CORRECTION", "UNKNOWN"
        }
        actual_values = {member.value for member in RespiratoryMotionCompensationTechnique}
        assert actual_values == expected_values

    # Additional coverage tests for key enums
    def test_application_setup_type_coverage(self):
        """Test that all expected ApplicationSetupType values are present."""
        expected_values = {
            "FLETCHER_SUIT", "DELCLOS", "BLOEDORN", "JOSLIN_FLYNN", "CHANDIGARH",
            "MANCHESTER", "HENSCHKE", "NASOPHARYNGEAL", "OESOPHAGEAL",
            "ENDOBRONCHIAL", "SYED_NEBLETT", "ENDORECTAL", "PERINEAL"
        }
        actual_values = {member.value for member in ApplicationSetupType}
        assert actual_values == expected_values

    def test_rt_plan_status_coverage(self):
        """Test that all expected RTPlanStatus values are present."""
        expected_values = {"RESEARCH", "CLINICAL"}
        actual_values = {member.value for member in RTPlanStatus}
        assert actual_values == expected_values

    def test_rt_prescription_status_coverage(self):
        """Test that all expected RTPrescriptionStatus values are present."""
        expected_values = {"DRAFT", "ACTIVE", "SUSPENDED", "RETIRED"}
        actual_values = {member.value for member in RTPrescriptionStatus}
        assert actual_values == expected_values