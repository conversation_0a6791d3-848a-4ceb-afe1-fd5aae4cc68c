"""Tests for Patient Orientation-related DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.patient_enums import (
    PatientOrientationCode,
    PatientOrientationModifierCode,
    PatientEquipmentRelationshipCode
)


class TestPatientOrientationEnums:
    """Test patient orientation enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(PatientOrientationCode))
    def test_patient_orientation_code_enum_values(self, enum_value: PatientOrientationCode):
        """Test that PatientOrientationCode enum values can be set to Dataset without error.
        
        Args:
            enum_value: PatientOrientationCode enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.PatientOrientationCode = enum_value.value
        assert dataset.PatientOrientationCode == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PatientOrientationModifierCode))
    def test_patient_orientation_modifier_code_enum_values(self, enum_value: PatientOrientationModifierCode):
        """Test that PatientOrientationModifierCode enum values can be set to Dataset without error.
        
        Args:
            enum_value: PatientOrientationModifierCode enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.PatientOrientationModifierCode = enum_value.value
        assert dataset.PatientOrientationModifierCode == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PatientEquipmentRelationshipCode))
    def test_patient_equipment_relationship_code_enum_values(self, enum_value: PatientEquipmentRelationshipCode):
        """Test that PatientEquipmentRelationshipCode enum values can be set to Dataset without error.
        
        Args:
            enum_value: PatientEquipmentRelationshipCode enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.PatientEquipmentRelationshipCode = enum_value.value
        assert dataset.PatientEquipmentRelationshipCode == enum_value.value

    # Coverage tests to ensure all expected values are present
    def test_patient_orientation_code_coverage(self):
        """Test that all expected PatientOrientationCode values are present."""
        # Test basic orientations
        assert PatientOrientationCode.RECUMBENT.value == "102538003"
        assert PatientOrientationCode.ERECT.value == "10904000"
        assert PatientOrientationCode.SITTING.value == "33586001"
        assert PatientOrientationCode.PRONE.value == "1240000"
        assert PatientOrientationCode.SUPINE.value == "40199007"
        assert PatientOrientationCode.LATERAL.value == "102536004"
        
        # Test decubitus positions
        assert PatientOrientationCode.LEFT_LATERAL_DECUBITUS.value == "102535000"
        assert PatientOrientationCode.RIGHT_LATERAL_DECUBITUS.value == "102537008"
        
        # Test Trendelenburg positions
        assert PatientOrientationCode.TRENDELENBURG.value == "34106002"
        assert PatientOrientationCode.REVERSE_TRENDELENBURG.value == "102539006"

    def test_patient_orientation_modifier_code_coverage(self):
        """Test that all expected PatientOrientationModifierCode values are present."""
        # Test position modifiers
        assert PatientOrientationModifierCode.SUPINE.value == "40199007"
        assert PatientOrientationModifierCode.PRONE.value == "1240000"
        assert PatientOrientationModifierCode.LEFT_LATERAL.value == "102535000"
        assert PatientOrientationModifierCode.RIGHT_LATERAL.value == "102537008"
        assert PatientOrientationModifierCode.SEMI_ERECT.value == "102540008"
        assert PatientOrientationModifierCode.JACKKNIFE.value == "102541007"
        
        # Test arm positions
        assert PatientOrientationModifierCode.ARMS_UP.value == "102542000"
        assert PatientOrientationModifierCode.ARMS_DOWN.value == "102543005"
        assert PatientOrientationModifierCode.ARMS_CROSSED.value == "102544004"
        
        # Test leg positions
        assert PatientOrientationModifierCode.LEGS_EXTENDED.value == "102545003"
        assert PatientOrientationModifierCode.LEGS_FLEXED.value == "102546002"

    def test_patient_equipment_relationship_code_coverage(self):
        """Test that all expected PatientEquipmentRelationshipCode values are present."""
        # Test entry directions
        assert PatientEquipmentRelationshipCode.HEAD_FIRST.value == "102540008"
        assert PatientEquipmentRelationshipCode.FEET_FIRST.value == "102541007"
        assert PatientEquipmentRelationshipCode.LEFT_FIRST.value == "102542000"
        assert PatientEquipmentRelationshipCode.RIGHT_FIRST.value == "102543005"
        
        # Test equipment orientations
        assert PatientEquipmentRelationshipCode.GANTRY_TILTED.value == "102544004"
        assert PatientEquipmentRelationshipCode.GANTRY_UPRIGHT.value == "102545003"
        assert PatientEquipmentRelationshipCode.TABLE_TILTED.value == "102546002"
        assert PatientEquipmentRelationshipCode.TABLE_HORIZONTAL.value == "102547006"
        
        # Test positioning aids
        assert PatientEquipmentRelationshipCode.IMMOBILIZATION_DEVICE.value == "102548001"
        assert PatientEquipmentRelationshipCode.POSITIONING_AID.value == "102549009"
        assert PatientEquipmentRelationshipCode.CONTRAST_AGENT.value == "102550009"

    # Test set coverage for completeness
    def test_patient_orientation_code_complete_coverage(self):
        """Test that PatientOrientationCode contains expected number of values."""
        expected_values = {
            "102538003", "10904000", "33586001", "1240000", "40199007", "102536004",
            "102535000", "102537008", "34106002", "102539006"
        }
        actual_values = {member.value for member in PatientOrientationCode}
        assert actual_values == expected_values

    def test_patient_orientation_modifier_code_complete_coverage(self):
        """Test that PatientOrientationModifierCode contains expected number of values."""
        expected_values = {
            "40199007", "1240000", "102535000", "102537008", "102540008", "102541007",
            "102542000", "102543005", "102544004", "102545003", "102546002"
        }
        actual_values = {member.value for member in PatientOrientationModifierCode}
        assert actual_values == expected_values

    def test_patient_equipment_relationship_code_complete_coverage(self):
        """Test that PatientEquipmentRelationshipCode contains expected number of values."""
        expected_values = {
            "102540008", "102541007", "102542000", "102543005", "102544004", "102545003",
            "102546002", "102547006", "102548001", "102549009", "102550009"
        }
        actual_values = {member.value for member in PatientEquipmentRelationshipCode}
        assert actual_values == expected_values