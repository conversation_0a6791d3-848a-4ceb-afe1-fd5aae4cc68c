"""Tests for Specimen Module DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.specimen_enums import ContainerComponentMaterial


class TestSpecimenEnums:
    """Test specimen enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(ContainerComponentMaterial))
    def test_container_component_material_enum_values(self, enum_value: ContainerComponentMaterial):
        """Test that ContainerComponentMaterial enum values can be set to Dataset.ContainerComponentMaterial without error.
        
        Args:
            enum_value: ContainerComponentMaterial enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.ContainerComponentMaterial = enum_value.value
        assert dataset.ContainerComponentMaterial == enum_value.value

    def test_container_component_material_coverage(self):
        """Test that all expected ContainerComponentMaterial values are present."""
        expected_values = {"GLASS", "PLASTIC", "METAL"}
        actual_values = {member.value for member in ContainerComponentMaterial}
        assert actual_values == expected_values