"""Unit tests for common enums compatibility with pydicom Dataset.

This module tests that common enum values can be properly set
as attributes on pydicom Dataset objects without errors.
"""

from pydicom import Dataset
from pyrt_dicom.enums.common_enums import (
    SyntheticData,
    SOPInstanceStatus,
    QueryRetrieveView,
    ContentQualification,
    LongitudinalTemporalInformationModified,
    OriginalSpecializedEquipment,
    SpatialLocationsPreserved,
    PositionReferenceIndicator
)


class TestSyntheticDataEnum:
    """Test SyntheticData enum compatibility with pydicom Dataset."""
    
    def test_synthetic_data_values_exist(self):
        """Test that all expected SyntheticData values exist."""
        assert SyntheticData.YES.value == "YES"
        assert SyntheticData.NO.value == "NO"
    
    def test_synthetic_data_yes_can_be_set_to_dataset(self):
        """Test that SyntheticData.YES can be set to Dataset without error."""
        ds = Dataset()
        ds.SyntheticData = SyntheticData.YES.value
        
        assert ds.SyntheticData == "YES"
        assert hasattr(ds, 'SyntheticData')
    
    def test_synthetic_data_no_can_be_set_to_dataset(self):
        """Test that SyntheticData.NO can be set to Dataset without error."""
        ds = Dataset()
        ds.SyntheticData = SyntheticData.NO.value
        
        assert ds.SyntheticData == "NO"
        assert hasattr(ds, 'SyntheticData')
    
    def test_all_synthetic_data_values_can_be_set_to_dataset(self):
        """Test that all SyntheticData values can be set to Dataset without error."""
        for synthetic_data in SyntheticData:
            ds = Dataset()
            # Should not raise any exception
            ds.SyntheticData = synthetic_data.value
            
            # Verify the value was set correctly
            assert ds.SyntheticData == synthetic_data.value
            assert hasattr(ds, 'SyntheticData')
    
    def test_synthetic_data_enum_directly_can_be_set_to_dataset(self):
        """Test that SyntheticData enum instances can be used directly with Dataset."""
        for synthetic_data in SyntheticData:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.SyntheticData = synthetic_data
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.SyntheticData).endswith(synthetic_data.value)
            assert hasattr(ds, 'SyntheticData')
    
    def test_synthetic_data_with_dicom_tag(self):
        """Test that SyntheticData can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # First set the element using attribute notation
        ds.SyntheticData = SyntheticData.YES.value
        
        # Then verify it can be accessed via tag notation
        assert ds[0x0008, 0x001C].value == "YES"
        assert ds.SyntheticData == "YES"
        
        # Test setting via DataElement creation
        ds2 = Dataset()
        ds2[0x0008, 0x001C] = DataElement(0x0008001C, 'CS', SyntheticData.NO.value)
        assert ds2.SyntheticData == "NO"


class TestSOPInstanceStatusEnum:
    """Test SOPInstanceStatus enum compatibility with pydicom Dataset."""
    
    def test_sop_instance_status_values_exist(self):
        """Test that all expected SOPInstanceStatus values exist."""
        assert SOPInstanceStatus.NS.value == "NS"
        assert SOPInstanceStatus.AO.value == "AO"
        assert SOPInstanceStatus.AC.value == "AC"
    
    def test_sop_instance_status_ns_can_be_set_to_dataset(self):
        """Test that SOPInstanceStatus.NS can be set to Dataset without error."""
        ds = Dataset()
        ds.SOPInstanceStatus = SOPInstanceStatus.NS.value
        
        assert ds.SOPInstanceStatus == "NS"
        assert hasattr(ds, 'SOPInstanceStatus')
    
    def test_sop_instance_status_ao_can_be_set_to_dataset(self):
        """Test that SOPInstanceStatus.AO can be set to Dataset without error."""
        ds = Dataset()
        ds.SOPInstanceStatus = SOPInstanceStatus.AO.value
        
        assert ds.SOPInstanceStatus == "AO"
        assert hasattr(ds, 'SOPInstanceStatus')
    
    def test_sop_instance_status_ac_can_be_set_to_dataset(self):
        """Test that SOPInstanceStatus.AC can be set to Dataset without error."""
        ds = Dataset()
        ds.SOPInstanceStatus = SOPInstanceStatus.AC.value
        
        assert ds.SOPInstanceStatus == "AC"
        assert hasattr(ds, 'SOPInstanceStatus')
    
    def test_all_sop_instance_status_values_can_be_set_to_dataset(self):
        """Test that all SOPInstanceStatus values can be set to Dataset without error."""
        for status in SOPInstanceStatus:
            ds = Dataset()
            # Should not raise any exception
            ds.SOPInstanceStatus = status.value
            
            # Verify the value was set correctly
            assert ds.SOPInstanceStatus == status.value
            assert hasattr(ds, 'SOPInstanceStatus')
    
    def test_sop_instance_status_enum_directly_can_be_set_to_dataset(self):
        """Test that SOPInstanceStatus enum instances can be used directly with Dataset."""
        for status in SOPInstanceStatus:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.SOPInstanceStatus = status
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.SOPInstanceStatus).endswith(status.value)
            assert hasattr(ds, 'SOPInstanceStatus')
    
    def test_sop_instance_status_with_dicom_tag(self):
        """Test that SOPInstanceStatus can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # Test setting via DataElement creation (SOPInstanceStatus not in standard dict)
        ds[0x0100, 0x0410] = DataElement(0x01000410, 'CS', SOPInstanceStatus.AO.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0100, 0x0410].value == "AO"
        
        # Test another value via DataElement creation
        ds2 = Dataset()
        ds2[0x0100, 0x0410] = DataElement(0x01000410, 'CS', SOPInstanceStatus.NS.value)
        assert ds2[0x0100, 0x0410].value == "NS"


class TestQueryRetrieveViewEnum:
    """Test QueryRetrieveView enum compatibility with pydicom Dataset."""
    
    def test_query_retrieve_view_values_exist(self):
        """Test that all expected QueryRetrieveView values exist."""
        assert QueryRetrieveView.CLASSIC.value == "CLASSIC"
        assert QueryRetrieveView.ENHANCED.value == "ENHANCED"
    
    def test_query_retrieve_view_classic_can_be_set_to_dataset(self):
        """Test that QueryRetrieveView.CLASSIC can be set to Dataset without error."""
        ds = Dataset()
        ds.QueryRetrieveView = QueryRetrieveView.CLASSIC.value
        
        assert ds.QueryRetrieveView == "CLASSIC"
        assert hasattr(ds, 'QueryRetrieveView')
    
    def test_query_retrieve_view_enhanced_can_be_set_to_dataset(self):
        """Test that QueryRetrieveView.ENHANCED can be set to Dataset without error."""
        ds = Dataset()
        ds.QueryRetrieveView = QueryRetrieveView.ENHANCED.value
        
        assert ds.QueryRetrieveView == "ENHANCED"
        assert hasattr(ds, 'QueryRetrieveView')
    
    def test_all_query_retrieve_view_values_can_be_set_to_dataset(self):
        """Test that all QueryRetrieveView values can be set to Dataset without error."""
        for view in QueryRetrieveView:
            ds = Dataset()
            # Should not raise any exception
            ds.QueryRetrieveView = view.value
            
            # Verify the value was set correctly
            assert ds.QueryRetrieveView == view.value
            assert hasattr(ds, 'QueryRetrieveView')
    
    def test_query_retrieve_view_enum_directly_can_be_set_to_dataset(self):
        """Test that QueryRetrieveView enum instances can be used directly with Dataset."""
        for view in QueryRetrieveView:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.QueryRetrieveView = view
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.QueryRetrieveView).endswith(view.value)
            assert hasattr(ds, 'QueryRetrieveView')
    
    def test_query_retrieve_view_with_dicom_tag(self):
        """Test that QueryRetrieveView can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # Test setting via DataElement creation (QueryRetrieveView not in standard dict)
        ds[0x0008, 0x0053] = DataElement(0x00080053, 'CS', QueryRetrieveView.ENHANCED.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0008, 0x0053].value == "ENHANCED"
        
        # Test another value via DataElement creation
        ds2 = Dataset()
        ds2[0x0008, 0x0053] = DataElement(0x00080053, 'CS', QueryRetrieveView.CLASSIC.value)
        assert ds2[0x0008, 0x0053].value == "CLASSIC"


class TestContentQualificationEnum:
    """Test ContentQualification enum compatibility with pydicom Dataset."""
    
    def test_content_qualification_values_exist(self):
        """Test that all expected ContentQualification values exist."""
        assert ContentQualification.PRODUCT.value == "PRODUCT"
        assert ContentQualification.RESEARCH.value == "RESEARCH"
        assert ContentQualification.SERVICE.value == "SERVICE"
    
    def test_content_qualification_product_can_be_set_to_dataset(self):
        """Test that ContentQualification.PRODUCT can be set to Dataset without error."""
        ds = Dataset()
        ds.ContentQualification = ContentQualification.PRODUCT.value
        
        assert ds.ContentQualification == "PRODUCT"
        assert hasattr(ds, 'ContentQualification')
    
    def test_content_qualification_research_can_be_set_to_dataset(self):
        """Test that ContentQualification.RESEARCH can be set to Dataset without error."""
        ds = Dataset()
        ds.ContentQualification = ContentQualification.RESEARCH.value
        
        assert ds.ContentQualification == "RESEARCH"
        assert hasattr(ds, 'ContentQualification')
    
    def test_content_qualification_service_can_be_set_to_dataset(self):
        """Test that ContentQualification.SERVICE can be set to Dataset without error."""
        ds = Dataset()
        ds.ContentQualification = ContentQualification.SERVICE.value
        
        assert ds.ContentQualification == "SERVICE"
        assert hasattr(ds, 'ContentQualification')
    
    def test_all_content_qualification_values_can_be_set_to_dataset(self):
        """Test that all ContentQualification values can be set to Dataset without error."""
        for qualification in ContentQualification:
            ds = Dataset()
            # Should not raise any exception
            ds.ContentQualification = qualification.value
            
            # Verify the value was set correctly
            assert ds.ContentQualification == qualification.value
            assert hasattr(ds, 'ContentQualification')
    
    def test_content_qualification_enum_directly_can_be_set_to_dataset(self):
        """Test that ContentQualification enum instances can be used directly with Dataset."""
        for qualification in ContentQualification:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.ContentQualification = qualification
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.ContentQualification).endswith(qualification.value)
            assert hasattr(ds, 'ContentQualification')
    
    def test_content_qualification_with_dicom_tag(self):
        """Test that ContentQualification can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # First set the element using attribute notation
        ds.ContentQualification = ContentQualification.RESEARCH.value
        
        # Then verify it can be accessed via tag notation
        assert ds[0x0018, 0x9004].value == "RESEARCH"
        assert ds.ContentQualification == "RESEARCH"
        
        # Test setting via DataElement creation
        ds2 = Dataset()
        ds2[0x0018, 0x9004] = DataElement(0x00189004, 'CS', ContentQualification.PRODUCT.value)
        assert ds2.ContentQualification == "PRODUCT"


class TestLongitudinalTemporalInformationModifiedEnum:
    """Test LongitudinalTemporalInformationModified enum compatibility with pydicom Dataset."""
    
    def test_longitudinal_temporal_information_modified_values_exist(self):
        """Test that all expected LongitudinalTemporalInformationModified values exist."""
        assert LongitudinalTemporalInformationModified.UNMODIFIED.value == "UNMODIFIED"
        assert LongitudinalTemporalInformationModified.MODIFIED.value == "MODIFIED"
        assert LongitudinalTemporalInformationModified.REMOVED.value == "REMOVED"
    
    def test_longitudinal_temporal_information_modified_unmodified_can_be_set_to_dataset(self):
        """Test that LongitudinalTemporalInformationModified.UNMODIFIED can be set to Dataset without error."""
        ds = Dataset()
        ds.LongitudinalTemporalInformationModified = LongitudinalTemporalInformationModified.UNMODIFIED.value
        
        assert ds.LongitudinalTemporalInformationModified == "UNMODIFIED"
        assert hasattr(ds, 'LongitudinalTemporalInformationModified')
    
    def test_longitudinal_temporal_information_modified_modified_can_be_set_to_dataset(self):
        """Test that LongitudinalTemporalInformationModified.MODIFIED can be set to Dataset without error."""
        ds = Dataset()
        ds.LongitudinalTemporalInformationModified = LongitudinalTemporalInformationModified.MODIFIED.value
        
        assert ds.LongitudinalTemporalInformationModified == "MODIFIED"
        assert hasattr(ds, 'LongitudinalTemporalInformationModified')
    
    def test_longitudinal_temporal_information_modified_removed_can_be_set_to_dataset(self):
        """Test that LongitudinalTemporalInformationModified.REMOVED can be set to Dataset without error."""
        ds = Dataset()
        ds.LongitudinalTemporalInformationModified = LongitudinalTemporalInformationModified.REMOVED.value
        
        assert ds.LongitudinalTemporalInformationModified == "REMOVED"
        assert hasattr(ds, 'LongitudinalTemporalInformationModified')
    
    def test_all_longitudinal_temporal_information_modified_values_can_be_set_to_dataset(self):
        """Test that all LongitudinalTemporalInformationModified values can be set to Dataset without error."""
        for info in LongitudinalTemporalInformationModified:
            ds = Dataset()
            # Should not raise any exception
            ds.LongitudinalTemporalInformationModified = info.value
            
            # Verify the value was set correctly
            assert ds.LongitudinalTemporalInformationModified == info.value
            assert hasattr(ds, 'LongitudinalTemporalInformationModified')
    
    def test_longitudinal_temporal_information_modified_enum_directly_can_be_set_to_dataset(self):
        """Test that LongitudinalTemporalInformationModified enum instances can be used directly with Dataset."""
        for info in LongitudinalTemporalInformationModified:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.LongitudinalTemporalInformationModified = info
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.LongitudinalTemporalInformationModified).endswith(info.value)
            assert hasattr(ds, 'LongitudinalTemporalInformationModified')
    
    def test_longitudinal_temporal_information_modified_with_dicom_tag(self):
        """Test that LongitudinalTemporalInformationModified can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # Test setting via DataElement creation (LongitudinalTemporalInformationModified not in standard dict)
        ds[0x0028, 0x0303] = DataElement(0x00280303, 'CS', LongitudinalTemporalInformationModified.MODIFIED.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0028, 0x0303].value == "MODIFIED"
        
        # Test another value via DataElement creation
        ds2 = Dataset()
        ds2[0x0028, 0x0303] = DataElement(0x00280303, 'CS', LongitudinalTemporalInformationModified.UNMODIFIED.value)
        assert ds2[0x0028, 0x0303].value == "UNMODIFIED"


class TestSpatialLocationsPreservedEnum:
    """Test SpatialLocationsPreserved enum compatibility with pydicom Dataset."""
    
    def test_spatial_locations_preserved_values_exist(self):
        """Test that all expected SpatialLocationsPreserved values exist."""
        assert SpatialLocationsPreserved.YES.value == "YES"
        assert SpatialLocationsPreserved.NO.value == "NO"
        assert SpatialLocationsPreserved.REORIENTED_ONLY.value == "REORIENTED_ONLY"
    
    def test_spatial_locations_preserved_yes_can_be_set_to_dataset(self):
        """Test that SpatialLocationsPreserved.YES can be set to Dataset without error."""
        ds = Dataset()
        ds.SpatialLocationsPreserved = SpatialLocationsPreserved.YES.value
        
        assert ds.SpatialLocationsPreserved == "YES"
        assert hasattr(ds, 'SpatialLocationsPreserved')
    
    def test_spatial_locations_preserved_no_can_be_set_to_dataset(self):
        """Test that SpatialLocationsPreserved.NO can be set to Dataset without error."""
        ds = Dataset()
        ds.SpatialLocationsPreserved = SpatialLocationsPreserved.NO.value
        
        assert ds.SpatialLocationsPreserved == "NO"
        assert hasattr(ds, 'SpatialLocationsPreserved')
    
    def test_spatial_locations_preserved_reoriented_only_can_be_set_to_dataset(self):
        """Test that SpatialLocationsPreserved.REORIENTED_ONLY can be set to Dataset without error."""
        ds = Dataset()
        ds.SpatialLocationsPreserved = SpatialLocationsPreserved.REORIENTED_ONLY.value
        
        assert ds.SpatialLocationsPreserved == "REORIENTED_ONLY"
        assert hasattr(ds, 'SpatialLocationsPreserved')
    
    def test_all_spatial_locations_preserved_values_can_be_set_to_dataset(self):
        """Test that all SpatialLocationsPreserved values can be set to Dataset without error."""
        for location in SpatialLocationsPreserved:
            ds = Dataset()
            # Should not raise any exception
            ds.SpatialLocationsPreserved = location.value
            
            # Verify the value was set correctly
            assert ds.SpatialLocationsPreserved == location.value
            assert hasattr(ds, 'SpatialLocationsPreserved')
    
    def test_spatial_locations_preserved_enum_directly_can_be_set_to_dataset(self):
        """Test that SpatialLocationsPreserved enum instances can be used directly with Dataset."""
        for location in SpatialLocationsPreserved:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.SpatialLocationsPreserved = location
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.SpatialLocationsPreserved).endswith(location.value)
            assert hasattr(ds, 'SpatialLocationsPreserved')
    
    def test_spatial_locations_preserved_with_dicom_tag(self):
        """Test that SpatialLocationsPreserved can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # Test setting via DataElement creation (SpatialLocationsPreserved not in standard dict)
        ds[0x0028, 0x135A] = DataElement(0x0028135A, 'CS', SpatialLocationsPreserved.REORIENTED_ONLY.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0028, 0x135A].value == "REORIENTED_ONLY"
        
        # Test another value via DataElement creation
        ds2 = Dataset()
        ds2[0x0028, 0x135A] = DataElement(0x0028135A, 'CS', SpatialLocationsPreserved.YES.value)
        assert ds2[0x0028, 0x135A].value == "YES"


class TestOriginalSpecializedEquipmentEnum:
    """Test OriginalSpecializedEquipment enum compatibility with pydicom Dataset."""
    
    def test_original_specialized_equipment_values_exist(self):
        """Test that all expected OriginalSpecializedEquipment values exist."""
        assert OriginalSpecializedEquipment.YES.value == "YES"
        assert OriginalSpecializedEquipment.NO.value == "NO"
    
    def test_original_specialized_equipment_yes_can_be_set_to_dataset(self):
        """Test that OriginalSpecializedEquipment.YES can be set to Dataset without error."""
        ds = Dataset()
        ds.OriginalSpecializedEquipment = OriginalSpecializedEquipment.YES.value
        
        assert ds.OriginalSpecializedEquipment == "YES"
        assert hasattr(ds, 'OriginalSpecializedEquipment')
    
    def test_original_specialized_equipment_no_can_be_set_to_dataset(self):
        """Test that OriginalSpecializedEquipment.NO can be set to Dataset without error."""
        ds = Dataset()
        ds.OriginalSpecializedEquipment = OriginalSpecializedEquipment.NO.value
        
        assert ds.OriginalSpecializedEquipment == "NO"
        assert hasattr(ds, 'OriginalSpecializedEquipment')
    
    def test_all_original_specialized_equipment_values_can_be_set_to_dataset(self):
        """Test that all OriginalSpecializedEquipment values can be set to Dataset without error."""
        for equipment in OriginalSpecializedEquipment:
            ds = Dataset()
            # Should not raise any exception
            ds.OriginalSpecializedEquipment = equipment.value
            
            # Verify the value was set correctly
            assert ds.OriginalSpecializedEquipment == equipment.value
            assert hasattr(ds, 'OriginalSpecializedEquipment')
    
    def test_original_specialized_equipment_enum_directly_can_be_set_to_dataset(self):
        """Test that OriginalSpecializedEquipment enum instances can be used directly with Dataset."""
        for equipment in OriginalSpecializedEquipment:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.OriginalSpecializedEquipment = equipment
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.OriginalSpecializedEquipment).endswith(equipment.value)
            assert hasattr(ds, 'OriginalSpecializedEquipment')
    
    def test_original_specialized_equipment_with_dicom_tag(self):
        """Test that OriginalSpecializedEquipment can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # Test setting via DataElement creation (OriginalSpecializedEquipment not in standard dict)
        ds[0x0008, 0x0103] = DataElement(0x00080103, 'CS', OriginalSpecializedEquipment.YES.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0008, 0x0103].value == "YES"
        
        # Test another value via DataElement creation
        ds2 = Dataset()
        ds2[0x0008, 0x0103] = DataElement(0x00080103, 'CS', OriginalSpecializedEquipment.NO.value)
        assert ds2[0x0008, 0x0103].value == "NO"


class TestPositionReferenceIndicatorEnum:
    """Test PositionReferenceIndicator enum compatibility with pydicom Dataset."""
    
    def test_position_reference_indicator_values_exist(self):
        """Test that all expected PositionReferenceIndicator values exist."""
        # Test anatomical reference points
        assert PositionReferenceIndicator.ILIAC_CREST.value == "ILIAC_CREST"
        assert PositionReferenceIndicator.ORBITAL_MEDIAL.value == "ORBITAL_MEDIAL"
        assert PositionReferenceIndicator.STERNAL_NOTCH.value == "STERNAL_NOTCH"
        assert PositionReferenceIndicator.SYMPHYSIS_PUBIS.value == "SYMPHYSIS_PUBIS"
        assert PositionReferenceIndicator.XIPHOID.value == "XIPHOID"
        assert PositionReferenceIndicator.LOWER_COSTAL_MARGIN.value == "LOWER_COSTAL_MARGIN"
        assert PositionReferenceIndicator.EXTERNAL_AUDITORY_MEATUS.value == "EXTERNAL_AUDITORY_MEATUS"
        
        # Test slide-based reference
        assert PositionReferenceIndicator.SLIDE_CORNER.value == "SLIDE_CORNER"
        
        # Test corneal reference points
        assert PositionReferenceIndicator.CORNEAL_VERTEX_R.value == "CORNEAL_VERTEX_R"
        assert PositionReferenceIndicator.CORNEAL_VERTEX_L.value == "CORNEAL_VERTEX_L"
    
    def test_all_position_reference_indicator_values_can_be_set_to_dataset(self):
        """Test that all PositionReferenceIndicator values can be set to Dataset without error."""
        for indicator in PositionReferenceIndicator:
            ds = Dataset()
            # Should not raise any exception
            ds.PositionReferenceIndicator = indicator.value
            
            # Verify the value was set correctly
            assert ds.PositionReferenceIndicator == indicator.value
            assert hasattr(ds, 'PositionReferenceIndicator')
    
    def test_position_reference_indicator_enum_directly_can_be_set_to_dataset(self):
        """Test that PositionReferenceIndicator enum instances can be used directly with Dataset."""
        for indicator in PositionReferenceIndicator:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.PositionReferenceIndicator = indicator
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.PositionReferenceIndicator).endswith(indicator.value)
            assert hasattr(ds, 'PositionReferenceIndicator')
    
    def test_position_reference_indicator_with_dicom_tag(self):
        """Test that PositionReferenceIndicator can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # Test setting via DataElement creation (tag 0020,1040 for Position Reference Indicator)
        ds[0x0020, 0x1040] = DataElement(0x00201040, 'LO', PositionReferenceIndicator.ILIAC_CREST.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0020, 0x1040].value == "ILIAC_CREST"
        
        # Test another value via DataElement creation
        ds2 = Dataset()
        ds2[0x0020, 0x1040] = DataElement(0x00201040, 'LO', PositionReferenceIndicator.STERNAL_NOTCH.value)
        assert ds2[0x0020, 0x1040].value == "STERNAL_NOTCH"