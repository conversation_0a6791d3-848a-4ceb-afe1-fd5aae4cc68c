"""Tests for Series-related DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.series_enums import Modality, Laterality, PatientPosition, AnatomicalOrientationType


class TestSeriesEnums:
    """Test series enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(Modality))
    def test_modality_enum_values(self, enum_value: Modality):
        """Test that Modality enum values can be set to Dataset.Modality without error.
        
        Args:
            enum_value: Modality enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.Modality = enum_value.value
        assert dataset.Modality == enum_value.value

    @pytest.mark.parametrize("enum_value", list(Laterality))
    def test_laterality_enum_values(self, enum_value: Laterality):
        """Test that Laterality enum values can be set to Dataset.Laterality without error.
        
        Args:
            enum_value: Laterality enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.Laterality = enum_value.value
        assert dataset.Laterality == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PatientPosition))
    def test_patient_position_enum_values(self, enum_value: PatientPosition):
        """Test that PatientPosition enum values can be set to Dataset.PatientPosition without error.
        
        Args:
            enum_value: PatientPosition enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.PatientPosition = enum_value.value
        assert dataset.PatientPosition == enum_value.value

    @pytest.mark.parametrize("enum_value", list(AnatomicalOrientationType))
    def test_anatomical_orientation_type_enum_values(self, enum_value: AnatomicalOrientationType):
        """Test that AnatomicalOrientationType enum values can be set to Dataset.AnatomicalOrientationType without error.
        
        Args:
            enum_value: AnatomicalOrientationType enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.AnatomicalOrientationType = enum_value.value
        assert dataset.AnatomicalOrientationType == enum_value.value

    def test_modality_coverage(self):
        """Test that all expected Modality values are present."""
        expected_values = {
            "CT", "MR", "US", "XA", "RF", "DX", "CR", "MG", "NM", "PT",
            "RTIMAGE", "RTDOSE", "RTSTRUCT", "RTPLAN", "RTRECORD", 
            "SR", "DOC", "PR", "KO", "SEG", "REG", "PLAN", "OT"
        }
        actual_values = {member.value for member in Modality}
        assert actual_values == expected_values

    def test_laterality_coverage(self):
        """Test that all expected Laterality values are present."""
        expected_values = {"R", "L"}
        actual_values = {member.value for member in Laterality}
        assert actual_values == expected_values

    def test_patient_position_coverage(self):
        """Test that all expected PatientPosition values are present."""
        expected_values = {
            "HFP", "HFS", "HFDR", "HFDL", "HFV", "HFI",
            "FFP", "FFS", "FFDR", "FFDL", "FFV", "FFI",
            "LFP", "LFS", "LFDR", "LFDL",
            "RFP", "RFS", "RFDR", "RFDL",
            "AFP", "AFS", "AFDR", "AFDL",
            "PFP", "PFS", "PFDR", "PFDL"
        }
        actual_values = {member.value for member in PatientPosition}
        assert actual_values == expected_values

    def test_anatomical_orientation_type_coverage(self):
        """Test that all expected AnatomicalOrientationType values are present."""
        expected_values = {"BIPED", "QUADRUPED"}
        actual_values = {member.value for member in AnatomicalOrientationType}
        assert actual_values == expected_values