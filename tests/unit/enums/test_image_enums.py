"""Tests for Image-related DICOM enumerations."""

import pytest
from pydicom import Dataset
from pydicom.dataelem import DataElement

from pyrt_dicom.enums.image_enums import (
    QualityControlImage,
    BurnedInAnnotation,
    RecognizableVisualFeatures,
    LossyImageCompression,
    PresentationLUTShape,
    ImageLaterality,
    PhotometricInterpretation,
    PlanarConfiguration,
    PixelRepresentation,
    StereoPairsPresent,
    PreferredPlaybackSequencing,
    ChannelMode,
    OverlayType,
    OverlaySubtype,
    LossyImageCompressionMethod,
    ImageType,
    ModalityLutType,
    VoiLutFunction,
    FrameType,
    ContrastBolusAgent,
    ContrastBolusIngredient,
    MultiEnergyCTAcquisition,
    RotationDirection,
    ExposureModulationType,
    CTImageTypeValue1,
    CTImageTypeValue2,
    CTImageTypeValue3,
    CTImageTypeValue4,
    CTSamplesPerPixel,
    CTBitsAllocated,
    CTBitsStored,
    MultiEnergySourceTechnique,
    MultiEnergyDetectorType,
    RescaleType,
    FilterMaterial,
    ScanOptions,
    FilterType
)


class TestImageEnums:
    """Test image enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(QualityControlImage))
    def test_quality_control_image_enum_values(self, enum_value: QualityControlImage):
        """Test that QualityControlImage enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.QualityControlImage = enum_value.value
        assert dataset.QualityControlImage == enum_value.value

    @pytest.mark.parametrize("enum_value", list(BurnedInAnnotation))
    def test_burned_in_annotation_enum_values(self, enum_value: BurnedInAnnotation):
        """Test that BurnedInAnnotation enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.BurnedInAnnotation = enum_value.value
        assert dataset.BurnedInAnnotation == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RecognizableVisualFeatures))
    def test_recognizable_visual_features_enum_values(self, enum_value: RecognizableVisualFeatures):
        """Test that RecognizableVisualFeatures enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RecognizableVisualFeatures = enum_value.value
        assert dataset.RecognizableVisualFeatures == enum_value.value

    @pytest.mark.parametrize("enum_value", list(LossyImageCompression))
    def test_lossy_image_compression_enum_values(self, enum_value: LossyImageCompression):
        """Test that LossyImageCompression enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.LossyImageCompression = enum_value.value
        assert dataset.LossyImageCompression == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PresentationLUTShape))
    def test_presentation_lut_shape_enum_values(self, enum_value: PresentationLUTShape):
        """Test that PresentationLUTShape enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PresentationLUTShape = enum_value.value
        assert dataset.PresentationLUTShape == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ImageLaterality))
    def test_image_laterality_enum_values(self, enum_value: ImageLaterality):
        """Test that ImageLaterality enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ImageLaterality = enum_value.value
        assert dataset.ImageLaterality == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PhotometricInterpretation))
    def test_photometric_interpretation_enum_values(self, enum_value: PhotometricInterpretation):
        """Test that PhotometricInterpretation enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PhotometricInterpretation = enum_value.value
        assert dataset.PhotometricInterpretation == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PlanarConfiguration))
    def test_planar_configuration_enum_values(self, enum_value: PlanarConfiguration):
        """Test that PlanarConfiguration enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PlanarConfiguration = enum_value.value
        assert dataset.PlanarConfiguration == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PixelRepresentation))
    def test_pixel_representation_enum_values(self, enum_value: PixelRepresentation):
        """Test that PixelRepresentation enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PixelRepresentation = enum_value.value
        assert dataset.PixelRepresentation == enum_value.value

    @pytest.mark.parametrize("enum_value", list(StereoPairsPresent))
    def test_stereo_pairs_present_enum_values(self, enum_value: StereoPairsPresent):
        """Test that StereoPairsPresent enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.StereoPairsPresent = enum_value.value
        assert dataset.StereoPairsPresent == enum_value.value

    @pytest.mark.parametrize("enum_value", list(PreferredPlaybackSequencing))
    def test_preferred_playback_sequencing_enum_values(self, enum_value: PreferredPlaybackSequencing):
        """Test that PreferredPlaybackSequencing enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.PreferredPlaybackSequencing = enum_value.value
        assert dataset.PreferredPlaybackSequencing == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ChannelMode))
    def test_channel_mode_enum_values(self, enum_value: ChannelMode):
        """Test that ChannelMode enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ChannelMode = enum_value.value
        assert dataset.ChannelMode == enum_value.value

    @pytest.mark.parametrize("enum_value", list(OverlayType))
    def test_overlay_type_enum_values(self, enum_value: OverlayType):
        """Test that OverlayType enum values can be set to Dataset using add_new method."""
        dataset = Dataset()
        # OverlayType is a repeating group element, use add_new method with appropriate tag
        dataset.add_new(0x60000040, 'CS', enum_value.value)  # Overlay Type tag pattern
        # Verify the value was stored correctly
        assert dataset[0x60000040].value == enum_value.value

    @pytest.mark.parametrize("enum_value", list(OverlaySubtype))
    def test_overlay_subtype_enum_values(self, enum_value: OverlaySubtype):
        """Test that OverlaySubtype enum values can be set to Dataset using add_new method."""
        dataset = Dataset()
        # OverlaySubtype is a repeating group element, use add_new method with appropriate tag
        dataset.add_new(0x60000045, 'CS', enum_value.value)  # Overlay Subtype tag pattern
        # Verify the value was stored correctly
        assert dataset[0x60000045].value == enum_value.value

    @pytest.mark.parametrize("enum_value", list(LossyImageCompressionMethod))
    def test_lossy_image_compression_method_enum_values(self, enum_value: LossyImageCompressionMethod):
        """Test that LossyImageCompressionMethod enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.LossyImageCompressionMethod = enum_value.value
        assert dataset.LossyImageCompressionMethod == enum_value.value

    def test_image_type_enum_values(self):
        """Test that ImageType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ImageType = ImageType().value
        assert dataset.ImageType == ImageType().value

    @pytest.mark.parametrize("enum_value", list(ModalityLutType))
    def test_modality_lut_type_enum_values(self, enum_value: ModalityLutType):
        """Test that ModalityLutType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.ModalityLUTType = enum_value.value
        assert dataset.ModalityLUTType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(RescaleType))
    def test_rescale_type_enum_values(self, enum_value: RescaleType):
        """Test that RescaleType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.RescaleType = enum_value.value
        assert dataset.RescaleType == enum_value.value

    @pytest.mark.parametrize("enum_value", list(VoiLutFunction))
    def test_voi_lut_function_enum_values(self, enum_value: VoiLutFunction):
        """Test that VoiLutFunction enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.VOILUTFunction = enum_value.value
        assert dataset.VOILUTFunction == enum_value.value

    @pytest.mark.parametrize("enum_value", list(FrameType))
    def test_frame_type_enum_values(self, enum_value: FrameType):
        """Test that FrameType enum values can be set to Dataset without error."""
        dataset = Dataset()
        dataset.FrameType = enum_value.value
        assert dataset.FrameType == enum_value.value

    # Coverage tests to ensure all expected values are present
    def test_quality_control_image_coverage(self):
        """Test that all expected QualityControlImage values are present."""
        expected_values = {"YES", "NO", "BOTH"}
        actual_values = {member.value for member in QualityControlImage}
        assert actual_values == expected_values

    def test_burned_in_annotation_coverage(self):
        """Test that all expected BurnedInAnnotation values are present."""
        expected_values = {"YES", "NO"}
        actual_values = {member.value for member in BurnedInAnnotation}
        assert actual_values == expected_values

    def test_recognizable_visual_features_coverage(self):
        """Test that all expected RecognizableVisualFeatures values are present."""
        expected_values = {"YES", "NO"}
        actual_values = {member.value for member in RecognizableVisualFeatures}
        assert actual_values == expected_values

    def test_lossy_image_compression_coverage(self):
        """Test that all expected LossyImageCompression values are present."""
        expected_values = {"00", "01"}
        actual_values = {member.value for member in LossyImageCompression}
        assert actual_values == expected_values

    def test_presentation_lut_shape_coverage(self):
        """Test that all expected PresentationLUTShape values are present."""
        expected_values = {"IDENTITY", "INVERSE"}
        actual_values = {member.value for member in PresentationLUTShape}
        assert actual_values == expected_values

    def test_image_laterality_coverage(self):
        """Test that all expected ImageLaterality values are present."""
        expected_values = {"R", "L", "U", "B"}
        actual_values = {member.value for member in ImageLaterality}
        assert actual_values == expected_values

    def test_photometric_interpretation_coverage(self):
        """Test that all expected PhotometricInterpretation values are present."""
        expected_values = {
            "MONOCHROME1", "MONOCHROME2", "PALETTE COLOR", "RGB", 
            "YBR_FULL", "YBR_FULL_422", "YBR_PARTIAL_420", "YBR_ICT", "YBR_RCT", "XYB"
        }
        actual_values = {member.value for member in PhotometricInterpretation}
        assert actual_values == expected_values

    def test_planar_configuration_coverage(self):
        """Test that all expected PlanarConfiguration values are present."""
        expected_values = {0, 1}
        actual_values = {member.value for member in PlanarConfiguration}
        assert actual_values == expected_values

    def test_pixel_representation_coverage(self):
        """Test that all expected PixelRepresentation values are present."""
        expected_values = {0, 1}
        actual_values = {member.value for member in PixelRepresentation}
        assert actual_values == expected_values

    def test_stereo_pairs_present_coverage(self):
        """Test that all expected StereoPairsPresent values are present."""
        expected_values = {"YES", "NO"}
        actual_values = {member.value for member in StereoPairsPresent}
        assert actual_values == expected_values

    def test_preferred_playback_sequencing_coverage(self):
        """Test that all expected PreferredPlaybackSequencing values are present."""
        expected_values = {0, 1}
        actual_values = {member.value for member in PreferredPlaybackSequencing}
        assert actual_values == expected_values

    def test_channel_mode_coverage(self):
        """Test that all expected ChannelMode values are present."""
        expected_values = {"MONO", "STEREO"}
        actual_values = {member.value for member in ChannelMode}
        assert actual_values == expected_values

    def test_overlay_type_coverage(self):
        """Test that all expected OverlayType values are present."""
        expected_values = {"G", "R"}
        actual_values = {member.value for member in OverlayType}
        assert actual_values == expected_values

    def test_overlay_subtype_coverage(self):
        """Test that all expected OverlaySubtype values are present."""
        expected_values = {"USER", "AUTOMATED", "ACTIVE IMAGE AREA"}
        actual_values = {member.value for member in OverlaySubtype}
        assert actual_values == expected_values

    def test_lossy_image_compression_method_coverage(self):
        """Test that all expected LossyImageCompressionMethod values are present."""
        expected_values = {
            "ISO_10918_1", "ISO_14495_1", "ISO_15444_1", "ISO_15444_15", 
            "ISO_18181_1", "ISO_13818_2", "ISO_14496_10", "ISO_23008_2"
        }
        actual_values = {member.value for member in LossyImageCompressionMethod}
        assert actual_values == expected_values

    def test_modality_lut_type_coverage(self):
        """Test that all expected ModalityLutType values are present."""
        expected_values = {"OD", "HU", "US", "MGML", "Z_EFF", "ED", "EDW", "HU_MOD", "PCT"}
        actual_values = {member.value for member in ModalityLutType}
        assert actual_values == expected_values

    def test_rescale_type_coverage(self):
        """Test that all expected RescaleType values are present."""
        # Updated to reflect the comprehensive RescaleType enum
        expected_values = {
            "HU", "US", "MGML", "PCNT", "CPS", "NONE", "CM", "MM", "PIXVAL", "COUNTS", 
            "PROPCNT", "DISP", "UMOL", "CONC", "RWU", "DENS", "TEMP", "FLOW", "PERF", 
            "DIFF", "RELAX", "METAB", "RATIO", "OTHER", "DVPX", "COEF", "GRAD", "FRAC", 
            "MASS", "MLMIN", "MLMINM2", "ML100GM", "MLMIN100G", "DEGC", "SEC", "MSEC", 
            "USEC", "HZ", "PPM", "RAD", "DEG", "OD", "Z_EFF", "ED", "EDW", "HU_MOD", "PCT"
        }
        actual_values = {member.value for member in RescaleType}
        assert actual_values == expected_values

    def test_voi_lut_function_coverage(self):
        """Test that all expected VoiLutFunction values are present."""
        expected_values = {"LINEAR", "LINEAR_EXACT", "SIGMOID"}
        actual_values = {member.value for member in VoiLutFunction}
        assert actual_values == expected_values

    def test_frame_type_coverage(self):
        """Test that all expected FrameType values are present."""
        # Test frame data characteristics
        assert FrameType.ORIGINAL.value == "ORIGINAL"
        assert FrameType.DERIVED.value == "DERIVED"
        
        # Test frame acquisition characteristics
        assert FrameType.PRIMARY.value == "PRIMARY"
        assert FrameType.SECONDARY.value == "SECONDARY"
        
        # Test frame-specific classifications (sample)
        assert FrameType.STATIC.value == "STATIC"
        assert FrameType.DYNAMIC.value == "DYNAMIC"
        assert FrameType.GATED.value == "GATED"
        assert FrameType.UNGATED.value == "UNGATED"
        assert FrameType.PERFUSION.value == "PERFUSION"
        assert FrameType.ANGIO.value == "ANGIO"
        assert FrameType.CARDIAC.value == "CARDIAC"
        assert FrameType.RESPIRATORY.value == "RESPIRATORY"
        assert FrameType.CONTRAST.value == "CONTRAST"
        assert FrameType.PRE_CONTRAST.value == "PRE_CONTRAST"
        assert FrameType.POST_CONTRAST.value == "POST_CONTRAST"


class TestContrastCTEnumsParametrized:
    """Parametrized tests for all contrast and CT enums."""
    
    @pytest.mark.parametrize("enum_class,attribute_name", [
        (ContrastBolusAgent, "ContrastBolusAgent"),
        (ContrastBolusIngredient, "ContrastBolusIngredient"),
        (MultiEnergyCTAcquisition, "MultiEnergyCTAcquisition"), 
        (RotationDirection, "RotationDirection"),
        (ExposureModulationType, "ExposureModulationType"),
        (CTImageTypeValue1, "ImageType"),
        (CTImageTypeValue2, "ImageType"),
        (CTImageTypeValue3, "ImageType"),
        (CTImageTypeValue4, "ImageType"),
        (MultiEnergySourceTechnique, "MultiEnergySourceTechnique"),
        (MultiEnergyDetectorType, "MultiEnergyDetectorType"),
        (RescaleType, "RescaleType"),
        (FilterMaterial, "FilterMaterial"),
        (ScanOptions, "ScanOptions"),
        (FilterType, "FilterType")
    ])
    def test_enum_values_can_be_set_to_dataset(self, enum_class, attribute_name):
        """Test that all enum values can be set to Dataset without error."""
        for enum_value in enum_class:
            ds = Dataset()
            # Should not raise any exception
            setattr(ds, attribute_name, enum_value.value)
            
            # Verify the value was set correctly
            assert getattr(ds, attribute_name) == enum_value.value
            assert hasattr(ds, attribute_name)
    
    @pytest.mark.parametrize("enum_class,attribute_name", [
        (ContrastBolusAgent, "ContrastBolusAgent"),
        (ContrastBolusIngredient, "ContrastBolusIngredient"),
        (MultiEnergyCTAcquisition, "MultiEnergyCTAcquisition"),
        (RotationDirection, "RotationDirection"),
        (ExposureModulationType, "ExposureModulationType"),
        (CTImageTypeValue1, "ImageType"),
        (CTImageTypeValue2, "ImageType"), 
        (CTImageTypeValue3, "ImageType"),
        (CTImageTypeValue4, "ImageType"),
        (MultiEnergySourceTechnique, "MultiEnergySourceTechnique"),
        (MultiEnergyDetectorType, "MultiEnergyDetectorType"),
        (RescaleType, "RescaleType"),
        (FilterMaterial, "FilterMaterial"),
        (ScanOptions, "ScanOptions"),
        (FilterType, "FilterType")
    ])
    def test_enum_instances_can_be_set_to_dataset_directly(self, enum_class, attribute_name):
        """Test that enum instances can be used directly with Dataset."""
        for enum_value in enum_class:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            setattr(ds, attribute_name, enum_value)
            
            # pydicom stores the enum object, so we get the full representation
            stored_value = getattr(ds, attribute_name)
            # For enum objects, we just verify the attribute exists and has the right value
            assert hasattr(ds, attribute_name)
            # The stored value should either be the enum value itself or contain it in string representation
            stored_str = str(stored_value)
            assert (stored_value == enum_value.value or 
                    enum_value.value in stored_str or 
                    enum_value.name in stored_str)
    
    @pytest.mark.parametrize("enum_class", [
        ContrastBolusAgent,
        ContrastBolusIngredient,
        MultiEnergyCTAcquisition,
        RotationDirection,
        ExposureModulationType,
        CTImageTypeValue1,
        CTImageTypeValue2,
        CTImageTypeValue3,
        CTImageTypeValue4,
        MultiEnergySourceTechnique,
        MultiEnergyDetectorType,
        RescaleType,
        FilterMaterial,
        ScanOptions,
        FilterType
    ])
    def test_enum_values_exist_and_are_correct_type(self, enum_class):
        """Test that enum values exist and have correct types."""
        for enum_value in enum_class:
            # All enum values should have a .value attribute
            assert hasattr(enum_value, 'value')
            
            # The value should be either string or int based on the enum
            if enum_class in [CTSamplesPerPixel, CTBitsAllocated, CTBitsStored]:
                assert isinstance(enum_value.value, int)
            else:
                assert isinstance(enum_value.value, str)


class TestSpecialCTEnums:
    """Tests for CT enums with special integer values."""
    
    @pytest.mark.parametrize("enum_class,attribute_name", [
        (CTSamplesPerPixel, "SamplesPerPixel"),
        (CTBitsAllocated, "BitsAllocated"),
        (CTBitsStored, "BitsStored")
    ])
    def test_integer_enum_values_can_be_set_to_dataset(self, enum_class, attribute_name):
        """Test that integer enum values can be set to Dataset without error."""
        for enum_value in enum_class:
            ds = Dataset()
            # Should not raise any exception
            setattr(ds, attribute_name, enum_value.value)
            
            # Verify the value was set correctly
            assert getattr(ds, attribute_name) == enum_value.value
            assert hasattr(ds, attribute_name)
            # Verify it's an integer
            assert isinstance(getattr(ds, attribute_name), int)
    
    @pytest.mark.parametrize("enum_class,attribute_name", [
        (CTSamplesPerPixel, "SamplesPerPixel"),
        (CTBitsAllocated, "BitsAllocated"), 
        (CTBitsStored, "BitsStored")
    ])
    def test_integer_enum_instances_can_be_set_to_dataset_directly(self, enum_class, attribute_name):
        """Test that integer enum instances can be used directly with Dataset."""
        for enum_value in enum_class:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            setattr(ds, attribute_name, enum_value)
            
            # pydicom stores the enum object, so we get the full representation
            stored_value = getattr(ds, attribute_name)
            # For enum objects, we just verify the attribute exists and contains the right value
            assert hasattr(ds, attribute_name)
            stored_str = str(stored_value)
            assert (stored_value == enum_value.value or 
                    str(enum_value.value) in stored_str or 
                    enum_value.name in stored_str)


class TestSpecificEnumValues:
    """Tests for specific enum values to ensure they exist."""
    
    def test_contrast_bolus_agent_values(self):
        """Test that ContrastBolusAgent has expected common values."""
        # Test iodinated contrast agents
        assert ContrastBolusAgent.IOHEXOL.value == "Iohexol"
        assert ContrastBolusAgent.IOPAMIDOL.value == "Iopamidol"
        assert ContrastBolusAgent.IOVERSOL.value == "Ioversol"
        assert ContrastBolusAgent.IODIXANOL.value == "Iodixanol"
        assert ContrastBolusAgent.IOPROMIDE.value == "Iopromide"
        
        # Test gadolinium-based agents
        assert ContrastBolusAgent.GADOPENTETATE.value == "Gadopentetate dimeglumine"
        assert ContrastBolusAgent.GADOBUTROL.value == "Gadobutrol"
        assert ContrastBolusAgent.GADOTERATE.value == "Gadoterate meglumine"
        assert ContrastBolusAgent.GADOTERIDOL.value == "Gadoteridol"
        assert ContrastBolusAgent.GADOBENATE.value == "Gadobenate dimeglumine"
        
        # Test other agents
        assert ContrastBolusAgent.BARIUM_SULFATE.value == "Barium sulfate"
        assert ContrastBolusAgent.CARBON_DIOXIDE.value == "Carbon dioxide"
        assert ContrastBolusAgent.SALINE.value == "Saline"
    
    def test_contrast_bolus_ingredient_values(self):
        """Test that ContrastBolusIngredient has expected values."""
        assert ContrastBolusIngredient.IODINE.value == "IODINE"
        assert ContrastBolusIngredient.GADOLINIUM.value == "GADOLINIUM"
        assert ContrastBolusIngredient.CARBON_DIOXIDE.value == "CARBON DIOXIDE"
        assert ContrastBolusIngredient.BARIUM.value == "BARIUM"
    
    def test_multi_energy_ct_acquisition_values(self):
        """Test that MultiEnergyCTAcquisition has expected values."""
        assert MultiEnergyCTAcquisition.YES.value == "YES"
        assert MultiEnergyCTAcquisition.NO.value == "NO"
    
    def test_rotation_direction_values(self):
        """Test that RotationDirection has expected values."""
        assert RotationDirection.CW.value == "CW"
        assert RotationDirection.CC.value == "CC"
    
    def test_ct_image_type_values(self):
        """Test that CTImageType enums have expected values."""
        assert CTImageTypeValue1.ORIGINAL.value == "ORIGINAL"
        assert CTImageTypeValue1.DERIVED.value == "DERIVED"
        
        assert CTImageTypeValue2.PRIMARY.value == "PRIMARY"
        assert CTImageTypeValue2.SECONDARY.value == "SECONDARY"
        
        assert CTImageTypeValue3.AXIAL.value == "AXIAL"
        assert CTImageTypeValue3.LOCALIZER.value == "LOCALIZER"
    
    def test_integer_enum_values(self):
        """Test that integer enums have expected values."""
        assert CTSamplesPerPixel.ONE.value == 1
        assert CTBitsAllocated.SIXTEEN.value == 16
        assert CTBitsStored.TWELVE.value == 12
        assert CTBitsStored.SIXTEEN.value == 16
    
    def test_rescale_type_common_values(self):
        """Test that RescaleType has expected common values."""
        assert RescaleType.HU.value == "HU"
        assert RescaleType.US.value == "US"
        assert RescaleType.MGML.value == "MGML"
        assert RescaleType.PCNT.value == "PCNT"
    
    def test_filter_material_values(self):
        """Test that FilterMaterial has expected values."""
        assert FilterMaterial.ALUMINUM.value == "AL"
        assert FilterMaterial.COPPER.value == "CU"
        assert FilterMaterial.TUNGSTEN.value == "W"
        assert FilterMaterial.LEAD.value == "PB"


class TestDICOMTagCompatibility:
    """Test compatibility with DICOM tag notation for selected enums."""
    
    def test_contrast_bolus_agent_with_tag(self):
        """Test ContrastBolusAgent can be set using DICOM tag notation."""
        ds = Dataset()
        
        # Set via DataElement (tag 0018,0010 for Contrast/Bolus Agent)
        ds[0x0018, 0x0010] = DataElement(0x00180010, 'LO', ContrastBolusAgent.IOHEXOL.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0018, 0x0010].value == "Iohexol"
    
    def test_contrast_bolus_ingredient_with_tag(self):
        """Test ContrastBolusIngredient can be set using DICOM tag notation."""
        ds = Dataset()
        
        # Set via DataElement (tag 0018,1048 for Contrast/Bolus Ingredient)
        ds[0x0018, 0x1048] = DataElement(0x00181048, 'CS', ContrastBolusIngredient.IODINE.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0018, 0x1048].value == "IODINE"
    
    def test_rotation_direction_with_tag(self):
        """Test RotationDirection can be set using DICOM tag notation."""
        ds = Dataset()
        
        # Set via DataElement (tag 0018,1140 for Rotation Direction)
        ds[0x0018, 0x1140] = DataElement(0x00181140, 'CS', RotationDirection.CW.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0018, 0x1140].value == "CW"
    
    def test_samples_per_pixel_with_tag(self):
        """Test CTSamplesPerPixel can be set using DICOM tag notation."""
        ds = Dataset()
        
        # Set via DataElement (tag 0028,0002 for Samples per Pixel)
        ds[0x0028, 0x0002] = DataElement(0x00280002, 'US', CTSamplesPerPixel.ONE.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0028, 0x0002].value == 1
    
    def test_bits_allocated_with_tag(self):
        """Test CTBitsAllocated can be set using DICOM tag notation."""
        ds = Dataset()
        
        # Set via DataElement (tag 0028,0100 for Bits Allocated)
        ds[0x0028, 0x0100] = DataElement(0x00280100, 'US', CTBitsAllocated.SIXTEEN.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0028, 0x0100].value == 16
    
    def test_rescale_type_with_tag(self):
        """Test RescaleType can be set using DICOM tag notation."""
        ds = Dataset()
        
        # Set via DataElement (tag 0028,1054 for Rescale Type)
        ds[0x0028, 0x1054] = DataElement(0x00281054, 'LO', RescaleType.HU.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0028, 0x1054].value == "HU"


class TestImageTypeClass:
    """Comprehensive tests for the enhanced ImageType class."""
    
    def test_default_constructor(self):
        """Test ImageType default constructor with default values."""
        image_type = ImageType()
        assert image_type.value == ['ORIGINAL', 'PRIMARY', 'AXIAL']
        assert image_type.is_original()
        assert image_type.is_primary()
        assert image_type.is_axial()
        assert not image_type.has_multi_energy_ct()
    
    def test_constructor_with_all_parameters(self):
        """Test ImageType constructor with all parameters."""
        image_type = ImageType(
            CTImageTypeValue1.DERIVED,
            CTImageTypeValue2.SECONDARY,
            CTImageTypeValue3.LOCALIZER,
            CTImageTypeValue4.VMI
        )
        assert image_type.value == ['DERIVED', 'SECONDARY', 'LOCALIZER', 'VMI']
        assert image_type.is_derived()
        assert image_type.is_secondary()
        assert image_type.is_localizer()
        assert image_type.has_multi_energy_ct()
    
    def test_constructor_validation(self):
        """Test constructor input validation."""
        with pytest.raises(TypeError, match="original_or_derived must be CTImageTypeValue1"):
            ImageType("INVALID", CTImageTypeValue2.PRIMARY, CTImageTypeValue3.AXIAL) # type: ignore
            
        with pytest.raises(TypeError, match="primary_or_secondary must be CTImageTypeValue2"):
            ImageType(CTImageTypeValue1.ORIGINAL, "INVALID", CTImageTypeValue3.AXIAL) # type: ignore
            
        with pytest.raises(TypeError, match="axial_or_localizer must be CTImageTypeValue3"):
            ImageType(CTImageTypeValue1.ORIGINAL, CTImageTypeValue2.PRIMARY, "INVALID") # type: ignore
            
        with pytest.raises(TypeError, match="multi_energy_ct_type must be CTImageTypeValue4 or None"):
            ImageType(CTImageTypeValue1.ORIGINAL, CTImageTypeValue2.PRIMARY, CTImageTypeValue3.AXIAL, "INVALID") # type: ignore
    
    def test_from_enumerations_factory(self):
        """Test from_enumerations factory method."""
        image_type = ImageType.from_enumerations(
            CTImageTypeValue1.ORIGINAL,
            CTImageTypeValue2.PRIMARY,
            CTImageTypeValue3.AXIAL,
            CTImageTypeValue4.VMI
        )
        assert image_type.value == ['ORIGINAL', 'PRIMARY', 'AXIAL', 'VMI']
    
    def test_from_enumerations_defaults(self):
        """Test from_enumerations with default values."""
        image_type = ImageType.from_enumerations()
        assert image_type.value == ['ORIGINAL', 'PRIMARY', 'AXIAL']
    
    def test_from_ct_characteristics_factory(self):
        """Test from_ct_characteristics factory method."""
        # Default characteristics
        image_type = ImageType.from_ct_characteristics()
        assert image_type.value == ['ORIGINAL', 'PRIMARY', 'AXIAL']
        
        # Custom characteristics
        image_type = ImageType.from_ct_characteristics(
            is_original=False,
            is_primary=False,
            is_axial=False
        )
        assert image_type.value == ['DERIVED', 'SECONDARY', 'LOCALIZER']
        
        # Mixed characteristics
        image_type = ImageType.from_ct_characteristics(
            is_original=True,
            is_primary=False,
            is_axial=True
        )
        assert image_type.value == ['ORIGINAL', 'SECONDARY', 'AXIAL']
    
    def test_with_multi_energy_ct(self):
        """Test with_multi_energy_ct fluent method."""
        image_type = ImageType.from_ct_characteristics().with_multi_energy_ct(CTImageTypeValue4.VMI)
        assert image_type.value == ['ORIGINAL', 'PRIMARY', 'AXIAL', 'VMI']
        assert image_type.has_multi_energy_ct()
    
    def test_with_multi_energy_ct_validation(self):
        """Test with_multi_energy_ct input validation."""
        image_type = ImageType()
        with pytest.raises(TypeError, match="multi_energy_ct_type must be CTImageTypeValue4"):
            image_type.with_multi_energy_ct("INVALID") # type: ignore
    
    def test_with_multi_energy_ct_chaining(self):
        """Test with_multi_energy_ct returns self for chaining."""
        image_type = ImageType()
        result = image_type.with_multi_energy_ct(CTImageTypeValue4.VMI)
        assert result is image_type
    
    def test_boolean_methods(self):
        """Test all boolean convenience methods."""
        # Test original image
        original_image = ImageType(CTImageTypeValue1.ORIGINAL, CTImageTypeValue2.PRIMARY, CTImageTypeValue3.AXIAL)
        assert original_image.is_original()
        assert not original_image.is_derived()
        assert original_image.is_primary()
        assert not original_image.is_secondary()
        assert original_image.is_axial()
        assert not original_image.is_localizer()
        assert not original_image.has_multi_energy_ct()
        
        # Test derived image
        derived_image = ImageType(CTImageTypeValue1.DERIVED, CTImageTypeValue2.SECONDARY, CTImageTypeValue3.LOCALIZER)
        assert not derived_image.is_original()
        assert derived_image.is_derived()
        assert not derived_image.is_primary()
        assert derived_image.is_secondary()
        assert not derived_image.is_axial()
        assert derived_image.is_localizer()
        assert not derived_image.has_multi_energy_ct()
        
        # Test with multi-energy CT
        multi_energy_image = ImageType(
            CTImageTypeValue1.ORIGINAL,
            CTImageTypeValue2.PRIMARY,
            CTImageTypeValue3.AXIAL,
            CTImageTypeValue4.VMI
        )
        assert multi_energy_image.has_multi_energy_ct()
    
    def test_equality_comparison(self):
        """Test __eq__ method."""
        image1 = ImageType.from_ct_characteristics()
        image2 = ImageType.from_ct_characteristics()
        image3 = ImageType.from_ct_characteristics(is_original=False)
        
        assert image1 == image2
        assert image1 != image3
        assert image2 != image3
    
    def test_equality_with_non_imagetype(self):
        """Test __eq__ with non-ImageType objects."""
        image_type = ImageType()
        assert image_type != "not_an_image_type"
        assert image_type != ['ORIGINAL', 'PRIMARY', 'AXIAL']
        assert image_type is not None
    
    def test_hash_method(self):
        """Test __hash__ method."""
        image1 = ImageType.from_ct_characteristics()
        image2 = ImageType.from_ct_characteristics()
        image3 = ImageType.from_ct_characteristics(is_original=False)
        
        # Equal objects should have same hash
        assert hash(image1) == hash(image2)
        # Different objects should have different hash
        assert hash(image1) != hash(image3)
        
        # Test that ImageType can be used in sets
        image_set = {image1, image2, image3}
        assert len(image_set) == 2  # image1 and image2 are equal
    
    def test_contains_method(self):
        """Test __contains__ method."""
        image_type = ImageType.from_ct_characteristics()
        
        assert 'ORIGINAL' in image_type
        assert 'PRIMARY' in image_type
        assert 'AXIAL' in image_type
        assert 'DERIVED' not in image_type
        assert 'SECONDARY' not in image_type
        assert 'LOCALIZER' not in image_type
        assert 'VMI' not in image_type
        
        # Test with multi-energy CT
        multi_energy = image_type.with_multi_energy_ct(CTImageTypeValue4.VMI)
        assert 'VMI' in multi_energy
    
    def test_matches_method(self):
        """Test matches method."""
        image1 = ImageType.from_ct_characteristics()
        image2 = ImageType.from_ct_characteristics()
        image3 = ImageType.from_ct_characteristics(is_original=False)
        
        assert image1.matches(image2)
        assert not image1.matches(image3)
    
    def test_matches_method_validation(self):
        """Test matches method input validation."""
        image_type = ImageType()
        with pytest.raises(TypeError, match="Can only match against ImageType"):
            image_type.matches("not_an_image_type") # type: ignore
    
    def test_string_representations(self):
        """Test __str__ and __repr__ methods."""
        image_type = ImageType.from_ct_characteristics()
        
        str_repr = str(image_type)
        assert str_repr == "[ORIGINAL, PRIMARY, AXIAL]"
        
        repr_str = repr(image_type)
        assert repr_str == "ImageType(['ORIGINAL', 'PRIMARY', 'AXIAL'])"
        
        # Test with multi-energy CT
        multi_energy = image_type.with_multi_energy_ct(CTImageTypeValue4.VMI)
        str_repr = str(multi_energy)
        assert str_repr == "[ORIGINAL, PRIMARY, AXIAL, VMI]"
        
        repr_str = repr(multi_energy)
        assert repr_str == "ImageType(['ORIGINAL', 'PRIMARY', 'AXIAL', 'VMI'])"
    
    def test_value_property(self):
        """Test value property returns correct list."""
        # Basic image type
        image_type = ImageType.from_ct_characteristics()
        value = image_type.value
        assert value == ['ORIGINAL', 'PRIMARY', 'AXIAL']
        assert isinstance(value, list)
        
        # With multi-energy CT
        multi_energy = image_type.with_multi_energy_ct(CTImageTypeValue4.MAT_SPECIFIC)
        value = multi_energy.value
        assert value == ['ORIGINAL', 'PRIMARY', 'AXIAL', 'MAT_SPECIFIC']
        assert isinstance(value, list)
    
    def test_value_property_immutability(self):
        """Test that modifying returned value doesn't affect original."""
        image_type = ImageType.from_ct_characteristics()
        value = image_type.value
        original_value = value.copy()
        
        # Modify the returned list
        value.append('EXTRA')
        
        # Original should be unchanged
        assert image_type.value == original_value
        assert 'EXTRA' not in image_type.value
    
    def test_pydicom_dataset_integration(self):
        """Test integration with pydicom Dataset."""
        dataset = Dataset()
        
        # Test basic image type
        image_type = ImageType.from_ct_characteristics()
        dataset.ImageType = image_type.value
        assert dataset.ImageType == ['ORIGINAL', 'PRIMARY', 'AXIAL']
        
        # Test with multi-energy CT
        multi_energy = ImageType.from_ct_characteristics().with_multi_energy_ct(CTImageTypeValue4.VMI)
        dataset.ImageType = multi_energy.value
        assert dataset.ImageType == ['ORIGINAL', 'PRIMARY', 'AXIAL', 'VMI']
    
    @pytest.mark.parametrize("value1,value2,value3,value4", [
        (CTImageTypeValue1.ORIGINAL, CTImageTypeValue2.PRIMARY, CTImageTypeValue3.AXIAL, None),
        (CTImageTypeValue1.DERIVED, CTImageTypeValue2.SECONDARY, CTImageTypeValue3.LOCALIZER, None),
        (CTImageTypeValue1.ORIGINAL, CTImageTypeValue2.PRIMARY, CTImageTypeValue3.AXIAL, CTImageTypeValue4.VMI),
        (CTImageTypeValue1.DERIVED, CTImageTypeValue2.SECONDARY, CTImageTypeValue3.AXIAL, CTImageTypeValue4.MAT_SPECIFIC),
        (CTImageTypeValue1.ORIGINAL, CTImageTypeValue2.SECONDARY, CTImageTypeValue3.LOCALIZER, CTImageTypeValue4.ELECTRON_DENSITY),
    ])
    def test_parametrized_combinations(self, value1, value2, value3, value4):
        """Test various combinations of ImageType values."""
        if value4 is None:
            image_type = ImageType(value1, value2, value3)
            expected = [value1.value, value2.value, value3.value]
        else:
            image_type = ImageType(value1, value2, value3, value4)
            expected = [value1.value, value2.value, value3.value, value4.value]
        
        assert image_type.value == expected
        
        # Test boolean methods
        assert image_type.is_original() == (value1 == CTImageTypeValue1.ORIGINAL)
        assert image_type.is_derived() == (value1 == CTImageTypeValue1.DERIVED)
        assert image_type.is_primary() == (value2 == CTImageTypeValue2.PRIMARY)
        assert image_type.is_secondary() == (value2 == CTImageTypeValue2.SECONDARY)
        assert image_type.is_axial() == (value3 == CTImageTypeValue3.AXIAL)
        assert image_type.is_localizer() == (value3 == CTImageTypeValue3.LOCALIZER)
        assert image_type.has_multi_energy_ct() == (value4 is not None)
    
    def test_multi_energy_ct_types(self):
        """Test all multi-energy CT type values."""
        base_image = ImageType.from_ct_characteristics()
        
        for ct_type in CTImageTypeValue4:
            enhanced_image = base_image.with_multi_energy_ct(ct_type)
            expected = ['ORIGINAL', 'PRIMARY', 'AXIAL', ct_type.value]
            assert enhanced_image.value == expected
            assert enhanced_image.has_multi_energy_ct()
            assert ct_type.value in enhanced_image
    
    def test_factory_method_equivalence(self):
        """Test that factory methods produce equivalent results."""
        # These should be equivalent
        image1 = ImageType()
        image2 = ImageType.from_enumerations()
        image3 = ImageType.from_ct_characteristics()
        image4 = ImageType(CTImageTypeValue1.ORIGINAL, CTImageTypeValue2.PRIMARY, CTImageTypeValue3.AXIAL)
        
        assert image1 == image2 == image3 == image4
        assert image1.value == image2.value == image3.value == image4.value
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Empty contains check
        image_type = ImageType()
        assert '' not in image_type
        assert 'NONEXISTENT' not in image_type
        
        # Case sensitivity
        assert 'original' not in image_type  # lowercase should not match
        assert 'ORIGINAL' in image_type     # uppercase should match
        
        # Partial matches
        assert 'ORIG' not in image_type     # partial matches should not work
