"""Tests for Equipment-related DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.equipment_enums import DeviceDiameterUnits, Manufacturer, ProcedureCode


class TestEquipmentEnums:
    """Test equipment enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(DeviceDiameterUnits))
    def test_device_diameter_units_enum_values(self, enum_value: DeviceDiameterUnits):
        """Test that DeviceDiameterUnits enum values can be set to Dataset.DeviceDiameterUnits without error.
        
        Args:
            enum_value: DeviceDiameterUnits enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.DeviceDiameterUnits = enum_value.value
        assert dataset.DeviceDiameterUnits == enum_value.value

    def test_device_diameter_units_coverage(self):
        """Test that all expected DeviceDiameterUnits values are present."""
        expected_values = {"FR", "GA", "IN", "MM"}
        actual_values = {member.value for member in DeviceDiameterUnits}
        assert actual_values == expected_values

    @pytest.mark.parametrize("enum_value", list(Manufacturer))
    def test_manufacturer_enum_values(self, enum_value: Manufacturer):
        """Test that Manufacturer enum values can be set to Dataset.Manufacturer without error.
        
        Args:
            enum_value: Manufacturer enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.Manufacturer = enum_value.value
        assert dataset.Manufacturer == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ProcedureCode))
    def test_procedure_code_enum_values(self, enum_value: ProcedureCode):
        """Test that ProcedureCode enum values can be set to Dataset.ProcedureCodeSequence without error.
        
        Args:
            enum_value: ProcedureCode enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.ProcedureCode = enum_value.value
        assert dataset.ProcedureCode == enum_value.value

    def test_manufacturer_coverage(self):
        """Test that all expected Manufacturer values are present."""
        # Test radiation therapy equipment
        assert Manufacturer.VARIAN.value == "Varian Medical Systems"
        assert Manufacturer.ELEKTA.value == "Elekta"
        assert Manufacturer.ACCURAY.value == "Accuray"
        assert Manufacturer.BRAINLAB.value == "Brainlab"
        
        # Test imaging equipment
        assert Manufacturer.SIEMENS.value == "Siemens Healthineers"
        assert Manufacturer.PHILIPS.value == "Philips Healthcare"
        assert Manufacturer.GE.value == "GE Healthcare"
        assert Manufacturer.CANON.value == "Canon Medical Systems"
        assert Manufacturer.FUJIFILM.value == "Fujifilm Healthcare"
        
        # Test treatment planning systems
        assert Manufacturer.RAYSTATION.value == "RaySearch Laboratories"
        assert Manufacturer.ECLIPSE.value == "Eclipse Treatment Planning System"
        assert Manufacturer.MONACO.value == "Monaco Treatment Planning System"
        assert Manufacturer.PINNACLE.value == "Pinnacle Treatment Planning System"

    def test_procedure_code_coverage(self):
        """Test that all expected ProcedureCode values are present."""
        # Test radiation therapy procedures
        assert ProcedureCode.RT_PLANNING.value == "77295"
        assert ProcedureCode.RT_DELIVERY.value == "77385"
        assert ProcedureCode.IMRT.value == "77301"
        assert ProcedureCode.SBRT.value == "77373"
        assert ProcedureCode.SRS.value == "77371"
        
        # Test imaging procedures
        assert ProcedureCode.CT_SIMULATION.value == "77014"
        assert ProcedureCode.CT_CHEST.value == "71250"
        assert ProcedureCode.CT_ABDOMEN.value == "74150"
        assert ProcedureCode.MR_BRAIN.value == "70551"
        assert ProcedureCode.PET_CT.value == "78815"