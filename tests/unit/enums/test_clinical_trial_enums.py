"""Unit tests for clinical trial enums compatibility with pydicom Dataset.

This module tests that clinical trial enum values can be properly set
as attributes on pydicom Dataset objects without errors.
"""

from pydicom import Dataset
from pyrt_dicom.enums.clinical_trial_enums import (
    ConsentForDistribution,
    DistributionType,
    LongitudinalTemporalEventType
)


class TestConsentForDistributionEnum:
    """Test ConsentForDistribution enum compatibility with pydicom Dataset."""
    
    def test_consent_for_distribution_values_exist(self):
        """Test that all expected ConsentForDistribution values exist."""
        assert ConsentForDistribution.NO.value == "NO"
        assert ConsentForDistribution.YES.value == "YES"
        assert ConsentForDistribution.WITHDRAWN.value == "WITHDRAWN"
    
    def test_consent_for_distribution_no_can_be_set_to_dataset(self):
        """Test that ConsentForDistribution.NO can be set to Dataset without error."""
        ds = Dataset()
        ds.ConsentForDistribution = ConsentForDistribution.NO.value
        
        assert ds.ConsentForDistribution == "NO"
        assert hasattr(ds, 'ConsentForDistribution')
    
    def test_consent_for_distribution_yes_can_be_set_to_dataset(self):
        """Test that ConsentForDistribution.YES can be set to Dataset without error."""
        ds = Dataset()
        ds.ConsentForDistribution = ConsentForDistribution.YES.value
        
        assert ds.ConsentForDistribution == "YES"
        assert hasattr(ds, 'ConsentForDistribution')
    
    def test_consent_for_distribution_withdrawn_can_be_set_to_dataset(self):
        """Test that ConsentForDistribution.WITHDRAWN can be set to Dataset without error."""
        ds = Dataset()
        ds.ConsentForDistribution = ConsentForDistribution.WITHDRAWN.value
        
        assert ds.ConsentForDistribution == "WITHDRAWN"
        assert hasattr(ds, 'ConsentForDistribution')
    
    def test_all_consent_for_distribution_values_can_be_set_to_dataset(self):
        """Test that all ConsentForDistribution values can be set to Dataset without error."""
        for consent in ConsentForDistribution:
            ds = Dataset()
            # Should not raise any exception
            ds.ConsentForDistribution = consent.value
            
            # Verify the value was set correctly
            assert ds.ConsentForDistribution == consent.value
            assert hasattr(ds, 'ConsentForDistribution')
    
    def test_consent_for_distribution_enum_directly_can_be_set_to_dataset(self):
        """Test that ConsentForDistribution enum instances can be used directly with Dataset."""
        for consent in ConsentForDistribution:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.ConsentForDistribution = consent
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.ConsentForDistribution).endswith(consent.value)
            assert hasattr(ds, 'ConsentForDistribution')
    
    def test_consent_for_distribution_with_dicom_tag(self):
        """Test that ConsentForDistribution can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # Test setting via DataElement creation (ConsentForDistribution not in standard dict)
        ds[0x0012, 0x0083] = DataElement(0x00120083, 'CS', ConsentForDistribution.YES.value)
        
        # Verify it can be accessed via tag notation
        assert ds[0x0012, 0x0083].value == "YES"
        
        # Test another value via DataElement creation
        ds2 = Dataset()
        ds2[0x0012, 0x0083] = DataElement(0x00120083, 'CS', ConsentForDistribution.NO.value)
        assert ds2[0x0012, 0x0083].value == "NO"


class TestDistributionTypeEnum:
    """Test DistributionType enum compatibility with pydicom Dataset."""
    
    def test_distribution_type_values_exist(self):
        """Test that all expected DistributionType values exist."""
        assert DistributionType.NAMED_PROTOCOL.value == "NAMED_PROTOCOL"
        assert DistributionType.RESTRICTED_REUSE.value == "RESTRICTED_REUSE"
        assert DistributionType.PUBLIC_RELEASE.value == "PUBLIC_RELEASE"
    
    def test_distribution_type_named_protocol_can_be_set_to_dataset(self):
        """Test that DistributionType.NAMED_PROTOCOL can be set to Dataset without error."""
        ds = Dataset()
        ds.DistributionType = DistributionType.NAMED_PROTOCOL.value
        
        assert ds.DistributionType == "NAMED_PROTOCOL"
        assert hasattr(ds, 'DistributionType')
    
    def test_distribution_type_restricted_reuse_can_be_set_to_dataset(self):
        """Test that DistributionType.RESTRICTED_REUSE can be set to Dataset without error."""
        ds = Dataset()
        ds.DistributionType = DistributionType.RESTRICTED_REUSE.value
        
        assert ds.DistributionType == "RESTRICTED_REUSE"
        assert hasattr(ds, 'DistributionType')
    
    def test_distribution_type_public_release_can_be_set_to_dataset(self):
        """Test that DistributionType.PUBLIC_RELEASE can be set to Dataset without error."""
        ds = Dataset()
        ds.DistributionType = DistributionType.PUBLIC_RELEASE.value
        
        assert ds.DistributionType == "PUBLIC_RELEASE"
        assert hasattr(ds, 'DistributionType')
    
    def test_all_distribution_type_values_can_be_set_to_dataset(self):
        """Test that all DistributionType values can be set to Dataset without error."""
        for distribution in DistributionType:
            ds = Dataset()
            # Should not raise any exception
            ds.DistributionType = distribution.value
            
            # Verify the value was set correctly
            assert ds.DistributionType == distribution.value
            assert hasattr(ds, 'DistributionType')
    
    def test_distribution_type_enum_directly_can_be_set_to_dataset(self):
        """Test that DistributionType enum instances can be used directly with Dataset."""
        for distribution in DistributionType:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.DistributionType = distribution
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.DistributionType).endswith(distribution.value)
            assert hasattr(ds, 'DistributionType')
    
    def test_distribution_type_with_dicom_tag(self):
        """Test that DistributionType can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # First set the element using attribute notation
        ds.DistributionType = DistributionType.PUBLIC_RELEASE.value
        
        # Then verify it can be accessed via tag notation
        assert ds[0x0012, 0x0084].value == "PUBLIC_RELEASE"
        assert ds.DistributionType == "PUBLIC_RELEASE"
        
        # Test setting via DataElement creation
        ds2 = Dataset()
        ds2[0x0012, 0x0084] = DataElement(0x00120084, 'CS', DistributionType.NAMED_PROTOCOL.value)
        assert ds2.DistributionType == "NAMED_PROTOCOL"


class TestLongitudinalTemporalEventTypeEnum:
    """Test LongitudinalTemporalEventType enum compatibility with pydicom Dataset."""
    
    def test_longitudinal_temporal_event_type_values_exist(self):
        """Test that all expected LongitudinalTemporalEventType values exist."""
        assert LongitudinalTemporalEventType.ENROLLMENT.value == "ENROLLMENT"
        assert LongitudinalTemporalEventType.BASELINE.value == "BASELINE"
    
    def test_longitudinal_temporal_event_type_enrollment_can_be_set_to_dataset(self):
        """Test that LongitudinalTemporalEventType.ENROLLMENT can be set to Dataset without error."""
        ds = Dataset()
        ds.LongitudinalTemporalEventType = LongitudinalTemporalEventType.ENROLLMENT.value
        
        assert ds.LongitudinalTemporalEventType == "ENROLLMENT"
        assert hasattr(ds, 'LongitudinalTemporalEventType')
    
    def test_longitudinal_temporal_event_type_baseline_can_be_set_to_dataset(self):
        """Test that LongitudinalTemporalEventType.BASELINE can be set to Dataset without error."""
        ds = Dataset()
        ds.LongitudinalTemporalEventType = LongitudinalTemporalEventType.BASELINE.value
        
        assert ds.LongitudinalTemporalEventType == "BASELINE"
        assert hasattr(ds, 'LongitudinalTemporalEventType')
    
    def test_all_longitudinal_temporal_event_type_values_can_be_set_to_dataset(self):
        """Test that all LongitudinalTemporalEventType values can be set to Dataset without error."""
        for event_type in LongitudinalTemporalEventType:
            ds = Dataset()
            # Should not raise any exception
            ds.LongitudinalTemporalEventType = event_type.value
            
            # Verify the value was set correctly
            assert ds.LongitudinalTemporalEventType == event_type.value
            assert hasattr(ds, 'LongitudinalTemporalEventType')
    
    def test_longitudinal_temporal_event_type_enum_directly_can_be_set_to_dataset(self):
        """Test that LongitudinalTemporalEventType enum instances can be used directly with Dataset."""
        for event_type in LongitudinalTemporalEventType:
            ds = Dataset()
            # Should not raise any exception when using enum directly
            ds.LongitudinalTemporalEventType = event_type
            
            # pydicom stores the enum object, so we get the full representation
            assert str(ds.LongitudinalTemporalEventType).endswith(event_type.value)
            assert hasattr(ds, 'LongitudinalTemporalEventType')
    
    def test_longitudinal_temporal_event_type_with_dicom_tag(self):
        """Test that LongitudinalTemporalEventType can be set using DICOM tag notation."""
        from pydicom.dataelem import DataElement
        
        ds = Dataset()
        
        # First set the element using attribute notation
        ds.LongitudinalTemporalEventType = LongitudinalTemporalEventType.BASELINE.value
        
        # Then verify it can be accessed via tag notation
        assert ds[0x0012, 0x0053].value == "BASELINE"
        assert ds.LongitudinalTemporalEventType == "BASELINE"
        
        # Test setting via DataElement creation
        ds2 = Dataset()
        ds2[0x0012, 0x0053] = DataElement(0x00120053, 'CS', LongitudinalTemporalEventType.ENROLLMENT.value)
        assert ds2.LongitudinalTemporalEventType == "ENROLLMENT"