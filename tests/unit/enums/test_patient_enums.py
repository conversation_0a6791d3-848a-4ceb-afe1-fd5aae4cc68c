"""Tests for Patient-related DICOM enumerations."""

import pytest
from pydicom import Dataset

from pyrt_dicom.enums.patient_enums import PatientSex, ResponsiblePersonRole, TypeOfPatientID


class TestPatientEnums:
    """Test patient enumeration values can be set on pydicom Dataset without error."""

    @pytest.mark.parametrize("enum_value", list(PatientSex))
    def test_patient_sex_enum_values(self, enum_value: PatientSex):
        """Test that PatientSex enum values can be set to Dataset.PatientSex without error.
        
        Args:
            enum_value: PatientSex enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.PatientSex = enum_value.value
        assert dataset.PatientSex == enum_value.value

    @pytest.mark.parametrize("enum_value", list(ResponsiblePersonRole))
    def test_responsible_person_role_enum_values(self, enum_value: ResponsiblePersonRole):
        """Test that ResponsiblePersonRole enum values can be set to Dataset.ResponsiblePersonRole without error.
        
        Args:
            enum_value: ResponsiblePersonRole enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.ResponsiblePersonRole = enum_value.value
        assert dataset.ResponsiblePersonRole == enum_value.value

    @pytest.mark.parametrize("enum_value", list(TypeOfPatientID))
    def test_type_of_patient_id_enum_values(self, enum_value: TypeOfPatientID):
        """Test that TypeOfPatientID enum values can be set to Dataset.TypeOfPatientID without error.
        
        Args:
            enum_value: TypeOfPatientID enum member to test
        """
        dataset = Dataset()
        # Should not raise any exception
        dataset.TypeOfPatientID = enum_value.value
        assert dataset.TypeOfPatientID == enum_value.value

    def test_patient_sex_coverage(self):
        """Test that all expected PatientSex values are present."""
        expected_values = {"M", "F", "O"}
        actual_values = {member.value for member in PatientSex}
        assert actual_values == expected_values

    def test_responsible_person_role_coverage(self):
        """Test that all expected ResponsiblePersonRole values are present."""
        expected_values = {
            "OWNER", "PARENT", "CHILD", "SPOUSE", "SIBLING", "RELATIVE", 
            "GUARDIAN", "CUSTODIAN", "AGENT", "INVESTIGATOR", "VETERINARIAN"
        }
        actual_values = {member.value for member in ResponsiblePersonRole}
        assert actual_values == expected_values

    def test_type_of_patient_id_coverage(self):
        """Test that all expected TypeOfPatientID values are present."""
        expected_values = {"TEXT", "RFID", "BARCODE"}
        actual_values = {member.value for member in TypeOfPatientID}
        assert actual_values == expected_values