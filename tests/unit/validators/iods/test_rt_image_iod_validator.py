"""Test RT Image IOD DICOM validation against PS3.3 A.17.3 standard.

This module contains comprehensive tests for the RTImageIODValidator to ensure 
it correctly implements the DICOM PS3.3 A.17.3 specification for RT Image IODs.

Tests focus on:
- Required modules presence and validation per DICOM standard
- Conditional modules based on image data characteristics 
- Optional modules when present
- IOD-specific requirements (SOP Class, modality, RT Image parameters)
- Cross-module dependencies and consistency
- ValidationResult structure compliance
"""

from unittest.mock import patch
from pydicom import Dataset
from pydicom import uid

from src.pyrt_dicom.validators.iods.rt_image_iod_validator import RTImageIODValidator
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators.validation_result import ValidationResult


class TestRTImageIODValidator:
    """Test suite for RTImageIODValidator DICOM standard compliance."""

    def setup_method(self):
        """Set up test fixtures."""
        self.config = ValidationConfig()
        self.minimal_rt_image_dataset = self._create_minimal_rt_image_dataset()
        self.complete_rt_image_dataset = self._create_complete_rt_image_dataset()

    def _create_minimal_rt_image_dataset(self) -> Dataset:
        """Create minimal valid RT Image dataset with only required modules."""
        dataset = Dataset()
        
        # SOP Common Module (required)
        dataset.SOPClassUID = uid.RTImageStorage
        dataset.SOPInstanceUID = "*******.*******.9"
        
        # Patient Module (required) - key elements
        dataset.PatientName = "Test^Patient"
        dataset.PatientID = "12345"
        dataset.PatientBirthDate = "19900101"
        dataset.PatientSex = "M"
        
        # General Study Module (required)
        dataset.StudyInstanceUID = "*******.*******.9.10"
        
        # RT Series Module (required) 
        dataset.Modality = "RTIMAGE"
        dataset.SeriesInstanceUID = "*******.*******.9.10.11"
        
        # General Equipment Module (required)
        dataset.Manufacturer = "Test Manufacturer"
        
        # General Acquisition Module (required)
        dataset.AcquisitionDate = "20240101"
        dataset.AcquisitionTime = "120000"
        dataset.AcquisitionNumber = 1
        
        # General Image Module (required)
        dataset.InstanceNumber = "1"
        
        # Image Pixel Module (required)
        dataset.Rows = 1024
        dataset.Columns = 768
        dataset.BitsAllocated = 16
        dataset.SamplesPerPixel = 1
        
        # RT Image Module (required)
        dataset.RTImageLabel = "Portal Image 1"
        dataset.ConversionType = "DI"
        dataset.RTImagePlane = "NORMAL"
        
        return dataset

    def _create_complete_rt_image_dataset(self) -> Dataset:
        """Create complete RT Image dataset with conditional and optional modules."""
        dataset = self._create_minimal_rt_image_dataset()
        
        # Frame of Reference Module (optional)
        dataset.FrameOfReferenceUID = "*******.*******.9.10.12"
        
        # Multi-frame data (conditional)
        dataset.NumberOfFrames = 5
        
        # Optional modules
        dataset.ClinicalTrialSponsorName = "Test Sponsor"
        dataset.PatientAge = "030Y"  # Proper DICOM AS (Age String) format
        dataset.ApprovalStatus = "APPROVED"
        
        # RT Image specific parameters
        dataset.RTImageSID = "1000.0"
        dataset.GantryAngle = "0.0"
        dataset.PatientSupportAngle = "0.0"
        dataset.BeamLimitingDeviceAngle = "0.0"
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "PORTAL"]
        
        # Spatial data
        dataset.ImagePositionPatient = ["-200.0", "-150.0", "-100.0"]
        dataset.ImageOrientationPatient = ["1.0", "0.0", "0.0", "0.0", "1.0", "0.0"]
        dataset.PixelSpacing = ["2.5", "2.5"]
        
        return dataset

    def _create_invalid_sop_class_dataset(self) -> Dataset:
        """Create dataset with invalid SOP Class UID."""
        dataset = self._create_minimal_rt_image_dataset()
        dataset.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage (wrong)
        return dataset

    def _create_missing_required_module_dataset(self) -> Dataset:
        """Create dataset missing a required module."""
        dataset = self._create_minimal_rt_image_dataset()
        del dataset.PatientName  # Remove Patient Module key element
        del dataset.PatientID
        del dataset.PatientBirthDate  
        del dataset.PatientSex
        return dataset

    def _create_wrong_modality_dataset(self) -> Dataset:
        """Create dataset with wrong modality."""
        dataset = self._create_minimal_rt_image_dataset()
        dataset.Modality = "CT"  # Wrong modality
        return dataset

    # Base IOD Requirements Tests
    
    def test_valid_sop_class_uid(self):
        """Test that valid RT Image SOP Class UID is accepted."""
        result = RTImageIODValidator.validate(self.minimal_rt_image_dataset, self.config)
        
        # Should not have SOP Class UID errors
        sop_errors = [error for error in result.errors if "SOP Class UID" in error]
        assert len(sop_errors) == 0, f"Should not have SOP Class UID errors, got: {sop_errors}"

    def test_invalid_sop_class_uid(self):
        """Test that invalid SOP Class UID is rejected per DICOM standard."""
        invalid_dataset = self._create_invalid_sop_class_dataset()
        result = RTImageIODValidator.validate(invalid_dataset, self.config)
        
        # Should have SOP Class UID error
        sop_errors = [error for error in result.errors if "SOP Class UID" in error]
        assert len(sop_errors) > 0, "Should have SOP Class UID error for invalid SOP Class"
        assert RTImageIODValidator.RT_IMAGE_SOP_CLASS_UID in sop_errors[0]

    def test_missing_sop_class_uid(self):
        """Test that missing SOP Class UID is detected."""
        dataset = self._create_minimal_rt_image_dataset()
        del dataset.SOPClassUID
        
        result = RTImageIODValidator.validate(dataset, self.config)
        
        sop_errors = [error for error in result.errors if "SOP Class UID" in error and "required" in error]
        assert len(sop_errors) > 0, "Should detect missing SOP Class UID"

    def test_missing_sop_instance_uid(self):
        """Test that missing SOP Instance UID is detected."""
        dataset = self._create_minimal_rt_image_dataset()
        del dataset.SOPInstanceUID
        
        result = RTImageIODValidator.validate(dataset, self.config)
        
        sop_errors = [error for error in result.errors if "SOP Instance UID" in error and "required" in error]
        assert len(sop_errors) > 0, "Should detect missing SOP Instance UID"

    # Required Modules Tests

    @patch('src.pyrt_dicom.validators.modules.patient_validator.PatientValidator')
    @patch('src.pyrt_dicom.validators.modules.general_study_validator.GeneralStudyValidator')
    @patch('src.pyrt_dicom.validators.modules.rt_series_validator.RTSeriesValidator')
    def test_required_modules_validation(self, mock_rt_series, mock_study, mock_patient):
        """Test that all required modules are validated when present."""
        # Set up mocks to return empty ValidationResult
        mock_result = ValidationResult()
        mock_patient.validate.return_value = mock_result
        mock_study.validate.return_value = mock_result
        mock_rt_series.validate.return_value = mock_result
        
        _ = RTImageIODValidator.validate(self.minimal_rt_image_dataset, self.config)
        
        # Verify validators were called
        mock_patient.validate.assert_called_once()
        mock_study.validate.assert_called_once()
        mock_rt_series.validate.assert_called_once()

    def test_missing_required_module_detected(self):
        """Test that missing required modules are detected."""
        invalid_dataset = self._create_missing_required_module_dataset()
        result = RTImageIODValidator.validate(invalid_dataset, self.config)
        
        # Should have error for missing Patient Module
        patient_errors = [error for error in result.errors if "Patient Module" in error and "missing" in error]
        assert len(patient_errors) > 0, "Should detect missing Patient Module"

    # IOD-Specific Requirements Tests

    def test_valid_rt_image_modality(self):
        """Test that RTIMAGE modality is accepted."""
        result = RTImageIODValidator.validate(self.minimal_rt_image_dataset, self.config)
        
        # Should not have modality errors
        modality_errors = [error for error in result.errors if "modality" in error.lower()]
        assert len(modality_errors) == 0, f"Should not have modality errors, got: {modality_errors}"

    def test_invalid_modality_rejected(self):
        """Test that wrong modality is rejected."""
        invalid_dataset = self._create_wrong_modality_dataset()
        result = RTImageIODValidator.validate(invalid_dataset, self.config)
        
        # Should have modality error
        modality_errors = [error for error in result.errors if "RTIMAGE" in error and "modality" in error.lower()]
        assert len(modality_errors) > 0, "Should detect wrong modality"

    def test_rt_image_parameters_validation(self):
        """Test RT Image specific parameter validation."""
        dataset = self._create_complete_rt_image_dataset()
        # Set invalid value by manipulating the data element directly
        from pydicom.dataelem import DataElement
        from pydicom.tag import Tag
        
        # Create data element and set internal value directly
        element = DataElement(Tag(0x3002, 0x0026), 'DS', '1000.0')  # Valid first
        element._value = "invalid_numeric"  # Then set invalid value directly
        dataset[Tag(0x3002, 0x0026)] = element
        
        result = RTImageIODValidator.validate(dataset, self.config)
        
        # Should have SID error
        sid_errors = [error for error in result.errors if "RT Image SID" in error]
        assert len(sid_errors) > 0, "Should detect invalid RT Image SID"

    def test_beam_geometry_validation(self):
        """Test beam geometry parameter validation."""
        dataset = self._create_complete_rt_image_dataset()
        # Set invalid value by manipulating the data element directly
        from pydicom.dataelem import DataElement
        from pydicom.tag import Tag
        
        # Create data element and set internal value directly
        element = DataElement(Tag(0x300A, 0x011E), 'DS', '0.0')  # Valid first
        element._value = "invalid_angle"  # Then set invalid value directly
        dataset[Tag(0x300A, 0x011E)] = element
        
        result = RTImageIODValidator.validate(dataset, self.config)
        
        # Should have angle error
        angle_errors = [error for error in result.errors if "Gantry Angle" in error]
        assert len(angle_errors) > 0, "Should detect invalid Gantry Angle"

    def test_image_type_consistency_validation(self):
        """Test Image Type consistency validation."""
        dataset = self._create_complete_rt_image_dataset()
        dataset.ImageType = ["INVALID", "PRIMARY"]  # Invalid first value
        
        result = RTImageIODValidator.validate(dataset, self.config)
        
        # Should have image type warning
        image_type_warnings = [warning for warning in result.warnings if "Image Type" in warning]
        assert len(image_type_warnings) > 0, "Should detect invalid Image Type"

    # Conditional Modules Tests

    def test_multi_frame_module_detection(self):
        """Test that multi-frame module requirement is detected."""
        dataset = self._create_minimal_rt_image_dataset()
        dataset.NumberOfFrames = 5  # Multi-frame data present
        # But no multi-frame module key elements
        
        result = RTImageIODValidator.validate(dataset, self.config)
        
        # Should require multi-frame module
        _ = [error for error in result.errors if "Multi-frame Module" in error]
        # Note: This might not trigger error if the conditional logic is sophisticated
        # The actual behavior depends on the implementation

    def test_contrast_media_detection(self):
        """Test contrast media detection for conditional module."""
        dataset = self._create_minimal_rt_image_dataset()
        dataset.ContrastBolusAgent = "Iodine"  # Contrast used
        # But no contrast module validation (mock would be needed)
        
        # This is basic test - full test would need to mock ContrastBolusValidator
        result = RTImageIODValidator.validate(dataset, self.config)
        
        # Test passes if no exceptions are thrown
        assert result is not None

    # Cross-Module Dependencies Tests

    def test_cross_module_validation_called(self):
        """Test that cross-module validation is called when enabled."""
        config = ValidationConfig()
        config.validate_cross_module_dependencies = True
        
        with patch('src.pyrt_dicom.validators.cross_module.uid_consistency_validator.UIDConsistencyValidator') as mock_uid:
            mock_uid.validate.return_value = ValidationResult()
            
            _ = RTImageIODValidator.validate(self.complete_rt_image_dataset, config)
            
            # Should call UID consistency validator
            mock_uid.validate.assert_called_once()

    def test_spatial_data_detection(self):
        """Test spatial data detection for cross-module validation."""
        # Dataset with spatial elements should trigger spatial validation
        assert RTImageIODValidator._has_spatial_data(self.complete_rt_image_dataset)
        
        # Dataset without spatial elements should not trigger spatial validation
        minimal_dataset = self._create_minimal_rt_image_dataset()
        assert not RTImageIODValidator._has_spatial_data(minimal_dataset)

    def test_temporal_data_detection(self):
        """Test temporal data detection for cross-module validation."""
        # Dataset with temporal elements should trigger temporal validation
        assert RTImageIODValidator._has_temporal_data(self.complete_rt_image_dataset)
        
        # Dataset without temporal elements should not trigger temporal validation
        dataset_no_temporal = Dataset()
        assert not RTImageIODValidator._has_temporal_data(dataset_no_temporal)

    # Utility Methods Tests

    def test_uid_validation(self):
        """Test UID format validation utility."""
        # Valid UIDs
        assert RTImageIODValidator._is_valid_uid("*******.5")
        assert RTImageIODValidator._is_valid_uid("1.2.840.10008.5.1.4.1.1.481.1")
        
        # Invalid UIDs
        assert not RTImageIODValidator._is_valid_uid("")
        assert not RTImageIODValidator._is_valid_uid("1.2.3..4")  # Double dot
        assert not RTImageIODValidator._is_valid_uid(".1.2.3")    # Leading dot
        assert not RTImageIODValidator._is_valid_uid("1.2.3.")    # Trailing dot
        assert not RTImageIODValidator._is_valid_uid("abc.def")   # Non-numeric

    def test_conditional_checks(self):
        """Test conditional module detection methods."""
        # Multi-frame data check
        dataset_single = self._create_minimal_rt_image_dataset()
        assert not RTImageIODValidator._check_multi_frame_data(dataset_single)
        
        dataset_multi = self._create_complete_rt_image_dataset()
        assert RTImageIODValidator._check_multi_frame_data(dataset_multi)
        
        # Contrast media check
        dataset_no_contrast = self._create_minimal_rt_image_dataset()
        assert not RTImageIODValidator._check_contrast_media_used(dataset_no_contrast)
        
        dataset_contrast = self._create_minimal_rt_image_dataset()
        dataset_contrast.ContrastBolusAgent = "Iodine"
        assert RTImageIODValidator._check_contrast_media_used(dataset_contrast)
        
        # Cine image check
        dataset_no_cine = self._create_minimal_rt_image_dataset()
        assert not RTImageIODValidator._check_cine_image(dataset_no_cine)
        
        dataset_cine = self._create_complete_rt_image_dataset()
        dataset_cine.CineRate = "10.0"
        assert RTImageIODValidator._check_cine_image(dataset_cine)  # Has both multi-frame and cine elements

    # ValidationResult Structure Tests

    def test_validation_result_structure(self):
        """Test that ValidationResult is properly structured."""
        result = RTImageIODValidator.validate(self.minimal_rt_image_dataset, self.config)
        
        # Should return ValidationResult instance
        assert isinstance(result, ValidationResult)
        
        # Should have proper attributes
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert hasattr(result, 'is_valid')
        assert hasattr(result, 'error_count')
        assert hasattr(result, 'warning_count')

    def test_validation_config_options(self):
        """Test validation configuration options."""
        # Test with optional modules disabled
        config = ValidationConfig()
        config.validate_optional_modules = False
        
        result = RTImageIODValidator.validate(self.complete_rt_image_dataset, config)
        assert isinstance(result, ValidationResult)
        
        # Test with cross-module validation disabled
        config.validate_cross_module_dependencies = False
        
        result = RTImageIODValidator.validate(self.complete_rt_image_dataset, config)
        assert isinstance(result, ValidationResult)

    def test_validation_with_no_config(self):
        """Test validation without providing configuration."""
        result = RTImageIODValidator.validate(self.minimal_rt_image_dataset)
        
        # Should work with default configuration
        assert isinstance(result, ValidationResult)

    def test_error_message_format(self):
        """Test that error messages are properly formatted."""
        invalid_dataset = self._create_invalid_sop_class_dataset()
        result = RTImageIODValidator.validate(invalid_dataset, self.config)
        
        # Error messages should be strings
        for error in result.errors:
            assert isinstance(error, str)
            assert len(error) > 0
        
        # Should have specific error content
        sop_errors = [error for error in result.errors if "SOP Class UID" in error]
        if sop_errors:
            assert "RT Image IOD" in sop_errors[0]