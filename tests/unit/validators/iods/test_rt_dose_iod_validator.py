"""Test RT Dose IOD DICOM validation against PS3.3 A.18.3 standard.

This module contains comprehensive tests for the RTDoseIODValidator to ensure 
it correctly implements the DICOM PS3.3 A.18.3 specification for RT Dose IODs.

Tests focus on:
- Required modules presence and validation per DICOM standard
- Conditional modules based on dose data characteristics 
- Optional modules when present
- IOD-specific requirements (SOP Class, modality, dose parameters)
- Cross-module dependencies and consistency
- ValidationResult structure compliance
"""

from unittest.mock import Mock, patch
from pydicom import Dataset
from pydicom import uid

from src.pyrt_dicom.validators.iods.rt_dose_iod_validator import RTDoseIODValidator
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators.validation_result import ValidationResult


class TestRTDoseIODValidator:
    """Test suite for RTDoseIODValidator DICOM standard compliance."""

    def setup_method(self):
        """Set up test fixtures."""
        self.config = ValidationConfig()
        self.minimal_rt_dose_dataset = self._create_minimal_rt_dose_dataset()
        self.complete_rt_dose_dataset = self._create_complete_rt_dose_dataset()

    def _create_minimal_rt_dose_dataset(self) -> Dataset:
        """Create minimal valid RT Dose dataset with only required modules."""
        dataset = Dataset()
        
        # SOP Common Module (required)
        dataset.SOPClassUID = uid.RTDoseStorage
        dataset.SOPInstanceUID = "*******.*******.9"
        
        # Patient Module (required) - key elements
        dataset.PatientName = "Test^Patient"
        dataset.PatientID = "12345"
        dataset.PatientBirthDate = "19900101"
        dataset.PatientSex = "M"
        
        # General Study Module (required)
        dataset.StudyInstanceUID = "*******.*******.9.10"
        
        # RT Series Module (required) 
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.*******.9.10.11"
        
        # Frame of Reference Module (required)
        dataset.FrameOfReferenceUID = "*******.*******.**********"
        
        # General Equipment Module (required)
        dataset.Manufacturer = "Test Manufacturer"
        
        # RT Dose Module (required)
        dataset.DoseUnits = "GY"
        dataset.DoseType = "PHYSICAL"
        dataset.DoseSummationType = "PLAN"
        
        return dataset

    def _create_complete_rt_dose_dataset(self) -> Dataset:
        """Create complete RT Dose dataset with grid-based doses and optional modules."""
        dataset = self._create_minimal_rt_dose_dataset()
        
        # Add grid-based dose data to trigger conditional modules
        dataset.PixelData = b'\x00' * 1000  # Dummy pixel data
        dataset.Rows = 100
        dataset.Columns = 100
        dataset.BitsAllocated = 16
        dataset.PixelSpacing = [1.0, 1.0]
        dataset.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        dataset.InstanceNumber = 1
        
        # Multi-frame data
        dataset.NumberOfFrames = 10
        
        # Optional modules
        dataset.ClinicalTrialSponsorName = "Test Sponsor"
        dataset.PatientAge = "030Y"  # Proper DICOM AS (Age String) format
        dataset.DVHSequence = []
        
        return dataset

    def _create_invalid_sop_class_dataset(self) -> Dataset:
        """Create dataset with invalid SOP Class UID."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage (wrong)
        return dataset

    def _create_missing_required_module_dataset(self) -> Dataset:
        """Create dataset missing a required module."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.PatientName  # Remove Patient Module key element
        del dataset.PatientID
        del dataset.PatientBirthDate  
        del dataset.PatientSex
        return dataset

    # Base IOD Requirements Tests
    
    def test_valid_sop_class_uid(self):
        """Test that valid RT Dose SOP Class UID is accepted."""
        result = RTDoseIODValidator.validate(self.minimal_rt_dose_dataset, self.config)
        
        # Should not have SOP Class UID errors
        sop_errors = [error for error in result.errors if "SOP Class UID" in error]
        assert len(sop_errors) == 0, f"Should not have SOP Class UID errors, got: {sop_errors}"

    def test_invalid_sop_class_uid(self):
        """Test that invalid SOP Class UID is rejected per DICOM standard."""
        invalid_dataset = self._create_invalid_sop_class_dataset()
        result = RTDoseIODValidator.validate(invalid_dataset, self.config)
        
        # Should have SOP Class UID error
        sop_errors = [error for error in result.errors if "SOP Class UID" in error]
        assert len(sop_errors) > 0, "Should have SOP Class UID error for invalid SOP Class"
        assert RTDoseIODValidator.RT_DOSE_SOP_CLASS_UID in sop_errors[0]

    def test_missing_sop_class_uid(self):
        """Test that missing SOP Class UID is detected."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.SOPClassUID
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have missing SOP Class UID error
        sop_errors = [error for error in result.errors if "SOP Class UID" in error and "required" in error]
        assert len(sop_errors) > 0, "Should have missing SOP Class UID error"

    def test_missing_sop_instance_uid(self):
        """Test that missing SOP Instance UID is detected."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.SOPInstanceUID
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have missing SOP Instance UID error
        sop_errors = [error for error in result.errors if "SOP Instance UID" in error and "required" in error]
        assert len(sop_errors) > 0, "Should have missing SOP Instance UID error"

    # Required Modules Tests (Per DICOM PS3.3 A.18.3 Table A.18.3-1)
    
    def test_all_required_modules_present(self):
        """Test that all 7 required modules are correctly identified as present."""
        # Mock all module validators to return successful results
        mock_validators = {
            'PatientValidator': Mock(return_value=ValidationResult()),
            'GeneralStudyValidator': Mock(return_value=ValidationResult()),
            'RTSeriesValidator': Mock(return_value=ValidationResult()),
            'FrameOfReferenceValidator': Mock(return_value=ValidationResult()),
            'GeneralEquipmentValidator': Mock(return_value=ValidationResult()),
            'RTDoseValidator': Mock(return_value=ValidationResult()),
            'SOPCommonValidator': Mock(return_value=ValidationResult())
        }
        
        with patch.dict('sys.modules', {
            'src.pyrt_dicom.validators.modules.patient_validator': Mock(PatientValidator=mock_validators['PatientValidator']),
            'src.pyrt_dicom.validators.modules.general_study_validator': Mock(GeneralStudyValidator=mock_validators['GeneralStudyValidator']),
            'src.pyrt_dicom.validators.modules.rt_series_validator': Mock(RTSeriesValidator=mock_validators['RTSeriesValidator']),
            'src.pyrt_dicom.validators.modules.frame_of_reference_validator': Mock(FrameOfReferenceValidator=mock_validators['FrameOfReferenceValidator']),
            'src.pyrt_dicom.validators.modules.general_equipment_validator': Mock(GeneralEquipmentValidator=mock_validators['GeneralEquipmentValidator']),
            'src.pyrt_dicom.validators.modules.rt_dose_validator': Mock(RTDoseValidator=mock_validators['RTDoseValidator']),
            'src.pyrt_dicom.validators.modules.sop_common_validator': Mock(SOPCommonValidator=mock_validators['SOPCommonValidator'])
        }):
            result = RTDoseIODValidator.validate(self.minimal_rt_dose_dataset, self.config)
        
        # Should not have "required" and "missing" errors for modules
        missing_errors = [error for error in result.errors if "required" in error.lower() and "missing" in error.lower()]
        assert len(missing_errors) == 0, f"Should not have missing required module errors, got: {missing_errors}"

    def test_missing_patient_module(self):
        """Test detection of missing Patient Module (required per DICOM standard)."""
        dataset = self._create_missing_required_module_dataset()
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have Patient Module missing error
        patient_errors = [error for error in result.errors if "Patient Module" in error and "required" in error]
        assert len(patient_errors) > 0, "Should detect missing Patient Module"

    def test_missing_study_module(self):
        """Test detection of missing General Study Module."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.StudyInstanceUID
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have General Study Module missing error
        study_errors = [error for error in result.errors if "General Study Module" in error and "required" in error]
        assert len(study_errors) > 0, "Should detect missing General Study Module"

    def test_missing_rt_series_module(self):
        """Test detection of missing RT Series Module."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.Modality
        del dataset.SeriesInstanceUID
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have RT Series Module missing error
        series_errors = [error for error in result.errors if "RT Series Module" in error and "required" in error]
        assert len(series_errors) > 0, "Should detect missing RT Series Module"

    def test_missing_frame_of_reference_module(self):
        """Test detection of missing Frame of Reference Module."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.FrameOfReferenceUID
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have Frame of Reference Module missing error
        frame_errors = [error for error in result.errors if "Frame of Reference Module" in error and "required" in error]
        assert len(frame_errors) > 0, "Should detect missing Frame of Reference Module"

    def test_missing_general_equipment_module(self):
        """Test detection of missing General Equipment Module."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.Manufacturer
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have General Equipment Module missing error
        equipment_errors = [error for error in result.errors if "General Equipment Module" in error and "required" in error]
        assert len(equipment_errors) > 0, "Should detect missing General Equipment Module"

    def test_missing_rt_dose_module(self):
        """Test detection of missing RT Dose Module."""
        dataset = self._create_minimal_rt_dose_dataset()
        del dataset.DoseUnits
        del dataset.DoseType
        del dataset.DoseSummationType
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have RT Dose Module missing error
        dose_errors = [error for error in result.errors if "RT Dose Module" in error and "required" in error]
        assert len(dose_errors) > 0, "Should detect missing RT Dose Module"

    # Conditional Modules Tests (Per DICOM PS3.3 A.18.3)
    
    def test_grid_based_dose_conditional_modules_required(self):
        """Test that grid-based dose data triggers required conditional modules."""
        # Create dataset with PixelData (grid-based doses)
        dataset = self.complete_rt_dose_dataset
        
        # Mock conditional module validators
        mock_validators = {
            'GeneralImageValidator': Mock(return_value=ValidationResult()),
            'ImagePlaneValidator': Mock(return_value=ValidationResult()),
            'ImagePixelValidator': Mock(return_value=ValidationResult()),
            'MultiFrameValidator': Mock(return_value=ValidationResult()),
            'FrameExtractionValidator': Mock(return_value=ValidationResult())
        }
        
        with patch.dict('sys.modules', {
            'src.pyrt_dicom.validators.modules.general_image_validator': Mock(GeneralImageValidator=mock_validators['GeneralImageValidator']),
            'src.pyrt_dicom.validators.modules.image_plane_validator': Mock(ImagePlaneValidator=mock_validators['ImagePlaneValidator']),
            'src.pyrt_dicom.validators.modules.image_pixel_validator': Mock(ImagePixelValidator=mock_validators['ImagePixelValidator']),
            'src.pyrt_dicom.validators.modules.multi_frame_validator': Mock(MultiFrameValidator=mock_validators['MultiFrameValidator']),
            'src.pyrt_dicom.validators.modules.frame_extraction_validator': Mock(FrameExtractionValidator=mock_validators['FrameExtractionValidator'])
        }):
            # Mock required module validators to avoid errors
            self._mock_required_validators()
            
            RTDoseIODValidator.validate(dataset, self.config)
        
        # Should call conditional validators when PixelData present
        assert mock_validators['GeneralImageValidator'].validate.called, "Should validate General Image Module for grid-based doses"
        assert mock_validators['ImagePlaneValidator'].validate.called, "Should validate Image Plane Module for grid-based doses"
        assert mock_validators['ImagePixelValidator'].validate.called, "Should validate Image Pixel Module for grid-based doses"

    def test_missing_general_image_module_for_grid_doses(self):
        """Test detection of missing General Image Module when grid-based doses present."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.PixelData = b'\x00' * 1000  # Add grid-based dose data
        # Don't add InstanceNumber (General Image Module key element)
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should require General Image Module
        image_errors = [error for error in result.errors if "General Image Module" in error and "required" in error]
        assert len(image_errors) > 0, "Should require General Image Module for grid-based doses"

    def test_missing_image_plane_module_for_grid_doses(self):
        """Test detection of missing Image Plane Module when grid-based doses present."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.PixelData = b'\x00' * 1000  # Add grid-based dose data
        # Don't add PixelSpacing, ImageOrientationPatient (Image Plane Module key elements)
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should require Image Plane Module
        plane_errors = [error for error in result.errors if "Image Plane Module" in error and "required" in error]
        assert len(plane_errors) > 0, "Should require Image Plane Module for grid-based doses"

    def test_missing_image_pixel_module_for_grid_doses(self):
        """Test detection of missing Image Pixel Module when grid-based doses present."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.PixelData = b'\x00' * 1000  # Add grid-based dose data
        # Don't add Rows, Columns, BitsAllocated (Image Pixel Module key elements)
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should require Image Pixel Module  
        pixel_errors = [error for error in result.errors if "Image Pixel Module" in error and "required" in error]
        assert len(pixel_errors) > 0, "Should require Image Pixel Module for grid-based doses"

    def test_multiframe_module_required_for_multiple_frames(self):
        """Test that Multi-frame Module condition is checked when NumberOfFrames > 1."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.PixelData = b'\x00' * 1000
        dataset.NumberOfFrames = 5  # Multi-frame data triggers condition
        
        # Mock multi-frame validator to verify it gets called
        mock_multiframe_validator = Mock(return_value=ValidationResult())
        
        with patch.dict('sys.modules', {
            'src.pyrt_dicom.validators.modules.multi_frame_validator': Mock(MultiFrameValidator=mock_multiframe_validator)
        }):
            # Mock all other validators to avoid errors
            self._mock_all_validators()
            
            RTDoseIODValidator.validate(dataset, self.config)
        
        # Should call Multi-frame validator when NumberOfFrames > 1 and PixelData present
        assert mock_multiframe_validator.validate.called, "Should validate Multi-frame Module for multi-frame data"

    def test_multiframe_module_not_required_for_single_frame(self):
        """Test that Multi-frame Module is not required when NumberOfFrames = 1."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.PixelData = b'\x00' * 1000
        # NumberOfFrames defaults to 1, so Multi-frame Module should not be required
        
        # Mock required validators to avoid other errors
        self._mock_required_validators()
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should not require Multi-frame Module for single frame
        frame_errors = [error for error in result.errors if "Multi-frame Module" in error and "required" in error and "multi-frame" in error.lower()]
        assert len(frame_errors) == 0, "Should not require Multi-frame Module for single frame data"

    def test_frame_extraction_module_conditional(self):
        """Test Frame Extraction Module validation when frame extraction attributes present."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.MultiFrameSourceSOPInstanceUID = "*******.5"  # Frame extraction attribute
        
        # Mock frame extraction validator to verify it gets called
        mock_extraction_validator = Mock(return_value=ValidationResult())
        
        with patch.dict('sys.modules', {
            'src.pyrt_dicom.validators.modules.frame_extraction_validator': Mock(FrameExtractionValidator=mock_extraction_validator)
        }):
            # Mock all other validators to avoid errors
            self._mock_all_validators()
            
            RTDoseIODValidator.validate(dataset, self.config)
        
        # Should call Frame Extraction validator when frame extraction attributes present
        assert mock_extraction_validator.validate.called, "Should validate Frame Extraction Module when frame extraction attributes present"

    def test_no_conditional_modules_without_triggers(self):
        """Test that conditional modules are not required without triggering conditions."""
        dataset = self._create_minimal_rt_dose_dataset()
        # No PixelData, no multi-frame, no frame extraction attributes
        
        # Mock required validators to avoid other errors
        self._mock_required_validators()
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should not require conditional modules
        conditional_errors = [
            error for error in result.errors 
            if any(module in error for module in ["General Image Module", "Image Plane Module", "Image Pixel Module", "Multi-frame Module", "Frame Extraction Module"])
            and "required" in error
        ]
        assert len(conditional_errors) == 0, f"Should not require conditional modules without triggers, got: {conditional_errors}"

    # Optional Modules Tests
    
    def test_optional_modules_validated_when_present(self):
        """Test that optional modules are validated when present but not required when absent."""
        dataset = self._create_complete_rt_dose_dataset()
        
        # Mock all validators including optional ones
        mock_optional_validators = {
            'ClinicalTrialSubjectValidator': Mock(return_value=ValidationResult()),
            'PatientStudyValidator': Mock(return_value=ValidationResult()),
            'ClinicalTrialStudyValidator': Mock(return_value=ValidationResult()),
            'ClinicalTrialSeriesValidator': Mock(return_value=ValidationResult()),
            'RTDVHValidator': Mock(return_value=ValidationResult()),
            'CommonInstanceReferenceValidator': Mock(return_value=ValidationResult())
        }
        
        with patch.dict('sys.modules', {
            'src.pyrt_dicom.validators.modules.clinical_trial_subject_validator': Mock(ClinicalTrialSubjectValidator=mock_optional_validators['ClinicalTrialSubjectValidator']),
            'src.pyrt_dicom.validators.modules.patient_study_validator': Mock(PatientStudyValidator=mock_optional_validators['PatientStudyValidator']),
            'src.pyrt_dicom.validators.modules.clinical_trial_study_validator': Mock(ClinicalTrialStudyValidator=mock_optional_validators['ClinicalTrialStudyValidator']),
            'src.pyrt_dicom.validators.modules.clinical_trial_series_validator': Mock(ClinicalTrialSeriesValidator=mock_optional_validators['ClinicalTrialSeriesValidator']),
            'src.pyrt_dicom.validators.modules.rt_dvh_validator': Mock(RTDVHValidator=mock_optional_validators['RTDVHValidator']),
            'src.pyrt_dicom.validators.modules.common_instance_reference_validator': Mock(CommonInstanceReferenceValidator=mock_optional_validators['CommonInstanceReferenceValidator'])
        }):
            # Mock all other validators
            self._mock_all_validators()
            
            self.config.validate_optional_modules = True
            RTDoseIODValidator.validate(dataset, self.config)
        
        # Optional modules should be validated when present
        assert mock_optional_validators['ClinicalTrialSubjectValidator'].validate.called, "Should validate Clinical Trial Subject Module when present"
        assert mock_optional_validators['PatientStudyValidator'].validate.called, "Should validate Patient Study Module when present"
        assert mock_optional_validators['RTDVHValidator'].validate.called, "Should validate RT DVH Module when present"

    def test_optional_modules_not_required_when_absent(self):
        """Test that missing optional modules do not generate errors."""
        dataset = self._create_minimal_rt_dose_dataset()
        # No optional module elements
        
        # Mock required validators
        self._mock_required_validators()
        
        self.config.validate_optional_modules = True
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should not have errors for missing optional modules
        optional_errors = [
            error for error in result.errors
            if any(module in error for module in [
                "Clinical Trial Subject Module", "Patient Study Module", 
                "Clinical Trial Study Module", "Clinical Trial Series Module",
                "RT DVH Module", "Common Instance Reference Module"
            ]) and "required" in error
        ]
        assert len(optional_errors) == 0, f"Should not require optional modules when absent, got: {optional_errors}"

    # RT Dose Specific Requirements Tests
    
    def test_rt_series_modality_validation(self):
        """Test that RT Series modality must be 'RTDOSE' for RT Dose IOD."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.Modality = "CT"  # Wrong modality
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have modality error
        modality_errors = [error for error in result.errors if "modality" in error.lower() and "RTDOSE" in error]
        assert len(modality_errors) > 0, "Should reject non-RTDOSE modality for RT Dose IOD"

    def test_dose_units_validation(self):
        """Test dose units validation (should warn for non-standard values)."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.DoseUnits = "INVALID_UNIT"
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have dose units warning
        dose_warnings = [warning for warning in result.warnings if "Dose Units" in warning]
        assert len(dose_warnings) > 0, "Should warn for non-standard dose units"

    def test_dose_type_validation(self):
        """Test dose type validation (should warn for non-standard values)."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.DoseType = "INVALID_TYPE"
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should have dose type warning
        type_warnings = [warning for warning in result.warnings if "Dose Type" in warning]
        assert len(type_warnings) > 0, "Should warn for non-standard dose type"

    def test_standard_dose_units_accepted(self):
        """Test that standard dose units are accepted without warnings."""
        for dose_unit in ['GY', 'RELATIVE']:
            dataset = self._create_minimal_rt_dose_dataset()
            dataset.DoseUnits = dose_unit
            
            # Mock required validators
            self._mock_required_validators()
            
            result = RTDoseIODValidator.validate(dataset, self.config)
            
            # Should not have dose units warnings
            dose_warnings = [warning for warning in result.warnings if "Dose Units" in warning]
            assert len(dose_warnings) == 0, f"Should accept standard dose unit '{dose_unit}' without warnings"

    def test_standard_dose_types_accepted(self):
        """Test that standard dose types are accepted without warnings."""
        for dose_type in ['PHYSICAL', 'EFFECTIVE', 'ERROR']:
            dataset = self._create_minimal_rt_dose_dataset()
            dataset.DoseType = dose_type
            
            # Mock required validators
            self._mock_required_validators()
            
            result = RTDoseIODValidator.validate(dataset, self.config)
            
            # Should not have dose type warnings
            type_warnings = [warning for warning in result.warnings if "Dose Type" in warning]
            assert len(type_warnings) == 0, f"Should accept standard dose type '{dose_type}' without warnings"

    # Cross-Module Dependencies Tests
    
    def test_cross_module_dependencies_when_enabled(self):
        """Test that cross-module dependencies are validated when enabled."""
        dataset = self._create_complete_rt_dose_dataset()
        
        # Mock cross-module validators
        mock_frame_ref_validator = Mock(return_value=ValidationResult())
        mock_rt_ref_validator = Mock(return_value=ValidationResult())
        
        with patch.dict('sys.modules', {
            'src.pyrt_dicom.validators.cross_module.frame_reference_consistency_validator': Mock(FrameReferenceConsistencyValidator=mock_frame_ref_validator),
            'src.pyrt_dicom.validators.cross_module.rt_plan_structure_reference_validator': Mock(RTPlanStructureReferenceValidator=mock_rt_ref_validator)
        }):
            # Mock all other validators
            self._mock_all_validators()
            
            self.config.validate_cross_module_dependencies = True
            RTDoseIODValidator.validate(dataset, self.config)
        
        # Cross-module validators should be called
        assert mock_frame_ref_validator.validate.called, "Should validate frame reference consistency"
        assert mock_rt_ref_validator.validate.called, "Should validate RT plan/structure references"

    def test_cross_module_dependencies_disabled(self):
        """Test that cross-module dependencies are skipped when disabled."""
        dataset = self._create_complete_rt_dose_dataset()
        
        # Mock cross-module validators
        mock_frame_ref_validator = Mock(return_value=ValidationResult())
        mock_rt_ref_validator = Mock(return_value=ValidationResult())
        
        with patch.dict('sys.modules', {
            'src.pyrt_dicom.validators.cross_module.frame_reference_consistency_validator': Mock(FrameReferenceConsistencyValidator=mock_frame_ref_validator),
            'src.pyrt_dicom.validators.cross_module.rt_plan_structure_reference_validator': Mock(RTPlanStructureReferenceValidator=mock_rt_ref_validator)
        }):
            # Mock required validators
            self._mock_required_validators()
            
            self.config.validate_cross_module_dependencies = False
            RTDoseIODValidator.validate(dataset, self.config)
        
        # Cross-module validators should not be called
        assert not mock_frame_ref_validator.validate.called, "Should not validate frame reference consistency when disabled"
        assert not mock_rt_ref_validator.validate.called, "Should not validate RT references when disabled"

    # ValidationResult Structure Tests
    
    def test_validation_result_structure(self):
        """Test that validator returns proper ValidationResult structure."""
        dataset = self._create_minimal_rt_dose_dataset()
        
        result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should return ValidationResult instance
        assert isinstance(result, ValidationResult), "Should return ValidationResult instance"
        
        # Should have required attributes
        assert hasattr(result, 'errors'), "ValidationResult should have errors attribute"
        assert hasattr(result, 'warnings'), "ValidationResult should have warnings attribute"
        assert isinstance(result.errors, list), "errors should be a list"
        assert isinstance(result.warnings, list), "warnings should be a list"

    def test_validation_result_merge_functionality(self):
        """Test that ValidationResult merge functionality works for module results."""
        # This tests the merge pattern used in the validator
        dataset = self._create_minimal_rt_dose_dataset()
        
        # Create mock module validator that returns result with errors/warnings
        mock_validator = Mock()
        module_result = ValidationResult()
        module_result.add_error("Test module error")
        module_result.add_warning("Test module warning")
        mock_validator.validate.return_value = module_result
        
        with patch('src.pyrt_dicom.validators.modules.patient_validator.PatientValidator', mock_validator):
            result = RTDoseIODValidator.validate(dataset, self.config)
        
        # Should include module errors and warnings
        assert "Test module error" in result.errors, "Should merge module errors"
        assert "Test module warning" in result.warnings, "Should merge module warnings"

    # Configuration Tests
    
    def test_validation_config_defaults(self):
        """Test validation with default configuration."""
        dataset = self._create_minimal_rt_dose_dataset()
        
        result = RTDoseIODValidator.validate(dataset)  # No config provided
        
        # Should handle default configuration
        assert isinstance(result, ValidationResult), "Should handle default config"

    def test_validation_config_conditional_requirements_disabled(self):
        """Test that conditional requirements can be disabled via config."""
        dataset = self._create_minimal_rt_dose_dataset()
        dataset.Modality = "CT"  # Wrong modality, but should be ignored
        
        config = ValidationConfig()
        config.validate_conditional_requirements = False
        
        # Mock all validators to return clean results (including RT Series validator)
        with self._mock_required_validators():
            result = RTDoseIODValidator.validate(dataset, config)
        
        # Should not have IOD-specific modality errors when conditional requirements disabled
        # Note: Module-level validation may still occur, but IOD-specific validation should be skipped
        iod_modality_errors = [error for error in result.errors if "RT Dose IOD" in error and "modality" in error.lower()]
        assert len(iod_modality_errors) == 0, "Should skip IOD-specific conditional requirements when disabled"

    # Helper Methods
    
    def _mock_required_validators(self):
        """Mock all required module validators to return successful results."""
        mock_validators = {
            'PatientValidator': Mock(return_value=ValidationResult()),
            'GeneralStudyValidator': Mock(return_value=ValidationResult()),
            'RTSeriesValidator': Mock(return_value=ValidationResult()),
            'FrameOfReferenceValidator': Mock(return_value=ValidationResult()),
            'GeneralEquipmentValidator': Mock(return_value=ValidationResult()),
            'RTDoseValidator': Mock(return_value=ValidationResult()),
            'SOPCommonValidator': Mock(return_value=ValidationResult())
        }
        
        # Configure the mock validators
        for validator in mock_validators.values():
            validator.validate = Mock(return_value=ValidationResult())
        
        patch_dict = {
            'src.pyrt_dicom.validators.modules.patient_validator': Mock(PatientValidator=mock_validators['PatientValidator']),
            'src.pyrt_dicom.validators.modules.general_study_validator': Mock(GeneralStudyValidator=mock_validators['GeneralStudyValidator']),
            'src.pyrt_dicom.validators.modules.rt_series_validator': Mock(RTSeriesValidator=mock_validators['RTSeriesValidator']),
            'src.pyrt_dicom.validators.modules.frame_of_reference_validator': Mock(FrameOfReferenceValidator=mock_validators['FrameOfReferenceValidator']),
            'src.pyrt_dicom.validators.modules.general_equipment_validator': Mock(GeneralEquipmentValidator=mock_validators['GeneralEquipmentValidator']),
            'src.pyrt_dicom.validators.modules.rt_dose_validator': Mock(RTDoseValidator=mock_validators['RTDoseValidator']),
            'src.pyrt_dicom.validators.modules.sop_common_validator': Mock(SOPCommonValidator=mock_validators['SOPCommonValidator'])
        }
        
        return patch.dict('sys.modules', patch_dict)

    def _mock_all_validators(self):
        """Mock all validators (required, conditional, optional) to return successful results."""
        all_validators = {
            # Required validators
            'PatientValidator': Mock(return_value=ValidationResult()),
            'GeneralStudyValidator': Mock(return_value=ValidationResult()),
            'RTSeriesValidator': Mock(return_value=ValidationResult()),
            'FrameOfReferenceValidator': Mock(return_value=ValidationResult()),
            'GeneralEquipmentValidator': Mock(return_value=ValidationResult()),
            'RTDoseValidator': Mock(return_value=ValidationResult()),
            'SOPCommonValidator': Mock(return_value=ValidationResult()),
            
            # Conditional validators
            'GeneralImageValidator': Mock(return_value=ValidationResult()),
            'ImagePlaneValidator': Mock(return_value=ValidationResult()),
            'ImagePixelValidator': Mock(return_value=ValidationResult()),
            'MultiFrameValidator': Mock(return_value=ValidationResult()),
            'FrameExtractionValidator': Mock(return_value=ValidationResult()),
            
            # Optional validators
            'ClinicalTrialSubjectValidator': Mock(return_value=ValidationResult()),
            'PatientStudyValidator': Mock(return_value=ValidationResult()),
            'ClinicalTrialStudyValidator': Mock(return_value=ValidationResult()),
            'ClinicalTrialSeriesValidator': Mock(return_value=ValidationResult()),
            'RTDVHValidator': Mock(return_value=ValidationResult()),
            'CommonInstanceReferenceValidator': Mock(return_value=ValidationResult())
        }
        
        # Configure all mock validators
        for validator in all_validators.values():
            validator.validate = Mock(return_value=ValidationResult())
        
        validator_modules = {
            'src.pyrt_dicom.validators.modules.patient_validator': Mock(PatientValidator=all_validators['PatientValidator']),
            'src.pyrt_dicom.validators.modules.general_study_validator': Mock(GeneralStudyValidator=all_validators['GeneralStudyValidator']),
            'src.pyrt_dicom.validators.modules.rt_series_validator': Mock(RTSeriesValidator=all_validators['RTSeriesValidator']),
            'src.pyrt_dicom.validators.modules.frame_of_reference_validator': Mock(FrameOfReferenceValidator=all_validators['FrameOfReferenceValidator']),
            'src.pyrt_dicom.validators.modules.general_equipment_validator': Mock(GeneralEquipmentValidator=all_validators['GeneralEquipmentValidator']),
            'src.pyrt_dicom.validators.modules.rt_dose_validator': Mock(RTDoseValidator=all_validators['RTDoseValidator']),
            'src.pyrt_dicom.validators.modules.sop_common_validator': Mock(SOPCommonValidator=all_validators['SOPCommonValidator']),
            'src.pyrt_dicom.validators.modules.general_image_validator': Mock(GeneralImageValidator=all_validators['GeneralImageValidator']),
            'src.pyrt_dicom.validators.modules.image_plane_validator': Mock(ImagePlaneValidator=all_validators['ImagePlaneValidator']),
            'src.pyrt_dicom.validators.modules.image_pixel_validator': Mock(ImagePixelValidator=all_validators['ImagePixelValidator']),
            'src.pyrt_dicom.validators.modules.multi_frame_validator': Mock(MultiFrameValidator=all_validators['MultiFrameValidator']),
            'src.pyrt_dicom.validators.modules.frame_extraction_validator': Mock(FrameExtractionValidator=all_validators['FrameExtractionValidator']),
            'src.pyrt_dicom.validators.modules.clinical_trial_subject_validator': Mock(ClinicalTrialSubjectValidator=all_validators['ClinicalTrialSubjectValidator']),
            'src.pyrt_dicom.validators.modules.patient_study_validator': Mock(PatientStudyValidator=all_validators['PatientStudyValidator']),
            'src.pyrt_dicom.validators.modules.clinical_trial_study_validator': Mock(ClinicalTrialStudyValidator=all_validators['ClinicalTrialStudyValidator']),
            'src.pyrt_dicom.validators.modules.clinical_trial_series_validator': Mock(ClinicalTrialSeriesValidator=all_validators['ClinicalTrialSeriesValidator']),
            'src.pyrt_dicom.validators.modules.rt_dvh_validator': Mock(RTDVHValidator=all_validators['RTDVHValidator']),
            'src.pyrt_dicom.validators.modules.common_instance_reference_validator': Mock(CommonInstanceReferenceValidator=all_validators['CommonInstanceReferenceValidator'])
        }
        
        return patch.dict('sys.modules', validator_modules)