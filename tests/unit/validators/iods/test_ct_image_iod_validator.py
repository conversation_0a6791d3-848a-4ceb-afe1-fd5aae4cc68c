"""Unit tests for CT Image IOD validator."""

from pydicom import Dataset
from pydicom.uid import generate_uid, CTImageStorage

from pyrt_dicom.validators.iods.ct_image_iod_validator import CTImageIODValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult


class TestCTImageIODValidator:
    """Test CT Image IOD validator functionality."""

    def test_valid_complete_ct_image_iod(self):
        """Test validation of valid complete CT Image IOD."""
        ds = self._create_valid_ct_image_dataset()
        
        result = CTImageIODValidator.validate(ds)
        assert isinstance(result, ValidationResult)
        # Note: Validation may have warnings but should have minimal errors for valid dataset
    
    def test_sop_class_uid_validation(self):
        """Test SOP Class UID validation."""
        ds = self._create_minimal_ct_dataset()
        
        # Test missing SOP Class UID
        if hasattr(ds, 'SOPClassUID'):
            delattr(ds, 'SOPClassUID')
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("SOP Class UID (0008,0016) is required" in error for error in result.errors)
        
        # Test wrong SOP Class UID
        ds.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2.1"  # Enhanced CT Image Storage
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("SOP Class UID must be" in error for error in result.errors)
        
        # Test correct SOP Class UID
        ds.SOPClassUID = CTImageStorage
        result = CTImageIODValidator.validate(ds)
        # Should not have SOP Class UID errors
        sop_errors = [err for err in result.errors if "SOP Class UID" in err]
        assert len(sop_errors) == 0
    
    def test_sop_instance_uid_validation(self):
        """Test SOP Instance UID validation."""
        ds = self._create_minimal_ct_dataset()
        
        # Test missing SOP Instance UID
        if hasattr(ds, 'SOPInstanceUID'):
            delattr(ds, 'SOPInstanceUID')
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("SOP Instance UID (0008,0018) is required" in error for error in result.errors)
        
        # Test invalid UID format
        ds.SOPInstanceUID = "invalid.uid.format."
        result = CTImageIODValidator.validate(ds)
        assert result.has_warnings
        assert any("SOP Instance UID format may be invalid" in warning for warning in result.warnings)
    
    def test_modality_validation(self):
        """Test CT modality validation."""
        ds = self._create_minimal_ct_dataset()
        
        # Test wrong modality
        ds.Modality = 'MR'
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("modality must be 'CT'" in error for error in result.errors)
        
        # Test correct modality
        ds.Modality = 'CT'
        result = CTImageIODValidator.validate(ds)
        # Should not have modality errors
        modality_errors = [err for err in result.errors if "modality must be 'CT'" in err]
        assert len(modality_errors) == 0
    
    def test_required_modules_validation(self):
        """Test required modules validation."""
        ds = self._create_minimal_ct_dataset()
        
        # Test missing Patient Module
        for attr in ['PatientName', 'PatientID', 'PatientBirthDate', 'PatientSex']:
            if hasattr(ds, attr):
                delattr(ds, attr)
        
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("Patient Module is required" in error for error in result.errors)
        
        # Test missing General Study Module
        if hasattr(ds, 'StudyInstanceUID'):
            delattr(ds, 'StudyInstanceUID')
        
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("General Study Module is required" in error for error in result.errors)
    
    def test_conditional_modules_validation(self):
        """Test conditional modules validation."""
        ds = self._create_minimal_ct_dataset()
        
        # Test Synchronization Module requirement
        ds.AcquisitionTimeSynchronized = 'Y'
        # Don't add the required synchronization elements
        
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("Synchronization Module is required when time synchronization was applied" in error 
                  for error in result.errors)
        
        # Test Contrast/Bolus Module requirement
        ds = self._create_minimal_ct_dataset()
        ds.ContrastBolusAgent = 'IODINE'
        # Don't add the contrast bolus elements beyond the agent
        
        result = CTImageIODValidator.validate(ds)
        # Should require contrast bolus module when agent is present
        # (This depends on the specific implementation of conditional logic)
        
        # Test Multi-energy CT Module requirement
        ds = self._create_minimal_ct_dataset()
        ds.MultiEnergyCTAcquisition = 'YES'
        # Don't add the required multi-energy elements
        
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("Multi-energy CT Image Module is required when Multi-energy CT Acquisition is YES" in error 
                  for error in result.errors)
    
    def test_multi_energy_ct_constraints(self):
        """Test Multi-energy CT specific constraints."""
        ds = self._create_minimal_ct_dataset()
        ds.MultiEnergyCTAcquisition = 'YES'
        
        # Test Real World Value Mapping Sequence requirement
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("Real World Value Mapping Sequence (0040,9096) is required" in error 
                  for error in result.errors)
        
        # Add Real World Value Mapping Sequence
        ds.RealWorldValueMappingSequence = [Dataset()]
        ds.RealWorldValueMappingSequence[0].MeasurementUnitsCodeSequence = [Dataset()]
        ds.RealWorldValueMappingSequence[0].MeasurementUnitsCodeSequence[0].CodingSchemeDesignator = 'UCUM'
        
        result = CTImageIODValidator.validate(ds)
        # Should warn about measurement units code sequence
        assert any("CID 301" in warning for warning in result.warnings)
    
    def test_ct_parameters_validation(self):
        """Test CT-specific parameters validation."""
        ds = self._create_minimal_ct_dataset()
        
        # Test invalid KVP
        ds.KVP = -50
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("KVP must be a positive number" in error for error in result.errors)
        
        # Test KVP out of range
        ds.KVP = 500  # Too high
        result = CTImageIODValidator.validate(ds)
        assert result.has_warnings
        assert any("KVP value" in warning and "outside typical range" in warning 
                  for warning in result.warnings)
        
        # Test invalid Exposure Time
        ds.ExposureTime = 0
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("Exposure Time must be a positive number" in error for error in result.errors)
        
        # Test invalid X-Ray Tube Current
        ds.XRayTubeCurrent = -100
        result = CTImageIODValidator.validate(ds)
        assert result.has_errors
        assert any("X-Ray Tube Current must be a positive number" in error for error in result.errors)
    
    def test_validation_config_options(self):
        """Test validation configuration options."""
        ds = self._create_ct_dataset_with_optional_modules()
        
        # Test with optional module validation disabled
        config = ValidationConfig(validate_optional_modules=False)
        result = CTImageIODValidator.validate(ds, config)
        
        # Should not validate optional modules
        optional_errors = [err for err in result.errors if "Clinical Trial" in err]
        assert len(optional_errors) == 0
        
        # Test with optional module validation enabled
        config = ValidationConfig(validate_optional_modules=True)
        result = CTImageIODValidator.validate(ds, config)
        
        # May have validation results from optional modules
        # (This depends on the specific optional module data)
    
    def test_cross_module_dependencies_validation(self):
        """Test cross-module dependencies validation."""
        ds = self._create_minimal_ct_dataset()
        
        # Test with cross-module validation enabled
        config = ValidationConfig(validate_cross_module_dependencies=True)
        result = CTImageIODValidator.validate(ds, config)
        
        # Should include cross-module validation results
        # (This depends on the specific cross-module validators implementation)
        assert isinstance(result, ValidationResult)
    
    def test_uid_format_validation(self):
        """Test UID format validation helper method."""
        # Test valid UIDs
        assert CTImageIODValidator._is_valid_uid("1.2.3.4.5")
        assert CTImageIODValidator._is_valid_uid("1.2.840.10008.5.1.4.1.1.2")
        
        # Test invalid UIDs
        assert not CTImageIODValidator._is_valid_uid("1.2.3.4.")  # Trailing dot
        assert not CTImageIODValidator._is_valid_uid(".1.2.3.4")  # Leading dot
        assert not CTImageIODValidator._is_valid_uid("1.2.3..4")  # Double dot
        assert not CTImageIODValidator._is_valid_uid("1.2.3.a")   # Letter
        assert not CTImageIODValidator._is_valid_uid("a" * 65)    # Too long
    
    def test_spatial_and_temporal_data_detection(self):
        """Test spatial and temporal data detection methods."""
        ds = self._create_minimal_ct_dataset()
        
        # Test spatial data detection
        assert CTImageIODValidator._has_spatial_data(ds)  # Should have spatial data
        
        # Remove spatial elements
        spatial_attrs = ['ImagePositionPatient', 'ImageOrientationPatient', 
                        'PixelSpacing', 'FrameOfReferenceUID']
        for attr in spatial_attrs:
            if hasattr(ds, attr):
                delattr(ds, attr)
        
        assert not CTImageIODValidator._has_spatial_data(ds)  # Should not have spatial data
        
        # Test temporal data detection
        assert CTImageIODValidator._has_temporal_data(ds)  # Should have temporal data
        
        # Remove temporal elements
        temporal_attrs = ['StudyDate', 'StudyTime', 'SeriesDate', 'SeriesTime', 
                         'AcquisitionDate', 'AcquisitionTime']
        for attr in temporal_attrs:
            if hasattr(ds, attr):
                delattr(ds, attr)
        
        assert not CTImageIODValidator._has_temporal_data(ds)  # Should not have temporal data

    def _create_minimal_ct_dataset(self):
        """Create minimal CT Image dataset for testing."""
        ds = Dataset()
        
        # SOP Common Module
        ds.SOPClassUID = CTImageStorage
        ds.SOPInstanceUID = generate_uid()
        
        # Patient Module
        ds.PatientName = "Test^Patient"
        ds.PatientID = "TEST001"
        ds.PatientBirthDate = "19900101"
        ds.PatientSex = "M"
        
        # General Study Module
        ds.StudyInstanceUID = generate_uid()
        ds.StudyDate = "20240101"
        ds.StudyTime = "120000"
        
        # General Series Module
        ds.Modality = "CT"
        ds.SeriesInstanceUID = generate_uid()
        
        # Frame of Reference Module
        ds.FrameOfReferenceUID = generate_uid()
        
        # General Equipment Module
        ds.Manufacturer = "Test Manufacturer"
        
        # General Acquisition Module
        ds.AcquisitionDate = "20240101"
        ds.AcquisitionTime = "120000"
        
        # General Image Module
        ds.InstanceNumber = "1"
        
        # Image Plane Module
        ds.PixelSpacing = [1.0, 1.0]
        ds.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ds.ImagePositionPatient = [0.0, 0.0, 0.0]
        
        # Image Pixel Module
        ds.Rows = 512
        ds.Columns = 512
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        ds.PixelRepresentation = 0
        
        # CT Image Module
        ds.KVP = 120.0
        ds.ExposureTime = 1000
        
        return ds
    
    def _create_valid_ct_image_dataset(self):
        """Create valid CT Image dataset with all required elements."""
        ds = self._create_minimal_ct_dataset()
        
        # Add additional valid elements
        ds.StudyDescription = "CT Study"
        ds.SeriesDescription = "CT Series"
        ds.XRayTubeCurrent = 300
        ds.Exposure = 300
        ds.SliceThickness = 5.0
        
        return ds
    
    def _create_ct_dataset_with_optional_modules(self):
        """Create CT dataset with optional modules for testing."""
        ds = self._create_minimal_ct_dataset()
        
        # Add Clinical Trial Subject Module elements
        ds.ClinicalTrialSponsorName = "Test Sponsor"
        ds.ClinicalTrialProtocolID = "PROTO-001"
        
        # Add Patient Study Module elements
        ds.PatientAge = "034Y"
        ds.PatientWeight = "70.0"
        
        return ds