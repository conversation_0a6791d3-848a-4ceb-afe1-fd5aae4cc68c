"""Unit tests for ValidationError exception class."""

import pytest
from pyrt_dicom.validators.validation_error import ValidationError


class TestValidationError:
    """Test suite for ValidationError exception class."""

    def test_init_message_only(self):
        """Test ValidationError initialization with message only."""
        error = ValidationError("Test error message")
        
        assert error.message == "Test error message"
        assert error.context is None
        assert str(error) == "Test error message"

    def test_init_with_context(self):
        """Test ValidationError initialization with message and context."""
        error = ValidationError("Test error message", "Test context")
        
        assert error.message == "Test error message"
        assert error.context == "Test context"
        assert str(error) == "Test context: Test error message"

    def test_str_representation_without_context(self):
        """Test string representation without context."""
        error = ValidationError("Simple error")
        
        assert str(error) == "Simple error"

    def test_str_representation_with_context(self):
        """Test string representation with context."""
        error = ValidationError("Field validation failed", "PatientModule")
        
        assert str(error) == "PatientModule: Field validation failed"

    def test_repr_representation_without_context(self):
        """Test repr representation without context."""
        error = ValidationError("Test error")
        
        expected = "ValidationError(message='Test error')"
        assert repr(error) == expected

    def test_repr_representation_with_context(self):
        """Test repr representation with context."""
        error = ValidationError("Test error", "Test context")
        
        expected = "ValidationError(message='Test error', context='Test context')"
        assert repr(error) == expected

    def test_inheritance_from_exception(self):
        """Test that ValidationError properly inherits from Exception."""
        error = ValidationError("Test error")
        
        assert isinstance(error, Exception)
        assert isinstance(error, ValidationError)

    def test_exception_raising_without_context(self):
        """Test raising ValidationError without context."""
        with pytest.raises(ValidationError) as exc_info:
            raise ValidationError("Validation failed")
        
        assert str(exc_info.value) == "Validation failed"
        assert exc_info.value.message == "Validation failed"
        assert exc_info.value.context is None

    def test_exception_raising_with_context(self):
        """Test raising ValidationError with context."""
        with pytest.raises(ValidationError) as exc_info:
            raise ValidationError("Required field missing", "PatientModule validation")
        
        assert str(exc_info.value) == "PatientModule validation: Required field missing"
        assert exc_info.value.message == "Required field missing"
        assert exc_info.value.context == "PatientModule validation"

    def test_exception_message_construction(self):
        """Test that exception message is properly constructed."""
        # Without context
        error1 = ValidationError("Simple error")
        assert str(error1) == "Simple error"
        
        # With context
        error2 = ValidationError("Complex error", "Module context")
        assert str(error2) == "Module context: Complex error"

    def test_empty_message(self):
        """Test behavior with empty message."""
        error = ValidationError("")
        
        assert error.message == ""
        assert error.context is None
        assert str(error) == ""

    def test_empty_context(self):
        """Test behavior with empty context."""
        error = ValidationError("Error message", "")
        
        assert error.message == "Error message"
        assert error.context == ""
        assert str(error) == ": Error message"

    def test_none_context_explicit(self):
        """Test explicit None context."""
        error = ValidationError("Error message", None)
        
        assert error.message == "Error message"
        assert error.context is None
        assert str(error) == "Error message"

    def test_multiline_message(self):
        """Test ValidationError with multiline message."""
        multiline_message = "Line 1\nLine 2\nLine 3"
        error = ValidationError(multiline_message, "Context")
        
        assert error.message == multiline_message
        assert error.context == "Context"
        assert str(error) == f"Context: {multiline_message}"

    def test_unicode_message_and_context(self):
        """Test ValidationError with Unicode characters."""
        error = ValidationError("Error with émojis 🔥", "Contexte français")
        
        assert error.message == "Error with émojis 🔥"
        assert error.context == "Contexte français"
        assert str(error) == "Contexte français: Error with émojis 🔥"