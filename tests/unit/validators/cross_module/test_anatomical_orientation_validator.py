"""Tests for AnatomicalOrientationValidator.

Tests the cross-module validation between patient species information
and anatomical orientation requirements according to DICOM PS3.3 C.7.3.1.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.cross_module.anatomical_orientation_validator import AnatomicalOrientationValidator
from pyrt_dicom.modules.patient_module import PatientModule  
from pyrt_dicom.modules.general_series_module import GeneralSeriesModule
from pyrt_dicom.enums.series_enums import Modality, AnatomicalOrientationType
from pyrt_dicom.validators.validation_result import ValidationResult


class TestAnatomicalOrientationValidator:
    """Test AnatomicalOrientationValidator cross-module validation."""
    
    def test_validate_returns_validation_result(self):
        """Test that validator returns proper ValidationResult instance."""
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient", 
            patient_id="123"
        )
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.5"
        )
        
        result = AnatomicalOrientationValidator.validate(patient, series)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
    
    def test_validate_human_patient_without_orientation_passes(self):
        """Test human patient without anatomical orientation passes validation."""
        # Human patient (no species information)
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345"
        )
        
        # Series without anatomical orientation
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.10"
        )
        
        result = AnatomicalOrientationValidator.validate(patient, series)
        
        assert not result.has_errors
        assert not result.has_warnings
        assert result.is_valid
    
    def test_validate_human_patient_with_orientation_warns(self):
        """Test human patient with anatomical orientation generates warning."""
        # Human patient (no species information)
        patient = PatientModule.from_required_elements(
            patient_name="Doe^John",
            patient_id="12345"
        )
        
        # Series with anatomical orientation
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.10"
        ).with_anatomical_orientation(
            anatomical_orientation_type=AnatomicalOrientationType.BIPED
        )
        
        result = AnatomicalOrientationValidator.validate(patient, series)
        
        assert not result.has_errors
        assert result.has_warnings
        assert len(result.warnings) == 1
        assert "BIPED" in result.warnings[0]
        assert "human patient" in result.warnings[0]
        assert "permitted but not required" in result.warnings[0]
    
    def test_validate_non_human_without_orientation_fails(self):
        """Test non-human patient without anatomical orientation fails validation."""
        # Non-human patient with species description
        patient = PatientModule.from_required_elements(
            patient_name="Canine^Subject",
            patient_id="DOG001"
        ).with_non_human_organism(
            patient_species_description="Canis lupus familiaris",
            patient_breed_description="Laboratory Beagle",
            responsible_person="Dr. Veterinarian"
        )
        
        # Series without anatomical orientation
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.11"
        )
        
        result = AnatomicalOrientationValidator.validate(patient, series)
        
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Anatomical Orientation Type (0010,2210) is required (Type 1C)" in result.errors[0]
        assert "non-human organisms" in result.errors[0]
        assert "DICOM PS3.3 C.7.3.1" in result.errors[0]
        assert not result.is_valid
    
    def test_validate_non_human_with_orientation_passes(self):
        """Test non-human patient with anatomical orientation passes validation."""
        # Non-human patient with species description  
        patient = PatientModule.from_required_elements(
            patient_name="Feline^Subject",
            patient_id="CAT001"
        ).with_non_human_organism(
            patient_species_description="Felis catus",
            patient_breed_description="Domestic Shorthair",
            responsible_person="Dr. Veterinarian"
        )
        
        # Series with anatomical orientation
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.12"
        ).with_anatomical_orientation(
            anatomical_orientation_type=AnatomicalOrientationType.QUADRUPED
        )
        
        result = AnatomicalOrientationValidator.validate(patient, series)
        
        assert not result.has_errors
        assert not result.has_warnings
        assert result.is_valid
    
    def test_validate_non_human_with_species_code_sequence(self):
        """Test non-human detection works with species code sequence."""
        # Create datasets manually to test species code sequence
        patient_dataset = Dataset()
        patient_dataset.PatientName = "Equine^Subject"
        patient_dataset.PatientID = "HORSE001"
        
        # Add species code sequence (indicates non-human)
        species_item = Dataset()
        species_item.CodeValue = "35354009"
        species_item.CodingSchemeDesignator = "SCT"
        species_item.CodeMeaning = "Horse"
        patient_dataset.PatientSpeciesCodeSequence = [species_item]
        
        series_dataset = Dataset()
        series_dataset.Modality = "CT"
        series_dataset.SeriesInstanceUID = "*******.*******.9.13"
        # No anatomical orientation type
        
        result = AnatomicalOrientationValidator.validate(patient_dataset, series_dataset)
        
        assert result.has_errors
        assert len(result.errors) == 1
        assert "required (Type 1C)" in result.errors[0]
        assert "non-human organisms" in result.errors[0]
    
    def test_validate_with_none_modules_passes(self):
        """Test validation passes when either module is None."""
        patient = PatientModule.from_required_elements(
            patient_name="Test^Patient",
            patient_id="123"
        )
        
        # Test with None patient module
        result1 = AnatomicalOrientationValidator.validate(None, patient)
        assert not result1.has_errors
        assert not result1.has_warnings
        
        # Test with None series module
        result2 = AnatomicalOrientationValidator.validate(patient, None)
        assert not result2.has_errors  
        assert not result2.has_warnings
        
        # Test with both None
        result3 = AnatomicalOrientationValidator.validate(None, None)
        assert not result3.has_errors
        assert not result3.has_warnings
    
    def test_validate_with_raw_datasets(self):
        """Test validation works with raw pydicom datasets."""
        # Human patient dataset
        patient_dataset = Dataset()
        patient_dataset.PatientName = "Human^Patient"
        patient_dataset.PatientID = "HUMAN001"
        # No species information = human
        
        # Series dataset with orientation
        series_dataset = Dataset()
        series_dataset.Modality = "MR"
        series_dataset.SeriesInstanceUID = "*******.*******.9.14"
        series_dataset.AnatomicalOrientationType = "BIPED"
        
        result = AnatomicalOrientationValidator.validate(patient_dataset, series_dataset)
        
        assert not result.has_errors
        assert result.has_warnings
        assert "human patient" in result.warnings[0]
        assert "BIPED" in result.warnings[0]
    
    def test_validate_mixed_module_and_dataset_types(self):
        """Test validation works with mixed module and dataset types."""
        # Patient module object
        patient_module = PatientModule.from_required_elements(
            patient_name="Mixed^Test",
            patient_id="MIX001"
        ).with_non_human_organism(
            patient_species_description="Test species",
            patient_breed_description="Test breed",
            responsible_person="Researcher"
        )
        
        # Raw series dataset
        series_dataset = Dataset()
        series_dataset.Modality = "CT"
        series_dataset.SeriesInstanceUID = "*******.*******.9.15"
        series_dataset.AnatomicalOrientationType = "QUADRUPED"
        
        result = AnatomicalOrientationValidator.validate(patient_module, series_dataset)
        
        assert not result.has_errors
        assert not result.has_warnings
        assert result.is_valid
    
    def test_orientation_value_extraction(self):
        """Test orientation value extraction for warning messages."""
        # Test with string orientation value
        patient = PatientModule.from_required_elements(
            patient_name="Human^Test",
            patient_id="HUM001"
        )
        
        series = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.16"
        ).with_anatomical_orientation(
            anatomical_orientation_type="QUADRUPED"  # String instead of enum
        )
        
        result = AnatomicalOrientationValidator.validate(patient, series)
        
        assert result.has_warnings
        assert "QUADRUPED" in result.warnings[0]
        assert "human patient" in result.warnings[0]
    
    def test_comprehensive_validation_scenarios(self):
        """Test comprehensive validation scenarios."""
        # Scenario 1: Non-human with proper orientation (valid)
        patient_nh = PatientModule.from_required_elements(
            patient_name="Animal^Subject",
            patient_id="ANIM001"
        ).with_non_human_organism(
            patient_species_description="Laboratory animal",
            patient_breed_description="Laboratory strain",
            responsible_person="Researcher"
        )
        
        series_oriented = GeneralSeriesModule.from_required_elements(
            modality=Modality.CT,
            series_instance_uid="*******.*******.9.17"
        ).with_anatomical_orientation(
            anatomical_orientation_type=AnatomicalOrientationType.QUADRUPED
        )
        
        result1 = AnatomicalOrientationValidator.validate(patient_nh, series_oriented)
        assert result1.is_valid
        assert not result1.has_errors
        assert not result1.has_warnings
        
        # Scenario 2: Non-human without orientation (invalid)
        series_no_orient = GeneralSeriesModule.from_required_elements(
            modality=Modality.MR,
            series_instance_uid="*******.*******.9.18"
        )
        
        result2 = AnatomicalOrientationValidator.validate(patient_nh, series_no_orient)
        assert not result2.is_valid
        assert result2.has_errors
        assert "required (Type 1C)" in result2.errors[0]
        
        # Scenario 3: Human with orientation (warning but valid)  
        patient_h = PatientModule.from_required_elements(
            patient_name="Human^Patient",
            patient_id="HUM001"
        )
        
        result3 = AnatomicalOrientationValidator.validate(patient_h, series_oriented)
        assert result3.is_valid  # No errors, just warnings
        assert not result3.has_errors
        assert result3.has_warnings
        assert "permitted but not required" in result3.warnings[0]