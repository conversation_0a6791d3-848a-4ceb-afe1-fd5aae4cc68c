"""Test RTDVHValidator functionality.

Tests comprehensive validation of RT DVH Module according to DICOM PS3.3 C.8.8.4.
All tests use datasets generated from modules via to_dataset() method following
the new composition-based architecture patterns.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTDVHModule
from pyrt_dicom.validators.modules.rt_dvh_validator import RTDVHValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums import DVHType, DoseUnits, DoseType, DVHVolumeUnits, DVHROIContributionType


class TestRTDVHValidator:
    """Test RTDVHValidator comprehensive validation logic."""
    
    def test_validate_method_signature(self):
        """Test validator method signature and return type."""
        # Create minimal valid dataset
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = []
        dataset.DVHSequence = []
        
        # Test with default config
        result = RTDVHValidator.validate(dataset)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Test with custom config
        config = ValidationConfig()
        result = RTDVHValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)
    
    def test_missing_required_sequences_validation(self):
        """Test validation when required sequences are missing."""
        dataset = Dataset()
        # No ReferencedStructureSetSequence or DVHSequence
        
        result = RTDVHValidator.validate(dataset)
        
        # Should have errors for missing required sequences
        assert len(result.errors) >= 2
        assert any('Referenced Structure Set Sequence (300C,0060) is required (Type 1)' in error 
                  for error in result.errors)
        assert any('DVH Sequence (3004,0050) is required (Type 1)' in error 
                  for error in result.errors)
    
    def test_empty_sequences_validation(self):
        """Test validation of empty required sequences."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = []
        dataset.DVHSequence = []
        
        result = RTDVHValidator.validate(dataset)
        
        # Should have errors for empty sequences
        assert len(result.errors) >= 2
        assert any('Referenced Structure Set Sequence (300C,0060) is required (Type 1)' in error 
                  for error in result.errors)
        assert any('DVH Sequence (3004,0050) is required (Type 1)' in error 
                  for error in result.errors)
    
    def test_referenced_structure_set_sequence_count_validation(self):
        """Test validation of Referenced Structure Set Sequence count constraint."""
        dataset = Dataset()
        # Create multiple structure set references (should only be one)
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            ),
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.10"
            )
        ]
        dataset.DVHSequence = []
        
        result = RTDVHValidator.validate(dataset)
        
        # Should have error for multiple structure set references
        assert any('must contain exactly one item' in error for error in result.errors)
    
    def test_valid_minimal_dvh_validation(self):
        """Test validation of minimally valid DVH."""
        # Create minimal valid DVH using module
        dvh_data = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        dvh = RTDVHModule.from_required_elements(
            referenced_structure_set_sequence=[
                RTDVHModule.create_referenced_structure_set_item(
                    referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                    referenced_sop_instance_uid="*******.*******.9"
                )
            ],
            dvh_sequence=[
                RTDVHModule.create_dvh_item(
                    dvh_referenced_roi_sequence=[
                        RTDVHModule.create_dvh_referenced_roi_item(
                            referenced_roi_number=1,
                            dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
                        )
                    ],
                    dvh_type=DVHType.CUMULATIVE,
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dvh_dose_scaling=1.0,
                    dvh_volume_units=DVHVolumeUnits.CM3,
                    dvh_number_of_bins=5,
                    dvh_data=dvh_data
                )
            ]
        )
        
        dataset = dvh.to_dataset()
        result = RTDVHValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
    
    def test_dvh_type_1_element_validation(self):
        """Test validation of Type 1 (required) elements in DVH sequence."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]
        
        # Create DVH item missing required Type 1 elements
        dvh_item = Dataset()
        dataset.DVHSequence = [dvh_item]
        
        result = RTDVHValidator.validate(dataset)
        
        # Should have errors for missing Type 1 elements
        expected_missing_elements = [
            'DVH Referenced ROI Sequence', 'DVHType', 'DoseUnits', 'DoseType',
            'DVHDoseScaling', 'DVHVolumeUnits', 'DVHNumberOfBins', 'DVHData'
        ]

        for element in expected_missing_elements:
            assert any(f'{element}' in error and 'is required (Type 1)' in error
                      for error in result.errors), f'Missing error for {element}'
    
    def test_dvh_referenced_roi_sequence_validation(self):
        """Test validation of DVH Referenced ROI Sequence requirements."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]
        
        # Create DVH item with empty ROI sequence
        dvh_item = Dataset()
        dvh_item.DVHReferencedROISequence = []
        dvh_item.DVHType = DVHType.CUMULATIVE.value
        dvh_item.DoseUnits = DoseUnits.GY.value
        dvh_item.DoseType = DoseType.PHYSICAL.value
        dvh_item.DVHDoseScaling = 1.0
        dvh_item.DVHVolumeUnits = DVHVolumeUnits.CM3.value
        dvh_item.DVHNumberOfBins = 5
        dvh_item.DVHData = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        
        dataset.DVHSequence = [dvh_item]
        
        result = RTDVHValidator.validate(dataset)
        
        # Should have error for empty ROI sequence
        assert any('DVH Referenced ROI Sequence (3004,0060) is required (Type 1)' in error 
                  for error in result.errors)
    
    def test_enumerated_values_validation(self):
        """Test validation of enumerated values."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]
        
        # Create DVH item with invalid enumerated values
        dvh_item = Dataset()
        dvh_item.DVHReferencedROISequence = [
            RTDVHModule.create_dvh_referenced_roi_item(
                referenced_roi_number=1,
                dvh_roi_contribution_type="INVALID_TYPE"  # Invalid value
            )
        ]
        dvh_item.DVHType = "INVALID_DVH_TYPE"  # Invalid value
        dvh_item.DoseUnits = "INVALID_UNITS"  # Invalid value
        dvh_item.DoseType = DoseType.PHYSICAL.value
        dvh_item.DVHDoseScaling = 1.0
        dvh_item.DVHVolumeUnits = "INVALID_VOLUME_UNITS"  # Invalid value
        dvh_item.DVHNumberOfBins = 5
        dvh_item.DVHData = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        
        dataset.DVHSequence = [dvh_item]
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTDVHValidator.validate(dataset, config)

        # Should have warnings for invalid enumerated values
        assert any('DVH Type' in warning and 'INVALID_DVH_TYPE' in warning for warning in result.warnings)
        assert any('Dose Units' in warning and 'INVALID_UNITS' in warning for warning in result.warnings)
        assert any('DVH Volume Units' in warning and 'INVALID_VOLUME_UNITS' in warning for warning in result.warnings)
        assert any('DVH ROI Contribution Type' in warning and 'INVALID_TYPE' in warning for warning in result.warnings)
    
    def test_dvh_data_consistency_validation(self):
        """Test validation of DVH data consistency."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]
        
        # Create DVH item with inconsistent data length
        dvh_item = Dataset()
        dvh_item.DVHReferencedROISequence = [
            RTDVHModule.create_dvh_referenced_roi_item(
                referenced_roi_number=1,
                dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
            )
        ]
        dvh_item.DVHType = DVHType.CUMULATIVE.value
        dvh_item.DoseUnits = DoseUnits.GY.value
        dvh_item.DoseType = DoseType.PHYSICAL.value
        dvh_item.DVHDoseScaling = 1.0
        dvh_item.DVHVolumeUnits = DVHVolumeUnits.CM3.value
        dvh_item.DVHNumberOfBins = 5  # Should have 10 data values (5 * 2)
        dvh_item.DVHData = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0]  # Only 6 values
        
        dataset.DVHSequence = [dvh_item]
        
        result = RTDVHValidator.validate(dataset)
        
        # Should have error for inconsistent data length
        assert any('DVH Data (3004,0058) length' in error and 'must be exactly twice' in error 
                  for error in result.errors)
    
    def test_relative_dose_units_conditional_validation(self):
        """Test conditional validation for RELATIVE dose units."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]
        
        # Create DVH item with RELATIVE dose units but no normalization dose value
        dvh_item = Dataset()
        dvh_item.DVHReferencedROISequence = [
            RTDVHModule.create_dvh_referenced_roi_item(
                referenced_roi_number=1,
                dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
            )
        ]
        dvh_item.DVHType = DVHType.CUMULATIVE.value
        dvh_item.DoseUnits = DoseUnits.RELATIVE.value  # RELATIVE requires normalization dose value
        dvh_item.DoseType = DoseType.PHYSICAL.value
        dvh_item.DVHDoseScaling = 1.0
        dvh_item.DVHVolumeUnits = DVHVolumeUnits.CM3.value
        dvh_item.DVHNumberOfBins = 5
        dvh_item.DVHData = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        
        dataset.DVHSequence = [dvh_item]
        # Missing DVHNormalizationDoseValue
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = RTDVHValidator.validate(dataset, config)
        
        # Should have warning for missing normalization dose value
        assert any('DVH Normalization Dose Value (3004,0042) should be present' in warning 
                  for warning in result.warnings)
    
    def test_dvh_normalization_point_validation(self):
        """Test validation of DVH normalization point coordinates."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]
        dataset.DVHSequence = [
            RTDVHModule.create_dvh_item(
                dvh_referenced_roi_sequence=[
                    RTDVHModule.create_dvh_referenced_roi_item(
                        referenced_roi_number=1,
                        dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
                    )
                ],
                dvh_type=DVHType.CUMULATIVE,
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dvh_dose_scaling=1.0,
                dvh_volume_units=DVHVolumeUnits.CM3,
                dvh_number_of_bins=5,
                dvh_data=[0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
            )
        ]
        
        # Invalid normalization point (should have 3 coordinates)
        dataset.DVHNormalizationPoint = [100.0, 50.0]  # Missing z coordinate
        
        result = RTDVHValidator.validate(dataset)
        
        # Should have error for invalid normalization point
        assert any('DVH Normalization Point (3004,0040) must contain exactly 3 coordinates' in error
                  for error in result.errors)

    def test_dvh_dose_scaling_validation(self):
        """Test validation of DVH dose scaling values."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]

        # Create DVH item with invalid dose scaling (negative value)
        dvh_item = Dataset()
        dvh_item.DVHReferencedROISequence = [
            RTDVHModule.create_dvh_referenced_roi_item(
                referenced_roi_number=1,
                dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
            )
        ]
        dvh_item.DVHType = DVHType.CUMULATIVE.value
        dvh_item.DoseUnits = DoseUnits.GY.value
        dvh_item.DoseType = DoseType.PHYSICAL.value
        dvh_item.DVHDoseScaling = -1.0  # Invalid negative value
        dvh_item.DVHVolumeUnits = DVHVolumeUnits.CM3.value
        dvh_item.DVHNumberOfBins = 5
        dvh_item.DVHData = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]

        dataset.DVHSequence = [dvh_item]

        result = RTDVHValidator.validate(dataset)

        # Should have error for negative dose scaling
        assert any('DVH Dose Scaling (3004,0052) must be positive' in error
                  for error in result.errors)

    def test_dvh_statistical_consistency_validation(self):
        """Test validation of DVH statistical parameters consistency."""
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]

        # Create DVH item with inconsistent statistical values
        dvh_item = Dataset()
        dvh_item.DVHReferencedROISequence = [
            RTDVHModule.create_dvh_referenced_roi_item(
                referenced_roi_number=1,
                dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
            )
        ]
        dvh_item.DVHType = DVHType.CUMULATIVE.value
        dvh_item.DoseUnits = DoseUnits.GY.value
        dvh_item.DoseType = DoseType.PHYSICAL.value
        dvh_item.DVHDoseScaling = 1.0
        dvh_item.DVHVolumeUnits = DVHVolumeUnits.CM3.value
        dvh_item.DVHNumberOfBins = 5
        dvh_item.DVHData = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]
        # Inconsistent statistical values
        dvh_item.DVHMinimumDose = 50.0  # Min > Max (invalid)
        dvh_item.DVHMaximumDose = 30.0
        dvh_item.DVHMeanDose = 100.0  # Mean outside min-max range

        dataset.DVHSequence = [dvh_item]

        result = RTDVHValidator.validate(dataset)

        # Should have errors for inconsistent statistical values
        assert any('DVH Minimum Dose' in error and 'cannot exceed DVH Maximum Dose' in error
                  for error in result.errors)
        assert any('DVH Mean Dose' in warning and 'should be between minimum' in warning
                  for warning in result.warnings)

    def test_configuration_options(self):
        """Test different validation configuration options."""
        # Create dataset with enumerated value issues
        dataset = Dataset()
        dataset.ReferencedStructureSetSequence = [
            RTDVHModule.create_referenced_structure_set_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
                referenced_sop_instance_uid="*******.*******.9"
            )
        ]

        dvh_item = Dataset()
        dvh_item.DVHReferencedROISequence = [
            RTDVHModule.create_dvh_referenced_roi_item(
                referenced_roi_number=1,
                dvh_roi_contribution_type=DVHROIContributionType.INCLUDED
            )
        ]
        dvh_item.DVHType = "INVALID_TYPE"  # Invalid enumerated value
        dvh_item.DoseUnits = DoseUnits.RELATIVE.value  # Will trigger conditional warning
        dvh_item.DoseType = DoseType.PHYSICAL.value
        dvh_item.DVHDoseScaling = 1.0
        dvh_item.DVHVolumeUnits = DVHVolumeUnits.CM3.value
        dvh_item.DVHNumberOfBins = 5
        dvh_item.DVHData = [0.0, 100.0, 10.0, 80.0, 20.0, 60.0, 30.0, 40.0, 40.0, 20.0]

        dataset.DVHSequence = [dvh_item]

        # Test with enumerated values disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result_no_enum = RTDVHValidator.validate(dataset, config_no_enum)

        # Should not have enumerated value warnings
        assert not any('INVALID_TYPE' in warning for warning in result_no_enum.warnings)

        # Test with conditional requirements disabled
        config_no_conditional = ValidationConfig(validate_conditional_requirements=False)
        result_no_conditional = RTDVHValidator.validate(dataset, config_no_conditional)

        # Should not have conditional warnings
        assert not any('DVH Normalization Dose Value' in warning for warning in result_no_conditional.warnings)

        # Test with all validations enabled
        config_all = ValidationConfig(
            check_enumerated_values=True,
            validate_conditional_requirements=True,
            validate_sequences=True
        )
        result_all = RTDVHValidator.validate(dataset, config_all)

        # Should have both enumerated and conditional issues
        assert any('INVALID_TYPE' in warning for warning in result_all.warnings)
        assert any('DVH Normalization Dose Value' in warning for warning in result_all.warnings)
