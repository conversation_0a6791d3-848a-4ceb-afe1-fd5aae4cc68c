"""
Test VOI LUT Module Validator functionality.

Tests validation logic for DICOM PS3.3 C.11.2 VOI LUT Module.
Validates conditional requirements, sequence structure, and window parameters.
"""

from pydicom import Dataset
from pyrt_dicom.validators.modules.voi_lut_validator import VoiLutValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.enums.image_enums import VoiLutFunction
from pyrt_dicom.modules import VoiLutModule


class TestVoiLutValidator:
    """Test VOI LUT Module Validator functionality."""
    
    @staticmethod
    def create_valid_dataset_with_sequence():
        """Create a valid dataset with VOI LUT Sequence for testing."""
        # Use matching LUT descriptor and data - 6 entries for 6 data points
        module = VoiLutModule.from_required_elements().with_voi_lut_sequence(
            voi_lut_sequence=[
                VoiLutModule.create_voi_lut_item(
                    lut_descriptor=[6, 0, 8],  # 6 entries, starting at 0, 8 bits
                    lut_data=[0, 1, 2, 3, 4, 5],  # 6 data points
                    lut_explanation="Test LUT"
                )
            ]
        )
        return module.to_dataset()
    
    @staticmethod
    def create_valid_dataset_with_windows():
        """Create a valid dataset with Window parameters for testing."""
        module = VoiLutModule.from_required_elements().with_window_parameters(
            window_center=[2048.0],
            window_width=[4096.0],
            window_explanation=["Standard window"],
            voi_lut_function=VoiLutFunction.LINEAR
        )
        return module.to_dataset()
    
    def test_validation_with_valid_voi_lut_sequence(self):
        """Test validation passes with valid VOI LUT Sequence data."""
        dataset = self.create_valid_dataset_with_sequence()
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validation_with_valid_window_parameters(self):
        """Test validation passes with valid Window Center/Width data."""
        dataset = self.create_valid_dataset_with_windows()
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validation_with_empty_dataset(self):
        """Test validation passes with empty dataset (identity transformation)."""
        dataset = Dataset()
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validation_with_both_sequence_and_windows(self):
        """Test validation warns when both VOI LUT Sequence and Window parameters present."""
        dataset = Dataset()
        # Use consistent LUT descriptor and data (3 entries for 3 data points)
        dataset.VOILUTSequence = [VoiLutModule.create_voi_lut_item([3, 0, 8], [0, 1, 2])]
        dataset.WindowCenter = 2048.0
        dataset.WindowWidth = 4096.0
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "(0028,3010)" in result.warnings[0]
        assert "(0028,1050/1051)" in result.warnings[0]
        assert "alternative views" in result.warnings[0]
    
    def test_missing_window_width_when_center_present(self):
        """Test validation error when Window Center present but Width missing."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        # Missing WindowWidth
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,1051)" in result.errors[0]
        assert "(0028,1050)" in result.errors[0]
        assert "Type 1C" in result.errors[0]
    
    def test_missing_window_center_when_width_present(self):
        """Test validation error when Window Width present but Center missing."""
        dataset = Dataset()
        dataset.WindowWidth = 4096.0
        # Missing WindowCenter
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,1050)" in result.errors[0]
        assert "(0028,1051)" in result.errors[0]
        assert "Type 1C" in result.errors[0]
    
    def test_empty_voi_lut_sequence(self):
        """Test validation error with empty VOI LUT Sequence."""
        dataset = Dataset()
        dataset.VOILUTSequence = []
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,3010)" in result.errors[0]
        assert "at least one item" in result.errors[0]
        assert "PS3.3 C.11.2" in result.errors[0]
    
    def test_missing_lut_descriptor_in_sequence_item(self):
        """Test validation error for missing LUT Descriptor in sequence item."""
        dataset = Dataset()
        item = Dataset()
        item.LUTData = [0, 1, 2, 3]
        # Missing LUTDescriptor
        dataset.VOILUTSequence = [item]
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,3002)" in result.errors[0]
        assert "Type 1 element" in result.errors[0]
        assert "item 1" in result.errors[0]
    
    def test_missing_lut_data_in_sequence_item(self):
        """Test validation error for missing LUT Data in sequence item."""
        dataset = Dataset()
        item = Dataset()
        item.LUTDescriptor = [256, 0, 8]
        # Missing LUTData
        dataset.VOILUTSequence = [item]
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,3006)" in result.errors[0]
        assert "Type 1 element" in result.errors[0]
        assert "item 1" in result.errors[0]
    
    def test_invalid_lut_descriptor_format(self):
        """Test validation error for invalid LUT Descriptor format."""
        dataset = Dataset()
        item = Dataset()
        item.LUTDescriptor = [256, 0]  # Only 2 values instead of 3
        item.LUTData = [0, 1, 2, 3]
        dataset.VOILUTSequence = [item]
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,3002)" in result.errors[0]
        assert "exactly 3 values" in result.errors[0]
        assert "item 1" in result.errors[0]
    
    def test_invalid_lut_descriptor_bits_per_entry(self):
        """Test validation error for invalid bits per entry in LUT Descriptor."""
        dataset = Dataset()
        item = Dataset()
        item.LUTDescriptor = [256, 0, 32]  # 32 bits invalid for VOI LUT
        item.LUTData = [0, 1, 2, 3]
        dataset.VOILUTSequence = [item]
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "bits per entry" in result.errors[0]
        assert "between 8 and 16" in result.errors[0]
        assert "PS3.3 C.********" in result.errors[0]
    
    def test_window_center_width_value_count_mismatch(self):
        """Test validation error when Window Center and Width have different value counts."""
        dataset = Dataset()
        dataset.WindowCenter = [2048.0, 1024.0]  # 2 values
        dataset.WindowWidth = [4096.0]  # 1 value
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,1050)" in result.errors[0]
        assert "(0028,1051)" in result.errors[0]
        assert "same number of values" in result.errors[0]
        assert "PS3.3 C.11.2.1.2" in result.errors[0]
    
    def test_window_width_too_small_linear_function(self):
        """Test validation error for Window Width < 1 with LINEAR function."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        dataset.WindowWidth = 0.5  # Too small for LINEAR
        dataset.VOILUTFunction = VoiLutFunction.LINEAR.value
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,1051)" in result.errors[0]
        assert ">= 1 for LINEAR" in result.errors[0]
        assert "PS3.3 C.11.2.1.2" in result.errors[0]
    
    def test_window_width_zero_sigmoid_function(self):
        """Test validation error for Window Width <= 0 with SIGMOID function."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        dataset.WindowWidth = 0  # Invalid for SIGMOID
        dataset.VOILUTFunction = VoiLutFunction.SIGMOID.value
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,1051)" in result.errors[0]
        assert "> 0 for SIGMOID" in result.errors[0]
        assert "PS3.3 C.11.2.1.3" in result.errors[0]
    
    def test_window_width_zero_linear_exact_function(self):
        """Test validation error for Window Width <= 0 with LINEAR_EXACT function."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        dataset.WindowWidth = -1  # Invalid for LINEAR_EXACT
        dataset.VOILUTFunction = VoiLutFunction.LINEAR_EXACT.value
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,1051)" in result.errors[0]
        assert "> 0 for LINEAR_EXACT" in result.errors[0]
        assert "PS3.3 C.11.2.1.3" in result.errors[0]
    
    def test_window_explanation_count_mismatch_warning(self):
        """Test validation warning when Window Explanation count doesn't match pairs."""
        dataset = Dataset()
        dataset.WindowCenter = [2048.0, 1024.0]  # 2 values
        dataset.WindowWidth = [4096.0, 2048.0]  # 2 values
        dataset.WindowCenterWidthExplanation = ["Soft tissue"]  # 1 explanation
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "(0028,1055)" in result.warnings[0]
        assert "same number of values" in result.warnings[0]
    
    def test_invalid_window_values_format(self):
        """Test validation error for invalid Window Center/Width value format."""
        dataset = Dataset()
        # Create dataset elements with invalid values by setting _value directly
        from pydicom.dataelem import DataElement
        from pydicom.tag import Tag
        
        # Create data elements and set their values directly to bypass validation
        elem1 = DataElement(Tag(0x0028, 0x1050), 'DS', "")
        elem1._value = "invalid_format"  # Set invalid value directly
        elem2 = DataElement(Tag(0x0028, 0x1051), 'DS', "")  
        elem2._value = "also_invalid"    # Set invalid value directly
        
        dataset[Tag(0x0028, 0x1050)] = elem1
        dataset[Tag(0x0028, 0x1051)] = elem2
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0028,1050)" in result.errors[0] or "(0028,1051)" in result.errors[0]
        assert "Invalid" in result.errors[0]
        assert "decimal strings" in result.errors[0]
    
    def test_invalid_voi_lut_function_enumerated_value(self):
        """Test validation warning for invalid VOI LUT Function enumerated value."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        dataset.WindowWidth = 4096.0
        dataset.VOILUTFunction = "INVALID_FUNCTION"
        config = ValidationConfig(check_enumerated_values=True)
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) == 1
        assert "VOILUTFunction" in result.warnings[0]
        assert "(0028,1056)" in result.warnings[0]
    
    def test_lut_data_length_mismatch_warning(self):
        """Test validation warning when LUT Data length doesn't match descriptor."""
        dataset = Dataset()
        item = Dataset()
        item.LUTDescriptor = [10, 0, 8]  # Expects 10 entries
        item.LUTData = [0, 1, 2, 3, 4]  # Only 5 entries
        dataset.VOILUTSequence = [item]
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "LUT Data length" in result.warnings[0]
        assert "display issues" in result.warnings[0]
    
    def test_multiple_sequence_items_validation(self):
        """Test validation with multiple VOI LUT Sequence items."""
        dataset = Dataset()
        item1 = Dataset()
        item1.LUTDescriptor = [256, 0, 8]
        item1.LUTData = list(range(256))
        
        item2 = Dataset()
        item2.LUTDescriptor = [512, 0, 16]
        item2.LUTData = list(range(512))
        
        dataset.VOILUTSequence = [item1, item2]
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validation_config_enumerated_values_disabled(self):
        """Test that enumerated value validation respects config setting."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        dataset.WindowWidth = 4096.0
        dataset.VOILUTFunction = "INVALID_FUNCTION"
        config = ValidationConfig(check_enumerated_values=False)
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not validate enumerated values when disabled
        assert all("VOILUTFunction" not in error for error in result.errors)
    
    def test_validation_result_structure(self):
        """Test that ValidationResult has proper structure and types."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        # Missing WindowWidth to generate an error
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        assert len(result.errors) >= 1
        assert all(isinstance(error, str) for error in result.errors)
        assert all(isinstance(warning, str) for warning in result.warnings)
    
    def test_comprehensive_error_message_quality(self):
        """Test that error messages contain required information."""
        dataset = Dataset()
        dataset.WindowCenter = 2048.0
        # Missing WindowWidth
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert len(result.errors) >= 1
        error_message = result.errors[0]
        
        # Check error message contains DICOM tag reference
        assert "(" in error_message and ")" in error_message
        
        # Check error message is specific and actionable
        assert "required" in error_message.lower() or "missing" in error_message.lower()
        
        # Check error message provides context
        assert "Type 1C" in error_message or "conditional" in error_message.lower()
    
    def test_real_world_ct_window_scenario(self):
        """Test validation with realistic CT window parameters."""
        dataset = Dataset()
        # Typical CT window values
        dataset.WindowCenter = [40.0, 400.0, -600.0]  # Soft tissue, bone, lung
        dataset.WindowWidth = [400.0, 1000.0, 1600.0]
        dataset.WindowCenterWidthExplanation = ["Soft tissue", "Bone", "Lung"]
        dataset.VOILUTFunction = VoiLutFunction.LINEAR.value
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_real_world_lut_sequence_scenario(self):
        """Test validation with realistic LUT sequence data."""
        dataset = Dataset()
        item = Dataset()
        item.LUTDescriptor = [4096, -1024, 12]  # 4K LUT, starting at -1024, 12 bits
        item.LUTData = list(range(4096))
        item.LUTExplanation = "CT Hounsfield Unit to Display Value LUT"
        dataset.VOILUTSequence = [item]
        config = ValidationConfig()
        
        result = VoiLutValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0