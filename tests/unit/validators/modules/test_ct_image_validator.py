"""Tests for CT Image Module Validator - DICOM PS3.3 C.8.2.1"""

import pytest
from pydicom import Dataset
from src.pyrt_dicom.validators.modules.ct_image_validator import CTImageValidator
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators.validation_result import ValidationResult
from src.pyrt_dicom.enums.image_enums import (
    MultiEnergyCTAcquisition,
    RotationDirection,
    ExposureModulationType,
    CTImageTypeValue1,
    CTImageTypeValue2,
    CTImageTypeValue3,
    CTImageTypeValue4,
    CTSamplesPerPixel,
    CTBitsAllocated,
    CTBitsStored,
    RescaleType,
)
from src.pyrt_dicom.enums.image_enums import PhotometricInterpretation


class TestCTImageValidator:
    """Test suite for CT Image Module Validator."""

    def setup_method(self):
        """Set up test fixtures for each test method."""
        self.config = ValidationConfig()
        self.valid_dataset = self._create_valid_ct_image_dataset()

    def _create_valid_ct_image_dataset(self) -> Dataset:
        """Create a valid CT Image module dataset for testing."""
        dataset = Dataset()

        # Type 1 elements (required)
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL"]
        dataset.SamplesPerPixel = CTSamplesPerPixel.ONE.value
        dataset.PhotometricInterpretation = PhotometricInterpretation.MONOCHROME2.value
        dataset.BitsAllocated = CTBitsAllocated.SIXTEEN.value
        dataset.BitsStored = CTBitsStored.SIXTEEN.value
        dataset.HighBit = 15
        dataset.RescaleIntercept = -1024.0
        dataset.RescaleSlope = 1.0

        # Type 2 elements (required but can be empty)
        dataset.KVP = 120.0
        dataset.AcquisitionNumber = 1

        # Type 3 elements (optional)
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.NO.value

        return dataset

    def test_validate_returns_validation_result(self):
        """Test that validate method returns ValidationResult instance."""
        result = CTImageValidator.validate(self.valid_dataset, self.config)
        assert isinstance(result, ValidationResult)

    def test_valid_dataset_passes_validation(self):
        """Test that a valid CT Image dataset passes validation without errors."""
        result = CTImageValidator.validate(self.valid_dataset, self.config)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    # Type 1 Required Element Tests

    @pytest.mark.parametrize(
        "missing_attr,expected_error_text",
        [
            ("ImageType", "Image Type (0008,0008) is required (Type 1)"),
            ("SamplesPerPixel", "Samples per Pixel (0028,0002) is required (Type 1)"),
            (
                "PhotometricInterpretation",
                "Photometric Interpretation (0028,0004) is required (Type 1)",
            ),
            ("BitsAllocated", "Bits Allocated (0028,0100) is required (Type 1)"),
            ("BitsStored", "Bits Stored (0028,0101) is required (Type 1)"),
            ("HighBit", "High Bit (0028,0102) is required (Type 1)"),
            ("RescaleIntercept", "Rescale Intercept (0028,1052) is required (Type 1)"),
            ("RescaleSlope", "Rescale Slope (0028,1053) is required (Type 1)"),
        ],
    )
    def test_missing_type1_elements_generate_errors(
        self, missing_attr, expected_error_text
    ):
        """Test that missing Type 1 elements generate appropriate validation errors."""
        dataset = self._create_valid_ct_image_dataset()
        delattr(dataset, missing_attr)

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(expected_error_text in error for error in result.errors)

    # Type 1C Conditional Logic Tests

    def test_rescale_type_required_when_multi_energy_yes(self):
        """Test Rescale Type is required when Multi-energy CT Acquisition is YES."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        # RescaleType is missing

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "Rescale Type (0028,1054) is required" in error for error in result.errors
        )

    def test_rescale_type_required_when_rescale_type_not_hu(self):
        """Test Rescale Type is required when it's not HU."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.RescaleType = "COUNTS"  # Not HU

        result = CTImageValidator.validate(dataset, self.config)
        # Should not generate error since RescaleType is present
        rescale_errors = [
            error
            for error in result.errors
            if "Rescale Type (0028,1054) is required" in error
        ]
        assert len(rescale_errors) == 0

    def test_energy_weighting_factor_required_with_multi_energy_derivation(self):
        """Test Energy Weighting Factor is required with specific derivation code."""
        dataset = self._create_valid_ct_image_dataset()

        # Create derivation code sequence with multi-energy proportional weighting
        derivation_item = Dataset()
        derivation_item.CodeValue = "113097"
        derivation_item.CodingSchemeDesignator = "DCM"
        derivation_item.CodeMeaning = "Multi-energy proportional weighting"
        dataset.DerivationCodeSequence = [derivation_item]
        # EnergyWeightingFactor is missing

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "Energy Weighting Factor (0018,9353) is required" in error
            for error in result.errors
        )

    def test_water_equivalent_diameter_calculation_method_required(self):
        """Test Water Equivalent Diameter Calculation Method is required when Water Equivalent Diameter is present."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.WaterEquivalentDiameter = 300.0
        # WaterEquivalentDiameterCalculationMethodCodeSequence is missing

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "Water Equivalent Diameter Calculation Method Code Sequence (0018,1272)"
            in error
            for error in result.errors
        )

    # Enumerated Value Tests

    @pytest.mark.parametrize(
        "invalid_value,attr_name,expected_text,message_type",
        [
            (
                "INVALID",
                "MultienergyCTAcquisition",
                "should be one of: YES, NO",
                "warning",
            ),
            (2, "SamplesPerPixel", "must be 1 for CT images", "error"),
            (
                "RGB",
                "PhotometricInterpretation",
                "must be one of",
                "error",
            ),
            (8, "BitsAllocated", "must be 16 for CT images", "error"),
            ("INVALID", "RotationDirection", "should be one of: CW, CC", "warning"),
            ("INVALID", "ExposureModulationType", "should be one of: NONE", "warning"),
            ("INVALID", "RescaleType", "should be one of: HU, US", "warning"),
        ],
    )
    def test_invalid_enumerated_values_generate_warnings(
        self, invalid_value, attr_name, expected_text, message_type
    ):
        """Test that invalid enumerated values generate appropriate validation messages."""
        dataset = self._create_valid_ct_image_dataset()
        setattr(dataset, attr_name, invalid_value)

        result = CTImageValidator.validate(dataset, self.config)

        if message_type == "warning":
            assert len(result.warnings) > 0
            assert any(expected_text in warning for warning in result.warnings)
        else:  # error
            assert len(result.errors) > 0
            assert any(expected_text in error for error in result.errors)

    @pytest.mark.parametrize("valid_bits_stored", [12, 13, 14, 15, 16])
    def test_valid_bits_stored_values_pass(self, valid_bits_stored):
        """Test that valid Bits Stored values pass validation."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.BitsStored = valid_bits_stored
        dataset.HighBit = valid_bits_stored - 1  # Adjust HighBit accordingly

        result = CTImageValidator.validate(dataset, self.config)
        bits_stored_errors = [
            error for error in result.errors if "Bits Stored" in error
        ]
        assert len(bits_stored_errors) == 0

    # Image Type Validation Tests

    def test_image_type_value4_required_for_multi_energy_yes(self):
        """Test that Image Type Value 4 is required when Multi-energy CT Acquisition is YES."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL"]  # Missing Value 4
        dataset.RescaleType = RescaleType.HU.value

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "Image Type (0008,0008) Value 4 shall be present" in error
            for error in result.errors
        )

    def test_image_type_value4_not_allowed_for_multi_energy_no(self):
        """Test that Image Type Value 4 generates warning when Multi-energy CT Acquisition is NO."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.NO.value
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL", "VMI"]  # Has Value 4

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.warnings) > 0
        assert any(
            "Image Type (0008,0008) Value 4 should not be present" in warning
            for warning in result.warnings
        )

    @pytest.mark.parametrize(
        "image_type_values,expected_valid",
        [
            (["ORIGINAL", "PRIMARY", "AXIAL"], True),
            (["DERIVED", "SECONDARY", "LOCALIZER"], True),
            (["INVALID", "PRIMARY", "AXIAL"], False),
            (["ORIGINAL", "INVALID", "AXIAL"], False),
            (["ORIGINAL", "PRIMARY", "INVALID"], False),
        ],
    )
    def test_image_type_value_validation(self, image_type_values, expected_valid):
        """Test Image Type values 1-3 validation."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.ImageType = image_type_values

        result = CTImageValidator.validate(dataset, self.config)
        # Check both errors and warnings for Image Type validation issues
        all_issues = result.errors + result.warnings
        image_type_issues = [
            issue for issue in all_issues if "Image Type (0008,0008)" in issue
        ]

        if expected_valid:
            assert len(image_type_issues) == 0
        else:
            assert len(image_type_issues) > 0

    # Multi-Energy Sequence Exclusion Tests

    def test_ct_additional_xray_source_not_allowed_with_multi_energy_yes(self):
        """Test that CT Additional X-Ray Source Sequence is not allowed when Multi-energy is YES."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        dataset.RescaleType = RescaleType.HU.value
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL", "VMI"]

        # Add CT Additional X-Ray Source Sequence (should not be present)
        additional_source = Dataset()
        additional_source.KVP = 80.0
        additional_source.XRayTubeCurrentInmA = 200.0
        dataset.CTAdditionalXRaySourceSequence = [additional_source]

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "CT Additional X-Ray Source Sequence (0018,9360) shall not be present"
            in error
            for error in result.errors
        )

    def test_multi_energy_sequence_exclusion_validation(self):
        """Test multi-energy sequence exclusion logic with conflicting values."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        dataset.RescaleType = RescaleType.HU.value
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL", "VMI"]
        dataset.KVP = 120.0  # Present at module level

        # Create multi-energy sequence with different KVP values
        multi_energy_item1 = Dataset()
        multi_energy_item1.KVP = 80.0
        multi_energy_item2 = Dataset()
        multi_energy_item2.KVP = 140.0
        dataset.MultienergyCTAcquisitionSequence = [
            multi_energy_item1,
            multi_energy_item2,
        ]

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "KVP (0018,0060) shall not be present at module level" in error
            for error in result.errors
        )

    # Pixel Data Consistency Tests

    def test_high_bit_consistency_with_bits_stored(self):
        """Test that High Bit is validated against Bits Stored."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.BitsStored = 12
        dataset.HighBit = 15  # Should be 11 (BitsStored - 1)

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "High Bit (0028,0102) should be one less than Bits Stored" in error
            for error in result.errors
        )

    def test_valid_high_bit_passes_validation(self):
        """Test that correct High Bit value passes validation."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.BitsStored = 12
        dataset.HighBit = 11  # Correct: BitsStored - 1

        result = CTImageValidator.validate(dataset, self.config)
        high_bit_errors = [error for error in result.errors if "High Bit" in error]
        assert len(high_bit_errors) == 0

    # Sequence Structure Validation Tests

    def test_ctdi_phantom_type_code_sequence_structure(self):
        """Test CTDI Phantom Type Code Sequence structure validation."""
        dataset = self._create_valid_ct_image_dataset()

        # Test multiple items (should error)
        item1 = Dataset()
        item1.CodeValue = "113691"
        item1.CodingSchemeDesignator = "DCM"
        item2 = Dataset()
        item2.CodeValue = "113690"
        item2.CodingSchemeDesignator = "DCM"
        dataset.CTDIPhantomTypeCodeSequence = [item1, item2]

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any(
            "CTDI Phantom Type Code Sequence (0018,9346) should contain only one item"
            in error
            for error in result.errors
        )

    def test_ctdi_phantom_type_code_sequence_missing_required_attrs(self):
        """Test CTDI Phantom Type Code Sequence missing required attributes."""
        dataset = self._create_valid_ct_image_dataset()

        # Test missing CodeValue
        item = Dataset()
        item.CodingSchemeDesignator = "DCM"
        # Missing CodeValue
        dataset.CTDIPhantomTypeCodeSequence = [item]

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        assert any("missing CodeValue" in error for error in result.errors)

    def test_water_equivalent_diameter_sequence_validation(self):
        """Test Water Equivalent Diameter Calculation Method Code Sequence validation."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.WaterEquivalentDiameter = 300.0

        # Test missing required attributes
        item = Dataset()
        item.CodeValue = "113835"
        # Missing CodingSchemeDesignator
        dataset.WaterEquivalentDiameterCalculationMethodCodeSequence = [item]

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        calculation_errors = [
            error
            for error in result.errors
            if "Water Equivalent Diameter Calculation Method" in error
        ]
        assert (
            len(calculation_errors) >= 1
        )  # Should have errors for missing required attr and missing sequence

    def test_ct_additional_xray_source_sequence_structure(self):
        """Test CT Additional X-Ray Source Sequence structure validation."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.NO.value

        # Test missing required attributes
        item = Dataset()
        item.KVP = 120.0
        # Missing other required attributes
        dataset.CTAdditionalXRaySourceSequence = [item]

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0
        xray_source_errors = [
            error
            for error in result.errors
            if "CT Additional X-Ray Source Sequence" in error
            and "missing required attribute" in error
        ]
        assert len(xray_source_errors) > 0

    # Error Message Quality Tests

    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include DICOM tag references."""
        dataset = Dataset()  # Empty dataset to trigger multiple errors

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0

        # Check that errors include DICOM tag references in format (xxxx,xxxx)
        import re

        tag_pattern = r"\(\d{4},\d{4}\)"
        errors_with_tags = [
            error for error in result.errors if re.search(tag_pattern, error)
        ]
        assert len(errors_with_tags) > 0

    def test_error_messages_are_descriptive(self):
        """Test that error messages are human-readable and descriptive."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        # Missing RescaleType (should trigger conditional error)

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) > 0

        # Check that conditional error message explains the condition
        conditional_errors = [
            error
            for error in result.errors
            if "Multi-energy CT Acquisition" in error and "required" in error
        ]
        assert len(conditional_errors) > 0
        assert any(
            "when" in error for error in conditional_errors
        )  # Should explain condition

    # Validation Configuration Tests

    def test_validation_config_disable_conditional_requirements(self):
        """Test that disabling conditional requirements validation works."""
        config = ValidationConfig()
        config.validate_conditional_requirements = False

        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        # Missing RescaleType - but conditional validation is disabled

        result = CTImageValidator.validate(dataset, config)
        conditional_errors = [
            error
            for error in result.errors
            if "Rescale Type (0028,1054) is required" in error
        ]
        assert len(conditional_errors) == 0

    def test_validation_config_disable_enumerated_values(self):
        """Test that disabling enumerated values validation works."""
        config = ValidationConfig()
        config.check_enumerated_values = False

        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = "INVALID_VALUE"

        result = CTImageValidator.validate(dataset, config)
        enum_errors = [
            error
            for error in result.errors
            if "Multi-energy CT Acquisition" in error and "valid values" in error
        ]
        assert len(enum_errors) == 0

    def test_validation_config_disable_sequence_validation(self):
        """Test that disabling sequence validation works."""
        config = ValidationConfig()
        config.validate_sequences = False

        dataset = self._create_valid_ct_image_dataset()
        # Add invalid sequence structure
        item = Dataset()
        # Missing required attributes
        dataset.CTDIPhantomTypeCodeSequence = [item]

        result = CTImageValidator.validate(dataset, config)
        sequence_errors = [
            error for error in result.errors if "missing CodeValue" in error
        ]
        assert len(sequence_errors) == 0

    # Edge Cases and Boundary Conditions

    def test_empty_sequences_validation(self):
        """Test validation with empty sequences."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.CTDIPhantomTypeCodeSequence = []
        dataset.MultienergyCTAcquisitionSequence = []

        result = CTImageValidator.validate(dataset, self.config)
        # Empty sequences should not generate errors
        empty_seq_errors = [
            error for error in result.errors if "empty" in error.lower()
        ]
        assert len(empty_seq_errors) == 0

    def test_none_values_validation(self):
        """Test validation behavior with None values."""
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = None
        dataset.RotationDirection = None

        result = CTImageValidator.validate(dataset, self.config)
        # None values should be treated as absent, not invalid
        none_errors = [error for error in result.errors if "None" in error]
        assert len(none_errors) == 0

    def test_validation_result_format_consistency(self):
        """Test that ValidationResult format is consistent."""
        dataset = Dataset()  # Empty to trigger errors

        result = CTImageValidator.validate(dataset, self.config)

        # Ensure ValidationResult has required attributes
        assert hasattr(result, "errors")
        assert hasattr(result, "warnings")
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)

        # Ensure all errors are strings
        for error in result.errors:
            assert isinstance(error, str)
            assert len(error) > 0

        for warning in result.warnings:
            assert isinstance(warning, str)
            assert len(warning) > 0

    def test_complex_multi_energy_scenario(self):
        """Test complex multi-energy CT scenario with all conditional logic."""
        dataset = self._create_valid_ct_image_dataset()

        # Set up multi-energy scenario
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL", "VMI"]
        dataset.RescaleType = RescaleType.HU.value

        # Add required Multi-energy CT Acquisition Sequence
        multi_energy_item = Dataset()
        multi_energy_item.KVP = 120.0
        multi_energy_item.XRayTubeCurrentInmA = 200.0
        dataset.MultienergyCTAcquisitionSequence = [multi_energy_item]

        # Add derivation code sequence requiring energy weighting factor
        derivation_item = Dataset()
        derivation_item.CodeValue = "113097"
        derivation_item.CodingSchemeDesignator = "DCM"
        derivation_item.CodeMeaning = "Multi-energy proportional weighting"
        dataset.DerivationCodeSequence = [derivation_item]
        dataset.EnergyWeightingFactor = 0.7

        # Add water equivalent diameter with calculation method
        dataset.WaterEquivalentDiameter = 300.0
        calc_method_item = Dataset()
        calc_method_item.CodeValue = "113835"
        calc_method_item.CodingSchemeDesignator = "DCM"
        calc_method_item.CodeMeaning = "Water equivalent diameter"
        dataset.WaterEquivalentDiameterCalculationMethodCodeSequence = [
            calc_method_item
        ]

        result = CTImageValidator.validate(dataset, self.config)
        assert len(result.errors) == 0, f"Unexpected errors: {result.errors}"
        assert len(result.warnings) == 0, f"Unexpected warnings: {result.warnings}"

    # Granular Validation Method Tests (Dataset | BaseModule support)
    
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements method with pydicom Dataset."""
        # Valid dataset with all required elements
        result = CTImageValidator.validate_required_elements(self.valid_dataset)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
        # Dataset missing required elements
        empty_dataset = Dataset()
        result = CTImageValidator.validate_required_elements(empty_dataset)
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert result.error_count == 8  # All required elements missing
        
    def test_validate_required_elements_with_base_module(self):
        """Test validate_required_elements method with BaseModule instance."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        from src.pyrt_dicom.enums.image_enums import ImageType
        
        # Valid module with all required elements
        ct_module = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        result = CTImageValidator.validate_required_elements(ct_module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
        # Empty module missing required elements
        empty_module = CTImageModule()
        result = CTImageValidator.validate_required_elements(empty_module)
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert result.error_count == 8
        
    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements method with pydicom Dataset."""
        # Dataset with valid conditional requirements
        dataset = self._create_valid_ct_image_dataset()
        dataset.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        dataset.RescaleType = RescaleType.HU.value
        
        result = CTImageValidator.validate_conditional_requirements(dataset)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
        # Dataset missing conditional requirements
        dataset_invalid = self._create_valid_ct_image_dataset()
        dataset_invalid.MultienergyCTAcquisition = MultiEnergyCTAcquisition.YES.value
        # Missing RescaleType
        
        result = CTImageValidator.validate_conditional_requirements(dataset_invalid)
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert "RescaleType" in result.errors[0]
        
    def test_validate_conditional_requirements_with_base_module(self):
        """Test validate_conditional_requirements method with BaseModule instance."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        from src.pyrt_dicom.enums.image_enums import ImageType
        
        # Valid module with conditional requirements
        ct_module = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.YES
        ).with_rescale_type_conditional(rescale_type=RescaleType.HU)
        
        result = CTImageValidator.validate_conditional_requirements(ct_module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values method with pydicom Dataset."""
        # Valid enums
        result = CTImageValidator.validate_enumerated_values(self.valid_dataset)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
        # Invalid enums
        dataset_invalid = self._create_valid_ct_image_dataset()
        dataset_invalid.SamplesPerPixel = 2  # Should be 1 for CT
        dataset_invalid.PhotometricInterpretation = "RGB"  # Invalid for CT
        dataset_invalid.BitsAllocated = 8  # Should be 16 for CT
        
        result = CTImageValidator.validate_enumerated_values(dataset_invalid)
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert result.error_count >= 3
        
    def test_validate_enumerated_values_with_base_module(self):
        """Test validate_enumerated_values method with BaseModule instance."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        from src.pyrt_dicom.enums.image_enums import ImageType
        
        # Valid enums
        ct_module = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            multi_energy_ct_acquisition=MultiEnergyCTAcquisition.NO,
            rotation_direction=RotationDirection.CW
        )
        
        result = CTImageValidator.validate_enumerated_values(ct_module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
    def test_validate_pixel_data_consistency_with_dataset(self):
        """Test validate_pixel_data_consistency method with pydicom Dataset."""
        # Valid pixel data consistency
        result = CTImageValidator.validate_pixel_data_consistency(self.valid_dataset)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
        # Invalid pixel data consistency
        dataset_invalid = self._create_valid_ct_image_dataset()
        dataset_invalid.BitsStored = 16
        dataset_invalid.HighBit = 14  # Should be 15 (bits_stored - 1)
        
        result = CTImageValidator.validate_pixel_data_consistency(dataset_invalid)
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        assert "High Bit" in result.errors[0]
        
    def test_validate_pixel_data_consistency_with_base_module(self):
        """Test validate_pixel_data_consistency method with BaseModule instance."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        from src.pyrt_dicom.enums.image_enums import ImageType
        
        # Valid pixel data consistency
        ct_module = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        result = CTImageValidator.validate_pixel_data_consistency(ct_module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures method with pydicom Dataset."""
        # Valid sequences
        dataset = self._create_valid_ct_image_dataset()
        ctdi_item = Dataset()
        ctdi_item.CodeValue = "113681"
        ctdi_item.CodingSchemeDesignator = "DCM"
        ctdi_item.CodeMeaning = "IEC Body Dosimetry Phantom"
        dataset.CTDIPhantomTypeCodeSequence = [ctdi_item]
        
        result = CTImageValidator.validate_sequence_structures(dataset)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
        # Invalid sequence structure
        dataset_invalid = self._create_valid_ct_image_dataset()
        invalid_item = Dataset()
        # Missing required attributes
        dataset_invalid.CTDIPhantomTypeCodeSequence = [invalid_item]
        
        result = CTImageValidator.validate_sequence_structures(dataset_invalid)
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert not result.is_valid
        
    def test_validate_sequence_structures_with_base_module(self):
        """Test validate_sequence_structures method with BaseModule instance."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        from src.pyrt_dicom.enums.image_enums import ImageType
        
        # Valid sequences
        ct_module = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        ).with_optional_elements(
            ctdi_phantom_type_code_sequence=[
                CTImageModule.create_ctdi_phantom_type_code_item(
                    code_value="113681", code_meaning="IEC Body Dosimetry Phantom"
                )
            ]
        )
        
        result = CTImageValidator.validate_sequence_structures(ct_module)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
        
    def test_validate_main_method_with_both_types(self):
        """Test main validate method with both Dataset and BaseModule instances."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        from src.pyrt_dicom.enums.image_enums import ImageType
        
        # Test with Dataset
        result_dataset = CTImageValidator.validate(self.valid_dataset, self.config)
        assert isinstance(result_dataset, ValidationResult)
        assert not result_dataset.has_errors
        assert result_dataset.is_valid
        
        # Test with BaseModule
        ct_module = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        result_module = CTImageValidator.validate(ct_module, self.config)
        assert isinstance(result_module, ValidationResult)
        assert not result_module.has_errors
        assert result_module.is_valid
        
        # Results should be equivalent (zero-copy validation)
        assert result_dataset.error_count == result_module.error_count
        assert result_dataset.warning_count == result_module.warning_count
        
    def test_granular_methods_use_in_operator(self):
        """Test that granular methods use 'in' operator instead of hasattr/getattr."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        
        # Create module with minimal data to test attribute access patterns
        ct_module = CTImageModule()
        ct_module.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL"]  # Only set one required element
        
        # Test that validation works with 'in' operator pattern
        result = CTImageValidator.validate_required_elements(ct_module)
        assert isinstance(result, ValidationResult)
        assert result.has_errors  # Should find missing elements using 'in' operator
        assert result.error_count == 7  # 7 missing required elements (8 total - 1 present)
        
        # Verify we can use 'in' operator directly
        assert 'ImageType' in ct_module
        assert 'SamplesPerPixel' not in ct_module
        
    def test_zero_copy_performance_validation(self):
        """Test that BaseModule validation doesn't create Dataset copies."""
        from src.pyrt_dicom.modules.ct_image_module import CTImageModule
        from src.pyrt_dicom.enums.image_enums import ImageType
        
        ct_module = CTImageModule.from_required_elements(
            image_type=ImageType.DEFAULT,
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            kvp=120.0,
            acquisition_number=1,
        )
        
        # Test all granular methods with BaseModule (should use zero-copy)
        methods_to_test = [
            CTImageValidator.validate_required_elements,
            CTImageValidator.validate_conditional_requirements,
            CTImageValidator.validate_enumerated_values,
            CTImageValidator.validate_pixel_data_consistency,
            CTImageValidator.validate_sequence_structures,
        ]
        
        for method in methods_to_test:
            result = method(ct_module)
            assert isinstance(result, ValidationResult)
            # Validation should work directly on module without creating Dataset copy
            
        # Test main validate method with BaseModule
        result = CTImageValidator.validate(ct_module, self.config)
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid
