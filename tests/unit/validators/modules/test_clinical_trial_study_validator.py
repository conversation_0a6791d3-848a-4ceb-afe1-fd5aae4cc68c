"""Test Clinical Trial Study Module validator - PS3.3 C.7.2.3

Comprehensive test suite for ClinicalTrialStudyValidator covering all DICOM standard requirements,
conditional logic validation, enumerated value validation, and error message quality.
"""

import pytest
from pydicom import Dataset

from src.pyrt_dicom.validators.modules.clinical_trial_study_validator import ClinicalTrialStudyValidator
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators.validation_result import ValidationResult
from src.pyrt_dicom.modules.clinical_trial_study_module import ClinicalTrialStudyModule


class TestClinicalTrialStudyValidator:
    """Test Clinical Trial Study Module validator implementation."""

    def test_validate_empty_dataset_passes(self):
        """Test that an empty dataset passes validation without errors."""
        dataset = Dataset()
        config = ValidationConfig()
        
        result = ClinicalTrialStudyValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_minimal_valid_dataset_passes(self):
        """Test that a minimal valid dataset passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialTimePointID = "TP001"
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_complete_valid_dataset_passes(self):
        """Test that a complete valid dataset passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialTimePointID = "TP001"
        dataset.IssuerOfClinicalTrialTimePointID = "ISSUER001"
        dataset.ClinicalTrialTimePointDescription = "Baseline imaging"
        dataset.LongitudinalTemporalOffsetFromEvent = 0.0
        dataset.LongitudinalTemporalEventType = "ENROLLMENT"
        
        # Add valid consent sequence
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        consent_item.DistributionType = "NAMED_PROTOCOL"
        consent_item.ClinicalTrialProtocolID = "PROTOCOL001"
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        # Add valid time point type code sequence
        code_item = Dataset()
        code_item.CodeValue = "BASELINE"
        code_item.CodingSchemeDesignator = "DCM"
        code_item.CodeMeaning = "Baseline"
        dataset.ClinicalTrialTimePointTypeCodeSequence = [code_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    # Type 1C Conditional Validation Tests
    
    def test_temporal_event_type_required_when_offset_present(self):
        """Test Type 1C: Temporal Event Type required when Offset is present."""
        dataset = Dataset()
        dataset.LongitudinalTemporalOffsetFromEvent = 30.5
        # Missing LongitudinalTemporalEventType
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Longitudinal Temporal Event Type (0012,0053) is required" in result.errors[0]
        assert "Longitudinal Temporal Offset from Event (0012,0052) is present" in result.errors[0]

    def test_temporal_event_type_not_required_when_offset_absent(self):
        """Test Type 1C: Temporal Event Type not required when Offset is absent."""
        dataset = Dataset()
        # No LongitudinalTemporalOffsetFromEvent
        dataset.LongitudinalTemporalEventType = "ENROLLMENT"
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    # Enumerated Value Validation Tests
    
    @pytest.mark.parametrize("event_type", ["ENROLLMENT", "BASELINE"])
    def test_valid_temporal_event_types_accepted(self, event_type):
        """Test that valid temporal event types are accepted."""
        dataset = Dataset()
        dataset.LongitudinalTemporalOffsetFromEvent = 30.5
        dataset.LongitudinalTemporalEventType = event_type
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    def test_invalid_temporal_event_type_rejected(self):
        """Test that invalid temporal event types generate warnings."""
        dataset = Dataset()
        dataset.LongitudinalTemporalEventType = "INVALID_TYPE"
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.warnings) == 1
        assert "Longitudinal Temporal Event Type (0012,0053)" in result.warnings[0]
        assert "INVALID_TYPE" in result.warnings[0]

    # Consent Sequence Validation Tests
    
    def test_consent_sequence_missing_distribution_flag_error(self):
        """Test error when Consent for Distribution Flag is missing from sequence."""
        dataset = Dataset()
        consent_item = Dataset()
        # Missing ConsentForDistributionFlag
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Consent for Distribution Flag (0012,0085) is required (Type 1)" in result.errors[0]

    @pytest.mark.parametrize("flag_value", ["NO", "YES", "WITHDRAWN"])
    def test_valid_consent_distribution_flags_accepted(self, flag_value):
        """Test that valid consent distribution flags are accepted."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = flag_value
        if flag_value in ["YES", "WITHDRAWN"]:
            consent_item.DistributionType = "PUBLIC_RELEASE"
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    def test_invalid_consent_distribution_flag_rejected(self):
        """Test that invalid consent distribution flags generate warnings."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "INVALID_FLAG"
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.warnings) >= 1
        warning_messages = ' '.join(result.warnings)
        assert "Consent for Distribution Flag (0012,0085)" in warning_messages
        assert "INVALID_FLAG" in warning_messages

    def test_distribution_type_required_when_consent_yes(self):
        """Test Type 1C: Distribution Type required when consent is YES."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        # Missing DistributionType
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Distribution Type (0012,0084) is required" in result.errors[0]
        assert "YES or WITHDRAWN (Type 1C)" in result.errors[0]

    def test_distribution_type_required_when_consent_withdrawn(self):
        """Test Type 1C: Distribution Type required when consent is WITHDRAWN."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "WITHDRAWN"
        # Missing DistributionType
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Distribution Type (0012,0084) is required" in result.errors[0]
        assert "YES or WITHDRAWN (Type 1C)" in result.errors[0]

    def test_distribution_type_not_required_when_consent_no(self):
        """Test Type 1C: Distribution Type not required when consent is NO."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "NO"
        # No DistributionType needed
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    @pytest.mark.parametrize("dist_type", ["NAMED_PROTOCOL", "RESTRICTED_REUSE", "PUBLIC_RELEASE"])
    def test_valid_distribution_types_accepted(self, dist_type):
        """Test that valid distribution types are accepted."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        consent_item.DistributionType = dist_type
        if dist_type == "NAMED_PROTOCOL":
            consent_item.ClinicalTrialProtocolID = "PROTOCOL001"
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    def test_invalid_distribution_type_rejected(self):
        """Test that invalid distribution types generate warnings."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        consent_item.DistributionType = "INVALID_TYPE"
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.warnings) >= 1
        warning_messages = ' '.join(result.warnings)
        assert "Distribution Type (0012,0084)" in warning_messages
        assert "INVALID_TYPE" in warning_messages

    def test_protocol_id_required_when_distribution_named_protocol(self):
        """Test Type 1C: Protocol ID required when distribution type is NAMED_PROTOCOL."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        consent_item.DistributionType = "NAMED_PROTOCOL"
        # Missing ClinicalTrialProtocolID
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Clinical Trial Protocol ID (0012,0020) is required" in result.errors[0]
        assert "NAMED_PROTOCOL (Type 1C)" in result.errors[0]

    def test_protocol_id_not_required_when_distribution_not_named_protocol(self):
        """Test Type 1C: Protocol ID not required when distribution type is not NAMED_PROTOCOL."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        consent_item.DistributionType = "PUBLIC_RELEASE"
        # No ClinicalTrialProtocolID needed
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    # Code Sequence Macro Validation Tests
    
    def test_code_sequence_macro_validation_complete_item(self):
        """Test that complete code sequence items pass validation."""
        dataset = Dataset()
        code_item = Dataset()
        code_item.CodeValue = "BASELINE"
        code_item.CodingSchemeDesignator = "DCM"
        code_item.CodeMeaning = "Baseline"
        dataset.ClinicalTrialTimePointTypeCodeSequence = [code_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    @pytest.mark.parametrize("missing_attr", ["CodeValue", "CodingSchemeDesignator", "CodeMeaning"])
    def test_code_sequence_macro_validation_missing_required_attributes(self, missing_attr):
        """Test that missing required code sequence attributes generate errors."""
        dataset = Dataset()
        code_item = Dataset()
        code_item.CodeValue = "BASELINE"
        code_item.CodingSchemeDesignator = "DCM"
        code_item.CodeMeaning = "Baseline"
        
        # Remove the attribute being tested
        delattr(code_item, missing_attr)
        
        dataset.ClinicalTrialTimePointTypeCodeSequence = [code_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert missing_attr in result.errors[0]
        assert "Clinical Trial Time Point Type Code Sequence" in result.errors[0]

    # Validation Configuration Tests
    
    def test_validation_config_conditional_requirements_disabled(self):
        """Test that conditional validation can be disabled via config."""
        dataset = Dataset()
        dataset.LongitudinalTemporalOffsetFromEvent = 30.5
        # Missing LongitudinalTemporalEventType - should normally cause error
        
        config = ValidationConfig()
        config.validate_conditional_requirements = False
        
        result = ClinicalTrialStudyValidator.validate(dataset, config)
        
        # Should not have conditional requirement errors
        temporal_errors = [e for e in result.errors if "Longitudinal Temporal Event Type" in e]
        assert len(temporal_errors) == 0

    def test_validation_config_enumerated_values_disabled(self):
        """Test that enumerated value validation can be disabled via config."""
        dataset = Dataset()
        dataset.LongitudinalTemporalEventType = "INVALID_TYPE"
        
        config = ValidationConfig()
        config.check_enumerated_values = False
        
        result = ClinicalTrialStudyValidator.validate(dataset, config)
        
        # Should not have enumerated value errors
        enum_errors = [e for e in result.errors if "should be one of" in e or "INVALID_TYPE" in e]
        assert len(enum_errors) == 0

    def test_validation_config_sequences_disabled(self):
        """Test that sequence validation can be disabled via config."""
        dataset = Dataset()
        consent_item = Dataset()
        # Missing ConsentForDistributionFlag - should normally cause error
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        config = ValidationConfig()
        config.validate_sequences = False
        
        result = ClinicalTrialStudyValidator.validate(dataset, config)
        
        # Should not have sequence validation errors
        sequence_errors = [e for e in result.errors if "Consent for Clinical Trial Use Sequence" in e]
        assert len(sequence_errors) == 0

    # Complex Scenario Tests
    
    def test_multiple_consent_items_validation(self):
        """Test validation of multiple consent sequence items."""
        dataset = Dataset()
        
        # First item - valid
        consent_item1 = Dataset()
        consent_item1.ConsentForDistributionFlag = "YES"
        consent_item1.DistributionType = "PUBLIC_RELEASE"
        
        # Second item - missing distribution type
        consent_item2 = Dataset()
        consent_item2.ConsentForDistributionFlag = "WITHDRAWN"
        # Missing DistributionType
        
        dataset.ConsentForClinicalTrialUseSequence = [consent_item1, consent_item2]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "item 1" in result.errors[0]  # Second item (0-indexed)
        assert "Distribution Type (0012,0084) is required" in result.errors[0]

    def test_multiple_code_sequence_items_validation(self):
        """Test validation of multiple code sequence items."""
        dataset = Dataset()
        
        # First item - valid
        code_item1 = Dataset()
        code_item1.CodeValue = "BASELINE"
        code_item1.CodingSchemeDesignator = "DCM"
        code_item1.CodeMeaning = "Baseline"
        
        # Second item - missing CodeMeaning
        code_item2 = Dataset()
        code_item2.CodeValue = "FOLLOWUP"
        code_item2.CodingSchemeDesignator = "DCM"
        # Missing CodeMeaning
        
        dataset.ClinicalTrialTimePointTypeCodeSequence = [code_item1, code_item2]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "item 1" in result.errors[0]  # Second item (0-indexed)
        assert "CodeMeaning" in result.errors[0]

    # Error Message Quality Tests
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include DICOM tag references."""
        dataset = Dataset()
        dataset.LongitudinalTemporalOffsetFromEvent = 30.5
        # Missing LongitudinalTemporalEventType
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "(0012,0053)" in result.errors[0]  # Temporal Event Type tag
        assert "(0012,0052)" in result.errors[0]  # Temporal Offset tag

    def test_error_messages_indicate_type_classification(self):
        """Test that error messages indicate DICOM Type classification."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        # Missing DistributionType
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Type 1C" in result.errors[0]

    def test_error_messages_provide_context(self):
        """Test that error messages provide clear context about conditions."""
        dataset = Dataset()
        consent_item = Dataset()
        consent_item.ConsentForDistributionFlag = "YES"
        consent_item.DistributionType = "NAMED_PROTOCOL"
        # Missing ClinicalTrialProtocolID
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "when" in result.errors[0].lower()
        assert "NAMED_PROTOCOL" in result.errors[0]

    # Edge Case Tests
    
    def test_empty_sequences_pass_validation(self):
        """Test that empty sequences pass validation."""
        dataset = Dataset()
        dataset.ConsentForClinicalTrialUseSequence = []
        dataset.ClinicalTrialTimePointTypeCodeSequence = []
        
        result = ClinicalTrialStudyValidator.validate(dataset)
        
        assert len(result.errors) == 0

    def test_none_config_uses_default(self):
        """Test that None config parameter uses default configuration."""
        dataset = Dataset()

        result = ClinicalTrialStudyValidator.validate(dataset, None)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    # Test granular validation methods individually
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements method with Dataset."""
        dataset = Dataset()
        dataset.ClinicalTrialTimePointID = "TP001"

        result = ClinicalTrialStudyValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # No Type 1 elements in this module

    def test_validate_required_elements_with_basemodule(self):
        """Test validate_required_elements method with BaseModule."""
        module = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        )

        result = ClinicalTrialStudyValidator.validate_required_elements(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # No Type 1 elements in this module

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements method with Dataset."""
        dataset = Dataset()
        dataset.LongitudinalTemporalOffsetFromEvent = 30.5
        # Missing LongitudinalTemporalEventType should cause error

        result = ClinicalTrialStudyValidator.validate_conditional_requirements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Longitudinal Temporal Event Type (0012,0053) is required" in result.errors[0]

    def test_validate_conditional_requirements_with_basemodule(self):
        """Test validate_conditional_requirements method with BaseModule."""
        module = ClinicalTrialStudyModule.from_required_elements()
        module.LongitudinalTemporalOffsetFromEvent = 30.5
        # Missing LongitudinalTemporalEventType should cause error

        result = ClinicalTrialStudyValidator.validate_conditional_requirements(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Longitudinal Temporal Event Type (0012,0053) is required" in result.errors[0]

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values method with Dataset."""
        dataset = Dataset()
        dataset.LongitudinalTemporalEventType = "INVALID_TYPE"

        result = ClinicalTrialStudyValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.warnings) == 1
        assert "INVALID_TYPE" in result.warnings[0]

    def test_validate_enumerated_values_with_basemodule(self):
        """Test validate_enumerated_values method with BaseModule."""
        module = ClinicalTrialStudyModule.from_required_elements()
        module.LongitudinalTemporalEventType = "INVALID_TYPE"

        result = ClinicalTrialStudyValidator.validate_enumerated_values(module)

        assert isinstance(result, ValidationResult)
        assert len(result.warnings) == 1
        assert "INVALID_TYPE" in result.warnings[0]

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures method with Dataset."""
        dataset = Dataset()
        consent_item = Dataset()
        # Missing ConsentForDistributionFlag should cause error
        dataset.ConsentForClinicalTrialUseSequence = [consent_item]

        result = ClinicalTrialStudyValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Consent for Distribution Flag (0012,0085) is required" in result.errors[0]

    def test_validate_sequence_structures_with_basemodule(self):
        """Test validate_sequence_structures method with BaseModule."""
        module = ClinicalTrialStudyModule.from_required_elements()
        consent_item = Dataset()
        # Missing ConsentForDistributionFlag should cause error
        module.ConsentForClinicalTrialUseSequence = [consent_item]

        result = ClinicalTrialStudyValidator.validate_sequence_structures(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Consent for Distribution Flag (0012,0085) is required" in result.errors[0]

    # Test validator independence (external Dataset validation)
    def test_validator_independence_external_dataset(self):
        """Test that validator works independently on external pydicom Dataset."""
        # Create external dataset (not from our module)
        external_dataset = Dataset()
        external_dataset.ClinicalTrialTimePointID = "EXTERNAL_TP"
        external_dataset.LongitudinalTemporalOffsetFromEvent = 15.0
        external_dataset.LongitudinalTemporalEventType = "ENROLLMENT"

        # Validator should work on external dataset
        result = ClinicalTrialStudyValidator.validate(external_dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validator_independence_granular_methods_external_dataset(self):
        """Test that granular methods work independently on external Dataset."""
        external_dataset = Dataset()
        external_dataset.LongitudinalTemporalOffsetFromEvent = 15.0
        # Missing event type should trigger conditional requirement error

        # Test each granular method independently
        req_result = ClinicalTrialStudyValidator.validate_required_elements(external_dataset)
        assert isinstance(req_result, ValidationResult)
        assert len(req_result.errors) == 0

        cond_result = ClinicalTrialStudyValidator.validate_conditional_requirements(external_dataset)
        assert isinstance(cond_result, ValidationResult)
        assert len(cond_result.errors) == 1
        assert "Longitudinal Temporal Event Type (0012,0053) is required" in cond_result.errors[0]

        enum_result = ClinicalTrialStudyValidator.validate_enumerated_values(external_dataset)
        assert isinstance(enum_result, ValidationResult)
        assert len(enum_result.errors) == 0

        seq_result = ClinicalTrialStudyValidator.validate_sequence_structures(external_dataset)
        assert isinstance(seq_result, ValidationResult)
        assert len(seq_result.errors) == 0

    # Test zero-copy validation works correctly
    def test_zero_copy_validation_basemodule(self):
        """Test that BaseModule validation uses zero-copy (passes self, not dataset copy)."""
        module = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        )

        # Add some data to verify it's accessible
        module.LongitudinalTemporalOffsetFromEvent = 30.5
        module.LongitudinalTemporalEventType = "ENROLLMENT"

        # Validate using granular methods - should work with module directly
        req_result = ClinicalTrialStudyValidator.validate_required_elements(module)
        cond_result = ClinicalTrialStudyValidator.validate_conditional_requirements(module)
        enum_result = ClinicalTrialStudyValidator.validate_enumerated_values(module)
        seq_result = ClinicalTrialStudyValidator.validate_sequence_structures(module)

        # All should pass without errors
        assert len(req_result.errors) == 0
        assert len(cond_result.errors) == 0
        assert len(enum_result.errors) == 0
        assert len(seq_result.errors) == 0

        # Main validate method should also work
        main_result = ClinicalTrialStudyValidator.validate(module)
        assert len(main_result.errors) == 0

    # Test that all existing functionality still works
    def test_existing_functionality_still_works(self):
        """Test that all existing test scenarios still pass with new implementation."""
        # This is a meta-test to ensure we didn't break existing functionality
        # Run a few key existing test scenarios

        # Test 1: Empty dataset should pass
        dataset = Dataset()
        result = ClinicalTrialStudyValidator.validate(dataset)
        assert len(result.errors) == 0

        # Test 2: Valid complete dataset should pass
        dataset = Dataset()
        dataset.ClinicalTrialTimePointID = "TP001"
        dataset.LongitudinalTemporalOffsetFromEvent = 0.0
        dataset.LongitudinalTemporalEventType = "ENROLLMENT"
        result = ClinicalTrialStudyValidator.validate(dataset)
        assert len(result.errors) == 0

        # Test 3: Conditional requirement violation should fail
        dataset = Dataset()
        dataset.LongitudinalTemporalOffsetFromEvent = 30.5
        # Missing LongitudinalTemporalEventType
        result = ClinicalTrialStudyValidator.validate(dataset)
        assert len(result.errors) == 1
        assert "Longitudinal Temporal Event Type (0012,0053) is required" in result.errors[0]