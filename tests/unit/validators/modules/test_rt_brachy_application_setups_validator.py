"""Test RTBrachyApplicationSetupsValidator functionality.

Tests comprehensive validation of RT Brachy Application Setups Module according to DICOM PS3.3 C.8.8.15.
All tests use datasets generated from modules via to_dataset() method following
the new composition-based architecture patterns.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTBrachyApplicationSetupsModule
from pyrt_dicom.validators.modules.rt_brachy_application_setups_validator import RTBrachyApplicationSetupsValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.rt_enums import (
    BrachyTreatmentTechnique, BrachyTreatmentType, ApplicationSetupType,
    SourceType, SourceMovementType, BrachyAccessoryDeviceType,
    SourceApplicatorType, SourceStrengthUnits
)


class TestRTBrachyApplicationSetupsValidator:
    """Test RTBrachyApplicationSetupsValidator comprehensive validation logic."""
    
    def test_validate_method_signature(self):
        """Test validator method signature and return type."""
        # Create minimal valid dataset
        dataset = Dataset()
        dataset.BrachyTreatmentTechnique = BrachyTreatmentTechnique.INTERSTITIAL.value
        dataset.BrachyTreatmentType = BrachyTreatmentType.HDR.value
        dataset.TreatmentMachineSequence = []
        dataset.SourceSequence = []
        dataset.ApplicationSetupSequence = []
        
        # Test with default config
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Test with custom config
        config = ValidationConfig()
        result = RTBrachyApplicationSetupsValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)
    
    def test_missing_type1_elements_validation(self):
        """Test validation when Type 1 elements are missing."""
        dataset = Dataset()
        # Missing all Type 1 elements
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        # Should have errors for missing Type 1 elements
        expected_missing_elements = [
            'BrachyTreatmentTechnique', 'BrachyTreatmentType', 
            'TreatmentMachineSequence', 'SourceSequence', 'ApplicationSetupSequence'
        ]
        
        for element in expected_missing_elements:
            assert any(f'{element}' in error and 'is required (Type 1)' in error 
                      for error in result.errors), f'Missing error for {element}'
    
    def test_empty_sequences_validation(self):
        """Test validation of empty required sequences."""
        dataset = Dataset()
        dataset.BrachyTreatmentTechnique = BrachyTreatmentTechnique.INTERSTITIAL.value
        dataset.BrachyTreatmentType = BrachyTreatmentType.HDR.value
        dataset.TreatmentMachineSequence = []  # Empty but should have one item
        dataset.SourceSequence = []  # Empty but should have at least one item
        dataset.ApplicationSetupSequence = []  # Empty but should have at least one item
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        # Should have errors for empty sequences (treated as missing by validator)
        assert any('TreatmentMachineSequence' in error and 'is required (Type 1)' in error
                  for error in result.errors)
        assert any('SourceSequence' in error and 'is required (Type 1)' in error
                  for error in result.errors)
        assert any('ApplicationSetupSequence' in error and 'is required (Type 1)' in error
                  for error in result.errors)
    
    def test_valid_minimal_brachy_setup_validation(self):
        """Test validation of minimally valid brachy setup."""
        # Create minimal valid setup using module
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000"
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        # Create minimal channel (required for Application Setup)
        channel_item = RTBrachyApplicationSetupsModule.create_channel_item(
            channel_number=1,
            channel_total_time=600.0,
            source_movement_type=SourceMovementType.STEPWISE,
            referenced_source_number=1,
            number_of_control_points=1,
            brachy_control_point_sequence=[Dataset()],
            source_applicator_step_size=2.5,  # Required for STEPWISE movement
            channel_length=150.0,  # Type 2 required
            transfer_tube_number=1,  # Type 2 required
            transfer_tube_length=50.0  # Type 2C required when transfer tube number is present
        )

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            channel_sequence=[channel_item]  # Include required channel
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()

        result = RTBrachyApplicationSetupsValidator.validate(dataset)

        # Should pass validation with no errors
        assert len(result.errors) == 0, f'Unexpected validation errors: {result.errors}'
    
    def test_enumerated_values_validation(self):
        """Test enumerated values validation."""
        dataset = Dataset()
        dataset.BrachyTreatmentTechnique = "INVALID_TECHNIQUE"  # Invalid enumerated value
        dataset.BrachyTreatmentType = "INVALID_TYPE"  # Invalid enumerated value
        dataset.TreatmentMachineSequence = []
        dataset.SourceSequence = []
        dataset.ApplicationSetupSequence = []
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        assert any('BrachyTreatmentTechnique' in error and 'INVALID_TECHNIQUE' in error
                  for error in result.errors)
        assert any('BrachyTreatmentType' in error and 'INVALID_TYPE' in error
                  for error in result.errors)
    
    def test_source_sequence_type1_elements_validation(self):
        """Test validation of Type 1 elements in Source Sequence."""
        dataset = Dataset()
        dataset.BrachyTreatmentTechnique = BrachyTreatmentTechnique.INTERSTITIAL.value
        dataset.BrachyTreatmentType = BrachyTreatmentType.HDR.value
        dataset.TreatmentMachineSequence = [Dataset()]
        dataset.ApplicationSetupSequence = [Dataset()]
        
        # Source with missing Type 1 elements
        source_item = Dataset()
        # Missing all required Type 1 elements
        dataset.SourceSequence = [source_item]
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        # Should have errors for missing Type 1 elements in source
        expected_missing_elements = [
            'SourceNumber', 'SourceType', 'SourceIsotopeName', 
            'SourceIsotopeHalfLife', 'ReferenceAirKermaRate',
            'SourceStrengthReferenceDate', 'SourceStrengthReferenceTime'
        ]
        
        for element in expected_missing_elements:
            assert any(f'SourceSequence[0].{element}' in error and 'is required (Type 1)' in error 
                      for error in result.errors), f'Missing error for {element}'
    
    def test_application_setup_sequence_type1_elements_validation(self):
        """Test validation of Type 1 elements in Application Setup Sequence."""
        dataset = Dataset()
        dataset.BrachyTreatmentTechnique = BrachyTreatmentTechnique.INTERSTITIAL.value
        dataset.BrachyTreatmentType = BrachyTreatmentType.HDR.value
        dataset.TreatmentMachineSequence = [Dataset()]
        dataset.SourceSequence = [Dataset()]
        
        # Application setup with missing Type 1 elements
        setup_item = Dataset()
        # Missing all required Type 1 elements
        dataset.ApplicationSetupSequence = [setup_item]
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        # Should have errors for missing Type 1 elements in application setup
        expected_missing_elements = [
            'ApplicationSetupType', 'ApplicationSetupNumber', 'TotalReferenceAirKerma'
        ]
        
        for element in expected_missing_elements:
            assert any(f'ApplicationSetupSequence[0].{element}' in error and 'is required (Type 1)' in error 
                      for error in result.errors), f'Missing error for {element}'
    
    def test_pdr_conditional_validation(self):
        """Test PDR treatment conditional requirements."""
        # Create dataset with PDR treatment type
        dataset = Dataset()
        dataset.BrachyTreatmentTechnique = BrachyTreatmentTechnique.INTERSTITIAL.value
        dataset.BrachyTreatmentType = BrachyTreatmentType.PDR.value
        dataset.TreatmentMachineSequence = [Dataset()]
        dataset.SourceSequence = [Dataset()]
        
        # Application setup with channel missing PDR-required elements
        setup_item = Dataset()
        setup_item.ApplicationSetupType = ApplicationSetupType.FLETCHER_SUIT.value
        setup_item.ApplicationSetupNumber = 1
        setup_item.TotalReferenceAirKerma = 0.0
        
        channel_item = Dataset()
        channel_item.ChannelNumber = 1
        channel_item.ChannelTotalTime = 600.0
        channel_item.SourceMovementType = SourceMovementType.STEPWISE.value
        channel_item.ReferencedSourceNumber = 1
        channel_item.NumberOfControlPoints = 1
        channel_item.BrachyControlPointSequence = [Dataset()]
        # Missing NumberOfPulses and PulseRepetitionInterval (required for PDR)
        
        setup_item.ChannelSequence = [channel_item]
        dataset.ApplicationSetupSequence = [setup_item]
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        assert any('NumberOfPulses' in error and 'required (Type 1C)' in error and 'PDR' in error
                  for error in result.errors)
        assert any('PulseRepetitionInterval' in error and 'required (Type 1C)' in error and 'PDR' in error
                  for error in result.errors)
    
    def test_uniqueness_constraints_validation(self):
        """Test uniqueness constraints validation."""
        # Create dataset with duplicate source numbers
        source1 = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,  # Same number
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000"
        )
        
        source2 = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,  # Same number (should be unique)
            source_type=SourceType.LINE,
            source_isotope_name="Co-60",
            source_isotope_half_life=1925.0,
            reference_air_kerma_rate=30000.0,
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000"
        )
        
        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )
        
        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )
        
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source1, source2],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        assert any('SourceSequence[1].SourceNumber (1) must be unique within the RT Plan' in error
                  for error in result.errors)
    
    def test_value_ranges_validation(self):
        """Test value ranges validation."""
        # Create source with invalid values
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=-10.0,  # Invalid: must be positive
            reference_air_kerma_rate=-1000.0,  # Invalid: must be non-negative
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000"
        )
        
        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )
        
        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            total_reference_air_kerma=-500.0  # Invalid: must be non-negative
        )
        
        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()
        
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        
        assert any('SourceIsotopeHalfLife (-10.0) must be positive' in error
                  for error in result.errors)
        assert any('ReferenceAirKermaRate (-1000.0) must be non-negative' in error
                  for error in result.errors)
        assert any('TotalReferenceAirKerma (-500.0) must be non-negative' in error
                  for error in result.errors)

    def test_datetime_format_validation(self):
        """Test date/time format validation."""
        # Create source with invalid date/time formats
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="2024-01-01",  # Invalid format (should be YYYYMMDD)
            source_strength_reference_time="12:00:00"  # Invalid format (should be HHMMSS)
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()

        result = RTBrachyApplicationSetupsValidator.validate(dataset)

        assert any('SourceStrengthReferenceDate' in error and 'YYYYMMDD format' in error
                  for error in result.errors)
        assert any('SourceStrengthReferenceTime' in error and 'HHMMSS' in error
                  for error in result.errors)

    def test_cross_references_validation(self):
        """Test cross-references validation."""
        # Create setup with channel referencing non-existent source
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000"
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        # Create channel that references non-existent source number 99
        channel_item = RTBrachyApplicationSetupsModule.create_channel_item(
            channel_number=1,
            channel_total_time=600.0,
            source_movement_type=SourceMovementType.STEPWISE,
            referenced_source_number=99,  # Non-existent source
            number_of_control_points=1,
            brachy_control_point_sequence=[Dataset()]
        )

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            channel_sequence=[channel_item]
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()

        result = RTBrachyApplicationSetupsValidator.validate(dataset)

        assert any('ReferencedSourceNumber (99)' in error and 'does not reference a valid Source Number' in error
                  for error in result.errors)

    def test_stepwise_movement_conditional_validation(self):
        """Test STEPWISE source movement conditional requirements."""
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000"
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        # Create channel with STEPWISE movement but missing step size
        channel_item = Dataset()
        channel_item.ChannelNumber = 1
        channel_item.ChannelTotalTime = 600.0
        channel_item.SourceMovementType = SourceMovementType.STEPWISE.value
        channel_item.ReferencedSourceNumber = 1
        channel_item.NumberOfControlPoints = 1
        channel_item.BrachyControlPointSequence = [Dataset()]
        # Missing SourceApplicatorStepSize (required for STEPWISE)

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            channel_sequence=[channel_item]
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTERSTITIAL,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()

        result = RTBrachyApplicationSetupsValidator.validate(dataset)

        assert any('SourceApplicatorStepSize' in error and 'required (Type 1C)' in error and 'STEPWISE' in error
                  for error in result.errors)

    def test_validation_configuration_flags(self):
        """Test different validation configuration flags."""
        # Create dataset with valid basic structure but enumerated value issues
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type="INVALID_SOURCE_TYPE",  # Invalid enumerated value
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000"
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1"
        )

        channel_item = RTBrachyApplicationSetupsModule.create_channel_item(
            channel_number=1,
            channel_total_time=600.0,
            source_movement_type=SourceMovementType.STEPWISE,
            referenced_source_number=1,
            number_of_control_points=1,
            brachy_control_point_sequence=[Dataset()],
            source_applicator_step_size=2.5,
            channel_length=150.0,
            transfer_tube_number=1,
            transfer_tube_length=50.0
        )

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type="INVALID_SETUP_TYPE",  # Invalid enumerated value
            application_setup_number=1,
            channel_sequence=[channel_item]
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique="INVALID_TECHNIQUE",  # Invalid enumerated value
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()

        # Test with default config (all validations enabled)
        result = RTBrachyApplicationSetupsValidator.validate(dataset)
        # Should have enumerated value errors
        assert any('INVALID_TECHNIQUE' in error for error in result.errors)
        assert any('INVALID_SOURCE_TYPE' in error for error in result.errors)
        assert any('INVALID_SETUP_TYPE' in error for error in result.errors)

    def test_complex_valid_brachy_setup(self):
        """Test validation of complex but valid brachy setup."""
        # Create complex setup with all optional elements
        source_item = RTBrachyApplicationSetupsModule.create_source_item(
            source_number=1,
            source_type=SourceType.POINT,
            source_isotope_name="Ir-192",
            source_isotope_half_life=73.8,
            reference_air_kerma_rate=40800.0,
            source_strength_reference_date="20240101",
            source_strength_reference_time="120000",
            source_serial_number="SN123456",
            source_manufacturer="Varian",
            active_source_diameter=0.6,
            active_source_length=3.5
        )

        machine_item = RTBrachyApplicationSetupsModule.create_treatment_machine_item(
            treatment_machine_name="HDR Unit 1",
            manufacturer="Varian",
            institution_name="General Hospital"
        )

        accessory_item = RTBrachyApplicationSetupsModule.create_brachy_accessory_device_item(
            brachy_accessory_device_number=1,
            brachy_accessory_device_id="SHIELD_001",
            brachy_accessory_device_type=BrachyAccessoryDeviceType.SHIELD,
            brachy_accessory_device_name="Lead Shield"
        )

        channel_item = RTBrachyApplicationSetupsModule.create_channel_item(
            channel_number=1,
            channel_total_time=600.0,
            source_movement_type=SourceMovementType.STEPWISE,
            referenced_source_number=1,
            number_of_control_points=2,
            brachy_control_point_sequence=[Dataset(), Dataset()],
            source_applicator_step_size=2.5,
            channel_length=150.0,
            transfer_tube_number=1,
            transfer_tube_length=50.0
        )

        setup_item = RTBrachyApplicationSetupsModule.create_application_setup_item(
            application_setup_type=ApplicationSetupType.FLETCHER_SUIT,
            application_setup_number=1,
            application_setup_name="Cervix Treatment",
            total_reference_air_kerma=1000.0,
            brachy_accessory_device_sequence=[accessory_item],
            channel_sequence=[channel_item]
        )

        module = RTBrachyApplicationSetupsModule.from_required_elements(
            brachy_treatment_technique=BrachyTreatmentTechnique.INTRACAVITARY,
            brachy_treatment_type=BrachyTreatmentType.HDR,
            treatment_machine_sequence=[machine_item],
            source_sequence=[source_item],
            application_setup_sequence=[setup_item]
        )
        dataset = module.to_dataset()

        result = RTBrachyApplicationSetupsValidator.validate(dataset)

        # Complex but valid setup should pass validation
        assert len(result.errors) == 0, f'Unexpected validation errors: {result.errors}'
