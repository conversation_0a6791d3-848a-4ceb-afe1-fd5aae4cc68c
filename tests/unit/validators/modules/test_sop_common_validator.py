"""Tests for SOP Common Module Validator - DICOM PS3.3 C.12.1"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.sop_common_validator import SOPCommonValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import Validation<PERSON><PERSON>ult
from pyrt_dicom.enums.common_enums import (
    SyntheticData, SOPInstanceStatus, QueryRetrieveView,
    ContentQualification, LongitudinalTemporalInformationModified
)


class TestSOPCommonValidator:
    """Test class for SOPCommonValidator following pytest framework requirements."""
    
    # Basic Validation Tests
    
    def test_validate_empty_dataset_fails(self):
        """Test that empty dataset fails validation (missing required Type 1 elements)."""
        dataset = Dataset()
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2  # Missing SOP Class UID and SOP Instance UID
        assert len(result.warnings) == 0
        
        # Check specific error messages
        error_messages = ' '.join(result.errors)
        assert "SOP Class UID (0008,0016) is required (Type 1)" in error_messages
        assert "SOP Instance UID (0008,0018) is required (Type 1)" in error_messages
    
    def test_validate_minimal_valid_dataset_passes(self):
        """Test that dataset with minimal required elements passes validation."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"  # RT Dose Storage
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Required Elements Validation Tests
    
    def test_validate_missing_sop_class_uid_fails(self):
        """Test that missing SOP Class UID fails validation."""
        dataset = Dataset()
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        # Missing SOPClassUID
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "SOP Class UID (0008,0016) is required (Type 1)" in result.errors[0]
    
    def test_validate_empty_sop_class_uid_fails(self):
        """Test that empty SOP Class UID fails validation."""
        dataset = Dataset()
        dataset.SOPClassUID = ""  # Empty string
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "SOP Class UID (0008,0016) cannot be empty (Type 1)" in result.errors[0]
    
    def test_validate_missing_sop_instance_uid_fails(self):
        """Test that missing SOP Instance UID fails validation."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        # Missing SOPInstanceUID
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "SOP Instance UID (0008,0018) is required (Type 1)" in result.errors[0]
    
    def test_validate_empty_sop_instance_uid_fails(self):
        """Test that empty SOP Instance UID fails validation."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = ""  # Empty string
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "SOP Instance UID (0008,0018) cannot be empty (Type 1)" in result.errors[0]
    
    # Enumerated Values Validation Tests
    
    def test_validate_synthetic_data_valid_values_pass(self):
        """Test that valid Synthetic Data values pass validation."""
        valid_values = [val.value for val in SyntheticData]
        
        for value in valid_values:
            dataset = Dataset()
            dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
            dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
            dataset.SyntheticData = value
            
            config = ValidationConfig(check_enumerated_values=True)
            result = SOPCommonValidator.validate(dataset, config)
            
            assert len(result.errors) == 0, f"Valid Synthetic Data value '{value}' should pass"
            assert len(result.warnings) == 0
    
    def test_validate_synthetic_data_invalid_value_fails(self):
        """Test that invalid Synthetic Data value generates error."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.SyntheticData = "INVALID"
        
        config = ValidationConfig(check_enumerated_values=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) == 1
        assert "Synthetic Data (0008,001C)" in result.warnings[0]
        assert "INVALID" in result.warnings[0]
    
    def test_validate_sop_instance_status_valid_values_pass(self):
        """Test that valid SOP Instance Status values pass validation."""
        valid_values = [val.value for val in SOPInstanceStatus]
        
        for value in valid_values:
            dataset = Dataset()
            dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
            dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
            dataset.SOPInstanceStatus = value
            
            config = ValidationConfig(check_enumerated_values=True)
            result = SOPCommonValidator.validate(dataset, config)
            
            assert len(result.errors) == 0, f"Valid SOP Instance Status value '{value}' should pass"
    
    def test_validate_query_retrieve_view_valid_values_pass(self):
        """Test that valid Query/Retrieve View values pass validation."""
        valid_values = [val.value for val in QueryRetrieveView]
        
        for value in valid_values:
            dataset = Dataset()
            dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
            dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
            dataset.QueryRetrieveView = value
            
            config = ValidationConfig(check_enumerated_values=True)
            result = SOPCommonValidator.validate(dataset, config)
            
            assert len(result.errors) == 0, f"Valid Query/Retrieve View value '{value}' should pass"
    
    def test_validate_content_qualification_valid_values_pass(self):
        """Test that valid Content Qualification values pass validation."""
        valid_values = [val.value for val in ContentQualification]
        
        for value in valid_values:
            dataset = Dataset()
            dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
            dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
            dataset.ContentQualification = value
            
            config = ValidationConfig(check_enumerated_values=True)
            result = SOPCommonValidator.validate(dataset, config)
            
            assert len(result.errors) == 0, f"Valid Content Qualification value '{value}' should pass"
    
    def test_validate_temporal_information_modified_valid_values_pass(self):
        """Test that valid Longitudinal Temporal Information Modified values pass validation."""
        valid_values = [val.value for val in LongitudinalTemporalInformationModified]
        
        for value in valid_values:
            dataset = Dataset()
            dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
            dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
            dataset.LongitudinalTemporalInformationModified = value
            
            config = ValidationConfig(check_enumerated_values=True)
            result = SOPCommonValidator.validate(dataset, config)
            
            assert len(result.errors) == 0, f"Valid Temporal Information Modified value '{value}' should pass"
    
    # Conditional Requirements Validation Tests
    
    def test_validate_specific_character_set_conditional_warns_appropriately(self):
        """Test that Specific Character Set conditional logic generates appropriate warnings."""
        # Test with non-ASCII characters but no character set declaration
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PatientName = "Müller^Hans"  # Contains non-ASCII character
        # No SpecificCharacterSet
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Specific Character Set (0008,0005) may be required" in result.warnings[0]
        assert "non-ASCII characters" in result.warnings[0]
    
    def test_validate_specific_character_set_conditional_with_declaration_passes(self):
        """Test that non-ASCII characters with character set declaration pass."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PatientName = "Müller^Hans"  # Contains non-ASCII character
        dataset.SpecificCharacterSet = "ISO_IR 100"  # Latin1 character set
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_query_retrieve_view_conditional_warns_appropriately(self):
        """Test that Query/Retrieve View conditional logic generates appropriate warnings."""
        # Test with conversion source but no query/retrieve view
        conversion_item = Dataset()
        conversion_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.2"
        conversion_item.ReferencedSOPInstanceUID = "*******.*******.9"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.ConversionSourceAttributesSequence = [conversion_item]
        # No QueryRetrieveView
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) >= 1
        warning_messages = ' '.join(result.warnings)
        assert "Query/Retrieve View (0008,0053) may be required" in warning_messages
        assert "C-MOVE operation" in warning_messages
    
    def test_validate_conversion_source_conditional_warns_appropriately(self):
        """Test that Conversion Source Attributes conditional logic generates appropriate warnings."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.QueryRetrieveView = "CLASSIC"
        # No ConversionSourceAttributesSequence
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Conversion Source Attributes Sequence (0020,9172) may be required" in result.warnings[0]
        assert "created by conversion" in result.warnings[0]
    
    # Sequence Validation Tests
    
    def test_validate_coding_scheme_identification_sequence_valid_passes(self):
        """Test that valid Coding Scheme Identification Sequence passes validation."""
        coding_item = Dataset()
        coding_item.CodingSchemeDesignator = "DCM"
        coding_item.CodingSchemeRegistry = "HL7"
        coding_item.CodingSchemeUID = "1.2.840.10008.2.16.4"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.CodingSchemeIdentificationSequence = [coding_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_coding_scheme_identification_sequence_missing_designator_fails(self):
        """Test that missing Coding Scheme Designator fails validation."""
        coding_item = Dataset()
        # Missing CodingSchemeDesignator
        coding_item.CodingSchemeRegistry = "HL7"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.CodingSchemeIdentificationSequence = [coding_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        assert any("Coding Scheme Designator (0008,0102) is required (Type 1)" in error
                  for error in result.errors)
    
    def test_validate_context_group_identification_sequence_valid_passes(self):
        """Test that valid Context Group Identification Sequence passes validation."""
        context_item = Dataset()
        context_item.ContextIdentifier = "CID 3"
        context_item.MappingResource = "DCMR"
        context_item.ContextGroupVersion = "20121130"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.ContextGroupIdentificationSequence = [context_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_context_group_identification_sequence_missing_elements_fail(self):
        """Test that missing required elements in Context Group Identification Sequence fail validation."""
        context_item = Dataset()
        # Missing all required elements
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.ContextGroupIdentificationSequence = [context_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 3  # Missing Context Identifier, Mapping Resource, Context Group Version
        error_messages = ' '.join(result.errors)
        assert "Context Identifier (0008,010F) is required (Type 1)" in error_messages
        assert "Mapping Resource (0008,0105) is required (Type 1)" in error_messages
        assert "Context Group Version (0008,0106) is required (Type 1)" in error_messages
    
    def test_validate_contributing_equipment_sequence_valid_passes(self):
        """Test that valid Contributing Equipment Sequence passes validation."""
        purpose_item = Dataset()
        purpose_item.CodeValue = "109104"
        purpose_item.CodingSchemeDesignator = "DCM"
        purpose_item.CodeMeaning = "De-identification"
        
        contrib_item = Dataset()
        contrib_item.PurposeOfReferenceCodeSequence = [purpose_item]
        contrib_item.Manufacturer = "Test Manufacturer"
        contrib_item.ManufacturerModelName = "Test Model"
        contrib_item.SoftwareVersions = "1.0.0"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.ContributingEquipmentSequence = [contrib_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_contributing_equipment_sequence_missing_elements_fail(self):
        """Test that missing required elements in Contributing Equipment Sequence fail validation."""
        contrib_item = Dataset()
        # Missing both required elements
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.ContributingEquipmentSequence = [contrib_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        error_messages = ' '.join(result.errors)
        assert "Purpose of Reference Code Sequence (0040,A170) is required (Type 1)" in error_messages
        assert "Manufacturer (0008,0070) is required (Type 1)" in error_messages
    
    def test_validate_encrypted_attributes_sequence_valid_passes(self):
        """Test that valid Encrypted Attributes Sequence passes validation."""
        encrypted_item = Dataset()
        encrypted_item.EncryptedContentTransferSyntaxUID = "1.2.840.10008.1.2.1"
        encrypted_item.EncryptedContent = b"encrypted_data_here"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.EncryptedAttributesSequence = [encrypted_item]
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_encrypted_attributes_sequence_missing_elements_fail(self):
        """Test that missing required elements in Encrypted Attributes Sequence fail validation."""
        encrypted_item = Dataset()
        # Missing both required elements
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.EncryptedAttributesSequence = [encrypted_item]
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        error_messages = ' '.join(result.errors)
        assert "Encrypted Content Transfer Syntax UID (0400,0510) is required" in error_messages
        assert "Encrypted Content (0400,0520) is required" in error_messages
    
    def test_validate_private_data_element_characteristics_sequence_valid_passes(self):
        """Test that valid Private Data Element Characteristics Sequence passes validation."""
        definition_item = Dataset()
        definition_item.PrivateDataElementTag = "0x00111001"
        definition_item.PrivateDataElementValueRepresentation = "CS"
        definition_item.PrivateDataElementDescription = "Test Element"
        
        private_item = Dataset()
        private_item.PrivateGroupReference = "0x0011"
        private_item.PrivateCreatorReference = "TEST_CREATOR"
        private_item.BlockIdentifyingInformationStatus = "SAFE"
        private_item.PrivateDataElementDefinitionSequence = [definition_item]
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PrivateDataElementCharacteristicsSequence = [private_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_private_data_element_characteristics_missing_elements_fail(self):
        """Test that missing required elements in Private Data Element Characteristics fail validation."""
        private_item = Dataset()
        # Missing both required elements
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PrivateDataElementCharacteristicsSequence = [private_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        error_messages = ' '.join(result.errors)
        assert "Private Group Reference (0008,0301) is required (Type 1)" in error_messages
        assert "Private Creator Reference (0008,0302) is required (Type 1)" in error_messages
    
    def test_validate_private_data_element_mixed_status_conditional_fails(self):
        """Test that MIXED status without NonidentifyingPrivateElements fails validation."""
        private_item = Dataset()
        private_item.PrivateGroupReference = "0x0011"
        private_item.PrivateCreatorReference = "TEST_CREATOR"
        private_item.BlockIdentifyingInformationStatus = "MIXED"
        # Missing NonidentifyingPrivateElements - should fail
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PrivateDataElementCharacteristicsSequence = [private_item]
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Nonidentifying Private Elements (0008,0304) is required when" in result.errors[0]
        assert "Block Identifying Information Status is MIXED" in result.errors[0]
    
    def test_validate_private_data_element_sq_vr_conditional_fails(self):
        """Test that SQ VR without Number of Items fails validation."""
        definition_item = Dataset()
        definition_item.PrivateDataElementTag = "0x00111001"
        definition_item.PrivateDataElementValueRepresentation = "SQ"
        # Missing PrivateDataElementNumberOfItems for SQ VR - should fail
        
        private_item = Dataset()
        private_item.PrivateGroupReference = "0x0011"
        private_item.PrivateCreatorReference = "TEST_CREATOR"
        private_item.PrivateDataElementDefinitionSequence = [definition_item]
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PrivateDataElementCharacteristicsSequence = [private_item]
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Private Data Element Number of Items (0008,030B) is required" in result.errors[0]
        assert "when Value Representation is SQ" in result.errors[0]
    
    def test_validate_hl7_structured_document_reference_sequence_valid_passes(self):
        """Test that valid HL7 Structured Document Reference Sequence passes validation."""
        hl7_item = Dataset()
        hl7_item.HL7InstanceIdentifier = "test-document-123"
        hl7_item.HL7DocumentEffectiveTime = "20240101120000"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.HL7StructuredDocumentReferenceSequence = [hl7_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_hl7_structured_document_reference_missing_identifier_fails(self):
        """Test that missing HL7 Instance Identifier fails validation."""
        hl7_item = Dataset()
        # Missing HL7InstanceIdentifier
        hl7_item.HL7DocumentEffectiveTime = "20240101120000"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.HL7StructuredDocumentReferenceSequence = [hl7_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "HL7 Instance Identifier (0040,E001) is required (Type 1)" in result.errors[0]
    
    def test_validate_conversion_source_attributes_sequence_valid_passes(self):
        """Test that valid Conversion Source Attributes Sequence passes validation."""
        conversion_item = Dataset()
        conversion_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        conversion_item.ReferencedSOPInstanceUID = "*******.*******.9.10"
        conversion_item.StudyInstanceUID = "*******.*******.9.11"
        conversion_item.SeriesInstanceUID = "*******.*******.9.12"
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.ConversionSourceAttributesSequence = [conversion_item]
        
        config = ValidationConfig(validate_sequences=True, validate_conditional_requirements=False)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_conversion_source_attributes_missing_references_fail(self):
        """Test that missing SOP Instance References fail validation."""
        conversion_item = Dataset()
        # Missing both ReferencedSOPClassUID and ReferencedSOPInstanceUID
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.ConversionSourceAttributesSequence = [conversion_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        error_messages = ' '.join(result.errors)
        assert "Referenced SOP Class UID is required" in error_messages
        assert "Referenced SOP Instance UID is required" in error_messages
    
    # ValidationConfig Tests
    
    def test_validate_with_conditional_requirements_disabled(self):
        """Test validation with conditional requirements disabled."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PatientName = "Müller^Hans"  # Non-ASCII characters
        # No SpecificCharacterSet but conditional validation disabled
        
        config = ValidationConfig(validate_conditional_requirements=False)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0  # Should not validate conditional requirements
    
    def test_validate_with_enumerated_values_disabled(self):
        """Test validation with enumerated value checking disabled."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.SyntheticData = "INVALID"  # Invalid value but checking disabled
        
        config = ValidationConfig(check_enumerated_values=False)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should not validate enumerated values
        assert len(result.warnings) == 0
    
    def test_validate_with_sequences_disabled(self):
        """Test validation with sequence validation disabled."""
        coding_item = Dataset()
        # Missing required CodingSchemeDesignator but sequence validation disabled
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.CodingSchemeIdentificationSequence = [coding_item]
        
        config = ValidationConfig(validate_sequences=False)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should not validate sequences
        assert len(result.warnings) == 0
    
    def test_validate_with_default_config(self):
        """Test validation with default configuration."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        
        result = SOPCommonValidator.validate(dataset)  # No config provided
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_with_none_config(self):
        """Test validation with None configuration."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        
        result = SOPCommonValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Error Message Quality Tests
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include DICOM tag references."""
        dataset = Dataset()
        # Missing both required elements
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        
        # Check that all error messages contain DICOM tag references
        for error in result.errors:
            assert any(tag in error for tag in ["(0008,0016)", "(0008,0018)"]), f"Error missing DICOM tag: {error}"
    
    def test_error_messages_are_descriptive(self):
        """Test that error messages provide helpful context and guidance."""
        dataset = Dataset()
        dataset.SOPClassUID = ""  # Empty value
        dataset.SOPInstanceUID = ""  # Empty value
        
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 2
        
        # Check that error messages are descriptive
        error_messages = ' '.join(result.errors)
        assert "cannot be empty" in error_messages
        assert "Type 1" in error_messages
    
    def test_conditional_error_messages_explain_context(self):
        """Test that conditional error messages explain the triggering conditions."""
        private_item = Dataset()
        private_item.PrivateGroupReference = "0x0011"
        private_item.PrivateCreatorReference = "TEST_CREATOR"
        private_item.BlockIdentifyingInformationStatus = "MIXED"
        # Missing NonidentifyingPrivateElements
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PrivateDataElementCharacteristicsSequence = [private_item]
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        error_msg = result.errors[0]
        assert "when Block Identifying Information Status is MIXED" in error_msg  # Explains condition
        assert "per DICOM PS3.3 C.12.1" in error_msg  # References standard
    
    def test_sequence_error_messages_include_item_index(self):
        """Test that sequence error messages include item index for clarity."""
        coding_item1 = Dataset()
        coding_item1.CodingSchemeDesignator = "DCM"  # Valid
        
        coding_item2 = Dataset()
        # Missing CodingSchemeDesignator - should fail
        
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.CodingSchemeIdentificationSequence = [coding_item1, coding_item2]
        
        config = ValidationConfig(validate_sequences=True)
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "item 1" in result.errors[0]  # Should identify the problematic item
    
    # ValidationResult Structure Tests
    
    def test_validation_result_structure(self):
        """Test that ValidationResult is properly structured."""
        dataset = Dataset()
        result = SOPCommonValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validation_result_error_and_warning_content(self):
        """Test that ValidationResult contains appropriate error and warning content."""
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.PatientName = "Müller^Hans"  # Non-ASCII to trigger warning
        dataset.SyntheticData = "INVALID"  # Invalid enum to trigger error
        
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True
        )
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # No errors, enumerated values are warnings
        assert len(result.warnings) == 2  # Character set warning + enum warning
        
        # Check warning content
        assert any("Synthetic Data" in warning and "INVALID" in warning 
                  for warning in result.warnings)
        assert any("Specific Character Set" in warning and "non-ASCII characters" in warning 
                  for warning in result.warnings)
    
    # Comprehensive Integration Tests
    
    def test_validate_comprehensive_valid_sop_common_dataset(self):
        """Test validation of comprehensive valid SOP Common dataset with all optional elements."""
        # Create valid coding scheme item
        coding_item = Dataset()
        coding_item.CodingSchemeDesignator = "DCM"
        coding_item.CodingSchemeRegistry = "HL7"
        coding_item.CodingSchemeUID = "1.2.840.10008.2.16.4"
        
        # Create valid context group item
        context_item = Dataset()
        context_item.ContextIdentifier = "CID 3"
        context_item.MappingResource = "DCMR"
        context_item.ContextGroupVersion = "20121130"
        
        # Create comprehensive dataset
        dataset = Dataset()
        dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        dataset.SpecificCharacterSet = "ISO_IR 100"
        dataset.InstanceCreationDate = "20240101"
        dataset.InstanceCreationTime = "120000"
        dataset.InstanceCreatorUID = "*******.*******.9.10"
        dataset.InstanceNumber = 1
        dataset.SyntheticData = SyntheticData.NO.value
        dataset.SOPInstanceStatus = SOPInstanceStatus.AO.value
        dataset.ContentQualification = ContentQualification.PRODUCT.value
        dataset.CodingSchemeIdentificationSequence = [coding_item]
        dataset.ContextGroupIdentificationSequence = [context_item]
        
        # Validate with all options enabled
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )
        result = SOPCommonValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_dicom_standard_conformance(self):
        """Test validation against DICOM PS3.3 C.12.1 requirements."""
        # Test that SOP Common Module meets all DICOM standard requirements
        
        # 1. SOP Class UID is Type 1 (required)
        dataset_missing_class_uid = Dataset()
        dataset_missing_class_uid.SOPInstanceUID = "*******.*******.9"
        result = SOPCommonValidator.validate(dataset_missing_class_uid)
        assert len(result.errors) == 1
        assert "Type 1" in result.errors[0]
        
        # 2. SOP Instance UID is Type 1 (required)
        dataset_missing_instance_uid = Dataset()
        dataset_missing_instance_uid.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        result = SOPCommonValidator.validate(dataset_missing_instance_uid)
        assert len(result.errors) == 1
        assert "Type 1" in result.errors[0]
        
        # 3. Valid complete module
        valid_dataset = Dataset()
        valid_dataset.SOPClassUID = "1.2.840.10008.*******.1.481.3"
        valid_dataset.SOPInstanceUID = "1.2.840.10008.*******.*******.9"
        result = SOPCommonValidator.validate(valid_dataset)
        assert len(result.errors) == 0