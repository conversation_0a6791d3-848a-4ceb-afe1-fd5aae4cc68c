"""Test RTDoseValidator functionality.

Tests comprehensive validation of RT Dose Module according to DICOM PS3.3 C.8.8.3.
All tests use datasets generated from modules via to_dataset() method following
the new composition-based architecture patterns.
"""

from pydicom import Dataset
from pyrt_dicom.modules import RTDoseModule
from pyrt_dicom.validators.modules.rt_dose_validator import RTDoseValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums import (
    DoseUnits, DoseType, DoseSummationType, SpatialTransformOfDose, 
    TissueHeterogeneityCorrection
)


class TestRTDoseValidator:
    """Test RTDoseValidator comprehensive validation logic."""
    
    def test_validate_method_signature_with_dataset(self):
        """Test validator method signature and return type with Dataset."""
        # Create minimal valid dataset
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value

        # Test with default config
        result = RTDoseValidator.validate(dataset)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)

        # Test with custom config
        config = ValidationConfig()
        result = RTDoseValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)

    def test_validate_method_signature_with_module(self):
        """Test validator method signature and return type with BaseModule."""
        # Create minimal valid module
        rt_dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )

        # Test with default config
        result = RTDoseValidator.validate(rt_dose)
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)

        # Test with custom config
        config = ValidationConfig()
        result = RTDoseValidator.validate(rt_dose, config)
        assert isinstance(result, ValidationResult)
    
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements method with Dataset."""
        # Test missing required elements
        dataset = Dataset()
        result = RTDoseValidator.validate_required_elements(dataset)
        assert len(result.errors) == 3  # Missing all 3 required elements
        assert "Dose Units (3004,0002) is required" in result.errors[0]
        assert "Dose Type (3004,0004) is required" in result.errors[1]
        assert "Dose Summation Type (3004,000A) is required" in result.errors[2]

        # Test with all required elements present
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        result = RTDoseValidator.validate_required_elements(dataset)
        assert len(result.errors) == 0

    def test_validate_required_elements_with_module(self):
        """Test validate_required_elements method with BaseModule."""
        # Test with valid module
        rt_dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        result = RTDoseValidator.validate_required_elements(rt_dose)
        assert len(result.errors) == 0

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements method with Dataset."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value

        # Test pixel data conditional requirements
        dataset.PixelData = b'\x00\x01\x02\x03'  # Add pixel data
        result = RTDoseValidator.validate_conditional_requirements(dataset)
        assert len(result.errors) == 8  # Missing all pixel data related elements (7 pixel + 1 RT plan)

        # Test dose summation type conditional requirements
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        result = RTDoseValidator.validate_conditional_requirements(dataset)
        assert any("Referenced RT Plan Sequence" in error for error in result.errors)

    def test_validate_conditional_requirements_with_module(self):
        """Test validate_conditional_requirements method with BaseModule."""
        rt_dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_referenced_rt_plan(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.5",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        result = RTDoseValidator.validate_conditional_requirements(rt_dose)
        assert len(result.errors) == 0

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values method with Dataset."""
        dataset = Dataset()
        dataset.DoseUnits = "INVALID_UNIT"
        dataset.DoseType = "INVALID_TYPE"
        dataset.DoseSummationType = "INVALID_SUMMATION"

        result = RTDoseValidator.validate_enumerated_values(dataset)
        assert len(result.warnings) >= 3  # Invalid enum values should generate warnings

    def test_validate_enumerated_values_with_module(self):
        """Test validate_enumerated_values method with BaseModule."""
        rt_dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        result = RTDoseValidator.validate_enumerated_values(rt_dose)
        assert len(result.warnings) == 0  # Valid enum values

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures method with Dataset."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value

        # Add invalid sequence structure
        invalid_plan_item = Dataset()
        # Missing required SOP Class UID and SOP Instance UID
        dataset.ReferencedRTPlanSequence = [invalid_plan_item]

        result = RTDoseValidator.validate_sequence_structures(dataset)
        assert len(result.errors) == 2  # Missing both required UIDs

    def test_validate_sequence_structures_with_module(self):
        """Test validate_sequence_structures method with BaseModule."""
        rt_dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_referenced_rt_plan(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.5",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        result = RTDoseValidator.validate_sequence_structures(rt_dose)
        assert len(result.errors) == 0

    def test_missing_type1_elements_validation(self):
        """Test validation when Type 1 required elements are missing."""
        dataset = Dataset()
        # No required elements

        result = RTDoseValidator.validate(dataset)
        
        # Should have errors for missing Type 1 elements
        assert len(result.errors) >= 3
        assert any('Dose Units (3004,0002) is required for RT Dose Module (Type 1)' in error 
                  for error in result.errors)
        assert any('Dose Type (3004,0004) is required for RT Dose Module (Type 1)' in error 
                  for error in result.errors)
        assert any('Dose Summation Type (3004,000A) is required for RT Dose Module (Type 1)' in error 
                  for error in result.errors)
    
    def test_valid_minimal_rt_dose_validation(self):
        """Test validation of minimally valid RT Dose module."""
        # Create minimal valid RT Dose using module
        rt_dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        dataset = rt_dose.to_dataset()
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for missing Referenced RT Plan Sequence when dose summation type is PLAN
        assert len(result.errors) > 0
        assert any('Referenced RT Plan Sequence (300C,0002) is required when Dose Summation Type is PLAN' in error
                  for error in result.errors)
    
    def test_pixel_data_conditional_requirements_validation(self):
        """Test Type 1C validation when Pixel Data is present."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        
        # Add pixel data but missing required conditional elements
        dataset.PixelData = b'\x00' * 1000
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have errors for missing Type 1C pixel elements
        expected_missing_elements = [
            'Samples per Pixel (0028,0002)',
            'Photometric Interpretation (0028,0004)',
            'Bits Allocated (0028,0100)',
            'Bits Stored (0028,0101)',
            'High Bit (0028,0102)',
            'Pixel Representation (0028,0103)',
            'Dose Grid Scaling (3004,000E)'
        ]
        
        for element in expected_missing_elements:
            assert any(element in error and 'Type 1C' in error 
                      for error in result.errors), f'Missing error for {element}'
    
    def test_dose_summation_type_conditional_requirements(self):
        """Test conditional requirements based on Dose Summation Type."""
        # Test PLAN summation type requiring Referenced RT Plan Sequence
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        # Missing ReferencedRTPlanSequence
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for missing Referenced RT Plan Sequence
        assert any('Referenced RT Plan Sequence (300C,0002) is required when Dose Summation Type is PLAN' in error
                  for error in result.errors)
    
    def test_plan_overview_conditional_requirements(self):
        """Test conditional requirements for PLAN_OVERVIEW Dose Summation Type."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN_OVERVIEW.value
        # Missing PlanOverviewSequence
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for missing Plan Overview Sequence
        assert any('Plan Overview Sequence (300C,0116) is required when Dose Summation Type is PLAN_OVERVIEW' in error
                  for error in result.errors)
    
    def test_record_conditional_requirements(self):
        """Test conditional requirements for RECORD Dose Summation Type."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.RECORD.value
        # Missing ReferencedTreatmentRecordSequence
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for missing Referenced Treatment Record Sequence
        assert any('Referenced Treatment Record Sequence (3008,0030) is required when Dose Summation Type is RECORD' in error
                  for error in result.errors)
    
    def test_spatial_transform_conditional_requirements(self):
        """Test conditional requirements for Spatial Transform of Dose."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.SpatialTransformOfDose = SpatialTransformOfDose.RIGID.value
        # Missing ReferencedSpatialRegistrationSequence
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for missing Referenced Spatial Registration Sequence
        assert any('Referenced Spatial Registration Sequence (0070,0404) is required when Spatial Transform of Dose is RIGID' in error
                  for error in result.errors)
    
    def test_multi_frame_conditional_requirements(self):
        """Test multi-frame conditional requirements."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.PixelData = b'\x00' * 1000
        dataset.NumberOfFrames = 5  # Multi-frame
        dataset.FrameIncrementPointer = 0x3004000C  # Points to GridFrameOffsetVector
        # Missing GridFrameOffsetVector
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for missing required pixel elements when Pixel Data is present
        assert any('Samples per Pixel (0028,0002) is required when Pixel Data' in error
                  for error in result.errors)
    
    def test_enumerated_values_validation(self):
        """Test validation of enumerated values."""
        dataset = Dataset()
        dataset.DoseUnits = "INVALID_UNITS"  # Invalid value
        dataset.DoseType = "INVALID_TYPE"  # Invalid value
        dataset.DoseSummationType = "INVALID_SUMMATION"  # Invalid value
        dataset.SpatialTransformOfDose = "INVALID_TRANSFORM"  # Invalid value
        dataset.TissueHeterogeneityCorrection = "INVALID_CORRECTION"  # Invalid value
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have warnings for invalid enumerated values
        assert any('Dose Units' in warning and 'INVALID_UNITS' in warning for warning in result.warnings)
        assert any('Dose Type' in warning and 'INVALID_TYPE' in warning for warning in result.warnings)
        assert any('Dose Summation Type' in warning and 'INVALID_SUMMATION' in warning for warning in result.warnings)
        assert any('Spatial Transform of Dose' in warning and 'INVALID_TRANSFORM' in warning for warning in result.warnings)
        assert any('Tissue Heterogeneity Correction' in warning and 'INVALID_CORRECTION' in warning for warning in result.warnings)
    
    def test_photometric_interpretation_constraint(self):
        """Test RT Dose specific constraint for Photometric Interpretation."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.PhotometricInterpretation = "RGB"  # Invalid for RT Dose
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for non-MONOCHROME2 photometric interpretation
        assert any('Photometric Interpretation (0028,0004) must be MONOCHROME2 for RT Dose' in error
                  for error in result.errors)
    
    def test_samples_per_pixel_constraint(self):
        """Test RT Dose specific constraint for Samples per Pixel."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.SamplesPerPixel = 3  # Invalid for RT Dose
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for samples per pixel != 1
        assert any('Samples per Pixel (0028,0002) must be 1 for RT Dose' in error
                  for error in result.errors)
    
    def test_sequence_structure_validation(self):
        """Test validation of sequence structure requirements."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        
        # Create RT Plan sequence with missing required elements
        rt_plan_item = Dataset()
        # Missing ReferencedSOPClassUID and ReferencedSOPInstanceUID
        dataset.ReferencedRTPlanSequence = [rt_plan_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have errors for missing sequence elements
        assert any('Referenced SOP Class UID (0008,1150) is required' in error
                  for error in result.errors)
        assert any('Referenced SOP Instance UID (0008,1155) is required' in error
                  for error in result.errors)
    
    def test_pixel_data_consistency_validation(self):
        """Test pixel data consistency validation."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.PixelData = b'\x00' * 1000
        
        # Invalid bits allocation for RT Dose
        dataset.BitsAllocated = 8  # Must be 16 or 32
        dataset.BitsStored = 16  # Must equal BitsAllocated 
        dataset.HighBit = 14  # Must be BitsStored - 1
        dataset.PixelRepresentation = 0
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have errors for pixel data inconsistencies
        assert any('Bits Allocated (0028,0100) must be 16 or 32 for RT Dose' in error
                  for error in result.errors)
        assert any('Bits Stored (0028,0101) must equal Bits Allocated for RT Dose' in error
                  for error in result.errors)
    
    def test_pixel_representation_dose_type_validation(self):
        """Test pixel representation validation based on dose type."""
        # Test ERROR dose type requiring signed pixels
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.ERROR.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.PixelData = b'\x00' * 1000
        dataset.PixelRepresentation = 0  # Should be 1 for ERROR dose type
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for unsigned pixel representation with ERROR dose type
        assert any('Pixel Representation (0028,0103) must be 1 (two\'s complement) when Dose Type is ERROR' in error
                  for error in result.errors)
        
        # Test non-ERROR dose type requiring unsigned pixels
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.PixelRepresentation = 1  # Should be 0 for non-ERROR dose type
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for signed pixel representation with non-ERROR dose type
        assert any('Pixel Representation (0028,0103) must be 0 (unsigned) when Dose Type is not ERROR' in error
                  for error in result.errors)
    
    def test_dose_grid_scaling_validation(self):
        """Test dose grid scaling validation."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.PixelData = b'\x00' * 1000
        dataset.DoseGridScaling = -1.0  # Invalid negative value
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for missing required pixel elements (negative value is allowed by pydicom)
        assert any('Samples per Pixel (0028,0002) is required when Pixel Data' in error
                  for error in result.errors)
    
    def test_normalization_point_validation(self):
        """Test normalization point coordinate validation."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.NormalizationPoint = [100.0, 50.0]  # Missing z coordinate
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for invalid normalization point coordinates
        assert any('Normalization Point (3004,0008) must contain exactly 3 coordinates' in error
                  for error in result.errors)
    
    def test_multi_plan_cross_field_dependencies(self):
        """Test cross-field dependencies for MULTI_PLAN summation type."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.MULTI_PLAN.value
        
        # Create only one RT Plan (should require 2 or more for MULTI_PLAN)
        rt_plan_item = Dataset()
        rt_plan_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.481.5"
        rt_plan_item.ReferencedSOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        dataset.ReferencedRTPlanSequence = [rt_plan_item]
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for insufficient number of plans for MULTI_PLAN
        assert any('Referenced RT Plan Sequence (300C,0002) must contain 2 or more items when Dose Summation Type is MULTI_PLAN' in error
                  for error in result.errors)
    
    def test_plan_overview_index_uniqueness(self):
        """Test Plan Overview Index uniqueness validation."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN_OVERVIEW.value
        
        # Create plan overview sequence with duplicate indices
        plan_overview_1 = Dataset()
        plan_overview_1.PlanOverviewIndex = 1
        plan_overview_1.RTPlanLabel = "Plan A"
        
        plan_overview_2 = Dataset()
        plan_overview_2.PlanOverviewIndex = 1  # Duplicate index
        plan_overview_2.RTPlanLabel = "Plan B"
        
        dataset.PlanOverviewSequence = [plan_overview_1, plan_overview_2]
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for duplicate Plan Overview Index
        assert any('Plan Overview Index (1) must be unique within the sequence' in error
                  for error in result.errors)
    
    def test_grid_frame_offset_vector_consistency(self):
        """Test Grid Frame Offset Vector consistency validation."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.NumberOfFrames = 5
        dataset.GridFrameOffsetVector = [0.0, 5.0, 10.0]  # Wrong length
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have warning for inconsistent grid frame offset vector length
        assert any('Grid Frame Offset Vector should contain 5 values' in warning
                  for warning in result.warnings)
        
        # Test non-monotonic values
        dataset.GridFrameOffsetVector = [0.0, 10.0, 5.0, 15.0, 20.0]  # Non-monotonic
        result = RTDoseValidator.validate(dataset)
        
        assert any('Grid Frame Offset Vector values should vary monotonically' in warning
                  for warning in result.warnings)
    
    def test_derivation_code_sequence_validation(self):
        """Test Derivation Code Sequence consistency validation."""
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value  # Should be EFFECTIVE for radiobiological effects
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        
        # Add derivation code for radiobiological effects
        derivation_item = Dataset()
        derivation_item.CodeValue = "121377"  # "Composed with radiobiological effects"
        dataset.DerivationCodeSequence = [derivation_item]
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have error for wrong dose type with radiobiological effects
        assert any('Dose Type (3004,0004) must be EFFECTIVE when Derivation Code Sequence contains' in error
                  for error in result.errors)
    
    def test_content_date_time_consistency(self):
        """Test Content Date/Time consistency validation."""
        # Test Content Date without Content Time
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.ContentDate = "20240101"
        # Missing ContentTime
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have warning about missing Content Time
        assert any('Content Time (0008,0033) should be provided when Content Date' in warning
                  for warning in result.warnings)
        
        # Test Content Time without Content Date
        dataset = Dataset()
        dataset.DoseUnits = DoseUnits.GY.value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value
        dataset.ContentTime = "123045"
        # Missing ContentDate
        
        result = RTDoseValidator.validate(dataset)
        
        # Should have warning about missing Content Date
        assert any('Content Date (0008,0023) should be provided when Content Time' in warning
                  for warning in result.warnings)
    
    def test_configuration_options(self):
        """Test different validation configuration options."""
        # Create dataset with various validation issues
        dataset = Dataset()
        dataset.DoseUnits = "INVALID_UNITS"  # Invalid enumerated value
        dataset.DoseType = DoseType.PHYSICAL.value
        dataset.DoseSummationType = DoseSummationType.PLAN.value  # Will trigger conditional requirement
        dataset.SpatialTransformOfDose = SpatialTransformOfDose.RIGID.value  # Will trigger conditional requirement
        
        # Create sequence with missing elements
        rt_plan_item = Dataset()
        # Missing required elements
        dataset.ReferencedRTPlanSequence = [rt_plan_item]
        
        # Test with enumerated values disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result_no_enum = RTDoseValidator.validate(dataset, config_no_enum)
        
        # Should not have enumerated value warnings
        assert not any('INVALID_UNITS' in warning for warning in result_no_enum.warnings)
        
        # Test with conditional requirements disabled
        config_no_conditional = ValidationConfig(validate_conditional_requirements=False)
        result_no_conditional = RTDoseValidator.validate(dataset, config_no_conditional)
        
        # Should not have conditional errors
        assert not any('Referenced Spatial Registration Sequence' in error for error in result_no_conditional.errors)
        
        # Test with sequence validation disabled
        config_no_sequences = ValidationConfig(validate_sequences=False)
        result_no_sequences = RTDoseValidator.validate(dataset, config_no_sequences)
        
        # Should not have sequence structure errors
        assert not any('Referenced SOP Class UID' in error for error in result_no_sequences.errors)
        
        # Test with all validations enabled
        config_all = ValidationConfig(
            check_enumerated_values=True,
            validate_conditional_requirements=True,
            validate_sequences=True
        )
        result_all = RTDoseValidator.validate(dataset, config_all)
        
        # Should have enumerated, conditional, and sequence issues
        assert any('INVALID_UNITS' in warning for warning in result_all.warnings)
        assert any('Referenced Spatial Registration Sequence' in error for error in result_all.errors)
        assert any('Referenced SOP Class UID' in error for error in result_all.errors)
    
    def test_comprehensive_validation_with_rt_dose_module(self):
        """Test comprehensive validation using RTDoseModule with all features."""
        # Create comprehensive RT Dose with pixel data
        rt_dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_optional_elements(
            spatial_transform_of_dose=SpatialTransformOfDose.NONE,
            tissue_heterogeneity_correction=TissueHeterogeneityCorrection.IMAGE,
            normalization_point=[100.0, 50.0, 25.0],
            dose_comment="Comprehensive test dose"
        )
        
        dataset = rt_dose.to_dataset()
        
        # Test with all validation options enabled
        config = ValidationConfig(
            check_enumerated_values=True,
            validate_conditional_requirements=True,
            validate_sequences=True
        )
        result = RTDoseValidator.validate(dataset, config)
        
        # Should have error for missing Referenced RT Plan Sequence since dose summation type is PLAN
        assert len(result.errors) > 0
        assert any('Referenced RT Plan Sequence (300C,0002) is required when Dose Summation Type is PLAN' in error
                  for error in result.errors)