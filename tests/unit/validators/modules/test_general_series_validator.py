"""Tests for General Series Module Validator - DICOM PS3.3 C.7.3.1

This module tests comprehensive validation of the General Series Module
according to DICOM PS3.3 Section C.7.3.1, ensuring compliance with
Type 1, Type 2, Type 2C, Type 1C, and Type 3 requirements.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.general_series_validator import GeneralSeriesValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult


class TestGeneralSeriesValidator:
    """Test class for GeneralSeriesValidator following pytest framework requirements."""
    
    def test_validate_returns_validation_result(self):
        """Test that validator returns proper ValidationResult instance."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.10"
        
        result = GeneralSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_minimal_valid_dataset_passes(self):
        """Test that dataset with minimal required elements passes validation."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.10"
        dataset.SeriesNumber = 1  # Type 2
        
        result = GeneralSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_missing_modality_fails(self):
        """Test that missing modality (Type 1) fails validation."""
        dataset = Dataset()
        dataset.SeriesInstanceUID = "*******.*******.9.10"
        dataset.SeriesNumber = 1
        
        result = GeneralSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Modality (0008,0060) is required (Type 1)" in result.errors[0]
        assert "DICOM PS3.3 C.7.3.1.1.1" in result.errors[0]
    
    def test_validate_missing_series_instance_uid_fails(self):
        """Test that missing Series Instance UID (Type 1) fails validation."""
        dataset = Dataset()
        dataset.Modality = "MR"
        dataset.SeriesNumber = 1
        
        result = GeneralSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Series Instance UID (0020,000E) is required (Type 1)" in result.errors[0]
        assert "DICOM UID construction rules" in result.errors[0]
    
    def test_validate_missing_series_number_warns(self):
        """Test that missing Series Number (Type 2) generates warning."""
        dataset = Dataset()
        dataset.Modality = "US"
        dataset.SeriesInstanceUID = "*******.*******.9.11"
        # No SeriesNumber
        
        result = GeneralSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Series Number (0020,0011) should be present (Type 2)" in result.warnings[0]
    
    def test_validate_empty_series_number_passes(self):
        """Test that empty Series Number (Type 2) passes validation."""
        dataset = Dataset()
        dataset.Modality = "CR"
        dataset.SeriesInstanceUID = "*******.*******.9.12"
        dataset.SeriesNumber = None  # Type 2 can be empty
        
        result = GeneralSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_invalid_modality_warns(self):
        """Test that invalid modality generates warning."""
        dataset = Dataset()
        dataset.Modality = "INVALID_MODALITY"
        dataset.SeriesInstanceUID = "*******.*******.9.13"
        dataset.SeriesNumber = 1
        
        config = ValidationConfig()
        config.check_enumerated_values = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) >= 1
        assert any("INVALID_MODALITY" in warning for warning in result.warnings)
        assert any("not a recognized DICOM defined term" in warning for warning in result.warnings)
    
    def test_validate_valid_modalities_pass(self):
        """Test that valid modalities pass enumeration validation."""
        valid_modalities = ["CT", "MR", "RTDOSE", "RTSTRUCT", "RTPLAN", "US", "XA", "CR"]
        
        for modality in valid_modalities:
            dataset = Dataset()
            dataset.Modality = modality
            dataset.SeriesInstanceUID = f"*******.*******.9.{hash(modality) % 1000}"
            dataset.SeriesNumber = 1
            
            config = ValidationConfig()
            config.check_enumerated_values = True
            
            result = GeneralSeriesValidator.validate(dataset, config)
            
            # Should not have warnings about invalid modality
            modality_warnings = [w for w in result.warnings if "not a recognized DICOM defined term" in w]
            assert len(modality_warnings) == 0, f"Valid modality {modality} should not generate warnings"
    
    def test_validate_laterality_for_paired_body_parts(self):
        """Test laterality validation for paired body parts."""
        paired_body_parts = ["BREAST", "KIDNEY", "LUNG", "EYE", "HAND", "FOOT"]
        
        for body_part in paired_body_parts:
            dataset = Dataset()
            dataset.Modality = "MG"
            dataset.SeriesInstanceUID = f"*******.*******.9.{hash(body_part) % 1000}"
            dataset.SeriesNumber = 1
            dataset.BodyPartExamined = body_part
            # No Laterality
            
            config = ValidationConfig()
            config.validate_conditional_requirements = True
            
            result = GeneralSeriesValidator.validate(dataset, config)
            
            assert isinstance(result, ValidationResult)
            assert len(result.warnings) >= 1
            laterality_warnings = [w for w in result.warnings if "Laterality (0020,0060) may be required" in w]
            assert len(laterality_warnings) >= 1, f"Body part {body_part} should trigger laterality warning"
            assert body_part in laterality_warnings[0]
    
    def test_validate_laterality_with_paired_body_parts_passes(self):
        """Test that providing laterality for paired body parts passes validation."""
        dataset = Dataset()
        dataset.Modality = "MG"
        dataset.SeriesInstanceUID = "*******.*******.9.20"
        dataset.SeriesNumber = 1
        dataset.BodyPartExamined = "BREAST"
        dataset.Laterality = "L"
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        config.check_enumerated_values = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have laterality warnings
        laterality_warnings = [w for w in result.warnings if "Laterality" in w and "may be required" in w]
        assert len(laterality_warnings) == 0
    
    def test_validate_patient_position_for_ct_sop_class(self):
        """Test patient position validation for CT SOP classes."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.21"
        dataset.SeriesNumber = 1
        dataset.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        # No PatientPosition
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        position_errors = [e for e in result.errors if "Patient Position (0018,5100) is required" in e]
        assert len(position_errors) >= 1
        assert "Type 2C" in position_errors[0]
    
    def test_validate_patient_position_with_ct_passes(self):
        """Test that providing patient position for CT passes validation."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.22"
        dataset.SeriesNumber = 1
        dataset.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        dataset.PatientPosition = "HFS"
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        config.check_enumerated_values = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have patient position errors
        position_errors = [e for e in result.errors if "Patient Position" in e and "required" in e]
        assert len(position_errors) == 0
    
    def test_validate_anatomical_orientation_for_non_human(self):
        """Test anatomical orientation validation for non-human organisms."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.23"
        dataset.SeriesNumber = 1
        dataset.PatientSpeciesDescription = "Canine"  # Indicates non-human
        # No AnatomicalOrientationType
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) >= 1
        orientation_warnings = [w for w in result.warnings if "Anatomical Orientation Type" in w]
        assert len(orientation_warnings) >= 1
        assert "Type 1C" in orientation_warnings[0]
    
    def test_validate_request_attributes_sequence_valid(self):
        """Test valid Request Attributes Sequence validation."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.24"
        dataset.SeriesNumber = 1
        
        # Create valid request attributes sequence
        request_item = Dataset()
        request_item.ScheduledProcedureStepID = "SPS001"
        request_item.ScheduledProcedureStepDescription = "Chest CT"
        request_item.RequestedProcedureID = "RP001"
        request_item.RequestedProcedureDescription = "Chest CT with contrast"
        dataset.RequestAttributesSequence = [request_item]
        
        config = ValidationConfig()
        config.validate_sequences = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have errors about request attributes
        request_errors = [e for e in result.errors if "Request Attributes Sequence" in e]
        assert len(request_errors) == 0
    
    def test_validate_request_attributes_sequence_missing_id_fails(self):
        """Test Request Attributes Sequence validation with missing required ID."""
        dataset = Dataset()
        dataset.Modality = "MR"
        dataset.SeriesInstanceUID = "*******.*******.9.25"
        dataset.SeriesNumber = 1
        
        # Create invalid request attributes sequence (missing required ID)
        request_item = Dataset()
        request_item.ScheduledProcedureStepDescription = "MR Brain"
        # Missing ScheduledProcedureStepID
        dataset.RequestAttributesSequence = [request_item]
        
        config = ValidationConfig()
        config.validate_sequences = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        request_errors = [e for e in result.errors if "Scheduled Procedure Step ID (0040,0009) is required" in e]
        assert len(request_errors) >= 1
    
    def test_validate_treatment_session_uid_valid(self):
        """Test valid Treatment Session UID validation."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.*******.9.26"
        dataset.SeriesNumber = 1
        dataset.TreatmentSessionUID = "*******.*******.9.100"
        
        config = ValidationConfig()
        config.validate_cross_field_dependencies = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have errors about treatment session UID
        uid_errors = [e for e in result.errors if "Treatment Session UID" in e]
        assert len(uid_errors) == 0
    
    def test_validate_treatment_session_uid_invalid_format_fails(self):
        """Test Treatment Session UID validation with invalid format."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.*******.9.27"
        dataset.SeriesNumber = 1
        dataset.TreatmentSessionUID = "invalid.uid.format"
        
        config = ValidationConfig()
        config.validate_cross_field_dependencies = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        uid_errors = [e for e in result.errors if "Treatment Session UID (300A,0700)" in e and "invalid characters" in e]
        assert len(uid_errors) >= 1
    
    def test_validate_treatment_session_uid_non_rt_modality_warns(self):
        """Test Treatment Session UID with non-RT modality generates warning."""
        dataset = Dataset()
        dataset.Modality = "CT"  # Non-RT modality
        dataset.SeriesInstanceUID = "*******.*******.9.28"
        dataset.SeriesNumber = 1
        dataset.TreatmentSessionUID = "*******.*******.9.101"
        
        config = ValidationConfig()
        config.validate_cross_field_dependencies = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) >= 1
        uid_warnings = [w for w in result.warnings if "Treatment Session UID" in w and "typically used with RT modalities" in w]
        assert len(uid_warnings) >= 1
    
    def test_validate_referenced_pps_sequence_multiple_items_fails(self):
        """Test Referenced Performed Procedure Step Sequence with multiple items fails."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.29"
        dataset.SeriesNumber = 1
        
        # Create multiple items (should only allow one)
        pps_item1 = Dataset()
        pps_item1.ReferencedSOPClassUID = "1.2.840.10008.*******.1"
        pps_item1.ReferencedSOPInstanceUID = "*******.*******.9.200"
        
        pps_item2 = Dataset()
        pps_item2.ReferencedSOPClassUID = "1.2.840.10008.*******.1"
        pps_item2.ReferencedSOPInstanceUID = "*******.*******.9.201"
        
        dataset.ReferencedPerformedProcedureStepSequence = [pps_item1, pps_item2]
        
        config = ValidationConfig()
        config.validate_sequences = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        pps_errors = [e for e in result.errors if "Only a single Item is permitted" in e and "0008,1111" in e]
        assert len(pps_errors) >= 1
    
    def test_validate_pixel_value_consistency_invalid_range_fails(self):
        """Test pixel value consistency validation with invalid range."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.30"
        dataset.SeriesNumber = 1
        dataset.SmallestPixelValueInSeries = 100
        dataset.LargestPixelValueInSeries = 50  # Smaller than smallest - invalid
        
        config = ValidationConfig()
        config.validate_cross_field_dependencies = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        pixel_errors = [e for e in result.errors if "Smallest Pixel Value" in e and "cannot be greater than" in e]
        assert len(pixel_errors) >= 1
    
    def test_validate_with_all_config_options_disabled(self):
        """Test validation with all optional config options disabled."""
        dataset = Dataset()
        dataset.Modality = "INVALID_MODALITY"  # This would normally warn
        dataset.SeriesInstanceUID = "*******.*******.9.31"
        # Missing SeriesNumber - would normally warn
        
        config = ValidationConfig()
        config.validate_conditional_requirements = False
        config.check_enumerated_values = False
        config.validate_sequences = False
        config.validate_cross_field_dependencies = False
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should only have basic Type 1 validation, no warnings about enums or conditional requirements
        assert len(result.errors) == 0  # No Type 1 missing
        # Should have at most the Type 2 warning about missing SeriesNumber
        assert len(result.warnings) <= 1
    
    def test_validate_complete_valid_dataset_passes(self):
        """Test comprehensive validation with complete valid dataset."""
        dataset = Dataset()
        
        # Type 1 elements
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.100"
        
        # Type 2 elements
        dataset.SeriesNumber = 1
        
        # Type 3 elements
        dataset.SeriesDescription = "Chest CT with contrast"
        dataset.SeriesDate = "20240315"
        dataset.SeriesTime = "143022.500000"
        dataset.ProtocolName = "CHEST_CT_PROTOCOL"
        dataset.BodyPartExamined = "CHEST"
        dataset.PatientPosition = "HFS"
        dataset.OperatorsName = "Tech^John"
        dataset.PerformingPhysicianName = "Dr^Smith"
        
        # Valid Treatment Session UID for CT (will warn but not error)
        dataset.TreatmentSessionUID = "*******.*******.9.200"
        
        # Valid Request Attributes Sequence
        request_item = Dataset()
        request_item.ScheduledProcedureStepID = "SPS001"
        request_item.ScheduledProcedureStepDescription = "Comprehensive CT scan"
        dataset.RequestAttributesSequence = [request_item]
        
        # Enable all validation
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        config.check_enumerated_values = True
        config.validate_sequences = True
        config.validate_cross_field_dependencies = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        
        # May have warnings about Treatment Session UID with non-RT modality
        if len(result.warnings) > 0:
            warning_messages = " ".join(result.warnings)
            assert "typically used with RT modalities" in warning_messages

    def test_validate_patient_position_type_2c_missing_for_ct(self):
        """Test Patient Position Type 2C validation for CT SOP class without position."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.100"
        dataset.SeriesNumber = 1
        dataset.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        # Missing PatientPosition and PatientOrientationCodeSequence
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        position_errors = [e for e in result.errors if "Patient Position (0018,5100) is required (Type 2C)" in e]
        assert len(position_errors) >= 1
        assert "1.2.840.10008.*******.1.2" in position_errors[0]

    def test_validate_patient_position_type_2c_with_orientation_sequence_passes(self):
        """Test Patient Position Type 2C validation passes when PatientOrientationCodeSequence is present."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.101"
        dataset.SeriesNumber = 1
        dataset.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT Image Storage
        dataset.PatientOrientationCodeSequence = []  # Present - should bypass requirement
        # No PatientPosition needed when PatientOrientationCodeSequence is present
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have patient position errors
        position_errors = [e for e in result.errors if "Patient Position" in e and "required" in e]
        assert len(position_errors) == 0

    def test_validate_laterality_type_2c_missing_for_breast(self):
        """Test Laterality Type 2C validation for paired body part without laterality."""
        dataset = Dataset()
        dataset.Modality = "MG"
        dataset.SeriesInstanceUID = "*******.*******.9.102"
        dataset.SeriesNumber = 1
        dataset.BodyPartExamined = "BREAST"  # Paired body part
        # Missing Laterality and no other laterality attributes
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        laterality_errors = [e for e in result.errors if "Laterality (0020,0060) is required (Type 2C)" in e]
        assert len(laterality_errors) >= 1
        assert "BREAST" in laterality_errors[0]

    def test_validate_laterality_type_2c_with_image_laterality_passes(self):
        """Test Laterality Type 2C validation passes when ImageLaterality is present."""
        dataset = Dataset()
        dataset.Modality = "MG"
        dataset.SeriesInstanceUID = "*******.*******.9.103"
        dataset.SeriesNumber = 1
        dataset.BodyPartExamined = "BREAST"  # Paired body part
        dataset.ImageLaterality = "L"  # Present - should bypass Laterality requirement
        # No Laterality needed when ImageLaterality is present
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have laterality errors
        laterality_errors = [e for e in result.errors if "Laterality" in e and "required" in e]
        assert len(laterality_errors) == 0

    def test_validate_laterality_type_2c_non_paired_body_part_passes(self):
        """Test Laterality Type 2C validation passes for non-paired body parts."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.104"
        dataset.SeriesNumber = 1
        dataset.BodyPartExamined = "CHEST"  # Non-paired body part
        # No Laterality needed for non-paired body parts
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have laterality errors
        laterality_errors = [e for e in result.errors if "Laterality" in e and "required" in e]
        assert len(laterality_errors) == 0

    def test_validate_anatomical_orientation_type_1c_missing_for_non_human(self):
        """Test Anatomical Orientation Type 1C validation for non-human without orientation."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.105"
        dataset.SeriesNumber = 1
        dataset.PatientSpeciesDescription = "Canine"  # Indicates non-human
        # Missing AnatomicalOrientationType
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        orientation_errors = [e for e in result.errors if "Anatomical Orientation Type (0010,2210) is required (Type 1C)" in e]
        assert len(orientation_errors) >= 1

    def test_validate_anatomical_orientation_type_1c_human_passes(self):
        """Test Anatomical Orientation Type 1C validation passes for human patients."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.106"
        dataset.SeriesNumber = 1
        # No PatientSpeciesDescription - assumes human
        # No AnatomicalOrientationType needed for humans
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        # Should not have orientation errors
        orientation_errors = [e for e in result.errors if "Anatomical Orientation Type" in e and "required" in e]
        assert len(orientation_errors) == 0

    def test_validate_module_conditional_requirements_comprehensive(self):
        """Test comprehensive validation of all module conditional requirements."""
        dataset = Dataset()
        dataset.Modality = "CT"
        dataset.SeriesInstanceUID = "*******.*******.9.107"
        dataset.SeriesNumber = 1
        
        # Set up conditions that should trigger all conditional requirement errors
        dataset.SOPClassUID = "1.2.840.10008.*******.1.2"  # CT - requires PatientPosition
        dataset.BodyPartExamined = "BREAST"  # Paired - requires Laterality
        dataset.PatientSpeciesDescription = "Feline"  # Non-human - requires AnatomicalOrientationType
        
        # Missing all required conditional elements
        # - PatientPosition (and no PatientOrientationCodeSequence)
        # - Laterality (and no Image/Frame/MeasurementLaterality)
        # - AnatomicalOrientationType
        
        config = ValidationConfig()
        config.validate_conditional_requirements = True
        
        result = GeneralSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert result.error_count >= 3  # Should have at least 3 errors
        
        error_messages = " ".join(result.errors)
        assert "Patient Position (0018,5100) is required (Type 2C)" in error_messages
        assert "Laterality (0020,0060) is required (Type 2C)" in error_messages
        assert "Anatomical Orientation Type (0010,2210) is required (Type 1C)" in error_messages