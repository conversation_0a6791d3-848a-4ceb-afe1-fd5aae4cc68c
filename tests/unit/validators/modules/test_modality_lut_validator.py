"""
Test ModalityLutValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.11.1 Modality LUT Module validation.
"""

import pytest
from pydicom import Dataset
from pydicom.dataelem import DataElement
from pyrt_dicom.validators.modules.modality_lut_validator import ModalityLutValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.image_enums import ModalityLutType, RescaleType


class TestModalityLutValidator:
    """Test ModalityLutValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = ModalityLutValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_fails_validation(self):
        """Test that empty dataset fails validation (missing transformation method)."""
        dataset = Dataset()
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Either Modality LUT Sequence (0028,3000) or Rescale Intercept (0028,1052) shall be present" in result.errors[0]
        assert "DICOM PS3.3 C.11.1 violation" in result.errors[0]
    
    def test_valid_rescale_parameters_pass_validation(self):
        """Test that valid rescale parameters pass validation."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0"
        dataset.RescaleType = RescaleType.HU.value
        
        result = ModalityLutValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_incomplete_rescale_parameters_fail_validation(self):
        """Test that incomplete rescale parameters fail validation."""
        # Missing RescaleSlope and RescaleType
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert "Missing required rescale fields" in result.errors[0]
        assert "RescaleSlope (0028,1053)" in result.errors[0]
        assert "RescaleType (0028,1054)" in result.errors[0]
    
    def test_both_lut_sequence_and_rescale_parameters_fail_validation(self):
        """Test that having both LUT sequence and rescale parameters fails validation."""
        dataset = Dataset()
        
        # Add rescale parameters
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0"
        dataset.RescaleType = RescaleType.HU.value
        
        # Add LUT sequence
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert "shall not both be present" in result.errors[0]
        assert "DICOM PS3.3 C.11.1 violation" in result.errors[0]
    
    def test_valid_modality_lut_sequence_passes_validation(self):
        """Test that valid modality LUT sequence passes validation."""
        dataset = Dataset()
        
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_modality_lut_sequence_multiple_items_fails_validation(self):
        """Test that modality LUT sequence with multiple items fails validation."""
        dataset = Dataset()
        
        lut_item1 = Dataset()
        lut_item1.LUTDescriptor = [256, 0, 8]
        lut_item1.ModalityLUTType = ModalityLutType.HU.value
        lut_item1.LUTData = [i for i in range(256)]
        
        lut_item2 = Dataset()
        lut_item2.LUTDescriptor = [256, 0, 8]
        lut_item2.ModalityLUTType = ModalityLutType.HU.value
        lut_item2.LUTData = [i for i in range(256)]
        
        dataset.ModalityLUTSequence = [lut_item1, lut_item2]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert "must contain exactly one item" in result.errors[0]
        assert "found 2 items" in result.errors[0]
    
    def test_modality_lut_sequence_missing_required_fields_fails_validation(self):
        """Test that modality LUT sequence missing required fields fails validation."""
        dataset = Dataset()
        
        # Missing ModalityLUTType and LUTData
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have errors for missing ModalityLUTType and LUTData
        error_messages = ' '.join(result.errors)
        assert "ModalityLUTType (0028,3004)" in error_messages
        assert "LUTData (0028,3006)" in error_messages
    
    def test_invalid_lut_descriptor_fails_validation(self):
        """Test that invalid LUT descriptor fails validation."""
        dataset = Dataset()
        
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0]  # Only 2 values instead of 3
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert "must contain exactly 3 values" in result.errors[0]
        assert "LUT Descriptor (0028,3002)" in result.errors[0]
    
    def test_invalid_bits_per_entry_fails_validation(self):
        """Test that invalid bits per entry in LUT descriptor fails validation."""
        dataset = Dataset()
        
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 12]  # Invalid bits per entry
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert "bits per entry) must be 8 or 16" in result.errors[0]
        assert "found 12" in result.errors[0]
    
    def test_missing_rescale_slope_fails_validation(self):
        """Test that missing rescale slope fails validation when intercept is present."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        # Missing RescaleSlope and RescaleType

        result = ModalityLutValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert "Missing required rescale fields" in result.errors[0]
        assert "RescaleSlope (0028,1053)" in result.errors[0]
    
    def test_zero_rescale_slope_generates_warning(self):
        """Test that zero rescale slope generates a warning."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "0"  # Zero slope
        dataset.RescaleType = RescaleType.HU.value
        
        result = ModalityLutValidator.validate(dataset)
        
        assert result.is_valid  # Valid but with warning
        assert not result.has_errors
        assert result.has_warnings
        assert "division by zero" in result.warnings[0]
    
    def test_missing_rescale_type_fails_validation(self):
        """Test that missing rescale type fails validation when intercept is present."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0"
        # Missing RescaleType

        result = ModalityLutValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert "Missing required rescale fields" in result.errors[0]
        assert "RescaleType (0028,1054)" in result.errors[0]
    
    def test_non_standard_rescale_type_generates_warning(self):
        """Test that non-standard rescale type generates a warning."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0"
        dataset.RescaleType = "CUSTOM_TYPE"  # Non-standard type
        
        result = ModalityLutValidator.validate(dataset)
        
        assert result.is_valid  # Valid but with warning
        assert not result.has_errors
        assert result.has_warnings
        assert "not a DICOM defined term" in result.warnings[0]
        assert "Rescale Type (0028,1054)" in result.warnings[0]
    
    def test_non_standard_modality_lut_type_generates_warning(self):
        """Test that non-standard modality LUT type generates a warning."""
        dataset = Dataset()
        
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = "CUSTOM_TYPE"  # Non-standard type
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert result.is_valid  # Valid but with warning
        assert not result.has_errors
        assert result.has_warnings
        assert "not a DICOM defined term" in result.warnings[0]
        assert "Modality LUT Type (0028,3004)" in result.warnings[0]
    
    def test_validation_config_affects_enumerated_value_checking(self):
        """Test that ValidationConfig controls enumerated value checking."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0"
        dataset.RescaleType = "CUSTOM_TYPE"  # Non-standard type
        
        # With enumerated value checking enabled (default)
        config_enabled = ValidationConfig(check_enumerated_values=True)
        result_enabled = ModalityLutValidator.validate(dataset, config_enabled)
        
        # With enumerated value checking disabled
        config_disabled = ValidationConfig(check_enumerated_values=False)
        result_disabled = ModalityLutValidator.validate(dataset, config_disabled)
        
        assert result_enabled.has_warnings
        assert not result_disabled.has_warnings
    
    def test_empty_lut_data_fails_validation(self):
        """Test that empty LUT data fails validation."""
        dataset = Dataset()
        
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = []  # Empty LUT data
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert "cannot be empty" in result.errors[0]
        assert "LUT Data (0028,3006)" in result.errors[0]
    
    def test_lut_data_entry_count_mismatch_generates_warning(self):
        """Test that LUT data entry count mismatch generates a warning."""
        dataset = Dataset()
        
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]  # Expects 256 entries
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(128)]  # Only 128 entries
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate(dataset)
        
        assert result.is_valid  # Valid but with warning
        assert not result.has_errors
        assert result.has_warnings
        assert "contains 128 entries but" in result.warnings[0]
        assert "specifies 256 entries" in result.warnings[0]

    def test_lut_descriptor_zero_entries_special_case(self):
        """Test that LUT descriptor with 0 entries (meaning 65536) is handled correctly."""
        dataset = Dataset()

        lut_item = Dataset()
        lut_item.LUTDescriptor = [0, 0, 16]  # 0 means 65536 entries
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(1000)]  # Smaller for test performance
        dataset.ModalityLUTSequence = [lut_item]

        result = ModalityLutValidator.validate(dataset)

        # Should be valid (though may have warning about entry count mismatch)
        assert not result.has_errors
        if result.has_warnings:
            assert "65536 expected" in result.warnings[0]

    def test_lut_descriptor_negative_entries_fails_validation(self):
        """Test that LUT descriptor with negative entries fails validation."""
        dataset = Dataset()

        lut_item = Dataset()
        lut_item.LUTDescriptor = [-1, 0, 8]  # Negative entries
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]

        result = ModalityLutValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert "must be positive" in result.errors[0]
        assert "found -1" in result.errors[0]

    def test_all_standard_rescale_types_pass_validation(self):
        """Test that all standard rescale types pass validation without warnings."""
        standard_types = [
            RescaleType.OD, RescaleType.HU, RescaleType.US, RescaleType.MGML,
            RescaleType.Z_EFF, RescaleType.ED, RescaleType.EDW,
            RescaleType.HU_MOD, RescaleType.PCT
        ]

        for rescale_type in standard_types:
            dataset = Dataset()
            dataset.RescaleIntercept = "0.0"
            dataset.RescaleSlope = "1.0"
            dataset.RescaleType = rescale_type.value

            result = ModalityLutValidator.validate(dataset)

            assert result.is_valid, f"Failed for {rescale_type.value}"
            assert not result.has_errors, f"Unexpected errors for {rescale_type.value}"
            assert not result.has_warnings, f"Unexpected warnings for {rescale_type.value}"

    def test_all_standard_modality_lut_types_pass_validation(self):
        """Test that all standard modality LUT types pass validation without warnings."""
        standard_types = [
            ModalityLutType.OD, ModalityLutType.HU, ModalityLutType.US, ModalityLutType.MGML,
            ModalityLutType.Z_EFF, ModalityLutType.ED, ModalityLutType.EDW,
            ModalityLutType.HU_MOD, ModalityLutType.PCT
        ]

        for modality_lut_type in standard_types:
            dataset = Dataset()

            lut_item = Dataset()
            lut_item.LUTDescriptor = [256, 0, 8]
            lut_item.ModalityLUTType = modality_lut_type.value
            lut_item.LUTData = [i for i in range(256)]
            dataset.ModalityLUTSequence = [lut_item]

            result = ModalityLutValidator.validate(dataset)

            assert result.is_valid, f"Failed for {modality_lut_type.value}"
            assert not result.has_errors, f"Unexpected errors for {modality_lut_type.value}"
            assert not result.has_warnings, f"Unexpected warnings for {modality_lut_type.value}"

    def test_error_messages_contain_dicom_references(self):
        """Test that error messages contain proper DICOM tag references and guidance."""
        dataset = Dataset()

        result = ModalityLutValidator.validate(dataset)

        assert result.has_errors
        error_message = result.errors[0]

        # Should contain DICOM PS3.3 reference
        assert "DICOM PS3.3 C.11.1" in error_message

        # Should contain DICOM tag references
        assert "(0028,3000)" in error_message  # Modality LUT Sequence
        assert "(0028,1052)" in error_message  # Rescale Intercept

        # Should contain actionable guidance
        assert "Add either" in error_message

    def test_validation_result_structure_consistency(self):
        """Test that ValidationResult structure is consistent across scenarios."""
        test_datasets = [
            Dataset(),  # Empty dataset
            self._create_valid_rescale_dataset(),
            self._create_valid_lut_sequence_dataset(),
            self._create_invalid_both_methods_dataset()
        ]

        for dataset in test_datasets:
            result = ModalityLutValidator.validate(dataset)

            # Verify ValidationResult structure
            assert isinstance(result, ValidationResult)
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)
            assert hasattr(result, 'is_valid')
            assert hasattr(result, 'has_errors')
            assert hasattr(result, 'has_warnings')

    def _create_valid_rescale_dataset(self) -> Dataset:
        """Helper to create a valid dataset with rescale parameters."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0"
        dataset.RescaleType = RescaleType.HU.value
        return dataset

    def _create_valid_lut_sequence_dataset(self) -> Dataset:
        """Helper to create a valid dataset with LUT sequence."""
        dataset = Dataset()
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        return dataset

    def _create_invalid_both_methods_dataset(self) -> Dataset:
        """Helper to create an invalid dataset with both transformation methods."""
        dataset = Dataset()
        # Add rescale parameters
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0"
        dataset.RescaleType = RescaleType.HU.value
        # Add LUT sequence
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        return dataset

    def test_validate_modality_lut_sequence_input_empty_list(self):
        """Test validation of empty modality LUT sequence input."""
        result = ModalityLutValidator.validate_modality_lut_sequence_input([])
        
        assert not result.is_valid
        assert result.has_errors
        assert "must contain exactly one item" in result.errors[0]
        assert "found 0 items" in result.errors[0]

    def test_validate_modality_lut_sequence_input_multiple_items(self):
        """Test validation of modality LUT sequence with multiple items."""
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        
        result = ModalityLutValidator.validate_modality_lut_sequence_input([lut_item, lut_item])
        
        assert not result.is_valid
        assert result.has_errors
        assert "must contain exactly one item" in result.errors[0]
        assert "found 2 items" in result.errors[0]

    def test_validate_modality_lut_sequence_input_single_item(self):
        """Test validation of modality LUT sequence with single item."""
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        
        result = ModalityLutValidator.validate_modality_lut_sequence_input([lut_item])
        
        assert result.is_valid
        assert not result.has_errors

    def test_validate_lut_item_creation_invalid_length(self):
        """Test validation of LUT item creation with invalid descriptor length."""
        result = ModalityLutValidator.validate_lut_item_creation([256, 0])  # Only 2 values
        
        assert not result.is_valid
        assert result.has_errors
        assert "LUT Descriptor must contain exactly 3 values" in result.errors[0]

    def test_validate_lut_item_creation_invalid_bits(self):
        """Test validation of LUT item creation with invalid bits per entry."""
        result = ModalityLutValidator.validate_lut_item_creation([256, 0, 12])  # Invalid bits per entry
        
        assert not result.is_valid
        assert result.has_errors
        assert "LUT Descriptor third value (bits per entry) must be 8 or 16" in result.errors[0]

    def test_validate_lut_item_creation_valid(self):
        """Test validation of LUT item creation with valid parameters."""
        result = ModalityLutValidator.validate_lut_item_creation([256, 0, 8])
        
        assert result.is_valid
        assert not result.has_errors

    def test_validate_mutual_exclusivity_adding_lut_with_rescale_present(self):
        """Test mutual exclusivity validation when adding LUT sequence with rescale parameters present."""
        dataset = Dataset()
        dataset.RescaleIntercept = "-1024.0"
        dataset.RescaleSlope = "1.0" 
        dataset.RescaleType = RescaleType.HU.value
        
        result = ModalityLutValidator.validate_mutual_exclusivity_on_add(dataset, adding_lut_sequence=True)
        
        assert not result.is_valid
        assert result.has_errors
        assert "Modality LUT Sequence shall not be present if Rescale Intercept is present" in result.errors[0]

    def test_validate_mutual_exclusivity_adding_rescale_with_lut_present(self):
        """Test mutual exclusivity validation when adding rescale parameters with LUT sequence present."""
        dataset = Dataset()
        lut_item = Dataset()
        lut_item.LUTDescriptor = [256, 0, 8]
        lut_item.ModalityLUTType = ModalityLutType.HU.value
        lut_item.LUTData = [i for i in range(256)]
        dataset.ModalityLUTSequence = [lut_item]
        
        result = ModalityLutValidator.validate_mutual_exclusivity_on_add(dataset, adding_lut_sequence=False)
        
        assert not result.is_valid 
        assert result.has_errors
        assert "Rescale parameters shall not be present if Modality LUT Sequence is present" in result.errors[0]

    def test_validate_mutual_exclusivity_clean_dataset(self):
        """Test mutual exclusivity validation with clean dataset."""
        dataset = Dataset()
        
        # Should be valid when adding LUT sequence to clean dataset
        result = ModalityLutValidator.validate_mutual_exclusivity_on_add(dataset, adding_lut_sequence=True)
        assert result.is_valid
        assert not result.has_errors
        
        # Should be valid when adding rescale parameters to clean dataset
        result = ModalityLutValidator.validate_mutual_exclusivity_on_add(dataset, adding_lut_sequence=False)
        assert result.is_valid 
        assert not result.has_errors
