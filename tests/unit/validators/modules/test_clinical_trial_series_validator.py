"""
Test ClinicalTrialSeriesValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.7.3.2 Clinical Trial Series Module validation.
"""

from pydicom import Dataset
from pyrt_dicom.validators.modules.clinical_trial_series_validator import ClinicalTrialSeriesValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.modules.clinical_trial_series_module import ClinicalTrialSeriesModule


class TestClinicalTrialSeriesValidator:
    """Test ClinicalTrialSeriesValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_missing_coordinating_center_name_generates_error(self):
        """Test that missing Type 2 Coordinating Center Name generates validation error."""
        dataset = Dataset()
        # Missing ClinicalTrialCoordinatingCenterName (Type 2 - required)
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Clinical Trial Coordinating Center Name (0012,0060) is required (Type 2)" in result.errors[0]
        assert "Must be present but may have zero-length value" in result.errors[0]
    
    def test_empty_coordinating_center_name_passes_validation(self):
        """Test that empty Coordinating Center Name (Type 2) passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = ""  # Type 2: can be empty
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_coordinating_center_name_passes_validation(self):
        """Test that valid Coordinating Center Name passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Medical Center"
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_all_optional_elements_present_passes_validation(self):
        """Test that dataset with all elements present passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Medical Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        dataset.IssuerOfClinicalTrialSeriesID = "ISSUER001"
        dataset.ClinicalTrialSeriesDescription = "Baseline CT imaging for trial"
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert not result.has_warnings
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_only_required_element_present_passes_validation(self):
        """Test that dataset with only required element passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Medical Research Institute"
        # No optional Type 3 elements
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_series_id_without_issuer_generates_warning(self):
        """Test that Series ID without issuer generates semantic validation warning."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        # Missing IssuerOfClinicalTrialSeriesID
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert len(result.warnings) == 1
        assert "Clinical Trial Series ID (0012,0071) is present" in result.warnings[0]
        assert "Issuer of Clinical Trial Series ID (0012,0073)" in result.warnings[0]
        assert "Consider adding issuer information" in result.warnings[0]
    
    def test_series_id_with_issuer_passes_validation(self):
        """Test that Series ID with issuer passes validation without warnings."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        dataset.IssuerOfClinicalTrialSeriesID = "ISSUER001"
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_warnings
        assert len(result.warnings) == 0
    
    def test_empty_series_id_does_not_require_issuer(self):
        """Test that empty Series ID does not trigger issuer warning."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = ""  # Empty, so issuer not needed
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_warnings
        assert len(result.warnings) == 0
    
    # LO VR Validation Tests
    
    def test_coordinating_center_name_exceeds_max_length_generates_error(self):
        """Test that Coordinating Center Name exceeding 64 characters generates error."""
        dataset = Dataset()
        # Create string longer than 64 characters
        long_name = "A" * 65  # 65 characters (LO VR max is 64)
        dataset.ClinicalTrialCoordinatingCenterName = long_name
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ClinicalTrialCoordinatingCenterName (0012,0060) exceeds maximum length" in error 
                  for error in result.errors)
        assert any("Current length: 65 characters" in error for error in result.errors)
    
    def test_series_id_exceeds_max_length_generates_error(self):
        """Test that Series ID exceeding 64 characters generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        long_id = "S" * 65  # 65 characters
        dataset.ClinicalTrialSeriesID = long_id
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ClinicalTrialSeriesID (0012,0071) exceeds maximum length" in error 
                  for error in result.errors)
    
    def test_issuer_id_exceeds_max_length_generates_error(self):
        """Test that Issuer ID exceeding 64 characters generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        long_issuer = "I" * 65  # 65 characters
        dataset.IssuerOfClinicalTrialSeriesID = long_issuer
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("IssuerOfClinicalTrialSeriesID (0012,0073) exceeds maximum length" in error 
                  for error in result.errors)
    
    def test_series_description_exceeds_max_length_generates_error(self):
        """Test that Series Description exceeding 64 characters generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        long_description = "D" * 65  # 65 characters
        dataset.ClinicalTrialSeriesDescription = long_description
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ClinicalTrialSeriesDescription (0012,0072) exceeds maximum length" in error 
                  for error in result.errors)
    
    def test_all_elements_at_max_length_pass_validation(self):
        """Test that all elements at exactly 64 characters pass validation."""
        dataset = Dataset()
        max_length_string = "A" * 64  # Exactly 64 characters
        
        dataset.ClinicalTrialCoordinatingCenterName = max_length_string
        dataset.ClinicalTrialSeriesID = max_length_string
        dataset.IssuerOfClinicalTrialSeriesID = max_length_string
        dataset.ClinicalTrialSeriesDescription = max_length_string
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        # Should NOT warn about issuer since both Series ID and issuer are provided
        assert not result.has_warnings
    
    def test_control_characters_in_coordinating_center_name_generate_error(self):
        """Test that control characters in Coordinating Center Name generate error."""
        dataset = Dataset()
        name_with_control = "Research\x01Center"  # Contains control character
        dataset.ClinicalTrialCoordinatingCenterName = name_with_control
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ClinicalTrialCoordinatingCenterName (0012,0060) contains invalid control characters" in error 
                  for error in result.errors)
        assert any("LO VR should not contain control characters" in error for error in result.errors)
    
    def test_spaces_in_values_are_allowed(self):
        """Test that spaces in LO values are allowed and pass validation."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Medical Center"
        dataset.ClinicalTrialSeriesID = "SERIES 001"
        dataset.IssuerOfClinicalTrialSeriesID = "ISSUER 001"
        dataset.ClinicalTrialSeriesDescription = "Baseline CT imaging series"
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        # Should NOT warn about issuer since both Series ID and issuer are provided
        assert not result.has_warnings
    
    def test_tab_character_in_value_generates_error(self):
        """Test that tab character in value generates error."""
        dataset = Dataset()
        name_with_tab = "Research\tCenter"  # Contains tab character
        dataset.ClinicalTrialCoordinatingCenterName = name_with_tab
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("contains invalid control characters" in error for error in result.errors)
    
    def test_newline_character_in_value_generates_error(self):
        """Test that newline character in value generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        description_with_newline = "Baseline CT\nimaging"  # Contains newline
        dataset.ClinicalTrialSeriesDescription = description_with_newline
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ClinicalTrialSeriesDescription (0012,0072) contains invalid control characters" in error 
                  for error in result.errors)
    
    # Edge Cases and Boundary Tests
    
    def test_none_values_in_optional_elements_pass_validation(self):
        """Test that None values in optional elements pass validation."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        # Set optional elements to None explicitly
        dataset.ClinicalTrialSeriesID = None
        dataset.IssuerOfClinicalTrialSeriesID = None
        dataset.ClinicalTrialSeriesDescription = None
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert not result.has_warnings
    
    def test_numeric_values_are_converted_to_string(self):
        """Test that numeric values are properly converted to string for validation."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = 12345  # Numeric value
        dataset.ClinicalTrialSeriesID = 67890
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        # Should warn about missing issuer
        assert result.has_warnings  # Semantic warning about issuer
    
    def test_very_large_numeric_value_generates_error_if_too_long(self):
        """Test that very large numeric values generate error if string representation too long."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        # Create a number that when converted to string exceeds 64 characters
        very_large_number = int("1" * 65)  # 65-digit number
        dataset.ClinicalTrialSeriesID = very_large_number
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ClinicalTrialSeriesID (0012,0071) exceeds maximum length" in error 
                  for error in result.errors)
    
    # Validation Configuration Tests
    
    def test_validation_with_default_config(self):
        """Test validation behavior with default ValidationConfig."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        
        # Test with None config (should use defaults)
        result = ClinicalTrialSeriesValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert result.has_warnings  # Should warn about missing issuer
    
    def test_validation_with_custom_config(self):
        """Test validation behavior with custom ValidationConfig."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        
        config = ValidationConfig()  # Custom config
        result = ClinicalTrialSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_warnings
    
    # Error Message Quality Tests
    
    def test_error_message_includes_dicom_tag_references(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        # Missing required element
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.has_errors
        assert any("(0012,0060)" in error for error in result.errors)
    
    def test_error_messages_are_human_readable(self):
        """Test that error messages are clear and actionable for end users."""
        dataset = Dataset()
        long_name = "A" * 65  # Too long
        dataset.ClinicalTrialCoordinatingCenterName = long_name
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.has_errors
        error_message = result.errors[0]
        assert "ClinicalTrialCoordinatingCenterName" in error_message
        assert "exceeds maximum length" in error_message
        assert "64 characters" in error_message
        assert "Current length: 65" in error_message
    
    def test_warning_messages_provide_helpful_guidance(self):
        """Test that warning messages provide helpful guidance for end users."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        # Missing issuer
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.has_warnings
        warning_message = result.warnings[0]
        assert "Clinical Trial Series ID" in warning_message
        assert "Issuer of Clinical Trial Series ID" in warning_message
        assert "Consider adding issuer information" in warning_message
        assert "better series identification" in warning_message
    
    def test_multiple_validation_issues_are_all_reported(self):
        """Test that multiple validation issues are all captured and reported."""
        dataset = Dataset()
        # Multiple issues:
        # 1. Missing required element (error)
        # 2. Long Series ID (error) 
        long_id = "S" * 65
        dataset.ClinicalTrialSeriesID = long_id
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        # Should have multiple errors
        assert result.has_errors
        assert len(result.errors) >= 2  # Missing required + length violation
        
        # Check for both types of errors
        required_error = any("Clinical Trial Coordinating Center Name" in error and "required" in error 
                           for error in result.errors)
        length_error = any("ClinicalTrialSeriesID" in error and "exceeds maximum length" in error 
                         for error in result.errors)
        assert required_error
        assert length_error
    
    def test_validation_result_consistency_across_scenarios(self):
        """Test that ValidationResult format is consistent across all validation scenarios."""
        test_datasets = [
            Dataset(),  # Empty dataset (should error)
            self._create_valid_dataset(),  # Valid dataset
            self._create_invalid_dataset()  # Invalid dataset
        ]
        
        for dataset in test_datasets:
            result = ClinicalTrialSeriesValidator.validate(dataset)
            
            # Verify consistent ValidationResult structure
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')
            assert hasattr(result, 'is_valid')
            assert hasattr(result, 'has_errors')
            assert hasattr(result, 'has_warnings')
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)
    
    def test_validation_performance_with_large_datasets(self):
        """Test that validation completes within reasonable time bounds."""
        import time
        
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center" * 5  # Longer but valid
        dataset.ClinicalTrialSeriesID = "SERIES001" * 5  # Longer but valid
        dataset.IssuerOfClinicalTrialSeriesID = "ISSUER001" * 5
        dataset.ClinicalTrialSeriesDescription = "Description" * 8
        
        start_time = time.time()
        result = ClinicalTrialSeriesValidator.validate(dataset)
        end_time = time.time()
        
        # Should complete very quickly (< 0.1 seconds)
        assert (end_time - start_time) < 0.1
        assert isinstance(result, ValidationResult)
    
    def test_validation_handles_edge_cases_gracefully(self):
        """Test that validators handle edge cases gracefully without crashes."""
        dataset = Dataset()
        
        # Test with extreme but valid values
        dataset.ClinicalTrialCoordinatingCenterName = ""  # Empty string (valid for Type 2)
        dataset.ClinicalTrialSeriesID = "1"  # Single character
        dataset.IssuerOfClinicalTrialSeriesID = "A" * 64  # Maximum length
        dataset.ClinicalTrialSeriesDescription = " "  # Single space
        
        # Should not crash, should return validation result
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        # Should NOT warn about issuer since issuer is provided
        assert not result.has_warnings
    
    def test_validation_handles_malformed_data_gracefully(self):
        """Test that validators handle malformed data gracefully without crashes."""
        dataset = Dataset()
        
        # Test with unusual but not necessarily invalid values
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "    "  # Only spaces
        dataset.IssuerOfClinicalTrialSeriesID = ""  # Empty string
        
        # Should not crash
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        # Should NOT warn about issuer since Series ID is only spaces (effectively empty after stripping)
        assert not result.has_warnings
    
    # Helper methods for test data creation
    def _create_valid_dataset(self) -> Dataset:
        """Create a valid clinical trial series dataset for testing."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Medical Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        dataset.IssuerOfClinicalTrialSeriesID = "ISSUER001"
        dataset.ClinicalTrialSeriesDescription = "Baseline CT imaging for oncology trial"
        return dataset
    
    def _create_invalid_dataset(self) -> Dataset:
        """Create an invalid clinical trial series dataset for testing."""
        dataset = Dataset()
        # Missing required ClinicalTrialCoordinatingCenterName
        long_id = "S" * 65  # Too long
        dataset.ClinicalTrialSeriesID = long_id
        dataset.ClinicalTrialSeriesDescription = "Description\x01with\x02control\x03chars"  # Control chars
        return dataset
    
    def _create_minimal_valid_dataset(self) -> Dataset:
        """Create a minimal valid clinical trial series dataset."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        return dataset
    
    # DICOM Standard Compliance Tests
    
    def test_dicom_tag_accuracy(self):
        """Test that all DICOM tags used in validation match PS3.3 specification exactly."""
        dataset = Dataset()
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        # Verify error message contains correct tags from DICOM standard
        assert result.has_errors
        assert "(0012,0060)" in result.errors[0]  # Coordinating Center Name
        
        # Test other elements trigger validation with correct tags
        dataset.ClinicalTrialCoordinatingCenterName = "A" * 65  # Too long
        dataset.ClinicalTrialSeriesID = "B" * 65
        dataset.IssuerOfClinicalTrialSeriesID = "C" * 65
        dataset.ClinicalTrialSeriesDescription = "D" * 65
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        error_text = " ".join(result.errors)
        assert "(0012,0060)" in error_text  # Coordinating Center Name
        assert "(0012,0071)" in error_text  # Series ID
        assert "(0012,0073)" in error_text  # Issuer of Series ID
        assert "(0012,0072)" in error_text  # Series Description
    
    def test_type_classification_accuracy(self):
        """Test that Type 1/2/3 classification matches DICOM standard exactly."""
        # Test Type 2 requirement (Coordinating Center Name)
        dataset = Dataset()
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.has_errors  # Type 2 missing should be error
        assert "Type 2" in result.errors[0]
        
        # Test empty Type 2 is valid
        dataset.ClinicalTrialCoordinatingCenterName = ""
        result = ClinicalTrialSeriesValidator.validate(dataset)
        assert result.is_valid  # Empty Type 2 should be valid
        
        # Test Type 3 elements are truly optional
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Required"
        # No Type 3 elements added
        result = ClinicalTrialSeriesValidator.validate(dataset)
        assert result.is_valid  # Type 3 elements optional
    
    def test_vr_compliance_with_dicom_standard(self):
        """Test that VR (Value Representation) validation matches DICOM standard."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Valid Center"
        
        # All elements in this module are LO (Long String) with VM=1
        # Test LO constraints: max 64 characters, no control chars
        
        # Test maximum length constraint
        dataset.ClinicalTrialSeriesID = "A" * 64  # Should pass
        result = ClinicalTrialSeriesValidator.validate(dataset)
        assert result.is_valid or result.has_warnings  # Valid length
        
        dataset.ClinicalTrialSeriesID = "A" * 65  # Should fail
        result = ClinicalTrialSeriesValidator.validate(dataset)
        assert result.has_errors  # Invalid length
    
    def test_semantic_validation_provides_clinical_context(self):
        """Test that semantic validation provides clinically relevant guidance."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        # Missing issuer - this is semantically important for clinical trials
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.has_warnings
        warning_message = result.warnings[0].lower()
        assert "series identification" in warning_message or "issuer" in warning_message
        assert "better" in warning_message or "consider" in warning_message
    
    def test_end_user_guidance_for_dicom_standard_navigation(self):
        """Test that validation provides guidance for navigating DICOM standard."""
        # Test error provides DICOM standard context
        dataset = Dataset()
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert result.has_errors
        error_message = result.errors[0]
        
        # Should explain Type 2 requirement clearly
        assert "Type 2" in error_message
        assert "required" in error_message.lower()
        assert "zero-length value" in error_message or "empty" in error_message.lower()
        
        # Should include DICOM tag for reference
        assert "(0012,0060)" in error_message
    
    # Tests for granular validation methods with both Dataset and BaseModule
    
    # validate_required_elements tests
    def test_validate_required_elements_with_dataset_success(self):
        """Test validate_required_elements with Dataset - success case."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        
        result = ClinicalTrialSeriesValidator.validate_required_elements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_validate_required_elements_with_basemodule_success(self):
        """Test validate_required_elements with BaseModule - success case."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        )
        
        result = ClinicalTrialSeriesValidator.validate_required_elements(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_validate_required_elements_with_dataset_missing(self):
        """Test validate_required_elements with Dataset - missing required element."""
        dataset = Dataset()  # Missing required element
        
        result = ClinicalTrialSeriesValidator.validate_required_elements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "Clinical Trial Coordinating Center Name (0012,0060) is required (Type 2)" in result.errors[0]
    
    def test_validate_required_elements_with_basemodule_missing(self):
        """Test validate_required_elements with BaseModule - missing required element."""
        module = ClinicalTrialSeriesModule()  # Missing required element
        
        result = ClinicalTrialSeriesValidator.validate_required_elements(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "Clinical Trial Coordinating Center Name (0012,0060) is required (Type 2)" in result.errors[0]
    
    # validate_conditional_requirements tests
    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements with Dataset."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        
        result = ClinicalTrialSeriesValidator.validate_conditional_requirements(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        # No Type 1C/2C elements in this module
    
    def test_validate_conditional_requirements_with_basemodule(self):
        """Test validate_conditional_requirements with BaseModule."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        )
        
        result = ClinicalTrialSeriesValidator.validate_conditional_requirements(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    # validate_enumerated_values tests
    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values with Dataset."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        
        result = ClinicalTrialSeriesValidator.validate_enumerated_values(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        # No enumerated values in this module
    
    def test_validate_enumerated_values_with_basemodule(self):
        """Test validate_enumerated_values with BaseModule."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001"
        )
        
        result = ClinicalTrialSeriesValidator.validate_enumerated_values(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    # validate_sequence_structures tests
    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures with Dataset."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        
        result = ClinicalTrialSeriesValidator.validate_sequence_structures(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        # No sequence structures in this module
    
    def test_validate_sequence_structures_with_basemodule(self):
        """Test validate_sequence_structures with BaseModule."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        )
        
        result = ClinicalTrialSeriesValidator.validate_sequence_structures(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    # validate_vr_constraints tests
    def test_validate_vr_constraints_with_dataset_success(self):
        """Test validate_vr_constraints with Dataset - success case."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        
        result = ClinicalTrialSeriesValidator.validate_vr_constraints(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_validate_vr_constraints_with_basemodule_success(self):
        """Test validate_vr_constraints with BaseModule - success case."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001"
        )
        
        result = ClinicalTrialSeriesValidator.validate_vr_constraints(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
    
    def test_validate_vr_constraints_with_dataset_too_long(self):
        """Test validate_vr_constraints with Dataset - too long values."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "A" * 65  # Too long
        
        result = ClinicalTrialSeriesValidator.validate_vr_constraints(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "exceeds maximum length of 64 characters" in result.errors[0]
    
    def test_validate_vr_constraints_with_basemodule_too_long(self):
        """Test validate_vr_constraints with BaseModule - too long values."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="A" * 65  # Too long
        )
        
        result = ClinicalTrialSeriesValidator.validate_vr_constraints(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "exceeds maximum length of 64 characters" in result.errors[0]
    
    def test_validate_vr_constraints_with_dataset_control_chars(self):
        """Test validate_vr_constraints with Dataset - control characters."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research\x01Center"  # Control character
        
        result = ClinicalTrialSeriesValidator.validate_vr_constraints(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "contains invalid control characters" in result.errors[0]
    
    def test_validate_vr_constraints_with_basemodule_control_chars(self):
        """Test validate_vr_constraints with BaseModule - control characters."""
        module = ClinicalTrialSeriesModule()
        # Manually set to avoid validation in constructor
        module.ClinicalTrialCoordinatingCenterName = "Research\x01Center"
        
        result = ClinicalTrialSeriesValidator.validate_vr_constraints(module)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert "contains invalid control characters" in result.errors[0]
    
    # validate_semantic_relationships tests
    def test_validate_semantic_relationships_with_dataset_no_warning(self):
        """Test validate_semantic_relationships with Dataset - no warning case."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        # No series ID, so no warning
        
        result = ClinicalTrialSeriesValidator.validate_semantic_relationships(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_warnings
    
    def test_validate_semantic_relationships_with_basemodule_no_warning(self):
        """Test validate_semantic_relationships with BaseModule - no warning case."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        )
        # No series ID, so no warning
        
        result = ClinicalTrialSeriesValidator.validate_semantic_relationships(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_warnings
    
    def test_validate_semantic_relationships_with_dataset_warning(self):
        """Test validate_semantic_relationships with Dataset - generates warning."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        # No issuer, should generate warning
        
        result = ClinicalTrialSeriesValidator.validate_semantic_relationships(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Warning doesn't affect validity
        assert result.has_warnings
        assert "Issuer of Clinical Trial Series ID (0012,0073)" in result.warnings[0]
    
    def test_validate_semantic_relationships_with_basemodule_warning(self):
        """Test validate_semantic_relationships with BaseModule - generates warning."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001"
            # No issuer, should generate warning
        )
        
        result = ClinicalTrialSeriesValidator.validate_semantic_relationships(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Warning doesn't affect validity
        assert result.has_warnings
        assert "Issuer of Clinical Trial Series ID (0012,0073)" in result.warnings[0]
    
    def test_validate_semantic_relationships_with_dataset_both_present(self):
        """Test validate_semantic_relationships with Dataset - both series ID and issuer present."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        dataset.IssuerOfClinicalTrialSeriesID = "ISSUER001"
        
        result = ClinicalTrialSeriesValidator.validate_semantic_relationships(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_warnings  # Both present, no warning
    
    def test_validate_semantic_relationships_with_basemodule_both_present(self):
        """Test validate_semantic_relationships with BaseModule - both series ID and issuer present."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="ISSUER001"
        )
        
        result = ClinicalTrialSeriesValidator.validate_semantic_relationships(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_warnings  # Both present, no warning
    
    # Main validate method tests with both Dataset and BaseModule
    def test_validate_main_method_with_dataset_success(self):
        """Test main validate method with Dataset - success case."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        dataset.ClinicalTrialSeriesID = "SERIES001"
        dataset.IssuerOfClinicalTrialSeriesID = "ISSUER001"
        dataset.ClinicalTrialSeriesDescription = "Test description"
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        assert not result.has_warnings
    
    def test_validate_main_method_with_basemodule_success(self):
        """Test main validate method with BaseModule - success case."""
        module = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"
        ).with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="ISSUER001",
            clinical_trial_series_description="Test description"
        )
        
        result = ClinicalTrialSeriesValidator.validate(module)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        assert not result.has_warnings
    
    def test_validate_main_method_orchestrates_all_validations(self):
        """Test that main validate method orchestrates all granular validation methods."""
        # Test with multiple issues to ensure all validations are called
        dataset = Dataset()
        # Missing required element (required validation)
        dataset.ClinicalTrialSeriesID = "A" * 65  # Too long (VR validation)
        # Series ID without issuer (semantic validation)
        
        result = ClinicalTrialSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid
        assert result.has_errors
        assert result.has_warnings
        
        # Should have error from required validation
        required_error = any("Clinical Trial Coordinating Center Name" in error and "required" in error 
                           for error in result.errors)
        assert required_error
        
        # Should have error from VR validation
        vr_error = any("exceeds maximum length" in error for error in result.errors)
        assert vr_error
        
        # Should have warning from semantic validation
        semantic_warning = any("Issuer of Clinical Trial Series ID" in warning 
                             for warning in result.warnings)
        assert semantic_warning
    
    def test_validate_with_configuration_options(self):
        """Test validate method with different configuration options."""
        dataset = Dataset()
        dataset.ClinicalTrialCoordinatingCenterName = "Research Center"
        
        # Test with custom config
        config = ValidationConfig()
        config.validate_conditional_requirements = False
        config.check_enumerated_values = False
        config.validate_sequences = False
        
        result = ClinicalTrialSeriesValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        # Still should validate required elements, VR constraints, and semantic relationships