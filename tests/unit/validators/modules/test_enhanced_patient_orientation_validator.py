"""Tests for Enhanced Patient Orientation Module DICOM validation - PS3.3 C.7.6.30"""

import pytest
from pydicom import Dataset

from src.pyrt_dicom.validators.modules.enhanced_patient_orientation_validator import (
    EnhancedPatientOrientationValidator
)
from src.pyrt_dicom.validators.modules.base_validator import ValidationConfig
from src.pyrt_dicom.validators import ValidationResult
from src.pyrt_dicom.modules.enhanced_patient_orientation_module import EnhancedPatientOrientationModule


class TestEnhancedPatientOrientationValidator:
    """Test suite for Enhanced Patient Orientation Module validator."""
    
    @pytest.fixture
    def valid_orientation_item(self):
        """Create a valid orientation code sequence item."""
        item = Dataset()
        item.CodeValue = "102538003"
        item.CodingSchemeDesignator = "SCT"
        item.CodeMeaning = "recumbent"
        return item
    
    @pytest.fixture
    def valid_modifier_item(self):
        """Create a valid modifier code sequence item."""
        item = Dataset()
        item.CodeValue = "40199007"
        item.CodingSchemeDesignator = "SCT"
        item.CodeMeaning = "supine"
        return item
    
    @pytest.fixture
    def valid_equipment_item(self):
        """Create a valid equipment relationship code sequence item."""
        item = Dataset()
        item.CodeValue = "102540008"
        item.CodingSchemeDesignator = "SCT"
        item.CodeMeaning = "headfirst"
        return item
    
    @pytest.fixture
    def valid_dataset(self, valid_orientation_item, valid_modifier_item, valid_equipment_item):
        """Create a valid Enhanced Patient Orientation dataset."""
        dataset = Dataset()
        dataset.PatientOrientationCodeSequence = [valid_orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [valid_modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [valid_equipment_item]
        return dataset
    
    @pytest.fixture
    def validation_config(self):
        """Create validation configuration for testing."""
        return ValidationConfig(
            validate_sequences=True,
            check_enumerated_values=True
        )
    
    def test_valid_data_passes_validation(self, valid_dataset, validation_config):
        """Test that valid module data passes validation without errors."""
        result = EnhancedPatientOrientationValidator.validate(valid_dataset, validation_config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_missing_patient_orientation_code_sequence_error(self, validation_config):
        """Test validation failure for missing Patient Orientation Code Sequence."""
        dataset = Dataset()
        dataset.PatientOrientationModifierCodeSequence = [Dataset()]
        dataset.PatientEquipmentRelationshipCodeSequence = [Dataset()]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Patient Orientation Code Sequence (0054,0410) is required" in error 
                  for error in result.errors)
    
    def test_missing_patient_orientation_modifier_sequence_error(self, validation_config):
        """Test validation failure for missing Patient Orientation Modifier Code Sequence."""
        dataset = Dataset()
        dataset.PatientOrientationCodeSequence = [Dataset()]
        dataset.PatientEquipmentRelationshipCodeSequence = [Dataset()]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Patient Orientation Modifier Code Sequence (0054,0412) is required" in error 
                  for error in result.errors)
    
    def test_missing_patient_equipment_relationship_sequence_error(self, validation_config):
        """Test validation failure for missing Patient Equipment Relationship Code Sequence."""
        dataset = Dataset()
        dataset.PatientOrientationCodeSequence = [Dataset()]
        dataset.PatientOrientationModifierCodeSequence = [Dataset()]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Patient Equipment Relationship Code Sequence (3010,0030) is required" in error 
                  for error in result.errors)
    
    def test_sequence_structure_validation_missing_code_value(self, validation_config):
        """Test validation of sequence structure with missing Code Value."""
        dataset = Dataset()
        
        # Create sequence item missing Code Value
        item = Dataset()
        item.CodingSchemeDesignator = "SCT"
        item.CodeMeaning = "recumbent"
        
        dataset.PatientOrientationCodeSequence = [item]
        dataset.PatientOrientationModifierCodeSequence = [Dataset()]
        dataset.PatientEquipmentRelationshipCodeSequence = [Dataset()]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Code Value (0008,0100) is required" in error for error in result.errors)
    
    def test_sequence_structure_validation_missing_coding_scheme(self, validation_config):
        """Test validation of sequence structure with missing Coding Scheme Designator."""
        dataset = Dataset()
        
        # Create sequence item missing Coding Scheme Designator
        item = Dataset()
        item.CodeValue = "102538003"
        item.CodeMeaning = "recumbent"
        
        dataset.PatientOrientationCodeSequence = [item]
        dataset.PatientOrientationModifierCodeSequence = [Dataset()]
        dataset.PatientEquipmentRelationshipCodeSequence = [Dataset()]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Coding Scheme Designator (0008,0102) is required" in error for error in result.errors)
    
    def test_sequence_structure_validation_missing_code_meaning(self, validation_config):
        """Test validation of sequence structure with missing Code Meaning."""
        dataset = Dataset()
        
        # Create sequence item missing Code Meaning
        item = Dataset()
        item.CodeValue = "102538003"
        item.CodingSchemeDesignator = "SCT"
        
        dataset.PatientOrientationCodeSequence = [item]
        dataset.PatientOrientationModifierCodeSequence = [Dataset()]
        dataset.PatientEquipmentRelationshipCodeSequence = [Dataset()]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Code Meaning (0008,0104) is required" in error for error in result.errors)
    
    def test_invalid_coding_scheme_for_code_value(self, validation_config):
        """Test validation of incorrect coding scheme for specific code values."""
        dataset = Dataset()
        
        # Create orientation item with wrong coding scheme
        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"  # recumbent - should be SCT
        orientation_item.CodingSchemeDesignator = "DCM"  # Wrong scheme
        orientation_item.CodeMeaning = "recumbent"
        
        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"
        
        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"
        
        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("requires Coding Scheme Designator 'SCT', got 'DCM'" in error 
                  for error in result.errors)
    
    def test_mutually_exclusive_orientations_error(self, validation_config):
        """Test validation failure for mutually exclusive orientations."""
        dataset = Dataset()
        
        # Create conflicting orientation items (recumbent and erect)
        recumbent_item = Dataset()
        recumbent_item.CodeValue = "102538003"
        recumbent_item.CodingSchemeDesignator = "SCT"
        recumbent_item.CodeMeaning = "recumbent"
        
        erect_item = Dataset()
        erect_item.CodeValue = "C86043"
        erect_item.CodingSchemeDesignator = "NCIt"
        erect_item.CodeMeaning = "erect"
        
        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"
        
        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"
        
        dataset.PatientOrientationCodeSequence = [recumbent_item, erect_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("cannot be both recumbent (102538003) and erect (C86043)" in error 
                  for error in result.errors)
    
    def test_incompatible_orientation_modifier_combination(self, validation_config):
        """Test validation failure for incompatible orientation-modifier combinations."""
        dataset = Dataset()
        
        # Erect orientation with supine modifier (incompatible)
        orientation_item = Dataset()
        orientation_item.CodeValue = "C86043"  # erect
        orientation_item.CodingSchemeDesignator = "NCIt"
        orientation_item.CodeMeaning = "erect"
        
        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"  # supine (incompatible with erect)
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"
        
        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"
        
        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("Erect orientation (C86043) requires compatible modifier" in error 
                  for error in result.errors)
    
    def test_mutually_exclusive_modifiers_error(self, validation_config):
        """Test validation failure for mutually exclusive modifiers."""
        dataset = Dataset()
        
        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "recumbent"
        
        # Create conflicting modifier items (supine and prone)
        supine_item = Dataset()
        supine_item.CodeValue = "40199007"
        supine_item.CodingSchemeDesignator = "SCT"
        supine_item.CodeMeaning = "supine"
        
        prone_item = Dataset()
        prone_item.CodeValue = "1240000"
        prone_item.CodingSchemeDesignator = "SCT"
        prone_item.CodeMeaning = "prone"
        
        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"
        
        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [supine_item, prone_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 1
        assert any("cannot be both supine (40199007) and prone (1240000)" in error 
                  for error in result.errors)
    
    @pytest.mark.parametrize("orientation_code,modifier_code,equipment_code", [
        ("102538003", "40199007", "102540008"),  # Conventional CT
        ("C86043", "10904000", "102540008"),     # Standing CT
        ("C86043", "33586001", "102540008"),     # Seated CT
        ("102538003", "1240000", "126833")       # Breast CT
    ])
    def test_known_valid_combinations_pass(self, orientation_code, modifier_code, equipment_code, validation_config):
        """Test that known valid combinations from DICOM examples pass validation."""
        dataset = Dataset()
        
        orientation_item = Dataset()
        orientation_item.CodeValue = orientation_code
        orientation_item.CodingSchemeDesignator = "SCT" if orientation_code != "C86043" else "NCIt"
        orientation_item.CodeMeaning = "test"
        
        modifier_item = Dataset()
        modifier_item.CodeValue = modifier_code
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "test"
        
        equipment_item = Dataset()
        equipment_item.CodeValue = equipment_code
        equipment_item.CodingSchemeDesignator = "SCT" if equipment_code != "126833" else "DCM"
        equipment_item.CodeMeaning = "test"
        
        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        # Should pass without errors (warnings about unknown combinations are okay)
        assert len(result.errors) == 0
    
    def test_unknown_combination_warning(self, validation_config):
        """Test that unknown combinations generate warnings."""
        dataset = Dataset()
        
        # Create a combination not in DICOM examples
        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"  # recumbent
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "recumbent"
        
        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"  # supine
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"
        
        # Use different equipment code than typical for this combination
        equipment_item = Dataset()
        equipment_item.CodeValue = "126833"  # anterior first (usually with prone)
        equipment_item.CodingSchemeDesignator = "DCM"
        equipment_item.CodeMeaning = "anterior first"
        
        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        # Should have warning about unknown combination
        assert len(result.warnings) >= 1
        assert any("does not match any known valid combinations" in warning 
                  for warning in result.warnings)
    
    def test_invalid_code_value_warning(self, validation_config):
        """Test that invalid code values generate warnings."""
        dataset = Dataset()
        
        # Create item with invalid code value
        orientation_item = Dataset()
        orientation_item.CodeValue = "INVALID123"
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "invalid"
        
        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"
        
        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"
        
        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.warnings) >= 1
        assert any("is not a DICOM-defined orientation code" in warning 
                  for warning in result.warnings)
    
    def test_validation_config_disabled_checks(self):
        """Test that validation respects disabled configuration options."""
        config = ValidationConfig(
            validate_sequences=False,
            check_enumerated_values=False
        )
        
        dataset = Dataset()
        # Create dataset with structure issues that should be ignored
        invalid_item = Dataset()
        # Missing required fields, but sequence validation is disabled
        
        dataset.PatientOrientationCodeSequence = [invalid_item]
        dataset.PatientOrientationModifierCodeSequence = [invalid_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [invalid_item]
        
        result = EnhancedPatientOrientationValidator.validate(dataset, config)
        
        # Should still validate required elements and cross-sequence logic
        # But not sequence structure or enumerated values
        assert len(result.errors) == 0  # No structure validation errors
    
    def test_validation_result_structure(self, valid_dataset):
        """Test that ValidationResult has proper structure."""
        result = EnhancedPatientOrientationValidator.validate(valid_dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_error_message_quality(self, validation_config):
        """Test that error messages are specific and actionable."""
        dataset = Dataset()
        # Missing all required sequences
        
        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)
        
        assert len(result.errors) >= 3
        
        # Check that error messages include DICOM tags and are specific
        for error in result.errors:
            assert isinstance(error, str)
            assert len(error) > 10  # Not just a short message
            # Should include either DICOM tag or specific guidance
            assert any(tag in error for tag in ['(0054,0410)', '(0054,0412)', '(3010,0030)']) or \
                   any(keyword in error.lower() for keyword in ['required', 'missing', 'invalid'])

    def test_semi_erect_orientation_validation(self, validation_config):
        """Test validation of semi-erect orientation with appropriate modifiers."""
        dataset = Dataset()

        # Semi-erect orientation with sitting modifier (should generate warning)
        orientation_item = Dataset()
        orientation_item.CodeValue = "102539006"  # semi-erect
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "semi-erect"

        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"  # supine (not typical for semi-erect)
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"

        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"

        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]

        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)

        # Should have warning about semi-erect modifier compatibility
        assert len(result.warnings) >= 1
        assert any("Semi-erect orientation (102539006)" in warning for warning in result.warnings)

    def test_feetfirst_equipment_code_validation(self, validation_config):
        """Test validation of feetfirst equipment relationship code."""
        dataset = Dataset()

        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "recumbent"

        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"

        # Use feetfirst equipment code
        equipment_item = Dataset()
        equipment_item.CodeValue = "102541007"  # feetfirst
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "feetfirst"

        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]

        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)

        # Should pass validation without errors (feetfirst is a valid equipment code)
        assert len(result.errors) == 0

    def test_semi_erect_mutually_exclusive_validation(self, validation_config):
        """Test that semi-erect is mutually exclusive with other orientations."""
        dataset = Dataset()

        # Create conflicting orientation items (semi-erect and recumbent)
        semi_erect_item = Dataset()
        semi_erect_item.CodeValue = "102539006"
        semi_erect_item.CodingSchemeDesignator = "SCT"
        semi_erect_item.CodeMeaning = "semi-erect"

        recumbent_item = Dataset()
        recumbent_item.CodeValue = "102538003"
        recumbent_item.CodingSchemeDesignator = "SCT"
        recumbent_item.CodeMeaning = "recumbent"

        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"

        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"

        dataset.PatientOrientationCodeSequence = [semi_erect_item, recumbent_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]

        result = EnhancedPatientOrientationValidator.validate(dataset, validation_config)

        assert len(result.errors) >= 1
        assert any("cannot be both recumbent (102538003) and semi-erect (102539006)" in error
                  for error in result.errors)

    def test_validate_lateral_decubitus_side_valid(self):
        """Test validation of valid lateral decubitus side parameters."""
        result = ValidationResult()
        
        # Test valid sides - should not add any errors
        EnhancedPatientOrientationValidator.validate_lateral_decubitus_side("left", result)
        assert len(result.errors) == 0
        
        EnhancedPatientOrientationValidator.validate_lateral_decubitus_side("right", result)
        assert len(result.errors) == 0
        
        # Test case insensitive
        EnhancedPatientOrientationValidator.validate_lateral_decubitus_side("LEFT", result)
        assert len(result.errors) == 0
        
        EnhancedPatientOrientationValidator.validate_lateral_decubitus_side("Right", result)
        assert len(result.errors) == 0

    def test_validate_lateral_decubitus_side_invalid(self):
        """Test validation of invalid lateral decubitus side parameters."""
        result = ValidationResult()
        
        # Test invalid side
        EnhancedPatientOrientationValidator.validate_lateral_decubitus_side("invalid", result)
        assert len(result.errors) == 1
        assert "Invalid side parameter 'invalid' for lateral decubitus orientation" in result.errors[0]
        assert "Must be one of: left, right" in result.errors[0]
        
        # Test another invalid side
        result.clear()
        EnhancedPatientOrientationValidator.validate_lateral_decubitus_side("center", result)
        assert len(result.errors) == 1
        assert "Invalid side parameter 'center' for lateral decubitus orientation" in result.errors[0]


class TestEnhancedPatientOrientationValidatorGranularMethods:
    """Test granular validation methods with Dataset and BaseModule support."""

    @pytest.fixture
    def valid_dataset(self):
        """Create a valid Enhanced Patient Orientation dataset."""
        dataset = Dataset()

        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "recumbent"

        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"

        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"

        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]

        return dataset

    @pytest.fixture
    def valid_module(self):
        """Create a valid Enhanced Patient Orientation module."""
        return EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )

    def test_validate_required_elements_dataset_success(self, valid_dataset):
        """Test validate_required_elements with valid Dataset."""
        result = EnhancedPatientOrientationValidator.validate_required_elements(valid_dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert len(result.errors) == 0

    def test_validate_required_elements_module_success(self, valid_module):
        """Test validate_required_elements with valid BaseModule."""
        result = EnhancedPatientOrientationValidator.validate_required_elements(valid_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert len(result.errors) == 0

    def test_validate_required_elements_dataset_missing(self):
        """Test validate_required_elements with missing sequences in Dataset."""
        dataset = Dataset()

        result = EnhancedPatientOrientationValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert len(result.errors) == 3  # All three sequences missing
        assert any("Patient Orientation Code Sequence" in error for error in result.errors)
        assert any("Patient Orientation Modifier Code Sequence" in error for error in result.errors)
        assert any("Patient Equipment Relationship Code Sequence" in error for error in result.errors)

    def test_validate_required_elements_module_missing(self):
        """Test validate_required_elements with missing sequences in BaseModule."""
        module = EnhancedPatientOrientationModule()  # Empty module

        result = EnhancedPatientOrientationValidator.validate_required_elements(module)

        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert len(result.errors) == 3  # All three sequences missing

    def test_validate_conditional_requirements_dataset(self, valid_dataset):
        """Test validate_conditional_requirements with Dataset."""
        result = EnhancedPatientOrientationValidator.validate_conditional_requirements(valid_dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No conditional requirements for this module

    def test_validate_conditional_requirements_module(self, valid_module):
        """Test validate_conditional_requirements with BaseModule."""
        result = EnhancedPatientOrientationValidator.validate_conditional_requirements(valid_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No conditional requirements for this module

    def test_validate_enumerated_values_dataset_valid(self, valid_dataset):
        """Test validate_enumerated_values with valid Dataset."""
        result = EnhancedPatientOrientationValidator.validate_enumerated_values(valid_dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_validate_enumerated_values_module_valid(self, valid_module):
        """Test validate_enumerated_values with valid BaseModule."""
        result = EnhancedPatientOrientationValidator.validate_enumerated_values(valid_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_validate_enumerated_values_dataset_invalid_scheme(self):
        """Test validate_enumerated_values with invalid coding scheme in Dataset."""
        dataset = Dataset()

        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"
        orientation_item.CodingSchemeDesignator = "INVALID"  # Wrong scheme
        orientation_item.CodeMeaning = "recumbent"

        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"

        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"

        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]

        result = EnhancedPatientOrientationValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any("requires Coding Scheme Designator 'SCT'" in error for error in result.errors)

    def test_validate_sequence_structures_dataset_valid(self, valid_dataset):
        """Test validate_sequence_structures with valid Dataset."""
        result = EnhancedPatientOrientationValidator.validate_sequence_structures(valid_dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_validate_sequence_structures_module_valid(self, valid_module):
        """Test validate_sequence_structures with valid BaseModule."""
        result = EnhancedPatientOrientationValidator.validate_sequence_structures(valid_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_validate_sequence_structures_dataset_missing_fields(self):
        """Test validate_sequence_structures with missing required fields in Dataset."""
        dataset = Dataset()

        # Create sequence item missing Code Value
        item = Dataset()
        item.CodingSchemeDesignator = "SCT"
        item.CodeMeaning = "recumbent"
        # Missing CodeValue

        dataset.PatientOrientationCodeSequence = [item]

        result = EnhancedPatientOrientationValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any("Code Value (0008,0100) is required" in error for error in result.errors)

    def test_validate_cross_sequence_consistency_dataset_valid(self, valid_dataset):
        """Test validate_cross_sequence_consistency with valid Dataset."""
        result = EnhancedPatientOrientationValidator.validate_cross_sequence_consistency(valid_dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_validate_cross_sequence_consistency_module_valid(self, valid_module):
        """Test validate_cross_sequence_consistency with valid BaseModule."""
        result = EnhancedPatientOrientationValidator.validate_cross_sequence_consistency(valid_module)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_validate_cross_sequence_consistency_dataset_invalid(self):
        """Test validate_cross_sequence_consistency with invalid Dataset combination."""
        dataset = Dataset()

        # Create incompatible combination: recumbent + standing
        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"  # recumbent
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "recumbent"

        modifier_item = Dataset()
        modifier_item.CodeValue = "10904000"  # standing (incompatible with recumbent)
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "standing"

        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"

        dataset.PatientOrientationCodeSequence = [orientation_item]
        dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]

        result = EnhancedPatientOrientationValidator.validate_cross_sequence_consistency(dataset)

        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any("Recumbent orientation" in error and "requires compatible modifier" in error
                  for error in result.errors)

    def test_validate_cross_sequence_consistency_module_invalid(self):
        """Test validate_cross_sequence_consistency with invalid BaseModule combination."""
        module = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="C86043",  # erect
                    coding_scheme_designator="NCIt",
                    code_meaning="erect"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",  # supine (incompatible with erect)
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )

        result = EnhancedPatientOrientationValidator.validate_cross_sequence_consistency(module)

        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any("Erect orientation" in error and "requires compatible modifier" in error
                  for error in result.errors)

    def test_main_validate_method_dataset_orchestration(self, valid_dataset):
        """Test main validate method orchestrates all validations with Dataset."""
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )

        result = EnhancedPatientOrientationValidator.validate(valid_dataset, config)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_main_validate_method_module_orchestration(self, valid_module):
        """Test main validate method orchestrates all validations with BaseModule."""
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )

        result = EnhancedPatientOrientationValidator.validate(valid_module, config)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_main_validate_method_config_options(self, valid_dataset):
        """Test main validate method respects configuration options."""
        # Test with all validations disabled except required elements
        config = ValidationConfig(
            validate_conditional_requirements=False,
            check_enumerated_values=False,
            validate_sequences=False
        )

        result = EnhancedPatientOrientationValidator.validate(valid_dataset, config)

        assert isinstance(result, ValidationResult)
        # Should still validate required elements and cross-sequence consistency
        assert not result.has_errors

    def test_validator_independence_external_dataset(self):
        """Test validator independence - works with external pydicom Dataset."""
        # Create external dataset (not from our module)
        external_dataset = Dataset()

        orientation_item = Dataset()
        orientation_item.CodeValue = "102538003"
        orientation_item.CodingSchemeDesignator = "SCT"
        orientation_item.CodeMeaning = "recumbent"

        modifier_item = Dataset()
        modifier_item.CodeValue = "40199007"
        modifier_item.CodingSchemeDesignator = "SCT"
        modifier_item.CodeMeaning = "supine"

        equipment_item = Dataset()
        equipment_item.CodeValue = "102540008"
        equipment_item.CodingSchemeDesignator = "SCT"
        equipment_item.CodeMeaning = "headfirst"

        external_dataset.PatientOrientationCodeSequence = [orientation_item]
        external_dataset.PatientOrientationModifierCodeSequence = [modifier_item]
        external_dataset.PatientEquipmentRelationshipCodeSequence = [equipment_item]

        # Validator should work with external dataset
        result = EnhancedPatientOrientationValidator.validate(external_dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

    def test_zero_copy_optimization_verification(self, valid_module):
        """Test that zero-copy optimization works (BaseModule passed directly)."""
        # This test verifies that the validator can work with BaseModule instances
        # without requiring conversion to Dataset

        # Test all granular methods with BaseModule
        result1 = EnhancedPatientOrientationValidator.validate_required_elements(valid_module)
        result2 = EnhancedPatientOrientationValidator.validate_conditional_requirements(valid_module)
        result3 = EnhancedPatientOrientationValidator.validate_enumerated_values(valid_module)
        result4 = EnhancedPatientOrientationValidator.validate_sequence_structures(valid_module)
        result5 = EnhancedPatientOrientationValidator.validate_cross_sequence_consistency(valid_module)

        # All should work without errors
        for result in [result1, result2, result3, result4, result5]:
            assert isinstance(result, ValidationResult)
            assert not result.has_errors

        # Main validate method should also work
        result_main = EnhancedPatientOrientationValidator.validate(valid_module)
        assert isinstance(result_main, ValidationResult)
        assert not result_main.has_errors