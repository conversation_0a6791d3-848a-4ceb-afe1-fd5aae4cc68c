"""
Test Structure Set Validator functionality.

Tests comprehensive validation of DICOM PS3.3 C.8.8.5 Structure Set Module
including core elements, conditional logic, cross-field dependencies, and error handling.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.structure_set_validator import StructureSetValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.enums.rt_enums import ROIGenerationAlgorithm


class TestStructureSetValidator:
    """Test StructureSetValidator functionality."""

    def test_valid_structure_set_passes_validation(self):
        """Test that a valid structure set passes validation without errors."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Structure Set"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        result = StructureSetValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_missing_structure_set_label_error(self):
        """Test error for missing Structure Set Label (Type 1)."""
        dataset = Dataset()
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        result = StructureSetValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Structure Set Label (3006,0002) is required (Type 1)" in result.errors[0]
        assert "user-defined label identifies" in result.errors[0]

    def test_empty_structure_set_label_error(self):
        """Test error for empty Structure Set Label (Type 1)."""
        dataset = Dataset()
        dataset.StructureSetLabel = ""  # Empty Type 1 is invalid
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        result = StructureSetValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Structure Set Label (3006,0002) is required (Type 1)" in result.errors[0]

    def test_missing_structure_set_date_error(self):
        """Test error for missing Structure Set Date (Type 2)."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetTime = "120000"
        
        result = StructureSetValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Structure Set Date (3006,0008) is required (Type 2)" in result.errors[0]
        assert "May be empty if date is unknown" in result.errors[0]

    def test_empty_structure_set_date_allowed(self):
        """Test that empty Structure Set Date is allowed (Type 2)."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = ""  # Empty Type 2 is valid
        dataset.StructureSetTime = "120000"
        
        result = StructureSetValidator.validate(dataset)
        
        assert len(result.errors) == 0

    def test_missing_structure_set_time_error(self):
        """Test error for missing Structure Set Time (Type 2)."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        
        result = StructureSetValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Structure Set Time (3006,0009) is required (Type 2)" in result.errors[0]
        assert "May be empty if time is unknown" in result.errors[0]

    def test_roi_sequence_validation_success(self):
        """Test successful validation of Structure Set ROI Sequence."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add valid ROI sequence
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 0

    def test_roi_missing_number_error(self):
        """Test error for missing ROI Number (Type 1 within sequence)."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI item missing ROI Number
        roi_item = Dataset()
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "ROI Number (3006,0022) is required (Type 1)" in result.errors[0]
        assert "uniquely identifies the ROI" in result.errors[0]

    def test_roi_missing_frame_reference_uid_error(self):
        """Test error for missing Referenced Frame of Reference UID."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI item missing Referenced Frame of Reference UID
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Referenced Frame of Reference UID (3006,0024) is required (Type 1)" in result.errors[0]

    def test_roi_missing_name_error(self):
        """Test error for missing ROI Name (Type 2 within sequence)."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI item missing ROI Name
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "ROI Name (3006,0026) is required (Type 2)" in result.errors[0]

    def test_roi_missing_generation_algorithm_error(self):
        """Test error for missing ROI Generation Algorithm (Type 2 within sequence)."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI item missing ROI Generation Algorithm
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIName = "PTV"
        
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "ROI Generation Algorithm (3006,0036) is required (Type 2)" in result.errors[0]
        assert "AUTOMATIC, SEMIAUTOMATIC, or MANUAL" in result.errors[0]

    def test_duplicate_roi_numbers_error(self):
        """Test error for duplicate ROI numbers within structure set."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add two ROI items with same ROI Number
        roi_item1 = Dataset()
        roi_item1.ROINumber = 1
        roi_item1.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item1.ROIName = "PTV"
        roi_item1.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        roi_item2 = Dataset()
        roi_item2.ROINumber = 1  # Same as roi_item1
        roi_item2.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item2.ROIName = "OAR"
        roi_item2.ROIGenerationAlgorithm = ROIGenerationAlgorithm.AUTOMATIC.value
        
        dataset.StructureSetROISequence = [roi_item1, roi_item2]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        duplicate_errors = [e for e in result.errors if "ROI Number 1 is not unique" in e]
        assert len(duplicate_errors) == 1
        assert "must be unique within the Structure Set" in duplicate_errors[0]
        assert "Found duplicate in items 0 and 1" in duplicate_errors[0]

    def test_invalid_roi_generation_algorithm_error(self):
        """Test error for invalid ROI Generation Algorithm enumerated value."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI item with invalid algorithm
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = "INVALID_ALGORITHM"
        
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True, check_enumerated_values=True)
        result = StructureSetValidator.validate(dataset, config)
        
        algorithm_warnings = [w for w in result.warnings if "ROI Generation Algorithm (3006,0036)" in w]
        assert len(algorithm_warnings) == 1
        assert "INVALID_ALGORITHM" in algorithm_warnings[0]
        assert "should be one of: AUTOMATIC, SEMIAUTOMATIC, MANUAL" in algorithm_warnings[0]

    def test_referenced_frame_validation_success(self):
        """Test successful validation of Referenced Frame of Reference Sequence."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add frame of reference
        frame_item = Dataset()
        frame_item.FrameOfReferenceUID = "1.2.3.4.5"
        dataset.ReferencedFrameOfReferenceSequence = [frame_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 0

    def test_missing_frame_of_reference_uid_error(self):
        """Test error for missing Frame of Reference UID in sequence."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add frame item without FrameOfReferenceUID
        frame_item = Dataset()
        dataset.ReferencedFrameOfReferenceSequence = [frame_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Frame of Reference UID (0020,0052) is required" in result.errors[0]

    def test_referenced_segment_number_conditional_validation(self):
        """Test Type 1C validation for Referenced Segment Number."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI with Definition Source Sequence containing Segmentation Storage
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        def_source_item = Dataset()
        def_source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.66.4"  # Segmentation Storage
        def_source_item.ReferencedSOPInstanceUID = "1.2.3.4.6"
        # Missing ReferencedSegmentNumber - should cause error
        
        roi_item.DefinitionSourceSequence = [def_source_item]
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Referenced Segment Number (0062,000B) is required (Type 1C)" in result.errors[0]
        assert "Segmentation Storage" in result.errors[0]

    def test_referenced_fiducial_uid_conditional_validation(self):
        """Test Type 1C validation for Referenced Fiducial UID."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI with Definition Source Sequence containing Spatial Fiducials Storage
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        def_source_item = Dataset()
        def_source_item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.66.2"  # Spatial Fiducials Storage
        def_source_item.ReferencedSOPInstanceUID = "1.2.3.4.6"
        # Missing ReferencedFiducialUID - should cause error
        
        roi_item.DefinitionSourceSequence = [def_source_item]
        dataset.StructureSetROISequence = [roi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Referenced Fiducial UID (0070,031B) is required (Type 1C)" in result.errors[0]
        assert "Spatial Fiducials Storage" in result.errors[0]

    def test_cross_field_roi_frame_reference_warning(self):
        """Test warning for ROI referencing non-existent Frame of Reference."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add frame of reference
        frame_item = Dataset()
        frame_item.FrameOfReferenceUID = "1.2.3.4.5"
        dataset.ReferencedFrameOfReferenceSequence = [frame_item]
        
        # Add ROI referencing different frame UID
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "9.8.7.6.5"  # Different from frame sequence
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        
        dataset.StructureSetROISequence = [roi_item]
        
        result = StructureSetValidator.validate(dataset)
        
        assert len(result.warnings) == 1
        assert "not present in Referenced Frame of Reference Sequence" in result.warnings[0]
        assert "inconsistent frame reference definitions" in result.warnings[0]

    def test_predecessor_structure_set_validation(self):
        """Test validation of Predecessor Structure Set Sequence."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add predecessor with missing SOP Class UID
        pred_item = Dataset()
        pred_item.ReferencedSOPInstanceUID = "1.2.3.4.6"
        # Missing ReferencedSOPClassUID
        
        dataset.PredecessorStructureSetSequence = [pred_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = StructureSetValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Referenced SOP Class UID (0008,1150) is required" in result.errors[0]
        assert "predecessor Structure Set" in result.errors[0]

    def test_validation_config_flags(self):
        """Test that validation config flags control validation behavior."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        # Add ROI with invalid algorithm  
        roi_item = Dataset()
        roi_item.ROINumber = 1
        roi_item.ReferencedFrameOfReferenceUID = "1.2.3.4.5"
        roi_item.ROIName = "PTV"
        roi_item.ROIGenerationAlgorithm = "INVALID_ALGORITHM"
        dataset.StructureSetROISequence = [roi_item]
        
        # Test with enumerated value checking disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result_no_enum = StructureSetValidator.validate(dataset, config_no_enum)
        algorithm_warnings_disabled = [w for w in result_no_enum.warnings if "INVALID_ALGORITHM" in w]
        assert len(algorithm_warnings_disabled) == 0
        
        # Test with enumerated value checking enabled  
        config_enum = ValidationConfig(check_enumerated_values=True)
        result_enum = StructureSetValidator.validate(dataset, config_enum)
        algorithm_warnings_enabled = [w for w in result_enum.warnings if "INVALID_ALGORITHM" in w]
        assert len(algorithm_warnings_enabled) == 1

    def test_validation_result_structure(self):
        """Test that ValidationResult objects are properly formed."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Test Label"
        dataset.StructureSetDate = "20240101"
        dataset.StructureSetTime = "120000"
        
        result = StructureSetValidator.validate(dataset)
        
        # Test ValidationResult structure
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Test that errors and warnings are strings
        for error in result.errors:
            assert isinstance(error, str)
        for warning in result.warnings:
            assert isinstance(warning, str)

    def test_multiple_validation_errors(self):
        """Test handling of multiple validation errors."""
        dataset = Dataset()
        # Missing all required elements
        
        result = StructureSetValidator.validate(dataset)
        
        # Should have multiple errors for missing required elements
        assert len(result.errors) == 3  # Label, Date, Time
        error_text = " ".join(result.errors)
        assert "Structure Set Label" in error_text
        assert "Structure Set Date" in error_text  
        assert "Structure Set Time" in error_text

    def test_realistic_structure_set_validation(self):
        """Test validation with realistic structure set data."""
        dataset = Dataset()
        dataset.StructureSetLabel = "Prostate Cancer Plan"
        dataset.StructureSetName = "Primary Structure Set"
        dataset.StructureSetDescription = "PTV and organs at risk for prostate treatment"
        dataset.StructureSetDate = "20240115"
        dataset.StructureSetTime = "143000"
        dataset.InstanceNumber = "1"
        
        # Add frame of reference
        frame_item = Dataset()
        frame_item.FrameOfReferenceUID = "1.2.826.0.1.3680043.2.1125.1.12345"
        dataset.ReferencedFrameOfReferenceSequence = [frame_item]
        
        # Add multiple ROIs
        roi_items = []
        
        # PTV
        ptv_roi = Dataset()
        ptv_roi.ROINumber = 1
        ptv_roi.ReferencedFrameOfReferenceUID = "1.2.826.0.1.3680043.2.1125.1.12345"
        ptv_roi.ROIName = "PTV_Prostate"
        ptv_roi.ROIGenerationAlgorithm = ROIGenerationAlgorithm.SEMIAUTOMATIC.value
        ptv_roi.ROIDescription = "Planning Target Volume for prostate"
        roi_items.append(ptv_roi)
        
        # Bladder OAR
        bladder_roi = Dataset()
        bladder_roi.ROINumber = 2
        bladder_roi.ReferencedFrameOfReferenceUID = "1.2.826.0.1.3680043.2.1125.1.12345"
        bladder_roi.ROIName = "Bladder"
        bladder_roi.ROIGenerationAlgorithm = ROIGenerationAlgorithm.AUTOMATIC.value
        bladder_roi.ROIDescription = "Bladder organ at risk"
        roi_items.append(bladder_roi)
        
        # Rectum OAR
        rectum_roi = Dataset()
        rectum_roi.ROINumber = 3
        rectum_roi.ReferencedFrameOfReferenceUID = "1.2.826.0.1.3680043.2.1125.1.12345"
        rectum_roi.ROIName = "Rectum"
        rectum_roi.ROIGenerationAlgorithm = ROIGenerationAlgorithm.MANUAL.value
        rectum_roi.ROIDescription = "Rectum organ at risk"
        roi_items.append(rectum_roi)
        
        dataset.StructureSetROISequence = roi_items
        
        result = StructureSetValidator.validate(dataset)
        
        # Should pass validation without errors
        assert len(result.errors) == 0
        assert len(result.warnings) == 0