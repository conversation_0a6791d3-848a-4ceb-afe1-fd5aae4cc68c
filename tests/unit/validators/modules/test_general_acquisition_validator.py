"""Tests for General Acquisition Module Validator - DICOM PS3.3 C.7.10.1"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.general_acquisition_validator import GeneralAcquisitionValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult


class TestGeneralAcquisitionValidator:
    """Test class for GeneralAcquisitionValidator following pytest framework requirements."""
    
    def test_validate_empty_dataset_passes(self):
        """Test that empty dataset passes validation (all elements are Type 3)."""
        dataset = Dataset()
        config = ValidationConfig()
        
        result = GeneralAcquisitionValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_valid_complete_dataset_passes(self):
        """Test that dataset with all valid elements passes validation."""
        dataset = Dataset()
        dataset.AcquisitionUID = "*******.*******.9"
        dataset.AcquisitionNumber = 1
        dataset.AcquisitionDate = "20240101"
        dataset.AcquisitionTime = "120000"
        dataset.AcquisitionDateTime = "20240101120000.000000"
        dataset.AcquisitionDuration = 30.5
        dataset.ImagesInAcquisition = 100
        dataset.IrradiationEventUID = "*******.*******.9.10"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    # UID Format Validation Tests
    
    def test_validate_valid_acquisition_uid_passes(self):
        """Test that valid acquisition UID passes validation."""
        dataset = Dataset()
        dataset.AcquisitionUID = "1.2.840.10008.*******.5"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_invalid_acquisition_uid_format_fails(self):
        """Test that invalid acquisition UID format fails validation."""
        dataset = Dataset()
        dataset.AcquisitionUID = "invalid-uid-format"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Acquisition UID (0008,0017) format invalid" in result.errors[0]
        assert "UIDs must contain only digits (0-9) and dots" in result.errors[0]
    
    def test_validate_acquisition_uid_too_long_fails(self):
        """Test that acquisition UID longer than 64 characters fails validation."""
        dataset = Dataset()
        # Create a UID longer than 64 characters
        dataset.AcquisitionUID = "*******.*******.9.10.11.12.13.14.15.16.17.18.19.20.21.22.23.24.25.26.27.28.29.30.31.32.33.34.35"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Acquisition UID (0008,0017) too long" in result.errors[0]
        assert "UIDs cannot exceed 64 characters" in result.errors[0]
    
    def test_validate_acquisition_uid_with_leading_dot_fails(self):
        """Test that acquisition UID starting with dot fails validation."""
        dataset = Dataset()
        dataset.AcquisitionUID = ".*******.5"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "cannot start or end with dot" in result.errors[0]
    
    def test_validate_acquisition_uid_with_trailing_dot_fails(self):
        """Test that acquisition UID ending with dot fails validation."""
        dataset = Dataset()
        dataset.AcquisitionUID = "*******.5."
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "cannot start or end with dot" in result.errors[0]
    
    def test_validate_acquisition_uid_with_consecutive_dots_fails(self):
        """Test that acquisition UID with consecutive dots fails validation."""
        dataset = Dataset()
        dataset.AcquisitionUID = "1.2..3.4.5"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "cannot contain consecutive dots" in result.errors[0]
    
    def test_validate_acquisition_uid_zero_warns(self):
        """Test that acquisition UID with value '0' generates warning."""
        dataset = Dataset()
        dataset.AcquisitionUID = "0"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "not a valid UID root" in result.warnings[0]
    
    # Irradiation Event UID VM=1-n Tests
    
    def test_validate_single_irradiation_event_uid_passes(self):
        """Test that single irradiation event UID passes validation."""
        dataset = Dataset()
        dataset.IrradiationEventUID = "*******.*******.9"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        # Should have no errors but may have informational warning about multiple events
        assert len(result.errors) == 0
    
    def test_validate_multiple_irradiation_event_uids_passes(self):
        """Test that multiple irradiation event UIDs pass validation."""
        dataset = Dataset()
        dataset.IrradiationEventUID = [
            "*******.*******.9.1",
            "*******.*******.9.2",
            "*******.*******.9.3"
        ]
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        # Should have warning about multiple irradiation events
        assert any("Multiple Irradiation Event UIDs" in warning for warning in result.warnings)
    
    def test_validate_empty_irradiation_event_uid_list_fails(self):
        """Test that empty irradiation event UID list fails validation."""
        dataset = Dataset()
        dataset.IrradiationEventUID = []
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "list cannot be empty" in result.errors[0]
    
    def test_validate_invalid_irradiation_event_uid_in_list_fails(self):
        """Test that invalid UID in irradiation event UID list fails validation."""
        dataset = Dataset()
        dataset.IrradiationEventUID = [
            "*******.*******.9.1",
            "invalid-uid",
            "*******.*******.9.3"
        ]
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Irradiation Event UID[1] (0008,3010)" in result.errors[0]
        assert "format invalid" in result.errors[0]
    
    # Date/Time Format Validation Tests
    
    def test_validate_valid_acquisition_date_passes(self):
        """Test that valid acquisition date passes validation."""
        dataset = Dataset()
        dataset.AcquisitionDate = "20240315"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_invalid_acquisition_date_format_fails(self):
        """Test that invalid acquisition date format fails validation."""
        dataset = Dataset()
        dataset.AcquisitionDate = "2024-03-15"  # Wrong format
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Acquisition Date (0008,0022) format invalid" in result.errors[0]
        assert "Must be YYYYMMDD format" in result.errors[0]
    
    def test_validate_acquisition_date_invalid_month_fails(self):
        """Test that acquisition date with invalid month fails validation."""
        dataset = Dataset()
        dataset.AcquisitionDate = "20241315"  # Month 13
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "invalid month: 13" in result.errors[0]
    
    def test_validate_acquisition_date_invalid_day_fails(self):
        """Test that acquisition date with invalid day fails validation."""
        dataset = Dataset()
        dataset.AcquisitionDate = "20240332"  # Day 32
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "invalid day: 32" in result.errors[0]
    
    def test_validate_valid_acquisition_time_passes(self):
        """Test that valid acquisition time passes validation."""
        dataset = Dataset()
        dataset.AcquisitionTime = "143022.123456"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_acquisition_time_without_microseconds_passes(self):
        """Test that acquisition time without microseconds passes validation."""
        dataset = Dataset()
        dataset.AcquisitionTime = "143022"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_invalid_acquisition_time_format_fails(self):
        """Test that invalid acquisition time format fails validation."""
        dataset = Dataset()
        dataset.AcquisitionTime = "14:30:22"  # Wrong format
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Acquisition Time (0008,0032) format invalid" in result.errors[0]
        assert "Must be HHMMSS or HHMMSS.FFFFFF format" in result.errors[0]
    
    def test_validate_acquisition_time_invalid_hour_fails(self):
        """Test that acquisition time with invalid hour fails validation."""
        dataset = Dataset()
        dataset.AcquisitionTime = "253022"  # Hour 25
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "invalid hour: 25" in result.errors[0]
    
    def test_validate_acquisition_time_invalid_minute_fails(self):
        """Test that acquisition time with invalid minute fails validation."""
        dataset = Dataset()
        dataset.AcquisitionTime = "146022"  # Minute 60
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "invalid minute: 60" in result.errors[0]
    
    def test_validate_acquisition_time_invalid_second_fails(self):
        """Test that acquisition time with invalid second fails validation."""
        dataset = Dataset()
        dataset.AcquisitionTime = "143060"  # Second 60
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "invalid second: 60" in result.errors[0]
    
    def test_validate_valid_acquisition_datetime_passes(self):
        """Test that valid acquisition datetime passes validation."""
        dataset = Dataset()
        dataset.AcquisitionDateTime = "20240315143022.123456"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_acquisition_datetime_without_microseconds_passes(self):
        """Test that acquisition datetime without microseconds passes validation."""
        dataset = Dataset()
        dataset.AcquisitionDateTime = "20240315143022"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_invalid_acquisition_datetime_format_fails(self):
        """Test that invalid acquisition datetime format fails validation."""
        dataset = Dataset()
        dataset.AcquisitionDateTime = "2024-03-15T14:30:22"  # Wrong format
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Acquisition DateTime (0008,002A) format invalid" in result.errors[0]
        assert "Must be YYYYMMDDHHMMSS.FFFFFF format" in result.errors[0]
    
    # Numeric Range Validation Tests
    
    def test_validate_positive_acquisition_duration_passes(self):
        """Test that positive acquisition duration passes validation."""
        dataset = Dataset()
        dataset.AcquisitionDuration = 30.5
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_negative_acquisition_duration_warns(self):
        """Test that negative acquisition duration generates warning."""
        dataset = Dataset()
        dataset.AcquisitionDuration = -10.0
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "should be positive" in result.warnings[0]
    
    def test_validate_zero_acquisition_duration_warns(self):
        """Test that zero acquisition duration generates warning."""
        dataset = Dataset()
        dataset.AcquisitionDuration = 0.0
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "may indicate no actual acquisition time" in result.warnings[0]
    
    def test_validate_non_numeric_acquisition_duration_fails(self):
        """Test that non-numeric acquisition duration fails validation."""
        dataset = Dataset()
        # Bypass pydicom's built-in validation by setting the attribute directly
        dataset.__dict__['AcquisitionDuration'] = "not a number"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "must be a numeric value" in result.errors[0]
    
    def test_validate_positive_images_in_acquisition_passes(self):
        """Test that positive images in acquisition passes validation."""
        dataset = Dataset()
        dataset.ImagesInAcquisition = 100
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_validate_negative_images_in_acquisition_fails(self):
        """Test that negative images in acquisition fails validation."""
        dataset = Dataset()
        dataset.ImagesInAcquisition = -5
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "cannot be negative" in result.errors[0]
    
    def test_validate_zero_images_in_acquisition_warns(self):
        """Test that zero images in acquisition generates warning."""
        dataset = Dataset()
        dataset.ImagesInAcquisition = 0
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "may indicate no images were acquired" in result.warnings[0]
    
    def test_validate_non_integer_images_in_acquisition_fails(self):
        """Test that non-integer images in acquisition fails validation."""
        dataset = Dataset()
        # Bypass pydicom's built-in validation by setting the attribute directly
        dataset.__dict__['ImagesInAcquisition'] = "not an integer"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "must be an integer value" in result.errors[0]
    
    # Logical Consistency Tests
    
    def test_validate_consistent_date_time_datetime_passes(self):
        """Test that consistent date, time, and datetime pass validation."""
        dataset = Dataset()
        dataset.AcquisitionDate = "20240315"
        dataset.AcquisitionTime = "143022"
        dataset.AcquisitionDateTime = "20240315143022"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        # Should have no consistency warnings
        consistency_warnings = [w for w in result.warnings if "inconsistent" in w]
        assert len(consistency_warnings) == 0
    
    def test_validate_inconsistent_date_time_datetime_warns(self):
        """Test that inconsistent date, time, and datetime generate warning."""
        dataset = Dataset()
        dataset.AcquisitionDate = "20240315"
        dataset.AcquisitionTime = "143022"
        dataset.AcquisitionDateTime = "20240316143022"  # Different date
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        consistency_warnings = [w for w in result.warnings if "inconsistent" in w]
        assert len(consistency_warnings) == 1
    
    def test_validate_date_without_time_warns(self):
        """Test that date without time generates warning."""
        dataset = Dataset()
        dataset.AcquisitionDate = "20240315"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        timing_warnings = [w for w in result.warnings if "Consider providing both" in w]
        assert len(timing_warnings) == 1
        assert "Acquisition Time (0008,0032) is missing" in timing_warnings[0]
    
    def test_validate_time_without_date_warns(self):
        """Test that time without date generates warning."""
        dataset = Dataset()
        dataset.AcquisitionTime = "143022"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        timing_warnings = [w for w in result.warnings if "Consider providing both" in w]
        assert len(timing_warnings) == 1
        assert "Acquisition Date (0008,0022) is missing" in timing_warnings[0]
    
    # Error Message Quality Tests
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include DICOM tag references."""
        dataset = Dataset()
        dataset.AcquisitionUID = "invalid-format"
        dataset.AcquisitionDate = "invalid-date"
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) >= 2
        # Check that DICOM tags are included in error messages
        uid_errors = [e for e in result.errors if "(0008,0017)" in e]
        date_errors = [e for e in result.errors if "(0008,0022)" in e]
        assert len(uid_errors) >= 1
        assert len(date_errors) >= 1
    
    def test_error_messages_are_actionable(self):
        """Test that error messages provide actionable guidance."""
        dataset = Dataset()
        dataset.AcquisitionUID = "123abc"  # Contains letters
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 1
        error = result.errors[0]
        # Should explain what's wrong and what format is expected
        assert "format invalid" in error
        assert "UIDs must contain only digits (0-9) and dots" in error
    
    # ValidationResult Structure Tests
    
    def test_validate_returns_validation_result_object(self):
        """Test that validate returns proper ValidationResult object."""
        dataset = Dataset()
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_with_validation_config_works(self):
        """Test that validate works with ValidationConfig parameter."""
        dataset = Dataset()
        config = ValidationConfig()
        
        result = GeneralAcquisitionValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_with_none_config_works(self):
        """Test that validate works with None config parameter."""
        dataset = Dataset()
        
        result = GeneralAcquisitionValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Real-World Scenario Tests
    
    def test_validate_typical_ct_acquisition_scenario(self):
        """Test validation of typical CT acquisition scenario."""
        dataset = Dataset()
        dataset.AcquisitionUID = "1.2.840.10008.1.234.567.890"
        dataset.AcquisitionNumber = 1
        dataset.AcquisitionDate = "20240315"
        dataset.AcquisitionTime = "143022.123"
        dataset.AcquisitionDateTime = "20240315143022.123000"
        dataset.AcquisitionDuration = 2.5
        dataset.ImagesInAcquisition = 120
        # No irradiation event UID for non-ionizing acquisition
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        # Should have minimal warnings for this clean scenario
    
    def test_validate_xray_with_multiple_sources_scenario(self):
        """Test validation of X-ray acquisition with multiple sources."""
        dataset = Dataset()
        dataset.AcquisitionUID = "1.2.840.10008.2.345.678.901"
        dataset.AcquisitionNumber = 2
        dataset.AcquisitionDate = "20240315"
        dataset.AcquisitionTime = "143500"
        dataset.AcquisitionDuration = 0.5
        dataset.ImagesInAcquisition = 2
        # Multiple irradiation events for multiple X-ray sources
        dataset.IrradiationEventUID = [
            "1.2.840.10008.3.111.222.333",
            "1.2.840.10008.3.111.222.334"
        ]
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        assert len(result.errors) == 0
        # Should have informational warning about multiple irradiation events
        multiple_event_warnings = [w for w in result.warnings if "Multiple Irradiation Event UIDs" in w]
        assert len(multiple_event_warnings) == 1
    
    def test_validate_comprehensive_error_scenario(self):
        """Test validation with multiple errors to ensure all are caught."""
        dataset = Dataset()
        dataset.AcquisitionUID = "invalid.uid.format."  # Ends with dot
        dataset.AcquisitionDate = "2024-03-15"  # Wrong format
        dataset.AcquisitionTime = "25:30:22"  # Wrong format and invalid hour
        dataset.AcquisitionDuration = -5.0  # Negative
        dataset.ImagesInAcquisition = -10  # Negative
        dataset.IrradiationEventUID = []  # Empty list
        
        result = GeneralAcquisitionValidator.validate(dataset)
        
        # Should have multiple errors
        assert len(result.errors) >= 5
        # Check that different types of errors are all present
        uid_errors = [e for e in result.errors if "UID" in e and "dot" in e]
        date_errors = [e for e in result.errors if "Date" in e and "format" in e]
        time_errors = [e for e in result.errors if "Time" in e and "format" in e]
        images_errors = [e for e in result.errors if "Images" in e and "negative" in e]
        irradiation_errors = [e for e in result.errors if "list cannot be empty" in e]
        
        assert len(uid_errors) >= 1
        assert len(date_errors) >= 1
        assert len(time_errors) >= 1
        assert len(images_errors) >= 1
        assert len(irradiation_errors) >= 1