"""
Test DeviceValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.7.6.12 Device Module validation.
Tests both Dataset and BaseModule support for Phase 2 refactor.
"""

from pydicom import Dataset
import pytest
from pyrt_dicom.validators.modules.device_validator import DeviceValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.equipment_enums import DeviceDiameterUnits
from pyrt_dicom.modules.device_module import DeviceModule


class TestDeviceValidator:
    """Test DeviceValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = DeviceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validation_config_default_behavior(self):
        """Test that validation uses default config when none provided."""
        dataset = Dataset()
        
        result = DeviceValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
    
    def test_validation_config_parameters_respected(self):
        """Test that ValidationConfig parameters control validation behavior."""
        # Create dataset with conditional requirement issue
        dataset = Dataset()
        device_item = Dataset()
        device_item.CodeValue = "A-04000"
        device_item.CodingSchemeDesignator = "SRT"
        device_item.CodeMeaning = "Catheter"
        device_item.DeviceDiameter = "2.5"  # Has diameter but no units
        dataset.DeviceSequence = [device_item]
        
        # Test with conditional validation disabled
        config_no_conditional = ValidationConfig(validate_conditional_requirements=False)
        result = DeviceValidator.validate(dataset, config_no_conditional)
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        assert len(conditional_errors) == 0  # Should skip conditional validation
        
        # Test with conditional validation enabled (default)
        config_with_conditional = ValidationConfig(validate_conditional_requirements=True)
        result = DeviceValidator.validate(dataset, config_with_conditional)
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        assert len(conditional_errors) > 0  # Should detect conditional validation errors
    
    def test_validation_config_sequence_validation(self):
        """Test that ValidationConfig sequence validation controls sequence structure checks."""
        # Create dataset with invalid sequence structure
        dataset = Dataset()
        dataset.DeviceSequence = [Dataset()]  # Missing required Code Sequence Macro attributes
        
        # Test with sequences validation disabled
        config_no_sequences = ValidationConfig(validate_sequences=False)
        result = DeviceValidator.validate(dataset, config_no_sequences)
        sequence_errors = [error for error in result.errors if 'Code Value' in error or 'Code Meaning' in error]
        assert len(sequence_errors) == 0  # Should skip sequence validation
        
        # Test with sequences validation enabled (default)
        config_with_sequences = ValidationConfig(validate_sequences=True)
        result = DeviceValidator.validate(dataset, config_with_sequences)
        sequence_errors = [error for error in result.errors if 'Code Value' in error or 'Code Meaning' in error]
        assert len(sequence_errors) > 0  # Should detect sequence validation errors
    
    def test_validation_config_enumerated_values(self):
        """Test that ValidationConfig enumerated values controls enum validation."""
        # Create dataset with invalid enumerated value
        dataset = Dataset()
        device_item = Dataset()
        device_item.CodeValue = "A-04000"
        device_item.CodingSchemeDesignator = "SRT"
        device_item.CodeMeaning = "Catheter"
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "INVALID"  # Invalid unit
        dataset.DeviceSequence = [device_item]
        
        # Test with enumerated values validation disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result = DeviceValidator.validate(dataset, config_no_enum)
        enum_errors = [error for error in result.errors if 'invalid value' in error]
        assert len(enum_errors) == 0  # Should skip enumerated value validation
        
        # Test with enumerated values validation enabled (default)
        config_with_enum = ValidationConfig(check_enumerated_values=True)
        result = DeviceValidator.validate(dataset, config_with_enum)
        enum_errors = [error for error in result.errors if 'invalid value' in error]
        assert len(enum_errors) > 0  # Should detect enumerated value errors


class TestType1RequiredElements:
    """Test Type 1 required elements validation."""
    
    def test_empty_dataset_fails_validation(self):
        """Test that empty dataset fails validation (Device Sequence is Type 1)."""
        dataset = Dataset()
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        required_errors = [error for error in result.errors if 'Device Sequence (0050,0010) is required (Type 1)' in error]
        assert len(required_errors) == 1
    
    def test_missing_device_sequence_fails_validation(self):
        """Test that missing Device Sequence generates appropriate error."""
        dataset = Dataset()
        # Explicitly not setting DeviceSequence
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        missing_sequence_errors = [error for error in result.errors if 'Device Sequence (0050,0010) is required (Type 1)' in error]
        assert len(missing_sequence_errors) == 1
        assert 'DeviceModule.from_required_elements()' in missing_sequence_errors[0]
    
    def test_empty_device_sequence_fails_validation(self):
        """Test that empty Device Sequence generates appropriate error."""
        dataset = Dataset()
        dataset.DeviceSequence = []  # Empty sequence
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        empty_sequence_errors = [error for error in result.errors if 'Device Sequence (0050,0010) cannot be empty (Type 1)' in error]
        assert len(empty_sequence_errors) == 1
        assert 'DeviceModule.create_device_item()' in empty_sequence_errors[0]
    
    def test_valid_device_sequence_passes_required_validation(self):
        """Test that valid Device Sequence passes Type 1 requirement validation."""
        dataset = Dataset()
        dataset.DeviceSequence = [self._create_valid_device_item()]
        
        result = DeviceValidator.validate(dataset)
        
        # Should have no Type 1 requirement errors
        type1_errors = [error for error in result.errors if 'is required (Type 1)' in error]
        assert len(type1_errors) == 0
    
    def _create_valid_device_item(self) -> Dataset:
        """Create a valid device item for testing."""
        item = Dataset()
        item.CodeValue = "A-04000"
        item.CodingSchemeDesignator = "SRT"
        item.CodeMeaning = "Catheter"
        return item


class TestType2CConditionalRequirements:
    """Test Type 2C conditional requirements validation."""
    
    def test_device_diameter_without_units_fails_validation(self):
        """Test that device diameter without units generates appropriate error."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        # Missing DeviceDiameterUnits
        dataset.DeviceSequence = [device_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        conditional_errors = [error for error in result.errors if 'Device Diameter Units (0050,0017) is required when Device Diameter (0050,0016) is present (Type 2C)' in error]
        assert len(conditional_errors) == 1
        assert 'Valid units: FR (French), GA (Gauge), IN (Inch), MM (Millimeter)' in conditional_errors[0]
    
    def test_device_diameter_with_units_passes_validation(self):
        """Test that device diameter with valid units passes conditional validation."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "MM"
        dataset.DeviceSequence = [device_item]
        
        result = DeviceValidator.validate(dataset)
        
        # Should have no Type 2C conditional errors
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        assert len(conditional_errors) == 0
    
    def test_no_diameter_no_units_passes_validation(self):
        """Test that having neither diameter nor units passes validation."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        # Neither DeviceDiameter nor DeviceDiameterUnits set
        dataset.DeviceSequence = [device_item]
        
        result = DeviceValidator.validate(dataset)
        
        # Should have no Type 2C conditional errors
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        assert len(conditional_errors) == 0
    
    def test_multiple_devices_conditional_validation(self):
        """Test conditional validation across multiple device items."""
        dataset = Dataset()
        
        # Valid device with diameter and units
        valid_device = self._create_valid_device_item()
        valid_device.DeviceDiameter = "3.0"
        valid_device.DeviceDiameterUnits = "FR"
        
        # Invalid device with diameter but no units
        invalid_device = self._create_valid_device_item()
        invalid_device.CodeValue = "A-04010"
        invalid_device.CodeMeaning = "Guidewire"
        invalid_device.DeviceDiameter = "2.0"
        # Missing DeviceDiameterUnits
        
        # Device with no diameter (should be fine)
        no_diameter_device = self._create_valid_device_item()
        no_diameter_device.CodeValue = "A-04020"
        no_diameter_device.CodeMeaning = "Marker"
        
        dataset.DeviceSequence = [valid_device, invalid_device, no_diameter_device]
        
        result = DeviceValidator.validate(dataset)
        
        # Should have exactly one conditional error for item 1
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        assert len(conditional_errors) == 1
        assert 'Device Sequence item 1' in conditional_errors[0]
    
    def _create_valid_device_item(self) -> Dataset:
        """Create a valid device item for testing."""
        item = Dataset()
        item.CodeValue = "A-04000"
        item.CodingSchemeDesignator = "SRT"
        item.CodeMeaning = "Catheter"
        return item


class TestEnumeratedValuesValidation:
    """Test enumerated values validation for Device Diameter Units."""
    
    def test_valid_diameter_units_pass_validation(self):
        """Test that all valid diameter units pass validation."""
        dataset = Dataset()
        
        valid_units = ["FR", "GA", "IN", "MM"]
        devices = []
        
        for i, unit in enumerate(valid_units):
            device_item = self._create_valid_device_item()
            device_item.CodeValue = f"A-0400{i}"
            device_item.CodeMeaning = f"Device {i}"
            device_item.DeviceDiameter = "2.5"
            device_item.DeviceDiameterUnits = unit
            devices.append(device_item)
        
        dataset.DeviceSequence = devices
        
        result = DeviceValidator.validate(dataset)
        
        # Should have no enumerated value errors
        enum_errors = [error for error in result.errors if 'invalid value' in error]
        assert len(enum_errors) == 0
    
    def test_invalid_diameter_units_fail_validation(self):
        """Test that invalid diameter units generate appropriate errors."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "INVALID"
        dataset.DeviceSequence = [device_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        enum_errors = [error for error in result.errors if 'invalid value \'INVALID\'' in error]
        assert len(enum_errors) == 1
        assert 'Valid values are: FR, GA, IN, MM' in enum_errors[0]
        assert 'FR=French, GA=Gauge, IN=Inch, MM=Millimeter' in enum_errors[0]
    
    def test_case_sensitive_diameter_units(self):
        """Test that diameter units validation is case sensitive."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "mm"  # Lowercase, should fail
        dataset.DeviceSequence = [device_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        enum_errors = [error for error in result.errors if 'invalid value \'mm\'' in error]
        assert len(enum_errors) == 1
    
    def test_empty_diameter_units_not_validated_as_enum(self):
        """Test that empty diameter units don't trigger enumerated value validation."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = ""  # Empty string
        dataset.DeviceSequence = [device_item]
        
        result = DeviceValidator.validate(dataset)
        
        # Should not have enumerated value errors (empty string is ignored)
        # But should have conditional requirement error
        enum_errors = [error for error in result.errors if 'invalid value' in error]
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        assert len(enum_errors) == 0
        assert len(conditional_errors) == 1  # Should still trigger conditional requirement
    
    def _create_valid_device_item(self) -> Dataset:
        """Create a valid device item for testing."""
        item = Dataset()
        item.CodeValue = "A-04000"
        item.CodingSchemeDesignator = "SRT"
        item.CodeMeaning = "Catheter"
        return item


class TestSequenceStructureValidation:
    """Test sequence structure validation for Device Sequence."""
    
    def test_device_item_must_be_dataset(self):
        """Test that pydicom enforces Dataset objects in sequences.
        
        Note: pydicom automatically validates sequence items must be Dataset objects,
        so this test verifies that the framework prevents invalid sequence items.
        """
        dataset = Dataset()
        
        # Try to create sequence with non-Dataset item (should raise TypeError at pydicom level)
        with pytest.raises(TypeError, match="Sequence contents must be 'Dataset' instances"):
            dataset.DeviceSequence = [{"CodeValue": "A-04000"}]  # Dict instead of Dataset
        
        # Verify that valid Dataset objects work fine
        dataset.DeviceSequence = [Dataset()]  # Empty Dataset should work  
        
        result = DeviceValidator.validate(dataset)
        
        # Should have validation errors for missing Code Sequence Macro, but no Dataset type errors
        assert len(result.errors) > 0
        code_errors = [error for error in result.errors if 'Code Value' in error or 'Code Meaning' in error]
        assert len(code_errors) > 0  # Should have code sequence errors
    
    def test_device_item_missing_code_value(self):
        """Test validation error when device item missing Code Value."""
        dataset = Dataset()
        incomplete_item = Dataset()
        incomplete_item.CodingSchemeDesignator = "SRT"
        incomplete_item.CodeMeaning = "Catheter"
        # Missing CodeValue
        dataset.DeviceSequence = [incomplete_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        code_value_errors = [error for error in result.errors if 'Code Value (0008,0100) is required' in error]
        assert len(code_value_errors) == 1
        assert 'device type code' in code_value_errors[0]
    
    def test_device_item_missing_coding_scheme_designator(self):
        """Test validation error when device item missing Coding Scheme Designator."""
        dataset = Dataset()
        incomplete_item = Dataset()
        incomplete_item.CodeValue = "A-04000"
        incomplete_item.CodeMeaning = "Catheter"
        # Missing CodingSchemeDesignator
        dataset.DeviceSequence = [incomplete_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        coding_scheme_errors = [error for error in result.errors if 'Coding Scheme Designator (0008,0102) is required' in error]
        assert len(coding_scheme_errors) == 1
        assert 'SRT' in coding_scheme_errors[0]
    
    def test_device_item_missing_code_meaning(self):
        """Test validation error when device item missing Code Meaning."""
        dataset = Dataset()
        incomplete_item = Dataset()
        incomplete_item.CodeValue = "A-04000"
        incomplete_item.CodingSchemeDesignator = "SRT"
        # Missing CodeMeaning
        dataset.DeviceSequence = [incomplete_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        code_meaning_errors = [error for error in result.errors if 'Code Meaning (0008,0104) is required' in error]
        assert len(code_meaning_errors) == 1
        assert 'human-readable device description' in code_meaning_errors[0]
    
    def test_device_item_missing_all_code_attributes(self):
        """Test validation errors when device item missing all Code Sequence Macro attributes."""
        dataset = Dataset()
        incomplete_item = Dataset()
        # Missing all Code Sequence Macro attributes
        dataset.DeviceSequence = [incomplete_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) >= 3  # Should have all three code sequence errors
        code_value_errors = [error for error in result.errors if 'Code Value (0008,0100) is required' in error]
        coding_scheme_errors = [error for error in result.errors if 'Coding Scheme Designator (0008,0102) is required' in error]
        code_meaning_errors = [error for error in result.errors if 'Code Meaning (0008,0104) is required' in error]
        
        assert len(code_value_errors) == 1
        assert len(coding_scheme_errors) == 1
        assert len(code_meaning_errors) == 1
    
    def test_valid_device_item_structure_passes_validation(self):
        """Test that valid device item structure passes validation."""
        dataset = Dataset()
        dataset.DeviceSequence = [self._create_valid_device_item()]
        
        result = DeviceValidator.validate(dataset)
        
        # Should have no Code Sequence Macro errors
        code_errors = [error for error in result.errors if 'Code Value' in error or 'Coding Scheme' in error or 'Code Meaning' in error]
        assert len(code_errors) == 0
    
    def _create_valid_device_item(self) -> Dataset:
        """Create a valid device item for testing."""
        item = Dataset()
        item.CodeValue = "A-04000"
        item.CodingSchemeDesignator = "SRT"
        item.CodeMeaning = "Catheter"
        return item


class TestErrorMessageQuality:
    """Test quality and specificity of validation error messages."""
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        incomplete_item = Dataset()
        incomplete_item.DeviceDiameter = "2.5"
        # Missing Code Sequence Macro attributes and DeviceDiameterUnits
        dataset.DeviceSequence = [incomplete_item]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        # Check that error messages include DICOM tags
        tag_errors = [error for error in result.errors if '(' in error and ')' in error]
        assert len(tag_errors) > 0
        
        # Verify specific tags are mentioned
        code_value_errors = [error for error in result.errors if '(0008,0100)' in error]
        diameter_errors = [error for error in result.errors if '(0050,0016)' in error or '(0050,0017)' in error]
        assert len(code_value_errors) > 0
        assert len(diameter_errors) > 0
    
    def test_error_messages_include_type_information(self):
        """Test that error messages include DICOM type information."""
        dataset = Dataset()
        incomplete_item = Dataset()
        incomplete_item.DeviceDiameter = "2.5"
        # Missing DeviceDiameterUnits (Type 2C)
        dataset.DeviceSequence = [incomplete_item]
        
        result = DeviceValidator.validate(dataset)
        
        # Check that error messages include Type information
        type_errors = [error for error in result.errors if 'Type 1' in error or 'Type 2C' in error]
        assert len(type_errors) > 0
    
    def test_error_messages_include_location_context(self):
        """Test that error messages include specific location context for sequence items."""
        dataset = Dataset()
        
        # Multiple devices with different errors
        device1 = self._create_valid_device_item()  # Valid
        device2 = Dataset()  # Missing all code attributes
        device3 = self._create_valid_device_item()
        device3.DeviceDiameter = "2.5"  # Missing units
        
        dataset.DeviceSequence = [device1, device2, device3]
        
        result = DeviceValidator.validate(dataset)
        
        # Check that error messages include item-specific location context
        item1_errors = [error for error in result.errors if 'Device Sequence item 1' in error]
        item2_errors = [error for error in result.errors if 'Device Sequence item 2' in error]
        assert len(item1_errors) > 0  # Should have errors for item 1
        assert len(item2_errors) > 0  # Should have errors for item 2
    
    def test_error_messages_provide_guidance(self):
        """Test that error messages provide helpful guidance for resolution."""
        dataset = Dataset()
        
        result = DeviceValidator.validate(dataset)  # Missing Device Sequence
        
        # Check that error messages provide guidance
        guidance_errors = [error for error in result.errors if 
                          'DeviceModule.from_required_elements()' in error or 
                          'DeviceModule.create_device_item()' in error]
        assert len(guidance_errors) > 0
    
    def test_enumerated_value_errors_list_valid_options(self):
        """Test that enumerated value errors list all valid options."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "INVALID"
        dataset.DeviceSequence = [device_item]
        
        result = DeviceValidator.validate(dataset)
        
        enum_errors = [error for error in result.errors if 'invalid value' in error]
        assert len(enum_errors) == 1
        
        # Check that all valid units are listed
        error_message = enum_errors[0]
        assert 'FR' in error_message
        assert 'GA' in error_message
        assert 'IN' in error_message
        assert 'MM' in error_message
        # Check that meanings are provided
        assert 'French' in error_message
        assert 'Gauge' in error_message
        assert 'Inch' in error_message
        assert 'Millimeter' in error_message
    
    def _create_valid_device_item(self) -> Dataset:
        """Create a valid device item for testing."""
        item = Dataset()
        item.CodeValue = "A-04000"
        item.CodingSchemeDesignator = "SRT"
        item.CodeMeaning = "Catheter"
        return item


class TestComplexScenarios:
    """Test complex validation scenarios with multiple devices and various configurations."""
    
    def test_multiple_valid_devices_pass_validation(self):
        """Test validation with multiple valid devices of different types."""
        dataset = Dataset()
        
        # Catheter with diameter and units
        catheter = self._create_device_item("A-04000", "SRT", "Catheter")
        catheter.Manufacturer = "ACME Medical"
        catheter.DeviceDiameter = "3.0"
        catheter.DeviceDiameterUnits = "FR"
        catheter.DeviceLength = "100.0"
        catheter.DeviceDescription = "Diagnostic catheter"
        
        # Guidewire with different units
        guidewire = self._create_device_item("A-04010", "SRT", "Guidewire")
        guidewire.DeviceDiameter = "0.014"
        guidewire.DeviceDiameterUnits = "IN"
        guidewire.DeviceID = "GW-001"
        
        # Marker without diameter (no conditional requirement)
        marker = self._create_device_item("A-04020", "SRT", "Marker")
        marker.DeviceVolume = "0.1"
        marker.InterMarkerDistance = "10.0"
        
        dataset.DeviceSequence = [catheter, guidewire, marker]
        
        result = DeviceValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_mixed_valid_and_invalid_devices(self):
        """Test validation with mix of valid and invalid devices."""
        dataset = Dataset()
        
        # Valid device
        valid_device = self._create_device_item("A-04000", "SRT", "Catheter")
        valid_device.DeviceDiameter = "3.0"
        valid_device.DeviceDiameterUnits = "FR"
        
        # Device missing Code Sequence Macro
        invalid_code_device = Dataset()
        invalid_code_device.Manufacturer = "ACME Medical"
        
        # Device with diameter but no units (conditional error)
        invalid_conditional_device = self._create_device_item("A-04010", "SRT", "Guidewire")
        invalid_conditional_device.DeviceDiameter = "2.0"
        # Missing DeviceDiameterUnits
        
        # Device with invalid units (enumerated value error)
        invalid_enum_device = self._create_device_item("A-04020", "SRT", "Marker")
        invalid_enum_device.DeviceDiameter = "1.5"
        invalid_enum_device.DeviceDiameterUnits = "INVALID"
        
        dataset.DeviceSequence = [valid_device, invalid_code_device, invalid_conditional_device, invalid_enum_device]
        
        result = DeviceValidator.validate(dataset)
        
        # Should have multiple errors but categorize them correctly
        assert len(result.errors) > 0
        
        # Check for specific error types
        code_errors = [error for error in result.errors if 'Code Value' in error or 'Code Meaning' in error]
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        enum_errors = [error for error in result.errors if 'invalid value' in error]
        
        assert len(code_errors) >= 2  # Item 1 missing code attributes
        assert len(conditional_errors) == 1  # Item 2 conditional error
        assert len(enum_errors) == 1  # Item 3 enumerated value error
    
    def test_comprehensive_device_with_all_optional_attributes(self):
        """Test validation with a device containing all optional attributes."""
        dataset = Dataset()
        
        comprehensive_device = self._create_device_item("A-04000", "SRT", "Comprehensive Device")
        comprehensive_device.Manufacturer = "ACME Medical Corporation"
        comprehensive_device.ManufacturerModelName = "Model X-2000"
        comprehensive_device.DeviceSerialNumber = "SN123456789"
        comprehensive_device.DeviceID = "DEVICE-001"
        comprehensive_device.DeviceLength = "150.5"
        comprehensive_device.DeviceDiameter = "4.2"
        comprehensive_device.DeviceDiameterUnits = "MM"
        comprehensive_device.DeviceVolume = "2.5"
        comprehensive_device.InterMarkerDistance = "12.7"
        comprehensive_device.DeviceDescription = "Advanced diagnostic and therapeutic device"
        comprehensive_device.DateOfManufacture = "20240101"
        
        dataset.DeviceSequence = [comprehensive_device]
        
        result = DeviceValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_edge_case_empty_string_attributes(self):
        """Test validation behavior with empty string attributes."""
        dataset = Dataset()
        
        device_with_empty_strings = self._create_device_item("A-04000", "SRT", "Test Device")
        device_with_empty_strings.Manufacturer = ""  # Empty string (should be fine)
        device_with_empty_strings.DeviceDiameter = "2.5"
        device_with_empty_strings.DeviceDiameterUnits = ""  # Empty string (should trigger conditional error)
        
        dataset.DeviceSequence = [device_with_empty_strings]
        
        result = DeviceValidator.validate(dataset)
        
        # Should have conditional requirement error for empty diameter units
        conditional_errors = [error for error in result.errors if 'Type 2C' in error]
        assert len(conditional_errors) == 1
    
    def test_performance_with_many_devices(self):
        """Test validation performance with many devices."""
        dataset = Dataset()
        
        # Create 50 valid devices
        devices = []
        for i in range(50):
            device = self._create_device_item(f"A-{4000+i:05d}", "SRT", f"Device {i}")
            if i % 3 == 0:  # Add diameter to every third device
                device.DeviceDiameter = f"{2.0 + i * 0.1}"
                device.DeviceDiameterUnits = "MM"
            devices.append(device)
        
        dataset.DeviceSequence = devices
        
        result = DeviceValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def _create_device_item(self, code_value: str, coding_scheme: str, code_meaning: str) -> Dataset:
        """Create a device item with specified Code Sequence Macro attributes."""
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme
        item.CodeMeaning = code_meaning
        return item


class TestRealWorldScenarios:
    """Test real-world usage scenarios for Device Module validation."""
    
    def test_cardiac_catheterization_devices(self):
        """Test validation of typical cardiac catheterization devices."""
        dataset = Dataset()
        
        # Diagnostic catheter
        diagnostic_catheter = self._create_device_item("A-04000", "SRT", "Catheter")
        diagnostic_catheter.Manufacturer = "Boston Scientific"
        diagnostic_catheter.ManufacturerModelName = "Judkins Right 4"
        diagnostic_catheter.DeviceDiameter = "6.0"
        diagnostic_catheter.DeviceDiameterUnits = "FR"
        diagnostic_catheter.DeviceLength = "100.0"
        
        # Guidewire
        guidewire = self._create_device_item("A-04010", "SRT", "Guidewire")
        guidewire.Manufacturer = "Abbott Vascular"
        guidewire.DeviceDiameter = "0.014"
        guidewire.DeviceDiameterUnits = "IN"
        guidewire.DeviceLength = "180.0"
        
        # Balloon catheter
        balloon = self._create_device_item("A-04030", "SRT", "Balloon catheter")
        balloon.DeviceDiameter = "3.0"
        balloon.DeviceDiameterUnits = "MM"
        balloon.DeviceLength = "20.0"
        
        dataset.DeviceSequence = [diagnostic_catheter, guidewire, balloon]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_radiation_therapy_devices(self):
        """Test validation of radiation therapy positioning devices."""
        dataset = Dataset()
        
        # Immobilization mask
        mask = self._create_device_item("A-05000", "SRT", "Immobilization mask")
        mask.Manufacturer = "Orfit Industries"
        mask.DeviceID = "MASK-001"
        mask.DeviceDescription = "Thermoplastic immobilization mask"
        
        # Positioning markers
        marker = self._create_device_item("A-05010", "SRT", "Fiducial marker")
        marker.DeviceDiameter = "1.0"
        marker.DeviceDiameterUnits = "MM"
        marker.DeviceVolume = "0.5"
        marker.InterMarkerDistance = "5.0"
        
        dataset.DeviceSequence = [mask, marker]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def test_surgical_instruments(self):
        """Test validation of surgical instruments."""
        dataset = Dataset()
        
        # Surgical probe
        probe = self._create_device_item("A-06000", "SRT", "Surgical probe")
        probe.DeviceLength = "25.0"
        probe.DeviceDiameter = "2.0"
        probe.DeviceDiameterUnits = "MM"
        probe.DeviceSerialNumber = "SP-2024-001"
        
        dataset.DeviceSequence = [probe]
        
        result = DeviceValidator.validate(dataset)
        
        assert len(result.errors) == 0
    
    def _create_device_item(self, code_value: str, coding_scheme: str, code_meaning: str) -> Dataset:
        """Create a device item with specified Code Sequence Macro attributes."""
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme
        item.CodeMeaning = code_meaning
        return item


class TestGranularValidationMethods:
    """Test new granular validation methods with Dataset and BaseModule support."""

    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements with pydicom Dataset."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_required_elements_with_base_module(self):
        """Test validate_required_elements with BaseModule instance."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter"
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)

        result = DeviceValidator.validate_required_elements(device_module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_required_elements_missing_sequence_dataset(self):
        """Test validate_required_elements with missing sequence in Dataset."""
        dataset = Dataset()
        # Missing DeviceSequence

        result = DeviceValidator.validate_required_elements(dataset)

        assert len(result.errors) > 0
        assert any("Device Sequence (0050,0010) is required (Type 1)" in error for error in result.errors)

    def test_validate_required_elements_missing_sequence_base_module(self):
        """Test validate_required_elements with missing sequence in BaseModule."""
        device_module = DeviceModule()  # Empty module

        result = DeviceValidator.validate_required_elements(device_module)

        assert len(result.errors) > 0
        assert any("Device Sequence (0050,0010) is required (Type 1)" in error for error in result.errors)

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements with pydicom Dataset."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "MM"
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate_conditional_requirements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_conditional_requirements_with_base_module(self):
        """Test validate_conditional_requirements with BaseModule instance."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter",
                device_diameter=2.5,
                device_diameter_units="MM"
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)

        result = DeviceValidator.validate_conditional_requirements(device_module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_conditional_requirements_failure_dataset(self):
        """Test validate_conditional_requirements failure with Dataset."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        # Missing DeviceDiameterUnits
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate_conditional_requirements(dataset)

        assert len(result.errors) > 0
        assert any("Device Diameter Units (0050,0017) is required" in error for error in result.errors)
        assert any("Type 2C" in error for error in result.errors)

    def test_validate_conditional_requirements_failure_base_module(self):
        """Test validate_conditional_requirements failure with BaseModule."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter",
                device_diameter=2.5  # Missing units
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)

        result = DeviceValidator.validate_conditional_requirements(device_module)

        assert len(result.errors) > 0
        assert any("Device Diameter Units (0050,0017) is required" in error for error in result.errors)
        assert any("Type 2C" in error for error in result.errors)

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values with pydicom Dataset."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "MM"
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_enumerated_values_with_base_module(self):
        """Test validate_enumerated_values with BaseModule instance."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter",
                device_diameter=2.5,
                device_diameter_units="MM"
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)

        result = DeviceValidator.validate_enumerated_values(device_module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_enumerated_values_failure_dataset(self):
        """Test validate_enumerated_values failure with Dataset."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "INVALID"
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate_enumerated_values(dataset)

        assert len(result.errors) > 0
        assert any("invalid value 'INVALID'" in error for error in result.errors)
        assert any("Valid values are: FR, GA, IN, MM" in error for error in result.errors)

    def test_validate_enumerated_values_failure_base_module(self):
        """Test validate_enumerated_values failure with BaseModule."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter",
                device_diameter=2.5,
                device_diameter_units="MM"
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)
        # Modify to invalid units
        device_module.DeviceSequence[0].DeviceDiameterUnits = "INVALID"

        result = DeviceValidator.validate_enumerated_values(device_module)

        assert len(result.errors) > 0
        assert any("invalid value 'INVALID'" in error for error in result.errors)
        assert any("Valid values are: FR, GA, IN, MM" in error for error in result.errors)

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures with pydicom Dataset."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_sequence_structures_with_base_module(self):
        """Test validate_sequence_structures with BaseModule instance."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter"
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)

        result = DeviceValidator.validate_sequence_structures(device_module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_sequence_structures_failure_dataset(self):
        """Test validate_sequence_structures failure with Dataset."""
        dataset = Dataset()
        device_item = Dataset()  # Missing Code Sequence Macro attributes
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate_sequence_structures(dataset)

        assert len(result.errors) >= 3  # Should have all three code sequence errors
        assert any("Code Value (0008,0100) is required" in error for error in result.errors)
        assert any("Coding Scheme Designator (0008,0102) is required" in error for error in result.errors)
        assert any("Code Meaning (0008,0104) is required" in error for error in result.errors)

    def test_validate_sequence_structures_failure_base_module(self):
        """Test validate_sequence_structures failure with BaseModule."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter"
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)
        # Remove required code attributes
        del device_module.DeviceSequence[0].CodeValue
        del device_module.DeviceSequence[0].CodeMeaning

        result = DeviceValidator.validate_sequence_structures(device_module)

        assert len(result.errors) >= 2
        assert any("Code Value (0008,0100) is required" in error for error in result.errors)
        assert any("Code Meaning (0008,0104) is required" in error for error in result.errors)

    def test_main_validate_method_with_dataset(self):
        """Test main validate method with pydicom Dataset."""
        dataset = Dataset()
        device_item = self._create_valid_device_item()
        device_item.DeviceDiameter = "2.5"
        device_item.DeviceDiameterUnits = "MM"
        dataset.DeviceSequence = [device_item]

        result = DeviceValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_main_validate_method_with_base_module(self):
        """Test main validate method with BaseModule instance."""
        device_sequence = [
            DeviceModule.create_device_item(
                code_value="A-04000",
                coding_scheme_designator="SRT",
                code_meaning="Catheter",
                device_diameter=2.5,
                device_diameter_units="MM"
            )
        ]

        device_module = DeviceModule.from_required_elements(device_sequence=device_sequence)

        result = DeviceValidator.validate(device_module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def _create_valid_device_item(self) -> Dataset:
        """Create a valid device item for testing."""
        item = Dataset()
        item.CodeValue = "A-04000"
        item.CodingSchemeDesignator = "SRT"
        item.CodeMeaning = "Catheter"
        return item