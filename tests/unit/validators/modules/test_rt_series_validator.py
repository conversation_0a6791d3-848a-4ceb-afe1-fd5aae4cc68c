"""
Test RT Series Module Validator functionality.

Comprehensive unit tests validating all Type 1, Type 2, Type 3, and conditional
requirements including RT-specific modality constraints, sequence validation,
and cross-field consistency checks. All tests use datasets generated from modules
via to_dataset() method per the composition-based architecture requirements.
"""
import pytest
import pydicom
from pydicom import Dataset
from pyrt_dicom.modules import RTSeriesModule
from pyrt_dicom.validators.modules.rt_series_validator import RTSeriesValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.series_enums import Modality


class TestRTSeriesValidatorBasic:
    """Test basic RT Series validator functionality."""
    
    def test_validate_method_exists(self):
        """Test that validate method exists and is callable."""
        assert hasattr(RTSeriesValidator, 'validate')
        assert callable(RTSeriesValidator.validate)
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns ValidationResult instance."""
        dataset = Dataset()
        result = RTSeriesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_with_none_config(self):
        """Test that validation works with None config."""
        dataset = Dataset()
        result = RTSeriesValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
    
    def test_validate_with_custom_config(self):
        """Test validation with custom configuration."""
        dataset = Dataset()
        config = ValidationConfig(
            validate_required_elements=True,
            check_enumerated_values=True,
            validate_sequences=True,
            validate_conditional_requirements=True
        )
        
        result = RTSeriesValidator.validate(dataset, config)
        assert isinstance(result, ValidationResult)


class TestType1RequiredElements:
    """Test Type 1 (required) element validation."""
    
    def test_missing_modality_error(self):
        """Test that missing Modality generates error."""
        dataset = Dataset()
        # Missing Modality
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        modality_errors = [error for error in result.errors if "Modality (0008,0060)" in error]
        assert len(modality_errors) == 1
        assert "Type 1" in modality_errors[0]
        assert "RTIMAGE, RTDOSE, RTSTRUCT, RTPLAN, RTRECORD" in modality_errors[0]
    
    def test_empty_modality_error(self):
        """Test that empty Modality generates error."""
        dataset = Dataset()
        dataset.Modality = ""  # Empty value
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        modality_errors = [error for error in result.errors if "Modality (0008,0060)" in error]
        assert len(modality_errors) == 1
    
    def test_missing_series_instance_uid_error(self):
        """Test that missing Series Instance UID generates error."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        # Missing SeriesInstanceUID
        dataset.OperatorsName = "Tech^John"
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        uid_errors = [error for error in result.errors if "Series Instance UID (0020,000E)" in error]
        assert len(uid_errors) == 1
        assert "Type 1" in uid_errors[0]
        assert "Query/Retrieve" in uid_errors[0]
    
    def test_empty_series_instance_uid_error(self):
        """Test that empty Series Instance UID generates error."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = ""  # Empty value
        dataset.OperatorsName = "Tech^John"
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        uid_errors = [error for error in result.errors if "Series Instance UID (0020,000E)" in error]
        assert len(uid_errors) == 1


class TestType2RequiredElements:
    """Test Type 2 (required but may be empty) element validation."""
    
    def test_missing_operators_name_error(self):
        """Test that missing Operators' Name generates error."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        # Missing OperatorsName
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        operators_errors = [error for error in result.errors if "Operators' Name (0008,1070)" in error]
        assert len(operators_errors) == 1
        assert "Type 2" in operators_errors[0]
        assert "RT workflow tracking" in operators_errors[0]
    
    def test_empty_operators_name_allowed(self):
        """Test that empty Operators' Name is allowed (Type 2)."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = ""  # Empty but present
        
        result = RTSeriesValidator.validate(dataset)
        
        # Should not have errors for empty OperatorsName
        operators_errors = [error for error in result.errors if "Operators' Name (0008,1070)" in error]
        assert len(operators_errors) == 0


class TestEnumeratedValues:
    """Test enumerated value validation."""
    
    def test_valid_rt_modalities_pass(self):
        """Test that all valid RT modalities pass validation."""
        valid_modalities = ["RTIMAGE", "RTDOSE", "RTSTRUCT", "RTPLAN", "RTRECORD"]
        
        for modality in valid_modalities:
            dataset = Dataset()
            dataset.Modality = modality
            dataset.SeriesInstanceUID = "*******.5"
            dataset.OperatorsName = "Tech^John"
            
            result = RTSeriesValidator.validate(dataset)
            
            # Should not have modality enumeration errors
            modality_enum_errors = [error for error in result.errors 
                                  if "invalid value" in error and "Modality" in error]
            assert len(modality_enum_errors) == 0, f"Failed for modality: {modality}"
    
    def test_invalid_modality_error(self):
        """Test that invalid modality generates error."""
        dataset = Dataset()
        dataset.Modality = "CT"  # Invalid for RT Series
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        modality_errors = [error for error in result.errors if "invalid value 'CT'" in error]
        assert len(modality_errors) == 1
        assert "RTIMAGE, RTDOSE, RTSTRUCT, RTPLAN, RTRECORD" in modality_errors[0]
        assert "IOD-specific requirements" in modality_errors[0]
    
    def test_enumerated_values_config_disabled(self):
        """Test that enumerated value validation can be disabled."""
        dataset = Dataset()
        dataset.Modality = "CT"  # Invalid for RT Series
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"
        
        config = ValidationConfig(check_enumerated_values=False)
        result = RTSeriesValidator.validate(dataset, config)
        
        # Should not have enumerated value errors when disabled
        modality_enum_errors = [error for error in result.errors 
                              if "invalid value" in error and "Modality" in error]
        assert len(modality_enum_errors) == 0


class TestSequenceValidation:
    """Test sequence structure validation."""
    
    def test_series_description_code_sequence_single_item_valid(self):
        """Test that single item in Series Description Code Sequence is valid."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"
        
        code_item = Dataset()
        code_item.CodeValue = "113014"
        code_item.CodingSchemeDesignator = "DCM"
        code_item.CodeMeaning = "Dose"
        dataset.SeriesDescriptionCodeSequence = [code_item]
        
        result = RTSeriesValidator.validate(dataset)
        
        # Should not have sequence cardinality errors
        sequence_errors = [error for error in result.errors 
                         if "Series Description Code Sequence" in error and "single Item" in error]
        assert len(sequence_errors) == 0
    
    def test_series_description_code_sequence_multiple_items_error(self):
        """Test that multiple items in Series Description Code Sequence generate error."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"
        
        code_item1 = Dataset()
        code_item1.CodeValue = "113014"
        code_item1.CodingSchemeDesignator = "DCM"
        code_item1.CodeMeaning = "Dose"
        
        code_item2 = Dataset()
        code_item2.CodeValue = "113015"
        code_item2.CodingSchemeDesignator = "DCM"
        code_item2.CodeMeaning = "Plan"
        
        dataset.SeriesDescriptionCodeSequence = [code_item1, code_item2]
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) >= 1
        sequence_errors = [error for error in result.errors 
                         if "Series Description Code Sequence" in error and "single Item" in error]
        assert len(sequence_errors) == 1
        assert "found 2 items" in sequence_errors[0]


class TestValidDataScenarios:
    """Test validation with valid data scenarios."""
    
    def test_minimal_valid_rt_series_passes(self):
        """Test that minimal valid RT Series passes validation."""
        rt_series = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE,
            series_instance_uid="*******.*******.9.10",
            operators_name="Tech^John"
        )
        dataset = rt_series.to_dataset()
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert result.is_valid is True
    
    def test_comprehensive_rt_series_passes(self):
        """Test that comprehensive RT Series with all elements passes validation."""
        rt_series = RTSeriesModule.from_required_elements(
            modality=Modality.RTDOSE,
            series_instance_uid="*******.*******.9.10",
            operators_name="Tech^John"
        ).with_optional_elements(
            series_number=1,
            series_date="20240101",
            series_time="120000",
            series_description="RT Dose Distribution"
        ).with_rt_specific_elements(
            treatment_session_uid="*******.*******.9.12"
        )
        dataset = rt_series.to_dataset()
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert result.is_valid is True


class TestErrorMessageQuality:
    """Test quality and specificity of validation error messages."""
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()  # Empty dataset to trigger multiple errors

        result = RTSeriesValidator.validate(dataset)

        assert len(result.errors) > 0
        for error in result.errors:
            error_msg = str(error)
            # Each error should contain a DICOM tag reference in parentheses
            assert "(" in error_msg and ")" in error_msg
            # Should contain a tag pattern like (0008,0060)
            import re
            tag_pattern = r'\([0-9A-Fa-f]{4},[0-9A-Fa-f]{4}\)'
            assert re.search(tag_pattern, error_msg) is not None
    
    def test_error_messages_provide_solutions(self):
        """Test that error messages provide actionable solutions."""
        dataset = Dataset()  # Empty dataset
        
        result = RTSeriesValidator.validate(dataset)
        
        assert len(result.errors) > 0
        for error in result.errors:
            error_msg = str(error)
            # Each error should provide guidance on how to fix it
            assert "Solution:" in error_msg or "Set " in error_msg
    
    def test_error_messages_explain_context(self):
        """Test that error messages explain RT-specific context."""
        dataset = Dataset()
        dataset.Modality = "CT"  # Invalid for RT
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"
        
        result = RTSeriesValidator.validate(dataset)
        
        modality_errors = [error for error in result.errors if "invalid value 'CT'" in error]
        assert len(modality_errors) == 1
        # Should explain RT-specific context
        assert "RT Series" in modality_errors[0]
        assert "IOD" in modality_errors[0]


class TestConditionalRequirements:
    """Test conditional requirements and cross-field validation."""

    def test_operator_identification_sequence_correspondence_valid(self):
        """Test that operator identification sequence corresponds to operators' name."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John\\Tech^Jane"  # Two operators backslash-separated

        # Create corresponding operator identification items
        op1_item = Dataset()
        op1_item.PersonIdentificationCodeSequence = []
        op1_item.PersonAddress = "123 Main St"

        op2_item = Dataset()
        op2_item.PersonIdentificationCodeSequence = []
        op2_item.PersonAddress = "456 Oak Ave"

        dataset.OperatorIdentificationSequence = [op1_item, op2_item]

        result = RTSeriesValidator.validate(dataset)

        # Should not have correspondence warnings
        correspondence_warnings = [warning for warning in result.warnings
                                 if "correspondence" in warning]
        assert len(correspondence_warnings) == 0

    def test_operator_identification_sequence_correspondence_warning(self):
        """Test warning when operator identification sequence doesn't correspond to operators' name."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"  # One operator

        # Create two operator identification items (mismatch)
        op1_item = Dataset()
        op1_item.PersonIdentificationCodeSequence = []

        op2_item = Dataset()
        op2_item.PersonIdentificationCodeSequence = []

        dataset.OperatorIdentificationSequence = [op1_item, op2_item]

        result = RTSeriesValidator.validate(dataset)

        assert len(result.warnings) >= 1
        correspondence_warnings = [warning for warning in result.warnings
                                 if "correspondence" in warning]
        assert len(correspondence_warnings) == 1
        assert "Number of sequence items (2)" in correspondence_warnings[0]
        assert "number of Operators' Name values (1)" in correspondence_warnings[0]

    def test_sequence_validation_config_disabled(self):
        """Test that sequence validation can be disabled."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"

        # Add invalid sequence (multiple items)
        code_item1 = Dataset()
        code_item2 = Dataset()
        dataset.SeriesDescriptionCodeSequence = [code_item1, code_item2]

        config = ValidationConfig(validate_sequences=False)
        result = RTSeriesValidator.validate(dataset, config)

        # Should not have sequence validation errors when disabled
        sequence_errors = [error for error in result.errors
                         if "Series Description Code Sequence" in error]
        assert len(sequence_errors) == 0


class TestCodeSequenceMacroValidation:
    """Test Code Sequence Macro validation in sequence items."""

    def test_valid_code_sequence_item_passes(self):
        """Test that valid code sequence item passes validation."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"

        code_item = Dataset()
        code_item.CodeValue = "113014"
        code_item.CodingSchemeDesignator = "DCM"
        code_item.CodeMeaning = "Dose"
        dataset.SeriesDescriptionCodeSequence = [code_item]

        result = RTSeriesValidator.validate(dataset)

        # Should not have code sequence macro errors
        code_errors = [error for error in result.errors
                      if "Code Value" in error or "Coding Scheme" in error or "Code Meaning" in error]
        assert len(code_errors) == 0

    def test_missing_code_value_error(self):
        """Test that missing Code Value generates error."""
        dataset = Dataset()
        dataset.Modality = "RTDOSE"
        dataset.SeriesInstanceUID = "*******.5"
        dataset.OperatorsName = "Tech^John"

        code_item = Dataset()
        # Missing CodeValue
        code_item.CodingSchemeDesignator = "DCM"
        code_item.CodeMeaning = "Dose"
        dataset.SeriesDescriptionCodeSequence = [code_item]

        result = RTSeriesValidator.validate(dataset)

        assert len(result.errors) >= 1
        code_value_errors = [error for error in result.errors if "Code Value (0008,0100)" in error]
        assert len(code_value_errors) == 1
        assert "required in Code Sequence Macro" in code_value_errors[0]


class TestRealWorldScenarios:
    """Test validation with real-world RT Series scenarios."""

    def test_rt_dose_series_validation(self):
        """Test validation of RT Dose series."""
        rt_series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid="1.2.840.113619.2.55.3.604688119.868.**********.123",
            operators_name="Physicist^Medical^PhD"
        ).with_optional_elements(
            series_number=1,
            series_date="20240101",
            series_time="143000",
            series_description="RT Dose Distribution - Plan 1"
        ).with_rt_specific_elements(
            treatment_session_uid="1.2.840.113619.2.55.3.604688119.868.**********.456"
        )
        dataset = rt_series.to_dataset()

        result = RTSeriesValidator.validate(dataset)

        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert result.is_valid is True

    def test_rt_plan_series_validation(self):
        """Test validation of RT Plan series."""
        rt_series = RTSeriesModule.from_required_elements(
            modality="RTPLAN",
            series_instance_uid="1.2.840.113619.2.55.3.604688119.868.**********.789",
            operators_name="Physicist^Treatment^Planning"
        ).with_optional_elements(
            series_number=2,
            series_description="RT Treatment Plan"
        )
        dataset = rt_series.to_dataset()

        result = RTSeriesValidator.validate(dataset)

        assert len(result.errors) == 0
        assert result.is_valid is True

    def test_rt_struct_series_with_operator_identification(self):
        """Test RT Structure Set series with operator identification."""
        rt_series = RTSeriesModule.from_required_elements(
            modality="RTSTRUCT",
            series_instance_uid="1.2.840.113619.2.55.3.604688119.868.**********.101",
            operators_name="Physician^Radiation^Oncology"
        ).with_operator_identification(
            operator_identification_sequence=[
                RTSeriesModule.create_operator_identification_item(
                    person_address="123 Hospital Drive",
                    person_telephone_numbers=["555-1234"],
                    institution_name="General Hospital",
                    institutional_department_name="Radiation Oncology"
                )
            ]
        )
        dataset = rt_series.to_dataset()

        result = RTSeriesValidator.validate(dataset)

        assert len(result.errors) == 0
        assert result.is_valid is True


class TestValidationResultStructure:
    """Test ValidationResult structure and behavior."""

    def test_validation_result_properties(self):
        """Test ValidationResult has expected properties and methods."""
        dataset = Dataset()
        result = RTSeriesValidator.validate(dataset)

        # Test basic properties
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert hasattr(result, 'is_valid')
        assert hasattr(result, 'has_errors')
        assert hasattr(result, 'error_count')
        assert hasattr(result, 'warning_count')

        # Test property types
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        assert isinstance(result.is_valid, bool)
        assert isinstance(result.has_errors, bool)
        assert isinstance(result.error_count, int)
        assert isinstance(result.warning_count, int)

    def test_validation_result_consistency(self):
        """Test ValidationResult property consistency."""
        dataset = Dataset()  # Empty dataset will have errors
        result = RTSeriesValidator.validate(dataset)

        # Test consistency between properties
        assert result.has_errors == (len(result.errors) > 0)
        assert result.error_count == len(result.errors)
        assert result.warning_count == len(result.warnings)
        assert result.is_valid == (len(result.errors) == 0)

    def test_multiple_validation_errors_captured(self):
        """Test that multiple validation errors are all captured."""
        dataset = Dataset()  # Empty dataset
        # Add some invalid values
        dataset.Modality = "INVALID"

        result = RTSeriesValidator.validate(dataset)

        # Should capture multiple different types of errors
        assert len(result.errors) >= 3  # Missing required elements + invalid modality

        # Check for specific error types
        type1_errors = [error for error in result.errors if "Type 1" in error]
        type2_errors = [error for error in result.errors if "Type 2" in error]
        enum_errors = [error for error in result.errors if "invalid value" in error]

        assert len(type1_errors) >= 1  # SeriesInstanceUID (Modality present but invalid)
        assert len(type2_errors) >= 1  # OperatorsName
        assert len(enum_errors) >= 1   # Invalid modality value
