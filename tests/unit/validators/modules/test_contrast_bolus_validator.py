"""Test ContrastBolusValidator - DICOM PS3.3 C.7.6.4

Tests validator functionality using the composition-based architecture.
Validators receive datasets via module.to_dataset(), not module instances directly.
This ensures proper separation of concerns and architectural compliance.
"""

from pydicom import Dataset

from pyrt_dicom.validators.modules.contrast_bolus_validator import ContrastBolusValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.modules.contrast_bolus_module import ContrastBolusModule
from pyrt_dicom.enums.image_enums import ContrastBolusIngredient


class TestContrastBolusValidator:
    """Test ContrastBolusValidator validation logic.
    
    Tests validator functionality using composition-based architecture where
    validators only receive pydicom Dataset objects, never module instances directly.
    """

    def test_validate_empty_dataset_fails_type2_validation(self):
        """Test that an empty dataset fails Type 2 element validation."""
        dataset = Dataset()
        result = ContrastBolusValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        # Should have error for missing Type 2 element ContrastBolusAgent
        assert len(result.errors) == 1
        assert "Contrast/Bolus Agent (0018,0010) is required (Type 2)" in result.errors[0]

    def test_validate_valid_dataset_from_module_passes(self):
        """Test that a valid dataset from ContrastBolusModule passes validation."""
        module = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0,
            contrast_bolus_volume=100.0,
            contrast_bolus_total_dose=50.0
        )
        
        # CRITICAL: Use dataset from module, not module directly
        dataset = module.to_dataset()
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_dataset_with_empty_type2_element_passes(self):
        """Test that a dataset with empty but present Type 2 elements passes validation."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Empty but present (Type 2 allowed)
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_enumerated_values_valid(self):
        """Test validation of valid enumerated values."""
        config = ValidationConfig(check_enumerated_values=True)
        
        for ingredient in ContrastBolusIngredient:
            dataset = Dataset()
            dataset.ContrastBolusAgent = ""  # Required Type 2 element
            dataset.ContrastBolusIngredient = ingredient.value
            
            result = ContrastBolusValidator.validate(dataset, config)
            assert len(result.errors) == 0

    def test_validate_enumerated_values_invalid(self):
        """Test validation detects invalid enumerated values."""
        config = ValidationConfig(check_enumerated_values=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Add required Type 2 element
        dataset.ContrastBolusIngredient = "INVALID_INGREDIENT"
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) >= 1
        # Find the enumeration error specifically
        enum_errors = [e for e in result.errors if "INVALID_INGREDIENT" in e and "valid defined term" in e]
        assert len(enum_errors) == 1
        assert "Contrast/Bolus Ingredient (0018,1048)" in enum_errors[0]
        assert "IODINE, GADOLINIUM, CARBON DIOXIDE, BARIUM" in enum_errors[0]

    def test_validate_flow_consistency_matching_counts(self):
        """Test flow rate and duration with matching counts passes validation."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        dataset.ContrastFlowRate = [5.0, 3.0, 2.0]
        dataset.ContrastFlowDuration = [10.0, 15.0, 20.0]
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.errors) == 0

    def test_validate_flow_consistency_mismatched_counts(self):
        """Test flow rate and duration with mismatched counts fails validation."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        dataset.ContrastFlowRate = [5.0, 3.0]
        dataset.ContrastFlowDuration = [10.0, 15.0, 20.0]
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Contrast Flow Rate (0018,1046) and Contrast Flow Duration (0018,1047)" in result.errors[0]
        assert "same number of values" in result.errors[0]
        assert "Flow Rate has 2 values" in result.errors[0]
        assert "Flow Duration has 3 values" in result.errors[0]

    def test_validate_flow_consistency_one_present(self):
        """Test validation passes when only one of flow rate or duration is present."""
        # Only flow rate present
        dataset1 = Dataset()
        dataset1.ContrastBolusAgent = ""  # Required Type 2 element
        dataset1.ContrastFlowRate = [5.0, 3.0]
        result1 = ContrastBolusValidator.validate(dataset1)
        assert len(result1.errors) == 0
        
        # Only flow duration present  
        dataset2 = Dataset()
        dataset2.ContrastBolusAgent = ""  # Required Type 2 element
        dataset2.ContrastFlowDuration = [10.0, 15.0]
        result2 = ContrastBolusValidator.validate(dataset2)
        assert len(result2.errors) == 0

    def test_validate_administration_route_sequence_single_item(self):
        """Test validation passes with single administration route item."""
        config = ValidationConfig(validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        route_item = Dataset()
        route_item.CodeValue = "47625008"
        route_item.CodingSchemeDesignator = "SCT"
        route_item.CodeMeaning = "Intravenous route"
        
        dataset.ContrastBolusAdministrationRouteSequence = [route_item]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) == 0

    def test_validate_administration_route_sequence_multiple_items_error(self):
        """Test validation fails with multiple administration route items."""
        config = ValidationConfig(validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        route_item1 = Dataset()
        route_item1.CodeValue = "47625008"
        route_item1.CodingSchemeDesignator = "SCT"
        route_item1.CodeMeaning = "Intravenous route"
        
        route_item2 = Dataset()
        route_item2.CodeValue = "78421000"
        route_item2.CodingSchemeDesignator = "SCT"
        route_item2.CodeMeaning = "Intramuscular route"
        
        dataset.ContrastBolusAdministrationRouteSequence = [route_item1, route_item2]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Administration Route Sequence (0018,0014)" in result.errors[0]
        assert "should contain only a single Item" in result.errors[0]
        assert "Found 2 items" in result.errors[0]

    def test_validate_code_sequence_item_valid(self):
        """Test validation of valid code sequence items."""
        config = ValidationConfig(validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        agent_item = Dataset()
        agent_item.CodeValue = "C-B0322"
        agent_item.CodingSchemeDesignator = "SRT"
        agent_item.CodeMeaning = "Iodinated contrast agent"
        
        dataset.ContrastBolusAgentSequence = [agent_item]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) == 0

    def test_validate_code_sequence_item_missing_code_value(self):
        """Test validation detects missing Code Value."""
        config = ValidationConfig(validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        agent_item = Dataset()
        agent_item.CodingSchemeDesignator = "SRT"
        agent_item.CodeMeaning = "Iodinated contrast agent"
        # Missing CodeValue
        
        dataset.ContrastBolusAgentSequence = [agent_item]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Code Value (0008,0100) is required (Type 1)" in result.errors[0]
        assert "Contrast/Bolus Agent Sequence item 1" in result.errors[0]

    def test_validate_code_sequence_item_missing_coding_scheme(self):
        """Test validation detects missing Coding Scheme Designator."""
        config = ValidationConfig(validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        agent_item = Dataset()
        agent_item.CodeValue = "C-B0322"
        agent_item.CodeMeaning = "Iodinated contrast agent"
        # Missing CodingSchemeDesignator
        
        dataset.ContrastBolusAgentSequence = [agent_item]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Coding Scheme Designator (0008,0102) is required (Type 1)" in result.errors[0]

    def test_validate_code_sequence_item_missing_code_meaning(self):
        """Test validation detects missing Code Meaning."""
        config = ValidationConfig(validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        agent_item = Dataset()
        agent_item.CodeValue = "C-B0322"
        agent_item.CodingSchemeDesignator = "SRT"
        # Missing CodeMeaning
        
        dataset.ContrastBolusAgentSequence = [agent_item]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) == 1
        assert "Code Meaning (0008,0104) is required (Type 1)" in result.errors[0]
        assert "Code Sequence Macro" in result.errors[0]

    def test_validate_additional_drug_sequence_nested(self):
        """Test validation of nested Additional Drug Sequence."""
        config = ValidationConfig(validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        # Create administration route item with nested additional drug sequence
        route_item = Dataset()
        route_item.CodeValue = "47625008"
        route_item.CodingSchemeDesignator = "SCT"
        route_item.CodeMeaning = "Intravenous route"
        
        drug_item = Dataset()
        drug_item.CodeValue = "387467008"
        drug_item.CodingSchemeDesignator = "SCT"
        drug_item.CodeMeaning = "Saline"
        
        route_item.AdditionalDrugSequence = [drug_item]
        dataset.ContrastBolusAdministrationRouteSequence = [route_item]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        assert len(result.errors) == 0

    def test_validate_timing_consistency_valid_order(self):
        """Test validation passes with valid start/stop time order."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        dataset.ContrastBolusStartTime = "120000"  # 12:00:00
        dataset.ContrastBolusStopTime = "120030"   # 12:00:30
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_timing_consistency_invalid_order(self):
        """Test validation warns about invalid start/stop time order."""
        dataset = Dataset()
        dataset.ContrastBolusStartTime = "120030"  # 12:00:30
        dataset.ContrastBolusStopTime = "120000"   # 12:00:00 (earlier)
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.warnings) == 1
        assert "Contrast/Bolus Start Time (0018,1042) should be before" in result.warnings[0]
        assert "Contrast/Bolus Stop Time (0018,1043)" in result.warnings[0]

    def test_validate_timing_consistency_stop_time_and_duration(self):
        """Test validation warns when both stop time and duration are present."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        dataset.ContrastBolusStopTime = "120030"
        dataset.ContrastFlowDuration = [30.0]
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.warnings) == 1
        assert "Both Contrast/Bolus Stop Time (0018,1043) and Contrast Flow Duration (0018,1047)" in result.warnings[0]
        assert "flow duration is an alternate method" in result.warnings[0]

    def test_validate_module_logical_consistency_flow_mismatch(self):
        """Test logical consistency validation with ContrastBolusModule for flow mismatch."""
        # Create module with matching flow counts first
        module = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_flow_rate=[5.0, 3.0],
            contrast_flow_duration=[20.0, 10.0]
        )
        
        # Create dataset and then modify to create mismatch for validator testing
        dataset = module.to_dataset()
        dataset.ContrastFlowRate = [5.0, 3.0]
        dataset.ContrastFlowDuration = [10.0]  # Mismatch for validator testing
        
        result = ContrastBolusValidator.validate(dataset)
        
        # Flow consistency validator will detect this error
        assert len(result.errors) >= 1
        error_messages = ' '.join(result.errors)
        assert "Flow Rate has 2 values, Flow Duration has 1 values" in error_messages
        assert "must have the same number of values" in error_messages

    def test_validate_module_logical_consistency_dose_volume_mismatch(self):
        """Test logical consistency validation with dose exceeding volume."""
        # Create module with valid values first
        module = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_volume=100.0,
            contrast_bolus_total_dose=50.0
        )
        
        # Create dataset and then modify to create inconsistency for validator testing
        dataset = module.to_dataset()
        dataset.ContrastBolusVolume = 50.0
        dataset.ContrastBolusTotalDose = 100.0  # Exceeds volume for validator testing
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.warnings) == 1
        assert "Total Dose (100.0 ml) should not exceed" in result.warnings[0]
        assert "Volume (50.0 ml)" in result.warnings[0]
        assert "undiluted contrast agent amount" in result.warnings[0]

    def test_validate_module_logical_consistency_ingredient_without_concentration(self):
        """Test validation warns when ingredient is specified without concentration."""
        module = ContrastBolusModule.from_required_elements()
        dataset = module.to_dataset()
        dataset.ContrastBolusIngredient = "IODINE"
        # No concentration specified
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.warnings) == 1
        assert "Ingredient Concentration (0018,1049) is recommended" in result.warnings[0]
        assert "when Contrast/Bolus Ingredient (0018,1048) is specified" in result.warnings[0]

    def test_validate_module_logical_consistency_concentration_without_ingredient(self):
        """Test validation warns when concentration is specified without ingredient."""
        module = ContrastBolusModule.from_required_elements()
        dataset = module.to_dataset()
        dataset.ContrastBolusIngredientConcentration = 370.0
        # No ingredient specified
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.warnings) == 1
        assert "Contrast/Bolus Ingredient (0018,1048) is recommended" in result.warnings[0]
        assert "when Contrast/Bolus Ingredient Concentration (0018,1049) is specified" in result.warnings[0]

    def test_validate_generic_dataset_logical_consistency(self):
        """Test logical consistency validation for generic Dataset objects."""
        dataset = Dataset()
        dataset.ContrastBolusVolume = 50.0
        dataset.ContrastBolusTotalDose = 100.0  # Exceeds volume
        dataset.ContrastBolusIngredient = "IODINE"
        # No concentration
        
        result = ContrastBolusValidator.validate(dataset)
        
        assert len(result.warnings) == 2
        warning_messages = ' '.join(result.warnings)
        assert "Total Dose (100.0 ml) should not exceed" in warning_messages
        assert "Ingredient Concentration (0018,1049) is recommended" in warning_messages

    def test_validate_with_configuration_options(self):
        """Test validation with different configuration options."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        dataset.ContrastBolusIngredient = "INVALID_VALUE"
        
        agent_item = Dataset()
        agent_item.CodeValue = "C-B0322"
        # Missing required fields for testing
        dataset.ContrastBolusAgentSequence = [agent_item]
        
        # Test with enumerated values disabled
        config1 = ValidationConfig(check_enumerated_values=False, validate_sequences=False)
        result1 = ContrastBolusValidator.validate(dataset, config1)
        assert len(result1.errors) == 0  # Should skip enum and sequence validation
        
        # Test with both enabled
        config2 = ValidationConfig(check_enumerated_values=True, validate_sequences=True)
        result2 = ContrastBolusValidator.validate(dataset, config2)
        assert len(result2.errors) >= 2  # Should catch both enum and sequence errors

    def test_validate_dicom_standard_example(self):
        """Test validation against the DICOM standard example."""
        # Example: 100 ml injection of 76% Diatrizoate diluted 1:1
        module = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="76% Diatrizoate"
        ).with_optional_elements(
            contrast_bolus_volume=100.0,           # 100 ml injection
            contrast_bolus_total_dose=50.0,        # 50 ml undiluted (diluted 1:1)
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0  # 370mg/ml
        )
        
        dataset = module.to_dataset()
        result = ContrastBolusValidator.validate(dataset)
        
        # Should pass validation with no errors or warnings
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_validate_comprehensive_module_with_sequences(self):
        """Test validation of a comprehensive module with all sequence types."""
        config = ValidationConfig(validate_sequences=True, check_enumerated_values=True)
        
        module = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_agent_sequence=[
                ContrastBolusModule.create_contrast_agent_code_item(
                    code_value="C-B0322",
                    coding_scheme_designator="SRT",
                    code_meaning="Iodinated contrast agent"
                )
            ],
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0,
            contrast_bolus_start_time="120000",
            # Don't set stop time to avoid timing warning 
            contrast_bolus_total_dose=50.0,
            contrast_flow_rate=[5.0, 3.0],
            contrast_flow_duration=[15.0, 15.0],  # Matching count
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0
        ).with_administration_route(
            administration_route_sequence=[
                ContrastBolusModule.create_administration_route_code_item(
                    code_value="47625008",
                    coding_scheme_designator="SCT",
                    code_meaning="Intravenous route"
                )
            ],
            additional_drug_sequence=[
                ContrastBolusModule.create_additional_drug_code_item(
                    code_value="387467008",
                    coding_scheme_designator="SCT",
                    code_meaning="Saline"
                )
            ]
        )
        
        result = ContrastBolusValidator.validate(module.to_dataset(), config)
        
        # Comprehensive valid module should pass all validation
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_error_message_contains_dicom_tags(self):
        """Test that error messages contain proper DICOM tag references."""
        config = ValidationConfig(check_enumerated_values=True, validate_sequences=True)
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""  # Required Type 2 element
        
        # Create various validation errors
        dataset.ContrastBolusIngredient = "INVALID"
        dataset.ContrastFlowRate = [5.0]
        dataset.ContrastFlowDuration = [10.0, 15.0]  # Mismatched count
        
        # Invalid sequence item
        agent_item = Dataset()
        # Missing all required fields
        dataset.ContrastBolusAgentSequence = [agent_item]
        
        result = ContrastBolusValidator.validate(dataset, config)
        
        # Check that all error messages contain DICOM tag references
        for error in result.errors:
            assert any(tag in error for tag in ["(0008,", "(0018,"]), f"Error missing DICOM tag: {error}"

    def test_validation_result_structure(self):
        """Test that ValidationResult is properly structured."""
        dataset = Dataset()
        result = ContrastBolusValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)


class TestContrastBolusValidatorGranularMethods:
    """Test ContrastBolusValidator granular validation methods with zero-copy support."""

    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements works with Dataset instances."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = "Iodinated contrast agent"

        result = ContrastBolusValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_required_elements_with_module(self):
        """Test validate_required_elements works with BaseModule instances (zero-copy)."""
        module = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        )

        result = ContrastBolusValidator.validate_required_elements(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_required_elements_missing_with_dataset(self):
        """Test validate_required_elements detects missing elements with Dataset."""
        dataset = Dataset()
        # Missing ContrastBolusAgent

        result = ContrastBolusValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Contrast/Bolus Agent (0018,0010) is required (Type 2)" in result.errors[0]

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements works with Dataset instances."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""

        result = ContrastBolusValidator.validate_conditional_requirements(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # No complex conditionals in this module

    def test_validate_conditional_requirements_with_module(self):
        """Test validate_conditional_requirements works with BaseModule instances."""
        module = ContrastBolusModule.from_required_elements()

        result = ContrastBolusValidator.validate_conditional_requirements(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # No complex conditionals in this module

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values works with Dataset instances."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""
        dataset.ContrastBolusIngredient = "IODINE"

        result = ContrastBolusValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_enumerated_values_with_module(self):
        """Test validate_enumerated_values works with BaseModule instances."""
        module = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )

        result = ContrastBolusValidator.validate_enumerated_values(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_enumerated_values_invalid_with_dataset(self):
        """Test validate_enumerated_values detects invalid values with Dataset."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""
        dataset.ContrastBolusIngredient = "INVALID_INGREDIENT"

        result = ContrastBolusValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "INVALID_INGREDIENT" in result.errors[0]
        assert "valid defined term" in result.errors[0]

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures works with Dataset instances."""
        dataset = Dataset()
        dataset.ContrastBolusAgent = ""

        agent_item = Dataset()
        agent_item.CodeValue = "C-B0322"
        agent_item.CodingSchemeDesignator = "SRT"
        agent_item.CodeMeaning = "Iodinated contrast agent"

        dataset.ContrastBolusAgentSequence = [agent_item]

        result = ContrastBolusValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_sequence_structures_with_module(self):
        """Test validate_sequence_structures works with BaseModule instances."""
        agent_code = ContrastBolusModule.create_contrast_agent_code_item(
            code_value="C-B0322",
            coding_scheme_designator="SRT",
            code_meaning="Iodinated contrast agent"
        )

        module = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_agent_sequence=[agent_code]
        )

        result = ContrastBolusValidator.validate_sequence_structures(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_sequence_structures_invalid_with_module(self):
        """Test validate_sequence_structures detects invalid sequences with BaseModule."""
        from pydicom import Dataset

        # Create invalid sequence item missing required fields
        invalid_agent_code = Dataset()
        # Missing CodeValue, CodingSchemeDesignator, CodeMeaning

        module = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_agent_sequence=[invalid_agent_code]
        )

        result = ContrastBolusValidator.validate_sequence_structures(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        error_messages = ' '.join(result.errors)
        assert "Code Value" in error_messages or "Coding Scheme" in error_messages

    def test_main_validate_method_with_module_zero_copy(self):
        """Test main validate method works with BaseModule instances (zero-copy)."""
        module = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0
        )

        result = ContrastBolusValidator.validate(module)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0