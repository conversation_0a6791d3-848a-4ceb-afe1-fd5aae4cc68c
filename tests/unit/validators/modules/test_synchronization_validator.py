"""
Test SynchronizationValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.7.4.2 Synchronization Module validation.
Tests the composition-based architecture where validators receive dataset parameters.
"""

from pydicom import Dataset
from pyrt_dicom.validators.modules.synchronization_validator import SynchronizationValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.synchronization_enums import (
    SynchronizationTrigger,
    AcquisitionTimeSynchronized,
    TimeDistributionProtocol
)


class TestSynchronizationValidator:
    """Test SynchronizationValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = SynchronizationValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_fails_validation(self):
        """Test that empty dataset fails validation (missing required elements)."""
        dataset = Dataset()
        
        result = SynchronizationValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 3  # Three required Type 1 elements
        
        # Check that all required elements are reported missing
        error_text = ' '.join(result.errors)
        assert "Synchronization Frame of Reference UID (0020,0200) is required" in error_text
        assert "Synchronization Trigger (0018,106A) is required" in error_text
        assert "Acquisition Time Synchronized (0018,1800) is required" in error_text
    
    def test_valid_minimal_dataset_passes_validation(self):
        """Test that valid minimal dataset with all required elements passes validation."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        
        result = SynchronizationValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_complete_dataset_passes_validation(self):
        """Test that complete dataset with all elements passes validation."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.EXTERNAL.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.TriggerSourceOrType = "LINAC_001"
        dataset.TimeSource = "NTP_SERVER_001"
        dataset.TimeDistributionProtocol = TimeDistributionProtocol.NTP.value
        dataset.NTPSourceAddress = "*************"
        dataset.SynchronizationChannel = [1, 2]
        
        result = SynchronizationValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_invalid_uid_format_generates_error(self):
        """Test that invalid UID format generates validation error."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "invalid.uid.format.with.letters"
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        
        result = SynchronizationValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("invalid characters" in error for error in result.errors)
    
    def test_invalid_synchronization_trigger_generates_error(self):
        """Test that invalid synchronization trigger value generates error."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = "INVALID_TRIGGER"
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        
        config = ValidationConfig(check_enumerated_values=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Synchronization Trigger (0018,106A) value 'INVALID_TRIGGER' is invalid" in error 
                  for error in result.errors)
    
    def test_invalid_acquisition_time_synchronized_generates_error(self):
        """Test that invalid acquisition time synchronized value generates error."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = "MAYBE"
        
        config = ValidationConfig(check_enumerated_values=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Acquisition Time Synchronized (0018,1800) value 'MAYBE' is invalid" in error
                  for error in result.errors)
    
    def test_invalid_time_distribution_protocol_generates_warning(self):
        """Test that invalid time distribution protocol generates warning (Type 3)."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.TimeDistributionProtocol = "INVALID_PROTOCOL"
        
        config = ValidationConfig(check_enumerated_values=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        # Should pass overall validation (Type 3 is optional)
        assert result.is_valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("Time Distribution Protocol (0018,1802) value 'INVALID_PROTOCOL' is invalid" in warning
                  for warning in result.warnings)
    
    def test_synchronization_channel_wrong_length_generates_error(self):
        """Test that synchronization channel with wrong length generates error."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.EXTERNAL.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.SynchronizationChannel = [1, 2, 3]  # Should be [M,C] pair only
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Synchronization Channel (0018,106C) must be specified as [M,C] pair with exactly 2 values" 
                  in error for error in result.errors)
    
    def test_synchronization_channel_non_integer_values_generates_error(self):
        """Test that synchronization channel with non-integer values generates error."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.EXTERNAL.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.SynchronizationChannel = [1.5, "2"]  # Should be integers
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Synchronization Channel (0018,106C) values must be integers" in error
                  for error in result.errors)
    
    def test_synchronization_channel_zero_values_generates_error(self):
        """Test that synchronization channel with zero values generates error."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.EXTERNAL.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.SynchronizationChannel = [0, 1]  # Ordinals should start from 1
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Synchronization Channel (0018,106C) values must be positive integers" in error
                  for error in result.errors)
    
    def test_missing_synchronization_channel_generates_warning(self):
        """Test that missing synchronization channel generates informational warning."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.EXTERNAL.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("Synchronization Channel (0018,106C) is conditionally required" in warning
                  for warning in result.warnings)
    
    def test_invalid_ntp_address_generates_warning(self):
        """Test that invalid NTP address format generates warning."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.NTPSourceAddress = "invalid-address"
        
        config = ValidationConfig(check_enumerated_values=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid  # Type 3 field, so overall valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("does not appear to be a valid IPv4" in warning for warning in result.warnings)
    
    def test_cross_field_ntp_address_without_protocol_generates_warning(self):
        """Test that NTP address without time distribution protocol generates warning."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.NTPSourceAddress = "*************"
        # Missing TimeDistributionProtocol
        
        config = ValidationConfig(validate_cross_field_dependencies=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("NTP Source Address (0018,1803) is present but Time Distribution Protocol (0018,1802) is not specified"
                  in warning for warning in result.warnings)
    
    def test_cross_field_ntp_address_with_incompatible_protocol_generates_warning(self):
        """Test that NTP address with incompatible protocol generates warning."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.NTPSourceAddress = "*************"
        dataset.TimeDistributionProtocol = TimeDistributionProtocol.GPS.value  # GPS doesn't use IP addresses
        
        config = ValidationConfig(validate_cross_field_dependencies=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("NTP Source Address (0018,1803) is present but Time Distribution Protocol (0018,1802) is 'GPS'"
                  in warning for warning in result.warnings)
    
    def test_cross_field_utc_uid_with_unsynchronized_time_generates_warning(self):
        """Test that UTC synchronization UID with unsynchronized time generates warning."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "1.2.840.10008.15.1.1"  # UTC Synchronization UID
        dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.NO.value  # Inconsistent
        
        config = ValidationConfig(validate_cross_field_dependencies=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("synchronized to UTC but acquisition times are not synchronized" in warning
                  for warning in result.warnings)
    
    def test_cross_field_no_trigger_with_sync_channel_generates_warning(self):
        """Test that NO TRIGGER with synchronization channel generates warning."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.NO_TRIGGER.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.NO.value
        dataset.SynchronizationChannel = [1, 2]  # Inconsistent with NO TRIGGER
        
        config = ValidationConfig(validate_cross_field_dependencies=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("Synchronization Trigger (0018,106A) is 'NO TRIGGER' but Synchronization Channel (0018,106C) is present"
                  in warning for warning in result.warnings)
    
    def test_cross_field_no_trigger_with_sync_fields_generates_warning(self):
        """Test that NO TRIGGER with other sync fields generates warning."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = SynchronizationTrigger.NO_TRIGGER.value
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.NO.value
        dataset.TriggerSourceOrType = "LINAC_001"
        dataset.TimeSource = "NTP_SERVER_001"
        
        config = ValidationConfig(validate_cross_field_dependencies=True)
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert result.has_warnings
        assert any("Synchronization Trigger (0018,106A) is 'NO TRIGGER' but synchronization-related fields are present"
                  in warning for warning in result.warnings)
    
    def test_all_synchronization_trigger_enum_values_are_valid(self):
        """Test that all synchronization trigger enum values pass validation."""
        trigger_values = [
            SynchronizationTrigger.SOURCE,
            SynchronizationTrigger.EXTERNAL,
            SynchronizationTrigger.PASSTHRU,
            SynchronizationTrigger.NO_TRIGGER
        ]
        
        for trigger in trigger_values:
            dataset = Dataset()
            dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
            dataset.SynchronizationTrigger = trigger.value
            dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
            
            config = ValidationConfig(check_enumerated_values=True)
            result = SynchronizationValidator.validate(dataset, config)
            
            assert result.is_valid, f"Trigger {trigger.value} should be valid"
            assert not result.has_errors, f"Trigger {trigger.value} should not have errors"
    
    def test_all_time_distribution_protocol_enum_values_are_valid(self):
        """Test that all time distribution protocol enum values pass validation."""
        protocol_values = [
            TimeDistributionProtocol.NTP,
            TimeDistributionProtocol.IRIG,
            TimeDistributionProtocol.GPS,
            TimeDistributionProtocol.SNTP,
            TimeDistributionProtocol.PTP
        ]
        
        for protocol in protocol_values:
            dataset = Dataset()
            dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
            dataset.SynchronizationTrigger = SynchronizationTrigger.SOURCE.value
            dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
            dataset.TimeDistributionProtocol = protocol.value
            
            config = ValidationConfig(check_enumerated_values=True)
            result = SynchronizationValidator.validate(dataset, config)
            
            assert result.is_valid, f"Protocol {protocol.value} should be valid"
            assert not result.has_errors, f"Protocol {protocol.value} should not have errors"
    
    def test_validation_config_controls_validation_types(self):
        """Test that ValidationConfig properly controls different validation types."""
        dataset = Dataset()
        dataset.SynchronizationFrameOfReferenceUID = "*******.*******.9.10"
        dataset.SynchronizationTrigger = "INVALID_TRIGGER"
        dataset.AcquisitionTimeSynchronized = AcquisitionTimeSynchronized.YES.value
        dataset.SynchronizationChannel = [1, 2, 3]  # Invalid length
        dataset.NTPSourceAddress = "*************"  # Without protocol
        
        # Test with no validations
        config = ValidationConfig(
            check_enumerated_values=False,
            validate_conditional_requirements=False,
            validate_cross_field_dependencies=False
        )
        result = SynchronizationValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert not result.has_warnings
        
        # Test with all validations
        config = ValidationConfig(
            check_enumerated_values=True,
            validate_conditional_requirements=True,
            validate_cross_field_dependencies=True
        )
        result = SynchronizationValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert result.has_warnings