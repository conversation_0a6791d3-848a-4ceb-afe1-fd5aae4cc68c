"""Tests for General Equipment Module Validator - DICOM PS3.3 C.7.5.1"""

from pydicom import Dataset
from pyrt_dicom.validators.modules.general_equipment_validator import GeneralEquipmentValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult


class TestGeneralEquipmentValidator:
    """Test class for GeneralEquipmentValidator following pytest framework requirements."""
    
    def test_validate_valid_minimal_dataset_passes(self):
        """Test that dataset with minimal required elements passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_empty_manufacturer_passes(self):
        """Test that empty manufacturer (Type 2) passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = ""  # Type 2 can be empty
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_missing_manufacturer_fails(self):
        """Test that missing manufacturer (Type 2) fails validation."""
        dataset = Dataset()
        # No Manufacturer attribute
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Manufacturer (0008,0070) is required (Type 2)" in result.errors[0]
        assert len(result.warnings) == 0
    
    def test_validate_complete_valid_dataset_passes(self):
        """Test that dataset with all valid optional elements passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Varian Medical Systems"
        dataset.InstitutionName = "Test Hospital"
        dataset.InstitutionAddress = "123 Main St, City, State"
        dataset.StationName = "TrueBeam STx"
        dataset.InstitutionalDepartmentName = "Radiation Oncology"
        dataset.ManufacturerModelName = "TrueBeam"
        dataset.ManufacturerDeviceClassUID = ["*******.5"]  # Fixed attribute name
        dataset.DeviceSerialNumber = "12345"
        dataset.SoftwareVersions = "v2.7.1"  # Single string instead of list
        dataset.GantryID = "Gantry01"
        dataset.DeviceUID = "*******.*******.9"
        dataset.SpatialResolution = 0.5
        dataset.DateOfManufacture = "20200101"
        dataset.DateOfInstallation = "20210101"
        dataset.DateOfLastCalibration = "20240101"
        dataset.TimeOfLastCalibration = "120000"

        result = GeneralEquipmentValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    # Pixel Padding Conditional Validation Tests
    
    def test_validate_pixel_padding_conditional_requirement_missing_fails(self):
        """Test that missing pixel padding value when required fails validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"
        # Missing PixelPaddingValue - should fail
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Pixel Padding Value (0028,0120) is required" in result.errors[0]
        assert "Pixel Padding Range Limit (0028,0121)" in result.errors[0]
    
    def test_validate_pixel_padding_conditional_requirement_present_passes(self):
        """Test that pixel padding value when required passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"
        dataset.PixelPaddingValue = 0  # Required and present
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    def test_validate_pixel_padding_not_required_without_range_limit(self):
        """Test that pixel padding value is not required without range limit."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelData = b"test_pixel_data"
        # No PixelPaddingRangeLimit, so PixelPaddingValue not required
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    def test_validate_pixel_padding_not_required_without_pixel_data(self):
        """Test that pixel padding value is not required without pixel data."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        # No PixelData or PixelDataProviderURL, so PixelPaddingValue not required
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    # Calibration Date/Time Pairing Tests
    
    def test_validate_calibration_date_only_passes(self):
        """Test that calibration date without time passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = "20240101"
        # No TimeOfLastCalibration - this is valid
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_calibration_time_without_date_fails(self):
        """Test that calibration time without date generates error (enhanced validation)."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.TimeOfLastCalibration = "120000"
        # No DateOfLastCalibration - should fail in enhanced validation

        result = GeneralEquipmentValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Time of Last Calibration (0018,1201) has no meaning unless" in result.errors[0]
        assert "Date of Last Calibration (0018,1200)" in result.errors[0]
    
    def test_validate_calibration_paired_single_values_passes(self):
        """Test that paired single calibration date/time values pass validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = "20240101"
        dataset.TimeOfLastCalibration = "120000"
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_calibration_paired_multiple_values_passes(self):
        """Test that paired multiple calibration date/time values pass validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = ["20240101", "20240201"]
        dataset.TimeOfLastCalibration = ["120000", "130000"]
        
        result = GeneralEquipmentValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_calibration_mismatched_count_fails(self):
        """Test that mismatched calibration date/time counts generate error (enhanced validation)."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = ["20240101", "20240201"]
        dataset.TimeOfLastCalibration = ["120000", "130000", "140000"]  # Mismatched count (3 vs 2)

        result = GeneralEquipmentValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Found 2 date values and 3 time values" in result.errors[0]
        assert "must have the same number of values" in result.errors[0]
    
    # Sequence Validation Tests
    
    def test_validate_institutional_department_sequence_valid_passes(self):
        """Test that valid institutional department type code sequence passes validation."""
        dept_item = Dataset()
        dept_item.CodeValue = "394802001"
        dept_item.CodingSchemeDesignator = "SCT"
        dept_item.CodeMeaning = "General medicine"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        # Should warn about single item requirement
        assert len(result.warnings) == 0  # Single item is valid
    
    def test_validate_institutional_department_sequence_multiple_items_fails(self):
        """Test that multiple items in institutional department sequence generates error (enhanced validation)."""
        dept_item1 = Dataset()
        dept_item1.CodeValue = "394802001"
        dept_item1.CodingSchemeDesignator = "SCT"
        dept_item1.CodeMeaning = "General medicine"

        dept_item2 = Dataset()
        dept_item2.CodeValue = "394803002"
        dept_item2.CodingSchemeDesignator = "SCT"
        dept_item2.CodeMeaning = "Cardiology"

        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item1, dept_item2]

        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Found 2 items" in result.errors[0]
        assert "may contain only a single item" in result.errors[0]
    
    def test_validate_institutional_department_sequence_missing_code_value_fails(self):
        """Test that missing code value in institutional department sequence fails validation."""
        dept_item = Dataset()
        # Missing CodeValue
        dept_item.CodingSchemeDesignator = "SCT"
        dept_item.CodeMeaning = "General medicine"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Code Value (0008,0100) is required" in result.errors[0]
        assert "Institutional Department Type Code Sequence" in result.errors[0]
    
    def test_validate_udi_sequence_valid_passes(self):
        """Test that valid UDI sequence passes validation."""
        udi_item = Dataset()
        udi_item.UniqueDeviceIdentifier = "(01)12345678901234(11)141231(17)150707(10)A123B456(21)12345"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.UDISequence = [udi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_udi_sequence_missing_identifier_fails(self):
        """Test that missing unique device identifier in UDI sequence fails validation."""
        udi_item = Dataset()
        # Missing UniqueDeviceIdentifier
        udi_item.DeviceIdentifier = "12345678901234"
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.UDISequence = [udi_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Unique Device Identifier is required" in result.errors[0]
        assert "UDI Sequence" in result.errors[0]
    
    # ValidationConfig Tests
    
    def test_validate_with_conditional_requirements_disabled(self):
        """Test validation with conditional requirements disabled."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"
        # Missing PixelPaddingValue but conditional validation disabled
        
        config = ValidationConfig(validate_conditional_requirements=False)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should not validate conditional requirements
    
    def test_validate_with_sequences_disabled(self):
        """Test validation with sequence validation disabled."""
        dept_item = Dataset()
        # Missing required fields
        
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.InstitutionalDepartmentTypeCodeSequence = [dept_item]
        
        config = ValidationConfig(validate_sequences=False)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should not validate sequences
    
    def test_validate_with_default_config(self):
        """Test validation with default configuration."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"

        result = GeneralEquipmentValidator.validate(dataset)  # No config provided

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    # Semantic Validation Tests

    def test_validate_pixel_padding_without_pixel_data_warns(self):
        """Test that pixel padding value without pixel data generates warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingValue = 0
        # No PixelData or PixelDataProviderURL - should warn

        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Pixel Padding Value (0028,0120) is present but no Pixel Data" in result.warnings[0]

    def test_validate_empty_software_versions_warns(self):
        """Test that empty software versions list generates warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.SoftwareVersions = []  # Empty list

        config = ValidationConfig(validate_semantic_constraints=True)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        # The validator detects this as a type issue rather than empty
        assert "Software Versions (0018,1020)" in result.warnings[0]

    def test_validate_invalid_device_class_uid_warns(self):
        """Test that invalid device class UID format generates warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        # Manually set the attribute to bypass pydicom's validation
        # This simulates a UID that made it through but has invalid characters
        setattr(dataset, 'ManufacturerDeviceClassUID', ["*******.invalid"])

        config = ValidationConfig(validate_semantic_constraints=True)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "does not appear to be a valid UID format" in result.warnings[0]

    def test_validate_negative_spatial_resolution_warns(self):
        """Test that negative spatial resolution generates warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.SpatialResolution = -0.5

        config = ValidationConfig(validate_semantic_constraints=True)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "should be a positive value" in result.warnings[0]

    def test_validate_invalid_date_format_warns(self):
        """Test that invalid date format generates warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfManufacture = "2024-01-01"  # Wrong format, should be YYYYMMDD

        config = ValidationConfig(validate_semantic_constraints=True)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "should be in DICOM DA format (YYYYMMDD)" in result.warnings[0]

    def test_validate_calibration_date_ordering_warns(self):
        """Test that incorrect calibration date ordering generates warning."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.DateOfLastCalibration = ["20240201", "20240101"]  # Wrong order (newer first)

        result = GeneralEquipmentValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "should be ordered from oldest to most recent" in result.warnings[0]

    def test_validate_semantic_constraints_disabled(self):
        """Test validation with semantic constraints disabled."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.SpatialResolution = -0.5  # Invalid but semantic validation disabled

        config = ValidationConfig(validate_semantic_constraints=False)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0  # Should not validate semantic constraints

    # Error Message Quality Tests

    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include DICOM tag references."""
        dataset = Dataset()
        # Missing Manufacturer

        result = GeneralEquipmentValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "(0008,0070)" in result.errors[0]  # DICOM tag reference

    def test_error_messages_provide_guidance(self):
        """Test that error messages provide actionable guidance."""
        dataset = Dataset()
        # Missing Manufacturer

        result = GeneralEquipmentValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        error_msg = result.errors[0]
        assert "identifies the manufacturer" in error_msg  # Explains purpose
        assert "may be empty but must be present" in error_msg  # Provides guidance

    def test_conditional_error_messages_explain_context(self):
        """Test that conditional error messages explain the triggering conditions."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"
        # Missing PixelPaddingValue

        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)

        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        error_msg = result.errors[0]
        assert "when Pixel Padding Range Limit" in error_msg  # Explains condition
        assert "is present and either Pixel Data" in error_msg  # Explains full condition
        assert "See DICOM PS3.3 C.*******.2" in error_msg  # References standard

    # Tests for pixel padding setup conditions validation (moved from module)

    def test_validate_pixel_padding_setup_with_range_limit_but_no_pixel_data_fails(self):
        """Test that pixel padding value with range limit but no pixel data fails validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingValue = 0
        dataset.PixelPaddingRangeLimit = 100
        # No PixelData or PixelDataProviderURL - should fail setup validation
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "Pixel Padding Value (0028,0120) cannot be set when Pixel Padding Range Limit" in result.errors[0]
        assert "is present but Pixel Data (7FE0,0010) or Pixel Data Provider URL" in result.errors[0]
        assert "is not present" in result.errors[0]

    def test_validate_pixel_padding_setup_with_range_limit_and_pixel_data_passes(self):
        """Test that pixel padding value with range limit and pixel data passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingValue = 0
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelData = b"test_pixel_data"  # Pixel data present - should pass
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_pixel_padding_setup_with_range_limit_and_pixel_data_url_passes(self):
        """Test that pixel padding value with range limit and pixel data URL passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingValue = 0
        dataset.PixelPaddingRangeLimit = 100
        dataset.PixelDataProviderURL = "https://example.com/pixel-data"  # Pixel data URL present - should pass
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_validate_pixel_padding_setup_without_range_limit_passes(self):
        """Test that pixel padding value without range limit passes validation."""
        dataset = Dataset()
        dataset.Manufacturer = "Test Manufacturer"
        dataset.PixelPaddingValue = 0
        # No PixelPaddingRangeLimit - should pass regardless of pixel data presence
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = GeneralEquipmentValidator.validate(dataset, config)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
