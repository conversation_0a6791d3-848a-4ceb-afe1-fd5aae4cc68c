"""
Test RT Tolerance Tables Validator functionality.

Tests DICOM PS3.3 C.8.8.11 RT Tolerance Tables Module validation including:
- Required elements (Tolerance Table Number)  
- Conditional requirements (nested sequence elements)
- Enumerated values (RT Beam Limiting Device Type)
- Uniqueness constraints (Tolerance Table Numbers)
- Value consistency and clinical warnings
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.rt_tolerance_tables_validator import RTToleranceTablesValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums import RTBeamLimitingDeviceType


class TestRTToleranceTablesValidator:
    """Test RT Tolerance Tables Validator functionality."""
    
    def test_validate_empty_dataset(self):
        """Test validation of empty dataset passes."""
        dataset = Dataset()
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_empty_sequence(self):
        """Test validation of empty tolerance table sequence passes."""
        dataset = Dataset()
        dataset.ToleranceTableSequence = []
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_valid_tolerance_table(self):
        """Test validation of valid tolerance table passes without errors."""
        dataset = Dataset()
        
        # Create valid tolerance table item
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.ToleranceTableLabel = "Standard Tolerances"
        table_item.GantryAngleTolerance = 1.0
        table_item.PatientSupportAngleTolerance = 2.0
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_valid_with_beam_limiting_device_sequence(self):
        """Test validation of valid tolerance table with beam limiting device sequence."""
        dataset = Dataset()
        
        # Create beam limiting device tolerance item
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = "X"
        bld_item.BeamLimitingDevicePositionTolerance = 2.0
        
        # Create tolerance table item
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_missing_tolerance_table_number(self):
        """Test validation fails when Tolerance Table Number is missing."""
        dataset = Dataset()
        
        # Create table item without required Tolerance Table Number
        table_item = Dataset()
        table_item.ToleranceTableLabel = "Invalid Table"
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Tolerance Table Number (300A,0042) is required (Type 1)" in result.errors[0]
        assert "DICOM PS3.3 C.8.8.11" in result.errors[0]
    
    def test_validate_invalid_tolerance_table_number_type(self):
        """Test validation fails when Tolerance Table Number is not a positive integer."""
        dataset = Dataset()
        
        # Test cases for invalid tolerance table numbers  
        # Note: pydicom may convert some values, so we test values that remain invalid
        invalid_values = [0, -1]  # Removed string/float as pydicom handles conversion
        
        for invalid_value in invalid_values:
            table_item = Dataset()
            table_item.ToleranceTableNumber = invalid_value
            dataset.ToleranceTableSequence = [table_item]
            
            result = RTToleranceTablesValidator.validate(dataset)
            
            assert len(result.errors) == 1
            assert "Tolerance Table Number (300A,0042) must be a positive integer" in result.errors[0]
            assert str(invalid_value) in result.errors[0]
    
    def test_validate_duplicate_tolerance_table_numbers(self):
        """Test validation fails when duplicate Tolerance Table Numbers exist."""
        dataset = Dataset()
        
        # Create two tables with same number
        table_item1 = Dataset()
        table_item1.ToleranceTableNumber = 1
        
        table_item2 = Dataset()
        table_item2.ToleranceTableNumber = 1  # Duplicate number
        
        dataset.ToleranceTableSequence = [table_item1, table_item2]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Tolerance Table Number (1) must be unique within the RT Plan" in result.errors[0]
        assert "DICOM PS3.3 C.8.8.11" in result.errors[0]
    
    def test_validate_beam_limiting_device_sequence_missing_device_type(self):
        """Test validation fails when RT Beam Limiting Device Type is missing."""
        dataset = Dataset()
        
        # Create beam limiting device item without required device type
        bld_item = Dataset()
        bld_item.BeamLimitingDevicePositionTolerance = 2.0
        # Missing: RTBeamLimitingDeviceType
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "RT Beam Limiting Device Type (300A,00B8) is required (Type 1)" in result.errors[0]
        assert "when Beam Limiting Device Tolerance Sequence is present" in result.errors[0]
    
    def test_validate_beam_limiting_device_sequence_invalid_device_type(self):
        """Test validation fails when RT Beam Limiting Device Type is invalid."""
        dataset = Dataset()
        
        # Test empty string - this should be treated as missing
        bld_item1 = Dataset()
        bld_item1.RTBeamLimitingDeviceType = ""  # Empty string
        bld_item1.BeamLimitingDevicePositionTolerance = 2.0
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item1]
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "RT Beam Limiting Device Type (300A,00B8) is required (Type 1)" in result.errors[0]
    
    def test_validate_beam_limiting_device_sequence_missing_position_tolerance(self):
        """Test validation fails when Beam Limiting Device Position Tolerance is missing."""
        dataset = Dataset()
        
        # Create beam limiting device item without required position tolerance
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = "X"
        # Missing: BeamLimitingDevicePositionTolerance
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Beam Limiting Device Position Tolerance (300A,004A) is required (Type 1)" in result.errors[0]
        assert "when Beam Limiting Device Tolerance Sequence is present" in result.errors[0]
    
    def test_validate_beam_limiting_device_sequence_invalid_position_tolerance_type(self):
        """Test validation fails when position tolerance is not numeric."""
        # Note: pydicom may convert some values, so this test checks behavior with None
        dataset = Dataset()
        
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = "X"
        # Don't set BeamLimitingDevicePositionTolerance to test missing scenario
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 1
        assert "Beam Limiting Device Position Tolerance (300A,004A) is required (Type 1)" in result.errors[0]
    
    def test_validate_enumerated_values_valid(self):
        """Test validation of valid enumerated values."""
        dataset = Dataset()
        
        # Test all valid RT Beam Limiting Device Types
        valid_device_types = ["X", "Y", "ASYMX", "ASYMY", "MLCX", "MLCY"]
        
        for device_type in valid_device_types:
            bld_item = Dataset()
            bld_item.RTBeamLimitingDeviceType = device_type
            bld_item.BeamLimitingDevicePositionTolerance = 1.0
            
            table_item = Dataset()
            table_item.ToleranceTableNumber = 1
            table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
            
            dataset.ToleranceTableSequence = [table_item]
            
            config = ValidationConfig(check_enumerated_values=True)
            result = RTToleranceTablesValidator.validate(dataset, config)
            
            assert len(result.errors) == 0, f"Valid device type {device_type} should not produce errors"
    
    def test_validate_enumerated_values_invalid(self):
        """Test validation of invalid enumerated values."""
        dataset = Dataset()
        
        # Test invalid RT Beam Limiting Device Type
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = "INVALID_TYPE"
        bld_item.BeamLimitingDevicePositionTolerance = 1.0
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item]
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTToleranceTablesValidator.validate(dataset, config)
        
        assert len(result.errors) == 0  # Enumerated values produce warnings, not errors
        assert len(result.warnings) == 1
        assert "RT Beam Limiting Device Type (300A,00B8)" in result.warnings[0]
        assert "INVALID_TYPE" in result.warnings[0]
    
    def test_validate_negative_angular_tolerance_values(self):
        """Test validation warnings for negative angular tolerance values."""
        dataset = Dataset()
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.GantryAngleTolerance = -1.0  # Negative value should warn
        table_item.PatientSupportAngleTolerance = -2.0  # Negative value should warn
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0  # Should be warnings, not errors
        assert len(result.warnings) == 2
        
        warning_messages = " ".join(result.warnings)
        assert "GantryAngleTolerance (300A,0044) should be non-negative, got -1.0" in warning_messages
        assert "PatientSupportAngleTolerance (300A,004C) should be non-negative, got -2.0" in warning_messages
    
    def test_validate_excessive_angular_tolerance_values(self):
        """Test validation warnings for angular tolerance values exceeding 360 degrees."""
        dataset = Dataset()
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.GantryAngleTolerance = 450.0  # Exceeds 360 degrees
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) >= 1
        warning_messages = " ".join(result.warnings)
        assert "GantryAngleTolerance (300A,0044) exceeds 360 degrees, got 450" in warning_messages
    
    def test_validate_negative_positional_tolerance_values(self):
        """Test validation warnings for negative positional tolerance values."""
        dataset = Dataset()
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.TableTopVerticalPositionTolerance = -1.0  # Negative value
        table_item.TableTopLongitudinalPositionTolerance = -2.0  # Negative value
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 2
        
        warning_messages = " ".join(result.warnings)
        assert "TableTopVerticalPositionTolerance (300A,0051) should be non-negative, got -1.0" in warning_messages
        assert "TableTopLongitudinalPositionTolerance (300A,0052) should be non-negative, got -2.0" in warning_messages
    
    def test_validate_negative_beam_limiting_device_position_tolerance(self):
        """Test validation warnings for negative beam limiting device position tolerance."""
        dataset = Dataset()
        
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = "X"
        bld_item.BeamLimitingDevicePositionTolerance = -1.5  # Negative value
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Beam Limiting Device Position Tolerance (300A,004A) should be non-negative, got -1.5" in result.warnings[0]
    
    def test_validate_unusually_large_gantry_tolerance(self):
        """Test validation warning for unusually large gantry angle tolerance."""
        dataset = Dataset()
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.GantryAngleTolerance = 15.0  # > 10 degrees
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 1
        assert "Gantry Angle Tolerance (15.0 degrees) seems unusually large for clinical use" in result.warnings[0]
    
    def test_validate_unusually_small_tolerance_values(self):
        """Test validation warnings for unusually small tolerance values."""
        dataset = Dataset()
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.GantryAngleTolerance = 0.005  # < 0.01 degrees
        table_item.TableTopVerticalPositionTolerance = 0.005  # < 0.01 mm
        
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 2
        
        warning_messages = " ".join(result.warnings)
        assert "GantryAngleTolerance (300A,0044) value (0.005 degrees) seems unusually small" in warning_messages
        assert "TableTopVerticalPositionTolerance (300A,0051) value (0.005 mm) seems unusually small" in warning_messages
    
    def test_validate_with_configuration_options(self):
        """Test validation with different configuration options."""
        dataset = Dataset()
        
        # Create dataset with enumerated value issue
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = "INVALID_TYPE"
        bld_item.BeamLimitingDevicePositionTolerance = 1.0
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = 1
        table_item.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item]
        
        # Test with enumerated values checking disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result_no_enum = RTToleranceTablesValidator.validate(dataset, config_no_enum)
        
        # Should not report enumerated value warnings when checking is disabled
        enum_warnings = [warn for warn in result_no_enum.warnings if "RT Beam Limiting Device Type" in warn and "INVALID_TYPE" in warn]
        assert len(enum_warnings) == 0
        
        # Test with enumerated values checking enabled
        config_with_enum = ValidationConfig(check_enumerated_values=True)
        result_with_enum = RTToleranceTablesValidator.validate(dataset, config_with_enum)
        
        # Should report enumerated value warnings when checking is enabled
        enum_warnings = [warn for warn in result_with_enum.warnings if "RT Beam Limiting Device Type" in warn and "INVALID_TYPE" in warn]
        assert len(enum_warnings) == 1
    
    def test_validate_complex_scenario(self):
        """Test validation of complex scenario with multiple tables and issues."""
        dataset = Dataset()
        
        # Create first table with valid data
        table_item1 = Dataset()
        table_item1.ToleranceTableNumber = 1
        table_item1.ToleranceTableLabel = "Standard Tolerances"
        table_item1.GantryAngleTolerance = 1.0
        
        # Create second table with duplicate number (error)
        table_item2 = Dataset()
        table_item2.ToleranceTableNumber = 1  # Duplicate number
        table_item2.GantryAngleTolerance = -2.0  # Negative value (warning)
        
        # Create third table with beam limiting device issues
        bld_item = Dataset()
        bld_item.RTBeamLimitingDeviceType = "INVALID_TYPE"  # Invalid enum
        bld_item.BeamLimitingDevicePositionTolerance = -1.0  # Negative value
        
        table_item3 = Dataset()
        table_item3.ToleranceTableNumber = 3
        table_item3.BeamLimitingDeviceToleranceSequence = [bld_item]
        
        dataset.ToleranceTableSequence = [table_item1, table_item2, table_item3]
        
        config = ValidationConfig(check_enumerated_values=True)
        result = RTToleranceTablesValidator.validate(dataset, config)
        
        # Should have both errors and warnings
        assert len(result.errors) >= 1  # Duplicate number (enum validation produces warnings)
        assert len(result.warnings) >= 2  # Invalid enum + negative values
        
        error_messages = " ".join(result.errors)
        assert "must be unique within the RT Plan" in error_messages
        
        warning_messages = " ".join(result.warnings)
        assert ("INVALID_TYPE" in warning_messages or "should be non-negative" in warning_messages)
    
    def test_validation_result_structure(self):
        """Test that ValidationResult objects are properly structured."""
        dataset = Dataset()
        
        table_item = Dataset()
        # Missing ToleranceTableNumber to trigger error
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        # Test ValidationResult structure
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Verify we got the expected error
        assert len(result.errors) == 1
        assert isinstance(result.errors[0], str)
        assert len(result.warnings) == 0
    
    def test_error_message_quality(self):
        """Test that error messages are clear and include necessary information."""
        dataset = Dataset()
        
        table_item = Dataset()
        table_item.ToleranceTableNumber = -5  # Invalid number
        dataset.ToleranceTableSequence = [table_item]
        
        result = RTToleranceTablesValidator.validate(dataset)
        
        assert len(result.errors) == 1
        error_msg = result.errors[0]
        
        # Error message should include:
        assert "Tolerance Table Sequence item 0" in error_msg  # Item location
        assert "Tolerance Table Number (300A,0042)" in error_msg  # DICOM tag
        assert "must be a positive integer" in error_msg  # Clear requirement
        assert "got -5" in error_msg  # Actual invalid value