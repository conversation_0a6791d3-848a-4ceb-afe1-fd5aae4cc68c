"""
Test DICOM data formatting utilities.

Tests for format_date_value, format_time_value, format_enum_int, and format_enum_string functions
that handle conversion of Python data types to DICOM-compliant string formats.
"""

from datetime import datetime, date
from enum import Enum

from pyrt_dicom.utils.dicom_formatters import (
    format_date_value,
    format_time_value,
    format_enum_int,
    format_enum_string
)


class SampleEnum(Enum):
    """Sample enum for testing enum value formatting."""
    OPTION_A = "A"
    OPTION_B = "B"


class TestFormatDateValue:
    """Test format_date_value function."""
    
    def test_datetime_object(self):
        """Test formatting datetime object to DICOM DA format."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_date_value(dt)
        assert result == "20240115"
    
    def test_date_object(self):
        """Test formatting date object to DICOM DA format."""
        d = date(2023, 12, 25)
        result = format_date_value(d)
        assert result == "20231225"
    
    def test_string_value(self):
        """Test that string values are returned as-is."""
        date_str = "20220601"
        result = format_date_value(date_str)
        assert result == "20220601"
    
    def test_integer_value(self):
        """Test that integer values are converted to string."""
        date_int = 20210301
        result = format_date_value(date_int)
        assert result == "20210301"
    
    def test_none_value(self):
        """Test that None is converted to string 'None'."""
        result = format_date_value(None)
        assert result == "None"


class TestFormatTimeValue:
    """Test format_time_value function."""
    
    def test_datetime_without_microseconds(self):
        """Test formatting datetime object without microseconds."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_time_value(dt)
        assert result == "143045"
    
    def test_datetime_with_microseconds(self):
        """Test formatting datetime object with microseconds."""
        dt = datetime(2024, 1, 15, 14, 30, 45, 123456)
        result = format_time_value(dt)
        assert result == "143045.123456"
    
    def test_datetime_midnight(self):
        """Test formatting datetime at midnight."""
        dt = datetime(2024, 1, 15, 0, 0, 0)
        result = format_time_value(dt)
        assert result == "000000"
    
    def test_datetime_with_partial_microseconds(self):
        """Test formatting datetime with partial microseconds."""
        dt = datetime(2024, 1, 15, 9, 5, 3, 100)
        result = format_time_value(dt)
        assert result == "090503.000100"
    
    def test_string_value(self):
        """Test that string values are returned as-is."""
        time_str = "120000"
        result = format_time_value(time_str)
        assert result == "120000"
    
    def test_integer_value(self):
        """Test that integer values are converted to string."""
        time_int = 235959
        result = format_time_value(time_int)
        assert result == "235959"
    
    def test_none_value(self):
        """Test that None is converted to string 'None'."""
        result = format_time_value(None)
        assert result == "None"


class TestFormatEnumString:
    """Test format_enum_string function."""
    
    def test_enum_with_value_attribute(self):
        """Test extracting string value from enum object."""
        enum_obj = SampleEnum.OPTION_A
        result = format_enum_string(enum_obj)
        assert result == "A"
    
    def test_enum_option_b(self):
        """Test extracting string value from different enum option."""
        enum_obj = SampleEnum.OPTION_B
        result = format_enum_string(enum_obj)
        assert result == "B"
    
    def test_string_value(self):
        """Test that string values are converted to string."""
        string_val = "some_string"
        result = format_enum_string(string_val)
        assert result == "some_string"
    
    def test_integer_value(self):
        """Test that integer values are converted to string."""
        int_val = 42
        result = format_enum_string(int_val)
        assert result == "42"
    
    def test_none_value(self):
        """Test that None is converted to string."""
        result = format_enum_string(None)
        assert result == "None"
    
    def test_object_without_value_attribute(self):
        """Test that objects without value attribute are converted to string."""
        class CustomObject:
            def __init__(self, data):
                self.data = data
            
            def __str__(self):
                return f"CustomObject({self.data})"
        
        obj = CustomObject("test_data")
        result = format_enum_string(obj)
        assert result == "CustomObject(test_data)"


class TestFormatEnumInt:
    """Test format_enum_int function."""
    
    def test_enum_with_value_attribute(self):
        """Test extracting int value from enum object."""
        class NumericEnum(Enum):
            OPTION_1 = 1
            OPTION_2 = 2
        
        enum_obj = NumericEnum.OPTION_1
        result = format_enum_int(enum_obj)
        assert result == 1
    
    def test_enum_numeric_option(self):
        """Test extracting int value from different numeric enum option."""
        class NumericEnum(Enum):
            OPTION_1 = 1
            OPTION_2 = 2
        
        enum_obj = NumericEnum.OPTION_2
        result = format_enum_int(enum_obj)
        assert result == 2
    
    def test_string_numeric_value(self):
        """Test that string numeric values are converted to int."""
        string_val = "42"
        result = format_enum_int(string_val)
        assert result == 42
    
    def test_integer_value(self):
        """Test that integer values are returned as int."""
        int_val = 42
        result = format_enum_int(int_val)
        assert result == 42
    
    def test_float_value(self):
        """Test that float values are converted to int."""
        float_val = 42.7
        result = format_enum_int(float_val)
        assert result == 42


class TestFormattersIntegration:
    """Integration tests for formatter functions."""
    
    def test_combined_datetime_formatting(self):
        """Test using both date and time formatters on same datetime."""
        dt = datetime(2024, 3, 20, 16, 45, 30, 500000)
        
        date_result = format_date_value(dt)
        time_result = format_time_value(dt)
        
        assert date_result == "20240320"
        assert time_result == "164530.500000"
    
    def test_dicom_standard_compliance(self):
        """Test that formatted values comply with DICOM standards."""
        # DICOM DA (Date) should be 8 characters YYYYMMDD
        dt = datetime(2024, 1, 1)
        date_result = format_date_value(dt)
        assert len(date_result) == 8
        assert date_result.isdigit()
        
        # DICOM TM (Time) should be HHMMSS or HHMMSS.FFFFFF
        time_result = format_time_value(dt)
        assert len(time_result) == 6  # HHMMSS format
        assert time_result.isdigit()
    
    def test_enum_formatter_coordination(self):
        """Test that enum formatters work together for different data types."""
        class MixedEnum(Enum):
            STRING_OPTION = "test_value"
            NUMERIC_OPTION = 42
        
        # Test string enum formatting
        string_result = format_enum_string(MixedEnum.STRING_OPTION)
        assert string_result == "test_value"
        
        # Test int enum formatting  
        int_result = format_enum_int(MixedEnum.NUMERIC_OPTION)
        assert int_result == 42
        
        # Test cross-type conversion
        string_from_int = format_enum_string(MixedEnum.NUMERIC_OPTION)
        assert string_from_int == "42"