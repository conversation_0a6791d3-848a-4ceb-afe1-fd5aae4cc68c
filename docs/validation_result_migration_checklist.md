# ValidationResult Migration Checklist

This checklist helps track the migration of all validators in the `validators/modules` subfolder from dict-based validation results to the new `ValidationResult` class.

## Migration Overview

**Goal**: Replace dict-based validation (`dict[str, list[str]]`) with type-safe `ValidationResult` class across all module validators.

**Benefits**:
- Type safety with IDE support
- Rich validation properties (`is_valid`, `has_errors`, `error_count`, etc.)
- Cleaner method calls (`result.add_error()` vs `result["errors"].append()`)
- Better error handling and validation workflows
- Consistent validation patterns across the codebase

## Migration Status

### ✅ Completed Validators

| Validator | Module | Test File | Status | Notes |
|-----------|--------|-----------|--------|--------|
| `approval_validator.py` | `approval_module.py` | `test_approval_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `general_image_validator.py` | `general_image_module.py` | `test_general_image_module.py` | ✅ Complete | Direct ValidationResult return |
| `patient_validator.py` | `patient_module.py` | `test_patient_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `cine_validator.py` | `cine_module.py` | `test_cine_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `general_equipment_validator.py` | `general_equipment_module.py` | `test_general_equipment_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `general_study_validator.py` | `general_study_module.py` | `test_general_study_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `general_series_validator.py` | `general_series_module.py` | `test_general_series_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `image_pixel_validator.py` | `image_pixel_module.py` | `test_image_pixel_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `multi_frame_validator.py` | `multi_frame_module.py` | `test_multi_frame_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `overlay_plane_validator.py` | `overlay_plane_module.py` | `test_overlay_plane_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `rt_dose_validator.py` | `rt_dose_module.py` | `test_rt_dose_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `rt_general_plan_validator.py` | `rt_general_plan_module.py` | `test_rt_general_plan_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `sop_common_validator.py` | `sop_common_module.py` | `test_sop_common_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `clinical_trial_series_validator.py` | `clinical_trial_series_module.py` | `test_clinical_trial_series_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `clinical_trial_study_validator.py` | `clinical_trial_study_module.py` | `test_clinical_trial_study_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `clinical_trial_subject_validator.py` | `clinical_trial_subject_module.py` | `test_clinical_trial_subject_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `common_instance_reference_validator.py` | `common_instance_reference_module.py` | `test_common_instance_reference_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `contrast_bolus_validator.py` | `contrast_bolus_module.py` | `test_contrast_bolus_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `ct_image_validator.py` | `ct_image_module.py` | `test_ct_image_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `rt_dvh_validator.py` | `rt_dvh_module.py` | `test_rt_dvh_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `device_validator.py` | `device_module.py` | `test_device_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `enhanced_patient_orientation_validator.py` | `enhanced_patient_orientation_module.py` | `test_enhanced_patient_orientation_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `frame_extraction_validator.py` | `frame_extraction_module.py` | `test_frame_extraction_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `frame_of_reference_validator.py` | `frame_of_reference_module.py` | `test_frame_of_reference_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `general_acquisition_validator.py` | `general_acquisition_module.py` | `test_general_acquisition_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `general_reference_validator.py` | `general_reference_module.py` | `test_general_reference_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `image_plane_validator.py` | `image_plane_module.py` | `test_image_plane_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `modality_lut_validator.py` | `modality_lut_module.py` | `test_modality_lut_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `multi_energy_ct_image_validator.py` | `multi_energy_ct_image_module.py` | `test_multi_energy_ct_image_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `patient_study_validator.py` | `patient_study_module.py` | `test_patient_study_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `roi_contour_validator.py` | `roi_contour_module.py` | `test_roi_contour_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `rt_beams_validator.py` | `rt_beams_module.py` | `test_rt_beams_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `rt_brachy_application_setups_validator.py` | `rt_brachy_application_setups_module.py` | `test_rt_brachy_application_setups_module.py` | ✅ Complete | Direct ValidationResult return |
| `rt_fraction_scheme_validator.py` | `rt_fraction_scheme_module.py` | `test_rt_fraction_scheme_module.py` | ✅ Complete | Direct ValidationResult return |

| `rt_image_validator.py` | `rt_image_module.py` | `test_rt_image_module.py` | ✅ Complete | Direct ValidationResult return |

| `rt_patient_setup_validator.py` | `rt_patient_setup_module.py` | `test_rt_patient_setup_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `rt_prescription_validator.py` | `rt_prescription_module.py` | `test_rt_prescription_module.py` | ✅ Complete | Direct ValidationResult return |

| `rt_roi_observations_validator.py` | `rt_roi_observations_module.py` | `test_rt_roi_observations_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `rt_series_validator.py` | `rt_series_module.py` | `test_rt_series_module.py` | ✅ Complete | Direct ValidationResult return |
| `rt_tolerance_tables_validator.py` | `rt_tolerance_tables_module.py` | `test_rt_tolerance_tables_module.py` | ✅ Complete | Direct ValidationResult return |

| `specimen_validator.py` | `specimen_module.py` | `test_specimen_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `structure_set_validator.py` | `structure_set_module.py` | `test_structure_set_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

| `synchronization_validator.py` | `synchronization_module.py` | `test_synchronization_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |
| `voi_lut_validator.py` | `voi_lut_module.py` | `test_voi_lut_module.py` | ✅ Complete | Dict compatibility via `.to_dict()` |

### 🔄 Pending Validators

**All validators have been migrated!** 🎉

## Migration Steps Per Validator

### Step 1: Update Validator Class

#### 1.1 Update Imports
```python
# Add these imports
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

# Remove if present
from typing import Union, Dict, List
```

#### 1.2 Update Main validate() Method
```python
# Change signature from:
def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:

# To:
def validate(dataset: Dataset, config: ValidationConfig = None) -> ValidationResult:

# Change result initialization from:
result = {"errors": [], "warnings": []}

# To:
result = BaseValidator.create_validation_result()

# Change method calls from:
result = self._some_validation_method(dataset, result)

# To:
self._some_validation_method(dataset, result)
```

#### 1.3 Update Internal Validation Methods
```python
# Change signature from:
def _validate_something(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:

# To:
def _validate_something(dataset: Dataset, result: ValidationResult) -> None:

# Change error/warning additions from:
result["errors"].append("Error message")
result["warnings"].append("Warning message")

# To:
result.add_error("Error message")
result.add_warning("Warning message")

# Remove return statement:
# return result  # Remove this
```

#### 1.4 Use BaseValidator Helper Methods
```python
# For enumerated values, change from manual validation:
if value not in allowed_values:
    result["errors"].append(f"Invalid value: {value}")

# To BaseValidator method:
BaseValidator.validate_enumerated_value(value, allowed_values, "Field Name", result)

# For conditional requirements:
BaseValidator.validate_conditional_requirement(condition, required_fields, dataset, "Error message", result)

# For either/or requirements:
BaseValidator.validate_either_or_requirement(condition, field_a, field_b, dataset, "Error message", result)
```

### Step 2: Update Module Class

#### 2.1 Update Imports
```python
# Add import
from ..validators import ValidationResult
```

#### 2.2 Return Strategy

**Direct ValidationResult Return**
```python
def validate(self, config: Optional[ValidationConfig] = None) -> ValidationResult:
    """Validate module data against DICOM standard."""
    return ValidatorClass.validate(self, config)
```

### Step 3: Test and Verify

#### 3.1 Run Existing Tests
```bash
python -m pytest tests/unit/modules/test_[module_name].py -v
```

#### 3.2 Create ValidationResult Test
```python
def test_validation_result_functionality():
    """Test that validator returns ValidationResult with correct functionality."""
    # Create test data with validation issues
    module = ModuleClass.from_required_elements(...)
    
    # Test direct validator
    validator_result = ValidatorClass.validate(module)
    assert isinstance(validator_result, ValidationResult)
    
    # Test module validate
    module_result = module.validate()
    assert isinstance(module_result, (ValidationResult, dict))  # Depending on strategy
    
    # Test ValidationResult properties
    if isinstance(module_result, ValidationResult):
        assert hasattr(module_result, 'is_valid')
        assert hasattr(module_result, 'has_errors')
        assert hasattr(module_result, 'error_count')
```

### Step 4: Update Documentation

#### 4.1 Update Docstrings
```python
def validate(self, config: ValidationConfig = None) -> ValidationResult:
    """Validate module requirements.
    
    Args:
        dataset: pydicom Dataset to validate
        config: Validation configuration options
        
    Returns:
        ValidationResult with errors and warnings  # Updated
    """
```

#### 4.2 Update Usage Examples
```python
# In module docstring, update examples:
# Validate
result = module.validate()
if result.is_valid:
    print("Validation passed")
else:
    print(f"Validation failed: {result}")
    for error in result.errors:
        print(f"Error: {error}")
    for warning in result.warnings:
        print(f"Warning: {warning}")
```

## Quality Assurance

### Validation Checklist Per Validator

- [ ] **Imports Updated**: ValidationResult and BaseValidator imported
- [ ] **Return Type Changed**: Main validate() method returns ValidationResult
- [ ] **Internal Methods Updated**: All private methods use ValidationResult parameter
- [ ] **Error/Warning Calls Updated**: Use `.add_error()` and `.add_warning()`
- [ ] **BaseValidator Integration**: Use helper methods where appropriate
- [ ] **Module Updated**: Module validate() method updated
- [ ] **Tests Pass**: All existing tests pass without modification
- [ ] **Functionality Verified**: ValidationResult properties and methods work
- [ ] **Documentation Updated**: Docstrings and examples updated

### Common Patterns to Update

#### Pattern 1: Manual Enum Validation
```python
# Before
if value not in [e.value for e in SomeEnum]:
    result["errors"].append(f"Invalid {field}: {value}")

# After
BaseValidator.validate_enumerated_value(value, [e.value for e in SomeEnum], field, result)
```

#### Pattern 2: Conditional Requirements
```python
# Before
if condition:
    missing = [f for f in required_fields if not hasattr(dataset, f)]
    if missing:
        result["errors"].append(f"Missing required fields: {missing}")

# After
BaseValidator.validate_conditional_requirement(condition, required_fields, dataset, "Missing required fields", result)
```

#### Pattern 3: Complex Validation Logic
```python
# Before
def _validate_complex(dataset, result):
    # Complex validation logic
    if some_condition:
        result["errors"].append("Error message")
    return result

# After
def _validate_complex(dataset, result):
    # Complex validation logic
    if some_condition:
        result.add_error("Error message")
    # No return statement needed
```

## Progress Tracking

### Migration Statistics
- **Total Validators**: 44
- **Completed**: 44 (100%) 🎉
- **High Priority Remaining**: 0
- **Medium Priority Remaining**: 0
- **Low Priority Remaining**: 0

### Weekly Goals
- **Week 1**: Complete 5 high-priority validators
- **Week 2**: Complete remaining 5 high-priority validators
- **Week 3**: Complete 10 medium-priority validators
- **Week 4**: Complete remaining medium and low-priority validators

### Notes
- Focus on high-priority validators first (core modules, widely used)
- Test thoroughly after each migration
- Consider validation complexity when prioritizing
- Document any issues or special cases encountered
- Update this checklist as validators are completed

## Troubleshooting

### Common Issues

1. **Test Failures**: Check if tests expect specific dict structure
2. **Import Errors**: Ensure ValidationResult is properly imported
3. **Type Errors**: Update type hints in method signatures
4. **BaseValidator Conflicts**: Ensure using updated BaseValidator methods
5. **Module Dependencies**: Check if other modules depend on dict return type

### Solutions

1. **Dict Compatibility**: Use `.to_dict()` method for backward compatibility
2. **Test Updates**: Minimal test updates should be needed
3. **Type Safety**: Use ValidationResult return type for better type checking
4. **Documentation**: Update examples and docstrings consistently