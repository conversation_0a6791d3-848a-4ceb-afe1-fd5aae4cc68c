# PyRT-DICOM Python Programming Guide

This document provides comprehensive standards and guidelines for developing within the PyRT-DICOM library. It establishes consistent patterns, documentation standards, and best practices to ensure high-quality, maintainable code across all modules.

## Table of Contents

- [1. Enumeration Classes](#1-enumeration-classes)
- [2. Module Classes](#2-module-classes)
- [3. IOD Classes](#3-iod-classes)
- [4. Validator Classes](#4-validator-classes)
- [5. Documentation Standards](#5-documentation-standards)
- [6. Testing Standards](#6-testing-standards)
- [7. Code Quality Standards](#7-code-quality-standards)

---

## 1. Enumeration Classes

Enumeration classes provide strongly-typed DICOM coded values with comprehensive IntelliSense support. All enumerations must follow strict formatting and documentation standards.

### 1.1 File Organization

#### ✅ **Checklist: Enumeration File Structure**

- [ ] File named with `_enums.py` suffix (e.g., `patient_enums.py`)
- [ ] Located in `src/pyrt_dicom/enums/` directory
- [ ] Module docstring follows standard format
- [ ] Single responsibility: enums grouped by logical DICOM domain
- [ ] Imports limited to `from enum import Enum`

#### **Required Module Docstring Format:**
```python
"""[Domain]-related DICOM enumerations."""

from enum import Enum
```

### 1.2 Enumeration Class Definition

#### ✅ **Checklist: Enumeration Class Structure**

- [ ] Inherits from `Enum` class
- [ ] Class name uses PascalCase
- [ ] Class name is descriptive and specific to DICOM context
- [ ] No multiple inheritance
- [ ] Class ordering follows logical DICOM hierarchy

#### **Required Class Docstring Format:**
```python
class ExampleEnum(Enum):
    """[DICOM Element Name] ([TAG]) - DICOM VR: [VR]
    
    Defined Terms per DICOM PS3.3 [SECTION]:
    - VALUE1 = Description of value 1
    - VALUE2 = Description of value 2
    - VALUE3 = Description of value 3
    """
```

### 1.3 Documentation Standards

#### ✅ **Checklist: Documentation Requirements**

- [ ] **DICOM Tag Format**: Must be exactly `([GGGG],[EEEE])` format
- [ ] **VR Specification**: Must include `- DICOM VR: [TYPE]` 
- [ ] **PS3.3 Reference**: Must use exact format `Defined Terms per DICOM PS3.3 [SECTION]:`
- [ ] **Value Descriptions**: Each enum value documented with `- VALUE = Description`
- [ ] **Consistent Terminology**: Use "Defined Terms" (never "Enumerated Values")
- [ ] **No Inline Comments**: All documentation in docstring only
- [ ] **Description Capitalization**: Descriptions use sentence case with proper nouns capitalized

#### **VR Type Standards:**
- Use official DICOM VR types: `CS`, `LO`, `US`, `SS`, `UI`, etc.
- Format: `DICOM VR: [TYPE]` (no variation allowed)

#### **PS3.3 Reference Standards:**
- Format: `Defined Terms per DICOM PS3.3 C.[PART].[SECTION].[SUBSECTION]:`
- Examples: `C.7.1.1`, `C.8.8.14`, `C.*******.2`
- Always use official DICOM PS3.3 section numbers
- Include trailing colon

### 1.4 Enum Value Definition

#### ✅ **Checklist: Enum Value Standards**

- [ ] **Value Names**: Use UPPERCASE_WITH_UNDERSCORES convention
- [ ] **DICOM Compliance**: Values match official DICOM defined terms exactly
- [ ] **String Values**: All enum values are strings (except numeric DICOM values)
- [ ] **No Inline Comments**: Remove all `# comment` style documentation
- [ ] **Logical Ordering**: Values ordered by DICOM specification or logical grouping
- [ ] **No Magic Numbers**: Use descriptive enum names, not raw DICOM values

#### **Acceptable Value Formats:**
```python
# String values (most common)
MALE = "M"
FEMALE = "F"

# Integer values (for specific DICOM numeric enums)
NOT_PREGNANT = 1
POSSIBLY_PREGNANT = 2

# Exact DICOM defined terms
NO_TRIGGER = "NO TRIGGER"  # Spaces preserved when official
```

### 1.5 Complete Example

```python
"""Patient-related DICOM enumerations."""

from enum import Enum


class PatientSex(Enum):
    """Patient Sex (0010,0040) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1:
    - M = Male
    - F = Female  
    - O = Other
    """
    MALE = "M"
    FEMALE = "F" 
    OTHER = "O"


class PregnancyStatus(Enum):
    """Pregnancy Status (0010,21C0) - DICOM VR: US
    
    Defined Terms per DICOM PS3.3 C.7.2.2:
    - 1 = Not pregnant
    - 2 = Possibly pregnant
    - 3 = Definitely pregnant
    - 4 = Unknown
    """
    NOT_PREGNANT = 1
    POSSIBLY_PREGNANT = 2
    DEFINITELY_PREGNANT = 3
    UNKNOWN = 4
```

### 1.6 Common Anti-Patterns to Avoid

#### ❌ **Avoid These Patterns:**

```python
# WRONG: Inline comments instead of docstring descriptions
MALE = "M"  # Male patient

# WRONG: Inconsistent terminology
"""
Enumerated Values per DICOM PS3.3:  # Should be "Defined Terms"
"""

# WRONG: Missing VR specification
"""Patient Sex (0010,0040)
"""

# WRONG: Incorrect tag format
"""Patient Sex 0010,0040 - DICOM VR: CS  # Missing parentheses
"""

# WRONG: Inconsistent PS3.3 reference format
"""
Defined Terms (DICOM PS3.3 C.7.1.1):  # Should be "per DICOM PS3.3"
"""

# WRONG: Organizational comments within enum values
class PatientPosition(Enum):
    # Head First positions  # Remove these
    HFP = "HFP"
```

---

## 2. Module Classes

*[This section will be expanded to cover DICOM module implementation standards]*

### 2.1 Module Class Structure

#### ✅ **Checklist: Module Class Requirements**

- [ ] Inherits from `Dataset`
- [ ] Implements required and optional DICOM elements
- [ ] Provides factory methods for creation
- [ ] Includes validation methods
- [ ] Follows composition over inheritance

*[To be expanded with detailed standards]*

---

## 3. IOD Classes

*[This section will be expanded to cover Information Object Definition standards]*

### 3.1 IOD Class Structure

#### ✅ **Checklist: IOD Class Requirements**

- [ ] Inherits from `SimpleBaseIOD`
- [ ] Implements required modules through composition
- [ ] Provides SOP Class UID
- [ ] Includes factory methods
- [ ] Supports DICOM file generation

*[To be expanded with detailed standards]*

---

## 4. Validator Classes

*[This section will be expanded to cover validation framework standards]*

### 4.1 Validator Structure

#### ✅ **Checklist: Validator Requirements**

- [ ] Implements validation for specific module
- [ ] Returns structured validation results
- [ ] Supports configurable validation levels
- [ ] Provides clear error messages
- [ ] Includes DICOM compliance checks

*[To be expanded with detailed standards]*

---

## 5. Documentation Standards

### 5.1 Docstring Standards

#### ✅ **Checklist: Docstring Requirements**

- [ ] **Google Style**: Use Google-style docstrings throughout
- [ ] **Sphinx Compatible**: All docstrings compatible with Sphinx autodoc
- [ ] **Type Hints**: Include comprehensive type hints in function signatures
- [ ] **Examples**: Provide usage examples for complex functions
- [ ] **DICOM References**: Include DICOM PS3.3 references where applicable

#### **Standard Docstring Format:**
```python
def example_function(param1: str, param2: int) -> bool:
    """Brief description of function purpose.
    
    Longer description if needed, including DICOM context
    and any important implementation details.
    
    Args:
        param1: Description of parameter 1
        param2: Description of parameter 2
        
    Returns:
        Description of return value
        
    Raises:
        ValueError: When invalid input provided
        DicomError: When DICOM validation fails
        
    Example:
        >>> result = example_function("test", 42)
        >>> print(result)
        True
    """
```

### 5.2 Code Comments

#### ✅ **Checklist: Comment Standards**

- [ ] **Minimal Comments**: Code should be self-documenting
- [ ] **Complex Logic**: Comment only complex algorithms or DICOM-specific requirements
- [ ] **No Inline Comments**: Avoid inline comments in enum definitions
- [ ] **TODO Comments**: Use TODO comments for planned improvements
- [ ] **DICOM Context**: Explain DICOM-specific requirements when not obvious

---

## 6. Testing Standards

*[This section will be expanded to cover testing framework and patterns]*

### 6.1 Test Structure

#### ✅ **Checklist: Test Requirements**

- [ ] **Pytest Framework**: Use pytest for all tests
- [ ] **Test Coverage**: Aim for >90% test coverage
- [ ] **DICOM Validation**: Include DICOM compliance tests
- [ ] **Edge Cases**: Test boundary conditions and error cases
- [ ] **Integration Tests**: Test module interactions

*[To be expanded with detailed testing standards]*

---

## 7. Code Quality Standards

### 7.1 Code Style

#### ✅ **Checklist: Code Style Requirements**

- [ ] **PEP 8 Compliance**: Follow PEP 8 style guidelines
- [ ] **Type Hints**: Use comprehensive type hints
- [ ] **Import Organization**: Organize imports logically
- [ ] **Line Length**: Maximum 88 characters (Black formatter standard)
- [ ] **Variable Naming**: Use descriptive variable names

### 7.2 Error Handling

#### ✅ **Checklist: Error Handling Standards**

- [ ] **Specific Exceptions**: Use specific exception types
- [ ] **Clear Messages**: Provide clear, actionable error messages
- [ ] **DICOM Context**: Include DICOM element context in errors
- [ ] **Validation Results**: Return structured validation results
- [ ] **No Silent Failures**: Never suppress errors silently

### 7.3 Performance Considerations

#### ✅ **Checklist: Performance Standards**

- [ ] **Memory Efficiency**: Minimize memory usage for large datasets
- [ ] **Fast Access**: Optimize for fast DICOM element access
- [ ] **Lazy Loading**: Implement lazy loading where appropriate
- [ ] **Caching**: Cache expensive operations appropriately
- [ ] **Profiling**: Profile performance-critical code paths

---

## Appendix A: DICOM Reference Resources

### Official DICOM Documentation
- DICOM PS3.3: Information Object Definitions
- DICOM PS3.5: Data Structures and Encoding
- DICOM PS3.6: Data Dictionary

### Internal Resources
- `docs/dicom_standard/modules/`: Local DICOM module documentation
- `missing_dicom_references.md`: Tracking document for incomplete references

---

## Appendix B: Development Tools

### Recommended Tools
- **Black**: Code formatting
- **mypy**: Static type checking
- **pytest**: Testing framework
- **Sphinx**: Documentation generation

### Pre-commit Hooks
- Code formatting verification
- Type checking
- Test execution
- Documentation generation

---

## Appendix C: Change Management

### Adding New Standards
1. Document proposed changes in this guide
2. Update existing code to match new standards
3. Add validation checks to prevent regression
4. Update development tools and CI/CD pipeline

### Deprecating Patterns
1. Mark patterns as deprecated with timeline
2. Provide migration guidance
3. Update existing codebase gradually
4. Remove deprecated patterns after grace period

---

*This guide is a living document that will be updated as the PyRT-DICOM library evolves. All contributions should adhere to these standards to maintain consistency and quality.*