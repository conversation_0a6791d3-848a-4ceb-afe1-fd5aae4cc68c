### C.12.4 General Reference Module
Table C.12-10 specifies the Attributes of the General Reference Module, which reference source and other related Instances and describe the manner of derivation.
**Table C.12-10. General Reference Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Referenced Image Sequence | (0008,1140) | ReferencedImageSequence | SQ | 1 | list[Dataset] | 3 | Other images significantly related to this image (e.g., post-localizer CT image, Mammographic biopsy or partial view images, or slide images containing control material). One or more Items are permitted in this Sequence. |
| &gt;Include Table 10-3 Image SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;Purpose of Reference Code Sequence | (0040,A170) | PurposeOfReferenceCodeSequence | SQ | 1 | list[Dataset] | 3 | Describes the purpose for which the reference is made. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7201 Referenced Image Purpose of Reference](part16.html#sect_CID_7201). |
| Referenced Instance Sequence | (0008,114A) | ReferencedInstanceSequence | SQ | 1 | list[Dataset] | 3 | Non-image composite SOP Instances that are significantly related to this Image, including waveforms that may or may not be temporally synchronized with this image. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;Purpose of Reference Code Sequence | (0040,A170) | PurposeOfReferenceCodeSequence | SQ | 1 | list[Dataset] | 1 | Code describing the purpose of the reference to the Instance(s). Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7004 Waveform Purpose of Reference](part16.html#sect_CID_7004) for referenced waveforms. D [CID 7022 Radiotherapy Purpose of Reference](part16.html#sect_CID_7022) for referenced RT Instances. |
| Derivation Description | (0008,2111) | DerivationDescription | ST | 1 | str | 3 | A text description of how this image was derived. See Section C.******** for further explanation. |
| Derivation Code Sequence | (0008,9215) | DerivationCodeSequence | SQ | 1 | list[Dataset] | 3 | A coded description of how this image was derived. See Section C.******** for further explanation. One or more Items are permitted in this Sequence. More than one Item indicates that successive derivation steps have been applied. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7203 Image Derivation](part16.html#sect_CID_7203). |
| Source Image Sequence | (0008,2112) | SourceImageSequence | SQ | 1 | list[Dataset] | 3 | The set of Image SOP Class/Instance pairs of the Images that were used to derive this Image. One or more Items are permitted in this Sequence. See Section C.******** for further explanation. |
| &gt;Include Table 10-3 Image SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;Purpose of Reference Code Sequence | (0040,A170) | PurposeOfReferenceCodeSequence | SQ | 1 | list[Dataset] | 3 | Describes the purpose for which the reference is made, that is what role the source image or Frame(s) played in the derivation of this image. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7202 Source Image Purpose of Reference](part16.html#sect_CID_7202). |
| &gt;Spatial Locations Preserved | (0028,135A) | SpatialLocationsPreserved | CS | 1 | str | 3 | The extent to which the spatial locations of all pixels are preserved during the processing of the source image that resulted in the current image **Enumerated Values:** YES NO REORIENTED_ONLY A projection radiograph that has been flipped, and/or rotated by a multiple of 90 degrees ### Note - This applies not only to images with a known relationship to a 3D space, but also to projection images. For example, a projection radiograph such as a mammogram that is processed by a point image processing operation such as contrast enhancement, or a smoothing or edge enhancing convolution, would have a Value of YES for this Attribute. A projection radiograph that had been magnified or warped geometrically would have a Value of NO for this Attribute. A projection radiograph that has been flipped, and/or rotated by a multiple of 90 degrees, such that transformation of pixel locations is possible by comparison of the Values of Patient Orientation (0020,0020) would have a Value of REORIENTED_ONLY. This Attribute is typically of importance in relating images with Presentation Intent Type (0008,0068) Values of FOR PROCESSING and FOR PRESENTATION. - When the Value of this Attribute is NO, it is not possible to locate on the current image any pixel coordinates that are referenced relative to the source image, such as for example, might be required for rendering CAD findings derived from a referenced FOR PROCESSING image on the current FOR PRESENTATION image. |
| &gt;Patient Orientation | (0020,0020) | PatientOrientation | CS | 2 | str | 1C | The Patient Orientation values of the source image. Required if the Value of Spatial Locations Preserved (0028,135A) is REORIENTED_ONLY. |
| Source Instance Sequence | (0042,0013) | SourceInstanceSequence | SQ | 1 | list[Dataset] | 3 | The set of non-image composite SOP Instances that were used to derive this Instance. One or more Items are permitted in this Sequence. See Section C.******** for further explanation. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;Purpose of Reference Code Sequence | (0040,A170) | PurposeOfReferenceCodeSequence | SQ | 1 | list[Dataset] | 3 | Describes the purpose for which the reference is made, that is what role the source Instance(s) played in the derivation of this Instance. Only a single Item single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7013 Non-Image Source Instance Purpose of Reference](part16.html#sect_CID_7013). |
#### C.12.4.1 General Reference Module Attribute Descriptions
##### C.******** Derivation Description
If an Image is identified to be a Derived Image (see Section C.*******.2 Image Type ), Derivation Description (0008,2111) and Derivation Code Sequence (0008,9215) describe the way in which the image was derived. They may be used whether or not the Source Image Sequence (0008,2112) is provided. They may also be used in cases when the Derived Image pixel data is not significantly changed from one of the source images and the SOP Instance UID of the Derived Image is the same as the one used for the source image.
### Note
- Examples of Derived Images that would normally be expected to affect professional interpretation and would thus have a new UID include: images resulting from image processing of another image (e.g., unsharp masking),
- a multiplanar reformatted CT image,
- a DSA image derived by subtracting pixel values of one image from another.
- an image that has been decompressed after having been compressed with a lossy compression algorithm. To ensure that the user has the necessary information about the lossy compression, the approximate compression ratio may be included in Derivation Description (0008,2111).
An example of a Derived Image that would normally not be expected to affect professional interpretation and thus would not require a new SOP Instance UID (0008,0018) is an image that has been padded with additional rows and columns for more display purposes.
- An image may be lossy compressed, e.g., for long term archive purposes, and its SOP Instance UID (0008,0018) changed. [PS3.4](part04.html#PS3.4) provides a mechanism by which a query for the original Image Instance may return a reference to the SOP Instance UID (0008,0018) of the lossy compressed version of the image using the Alternate Representation Sequence (0008,3001). This allows an application processing a SOP Instance that references the SOP Instance UID (0008,0018) of the original image, e.g., a Structured Report, to obtain a reference to an accessible version of the image even if the original SOP Instance is no longer available.
##### C.******** Source Image Sequence
If an Image is identified to be a Derived Image (see Section C.*******.2 Image Type ), Source Image Sequence (0008,2112) is an optional list of source images used to create the Derived image. Source Instance Sequence (0042,0013) is an optional list of non-image source Instances that were used to create this Instance. Source Image Sequence (0008,2112) and/or Source Instance Sequence (0042,0013) may be used whether or not there is a description of the way the Instance was derived in Derivation Description (0008,2111) or Derivation Code Sequence (0008,9215).
Images shall not be referenced by Source Instance Sequence (0042,0013).
### Note
Multiple Items may be present within Source Image Sequence (0008,2112) and/or Source Instance Sequence (0042,0013), in which case either:
- those Instance were combined to make the derived Instance (e.g., multiple source images to make an MPR or MIP), or
- each of the Items represents a step in the successive derivation of an Instance (e.g., when an image has had successive lossy compression steps applied to it),
- some combination of the above.
The Purpose of Reference Code Sequence (0040,A170) and the Attributes within the referenced Instances themselves may be used to determine the history of the derivation, which is not otherwise explicitly specified.