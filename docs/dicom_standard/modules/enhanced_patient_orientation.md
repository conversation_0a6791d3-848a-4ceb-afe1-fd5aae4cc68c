#### C.7.6.30 Enhanced Patient Orientation Module
Table C.7.6.30-1 specifies the Attributes of the Enhanced Patient Orientation Module, which describe the patient orientation with respect to gravity and equipment
**Table C.7.6.30-1. Enhanced Patient Orientation Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Include Table 10-15a Patient Orientation and Equipment Relationship Macro Attributes |  |  |  |  |  |  |  |
##### C.******** Enhanced Patient Orientation Module Attributes
The Enhanced Patient Orientation Module describes the patient orientation with respect to gravity and to the equipment (i.e., the gantry) using three Attributes invoked from the Patient Orientation and Equipment Relationship Macro Attributes. See examples in Table C.********-1
Patient Orientation Code Sequence (0054,0410) describes the rough orientation of the imaged part of the Patient with respect to gravity; vertical, horizontal, or in-between.
### Note
[(102539006, SCT, "semi-erect")](http://snomed.info/id/102539006) refers to the imaged anatomy being partway between erect and recumbent, for example, inclined 45 degrees.
Patient Orientation Modifier Code Sequence (0054,0412) provides a more detailed description of the orientation and positioning of the patient.
Patient Equipment Relationship Code Sequence (3010,0030) describes the orientation of the Patient with respect to the imaging equipment.
**Table C.********-1. Examples of Enhanced Patient Orientation Module Attribute Values**
| Graphic | Description |  |  |  |  | Patient Orientation Code Sequence (0054,0410) | Patient Orientation Modifier Code Sequence (0054,0412) | Patient Equipment Relationship Code Sequence (3010,0030) | Patient Position (0018,5100) |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
Current Conventional CT
[(102538003, SCT, "recumbent")](http://snomed.info/id/102538003)
[(40199007, SCT, "supine")](http://snomed.info/id/40199007)
[(102540008, SCT, "headfirst")](http://snomed.info/id/102540008)
HFS
Standing CT of the Chest
[(C86043, NCIt, "erect")](http://evsexplore.semantics.cancer.gov/evsexplore/concept/ncit/C86043)
[(10904000, SCT, "standing")](http://snomed.info/id/10904000)
[(102540008, SCT, "headfirst")](http://snomed.info/id/102540008)
HFV
Standing CT of the Foot
[(C86043, NCIt, "erect")](http://evsexplore.semantics.cancer.gov/evsexplore/concept/ncit/C86043)
[(10904000, SCT, "standing")](http://snomed.info/id/10904000)
[(102540008, SCT, "headfirst")](http://snomed.info/id/102540008)
HFV
Seated CT of the Chest
[(C86043, NCIt, "erect")](http://evsexplore.semantics.cancer.gov/evsexplore/concept/ncit/C86043)
[(33586001, SCT, "sitting")](http://snomed.info/id/33586001)
[(102540008, SCT, "headfirst")](http://snomed.info/id/102540008)
HFV
Dedicated Breast CT
[(102538003, SCT, "recumbent")](http://snomed.info/id/102538003)
[(1240000, SCT, "prone")](http://snomed.info/id/1240000)
[(126833, DCM, "anterior first")](part16.html#DCM_126833)
AFP
Dental Cone beam
[(C86043, NCIt, "erect")](http://evsexplore.semantics.cancer.gov/evsexplore/concept/ncit/C86043)
[(10904000, SCT, "standing")](http://snomed.info/id/10904000)
[(102540008, SCT, "headfirst")](http://snomed.info/id/102540008)
HFV
##### C.******** Guidance for Vertical Gantries
This Section provides guidance on the population of position and orientation attributes in images that were acquired on a vertical gantry. A vertical gantry is defined as one where the axis of the bore is aligned in the direction of gravity (see Table C.********-1 ), while a horizontal gantry (which is the most typical arrangement) has the axis of the bore aligned horizontally (i.e., orthogonal to gravity). If motion is required to cover the scan range, a vertical gantry might move up and/or down during scanning, or the patient support might move up and/or down.
Patient position and orientation can be considered in terms of several relationships; the image pixels with respect to the patient, the patient with respect to the gantry, and the patient with respect to gravity.
###### C.********.1 Image Pixels with Respect to the Patient
The position and orientation of the pixels with respect to the patient is independent of the gantry and is thus the same for both vertical and horizontal gantries. A mathematical description is provided in the Image Plane Module by Type 1 Attributes for Image Position (Patient) (0020,0032) and Image Orientation (Patient) (0020,0037). See Section C.*******.1.
The General Image Module includes the Type 2C Attribute Patient Orientation (0020,0020), which provides a rough anatomical orientation. As stated in Section C.*******.1, two letters indicate the direction from the first to last pixel in a row, and the direction from the first to last pixel in a column, respectively using letters for Anterior, Posterior, Left, Right, Head, and Feet.
###### C.********.2 Patient Position with Respect to the Gantry and Gravity
The Patient Position (0018,5100) and the Enhanced Patient Orientation Module Attributes:
- are intended for annotation, not for mathematical calculations; the patient orientation with respect to gravity can be useful to a clinician viewing the images and wanting to understand how gravity might be affecting the positioning of the organs;
- capture the relative orientation of the patient; the relative position (i.e., location) of the patient is not captured;
- relate the patient orientation to the scan axis of the gantry.
The orientation of the patient with respect to the gantry is generally expressed in terms of the "front" of the gantry. To maintain consistency with the existing definitions, for the purposes of these Attributes for a vertical gantry the "front" of the imaging equipment is considered to be the side containing the bore that is closest to the patient support (which may be the ground).
These Attributes describe the orientation of the patient with respect to the scan axis of the gantry in terms of the patient when they are fully outside the imaging equipment on the front side of the imaging equipment. The use of terms like "head-first" does not describe the direction of scan progression, the slice order, nor the direction of relative patient motion (if any). The Values only encode relative orientation. So, for a horizontal gantry, a Value of Head First is still valid when the patient table is advanced fully into the gantry and the patient is scanned as the table comes back out, resulting in the head being temporally the last body part scanned. Correspondingly, for a vertical gantry, the Value will almost always be Head First when scanning progresses either upwards or downwards, given that the front of the gantry is defined as the face closest to the patient support and the patient will almost always be seated or standing upright.
For horizontal gantries, the orientation of the patient with respect to gravity can also be used to infer the approximate patient "rotation" around the scan axis. The anatomical orientation of the image axes is captured in Image Position (Patient) (0020,0032) as described above.
For vertical gantries, the patient could readily face any direction while standing. The gantry may or may not be able to sense the such patient "rotation" around the scan axis. If not, the Values in Image Orientation (Patient) (0020,0037) will likely depend on standardized acquisition procedures and/or technologist input to correctly encode orientation details, just as is done for patients on conventional horizontal scanners who are prone or decubitus.
###### C.********.3 Relationship to Patient Position
The General Series Module includes the Type 2C Attribute Patient Position (0018,5100), which is intended to support annotation of the orientation of the patient with respect to the scan axis of the gantry and with respect to gravity. It does not describe the direction of scanning, slice order, or image orientation.
The information in the first two characters of the code string corresponds to the information in the Patient Equipment Relationship Code Sequence (3010,0030). See Section C.*******.2 for a definition of the code string character values.
The information in the subsequent characters of the code string value corresponds to the information in the Patient Orientation Modifier Code Sequence (0054,0412), although only the most common situations are covered, and not all combinations of these characters with the preceding characters are considered valid. See Section C.*******.2 for a definition of the code string character values.
###### C.********.4 Relationship to Acquisition Context
Acquisition Context Sequence (0040,0555) permits inclusion of Content Items such as the following, some of which might be more common in a vertical gantry:
- [(130324, DCM, "Functional condition present during acquisition")](part16.html#DCM_130324) = [(87731000, SCT, "Weight bearing")](http://snomed.info/id/87731000)
- [(130324, DCM, "Functional condition present during acquisition")](part16.html#DCM_130324) = [(367740008, SCT, "Suspension")](http://snomed.info/id/367740008)
- [(130324, DCM, "Functional condition present during acquisition")](part16.html#DCM_130324) = [(129411004, SCT, "Traction")](http://snomed.info/id/129411004)