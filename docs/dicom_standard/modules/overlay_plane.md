### C.9.2 Overlay Plane Module
Table C.9-2 specifies the Attributes of the Overlay Plane Module, which describe characteristics of an Overlay Plane.
An Overlay Plane describes graphics or bit-mapped text that is associated with an Image. It may also describe a Region of Interest in an Image.
Each Overlay Plane is one bit deep. Sixteen separate Overlay Planes may be associated with an Image.
Overlay data is stored in Overlay Data (60xx,3000). See the Section Repeating Groups in [PS3.5](part05.html#PS3.5) for a description of permitted values of 60xx.
### Note
Overlay data stored in unused bit planes of Pixel Data (7FE0,0010) with Samples per Pixel (0028,0002) of 1 was previously described in DICOM. This usage has now been retired. See [PS3.3-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf) and [PS3.5-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_05pu.pdf).
Attributes describing display of grayscale and color overlays were defined in a previous release of the DICOM Standard. These have now been retired. How an Overlay Plane is rendered is undefined; specifically there is no mechanism to specify with what color or intensity an Overlay Plane is to be displayed, except when rendered under the control of a Softcopy Presentation State SOP Instance.
**Table C.9-2. Overlay Plane Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Overlay Rows | (60xx,0010) |  |  |  |  | 1 | Number of Rows in Overlay. |
| Overlay Columns | (60xx,0011) |  |  |  |  | 1 | Number of Columns in Overlay. |
| Overlay Type | (60xx,0040) |  |  |  |  | 1 | Indicates whether this overlay represents a region of interest or other graphics. **Enumerated Values:** G Graphics R ROI |
| Overlay Origin | (60xx,0050) |  |  |  |  | 1 | Location of first overlay point with respect to pixels in the image, given as row\column. The upper left pixel of the image has the coordinate 1\1. Column values greater than 1 indicate the overlay plane origin is to the right of the image origin. Row values greater than 1 indicate the overlay plane origin is below the image origin. Values less than 1 indicate the overlay plane origin is above or to the left of the image origin. ### Note Values of 0\0 indicate that the overlay pixels start 1 row above and one column to the left of the image pixels. |
| Overlay Bits Allocated | (60xx,0100) |  |  |  |  | 1 | Number of Bits Allocated in the Overlay. The Value of this Attribute shall be 1. ### Note Formerly the Standard described embedding the overlay data in the Image Pixel Data (7FE0,0010), in which case the Value of this Attribute was required to be the same as Bits Allocated (0028,0100). This usage has been retired. See [PS3.3-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf). |
| Overlay Bit Position | (60xx,0102) |  |  |  |  | 1 | The Value of this Attribute shall be 0. ### Note Formerly the Standard described embedding the overlay data in the Image Pixel Data (7FE0,0010), in which case the Value of this Attribute specified the bit in which the overlay was stored. This usage has been retired. See [PS3.3-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf). |
| Overlay Data | (60xx,3000) |  |  |  |  | 1 | Overlay pixel data. The order of pixels encoded for each overlay is left to right, top to bottom, i.e., the upper left pixel is encoded first followed by the remainder of the first row, followed by the first pixel of the 2 nd row, then the remainder of the 2 nd row and so on. Overlay data shall be contained in this Attribute. See Section C.******* for further explanation. |
| Overlay Description | (60xx,0022) |  |  |  |  | 3 | User-defined comments about the overlay. |
| Overlay Subtype | (60xx,0045) |  |  |  |  | 3 | Defined Term that identifies the intended purpose of the Overlay Type. See Section C.******* for further explanation. |
| Overlay Label | (60xx,1500) |  |  |  |  | 3 | A user defined text string that may be used to label or name this overlay. |
| ROI Area | (60xx,1301) |  |  |  |  | 3 | Number of pixels in ROI area. See Section C.******* for further explanation. |
| ROI Mean | (60xx,1302) |  |  |  |  | 3 | ROI Mean. See Section C.******* for further explanation. |
| ROI Standard Deviation | (60xx,1303) |  |  |  |  | 3 | ROI standard deviation. See Section C.******* for further explanation. |
#### C.9.2.1 Overlay Plane Module Attribute Descriptions
##### C.******* Overlay Type
There are two specific types of overlays. The type is specified in this Attribute.
A Region of Interest (ROI) is a specific use of an Overlay. The overlay bits corresponding to all the pixels included in the ROI shall be set to 1. All other bits are set to 0. This is used to specify an area of the image of particular interest.
A Graphics overlay may express reference marks, graphic annotation, or bit mapped text, etc. A Graphics overlay may be used to mark the boundary of a ROI. If this is the case and the ROI statistical parameters are used, they will only refer to the pixels under the boundaries, not those in the included regions.
The overlay bits corresponding to all the pixels included in the Graphics shall be set to 1. All other bits are set to 0.
##### C.******* ROI Area, ROI Mean and ROI Standard Deviation
These Attributes contain the statistical parameters of the ROI. The values of these parameters are for the overlay pixel values set to 1.
##### C.******* Overlay Subtype
**Defined Terms:**
USER
User created graphic annotation (e.g., operator)
AUTOMATED
Machine or algorithm generated graphic annotation, such as output of a Computer Assisted Diagnosis algorithm
ACTIVE IMAGE AREA
Identification of the active area of an image
### Note
Additional or alternative Defined Terms may be specified in modality specific Modules, such as the specific use of "ACTIVE 2D/BMODE IMAGE AREA" as described in the Section C.8.5.6.1.11 Overlay Subtype.
An active image area overlay identifies all pixels in the Pixel Data that are generated from image data acquisition. Each pixel in the active area shall have an overlay bit value of 1; all other bits are set to 0. Any area of burned in annotation (not generated from image data acquisition) shall be excluded from the active image area.
### Note
- For example, the active image area overlay may delineate a non-rectangular (e.g., fan) shaped acquisition by an ultrasound transducer, or a circular reconstructed field of view from a tomographic acquisition.
- Whether or not the excluded area of burned in annotation extends beyond the specifically modified pixels, e.g. to exclude an entire bounding box area around the text or graphic annotation, is not specified by the Standard.
- The active image area for projection X-ray is specified by the Section C.8.7.3 X-Ray Collimator Module.
##### C.9.2.1.4 Multi-frame Image
When an overlay is part of a Multi-frame Image and is not a Multi-frame Overlay (Number of Frames in Overlay (60xx,0015) and Image Frame Origin (60xx,0051) are absent), the overlay shall be applied to all Frames in the image.