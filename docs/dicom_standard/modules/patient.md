#### C.7.1.1 Patient Module
Table C.7-1 specifies the Attributes of the Patient Module, which identify and describe the Patient who is the subject of the Study. This Module contains Attributes of the Patient that are needed for interpretation of the Composite Instances and are common for all Studies performed on the Patient. It contains Attributes that are also included in the Section C.2 Patient Modules.
**Table C.7-1. Patient Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Patient's Name | (0010,0010) | PatientName | PN | 1 | str, PersonName | 2 | Pat<PERSON>'s full name. |
| Patient ID | (0010,0020) | PatientID | LO | 1 | str | 2 | Primary identifier for the Patient. ### Note In the case of imaging a group of small animals simultaneously, the single Value of this identifier corresponds to the identification of the entire group. See also Section C.*******.1. |
| Include Table 10-18 Issuer of Patient ID Macro Attributes |  |  |  |  |  |  |  |
| Type of Patient ID | (0010,0022) | TypeOfPatientID | CS | 1 | str | 3 | The type of identifier in the Patient ID (0010,0020). **Defined Terms:** TEXT RFID BARCODE ### Note - The identifier is coded as a string regardless of the type, not as a binary value. - When this Attribute has a Value of BARCODE, Patient ID (0010,0020) may or may not have the same Value as Barcode Value (2200,0005) in the SOP Common Module, if present. |
| Patient's Birth Date | (0010,0030) | PatientBirthDate | DA | 1 | str | 2 | Birth date of the Patient. |
| Patient's Birth Date in Alternative Calendar | (0010,0033) | PatientBirthDateInAlternativeCalendar | LO | 1 | str | 3 | Date of birth of the named Patient in the Alternative Calendar (0010,0035). ### Note No format is specified for alternative calendar dates so none should be assumed. |
| Patient's Death Date in Alternative Calendar | (0010,0034) | PatientDeathDateInAlternativeCalendar | LO | 1 | str | 3 | Date of death of the named Patient in the Alternative Calendar (0010,0035). ### Note No format is specified for alternative calendar dates so none should be assumed. |
| Patient's Alternative Calendar | (0010,0035) | PatientAlternativeCalendar | CS | 1 | str | 1C | The Alternative Calendar used for Patient's Birth Date in Alternative Calendar (0010,0033) and Patient's Death Date in Alternative Calendar (0010,0034). See Section C.7.1.5 for Defined Terms. Required if either Patient's Birth Date in Alternative Calendar (0010,0033) or Patient's Alternative Death Date in Calendar (0010,0034) is present. |
| Patient's Sex | (0010,0040) | PatientSex | CS | 1 | str | 2 | Sex of the named Patient. **Enumerated Values:** M male F female O other See Note 2 and Note 3. |
| Referenced Patient Photo Sequence | (0010,1100) | ReferencedPatientPhotoSequence | SQ | 1 | list[Dataset] | 3 | A photo to confirm the identity of a Patient. Only a single Item is permitted in this Sequence. See C.2.2.1.1. |
| &gt;Include Table 10-3b Referenced Instances and Access Macro Attributes |  |  |  |  |  |  |  |
| Quality Control Subject | (0010,0200) | QualityControlSubject | CS | 1 | str | 3 | Indicates whether or not the subject is a quality control phantom. **Enumerated Values:** YES NO If this Attribute is absent, then the subject may or may not be a phantom. This Attribute describes a characteristic of the Imaging Subject. It is distinct from Quality Control Image (0028,0300) in the General Image Module, which is used to describe an image acquired. |
| Referenced Patient Sequence | (0008,1120) | ReferencedPatientSequence | SQ | 1 | list[Dataset] | 3 | A Sequence that provides reference to a Patient. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| Patient's Birth Time | (0010,0032) | PatientBirthTime | TM | 1 | str | 3 | Birth time of the Patient. |
| Other Patient IDs Sequence | (0010,1002) | OtherPatientIDsSequence | SQ | 1 | list[Dataset] | 3 | A Sequence of identification numbers or codes used to identify the Patient, which may or may not be human readable, and may or may not have been obtained from an implanted or attached device such as an RFID or barcode. One or more Items are permitted in this Sequence. ### Note This Attribute replaces the use of Other Patient IDs (0010,1000), which did not specify an issuer for each other identifier, and which has been retired. |
| &gt;Patient ID | (0010,0020) | PatientID | LO | 1 | str | 1 | An identifier for the Patient. ### Note In the case of imaging a group of small animals simultaneously, the single Value of this identifier corresponds to the identification of the entire group. See also Section C.*******.1. |
| &gt;Include Table 10-18 Issuer of Patient ID Macro Attributes |  |  |  |  |  |  |  |
| &gt;Type of Patient ID | (0010,0022) | TypeOfPatientID | CS | 1 | str | 1 | The type of identifier in the Patient ID (0010,0020) in this Item. **Defined Terms:** TEXT RFID BARCODE ### Note - The identifier is coded as a string regardless of the type, not as a binary value. - When this Attribute has a Value of BARCODE, Patient ID (0010,0020) may or may not have the same Value as Barcode Value (2200,0005) in the SOP Common Module, if present. |
| Other Patient Names | (0010,1001) | OtherPatientNames | PN | 1-n | str, PersonName | 3 | Other names used to identify the Patient. |
| Ethnic Group Code Sequence | (0010,2161) | EthnicGroupCodeSequence | SQ | 1 | list[Dataset] | 3 | Ethnic group or race of Patient. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 6099 Racial Group](part16.html#sect_CID_6099). |
| Ethnic Groups | (0010,2162) |  |  |  |  | 3 | Ethnic group(s) or race(s) of Patient. One or more Values may be present. ### Note This Attribute replaces the use of Ethnic Group (0010,2160), which is now retired. See [PS3.3-2025a](http://medical.nema.org/MEDICAL/Dicom/2025a/output/pdf/part03.pdf). |
| Patient Comments | (0010,4000) | PatientComments | LT | 1 | str | 3 | User-defined additional information about the Patient. |
| Patient Species Description | (0010,2201) | PatientSpeciesDescription | LO | 1 | str | 1C | The taxonomic rank value (e.g., genus, subgenus, species or subspecies) of the Patient. See Section C.*******.3. Required if the Patient is a non-human organism and if Patient Species Code Sequence (0010,2202) is not present. May be present otherwise. |
| Patient Species Code Sequence | (0010,2202) | PatientSpeciesCodeSequence | SQ | 1 | list[Dataset] | 1C | The taxonomic rank value (e.g., genus, subgenus, species or subspecies) of the Patient. See Section C.*******.3. Only a single Item shall be included in this Sequence. Required if the Patient is a non-human organism and if Patient Species Description (0010,2201) is not present. May be present otherwise. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7454 Animal Taxonomic Rank Value](part16.html#sect_CID_7454). |
| Patient Breed Description | (0010,2292) | PatientBreedDescription | LO | 1 | str | 2C | The breed of the Patient. See Section C.*******.1. Required if the Patient is a non-human organism and if Patient Breed Code Sequence (0010,2293) is empty. May be present otherwise. |
| Patient Breed Code Sequence | (0010,2293) | PatientBreedCodeSequence | SQ | 1 | list[Dataset] | 2C | The breed of the Patient. See Section C.*******.1. Zero or more Items shall be included in this Sequence. Required if the Patient is a non-human organism. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7480 Breed](part16.html#sect_CID_7480). |
| Breed Registration Sequence | (0010,2294) | BreedRegistrationSequence | SQ | 1 | list[Dataset] | 2C | Information identifying a non-human organism within a breed registry. Zero or more Items shall be included in this Sequence. Required if the Patient is a non-human organism. |
| &gt;Breed Registration Number | (0010,2295) | BreedRegistrationNumber | LO | 1 | str | 1 | Identification number of a non-human organism within the registry. |
| &gt;Breed Registry Code Sequence | (0010,2296) | BreedRegistryCodeSequence | SQ | 1 | list[Dataset] | 1 | Identification of the organization with which a non-human organism is registered. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7481 Breed Registry](part16.html#sect_CID_7481). |
| Strain Description | (0010,0212) | StrainDescription | UC | 1 | str | 3 | The strain of the Patient. See Section C.*******.4. |
| Strain Nomenclature | (0010,0213) | StrainNomenclature | LO | 1 | str | 3 | The nomenclature used for Strain Description (0010,0212). See Section C.*******.4. |
| Strain Code Sequence | (0010,0219) | StrainCodeSequence | SQ | 1 | list[Dataset] | 3 | A coded identification of the strain of the Patient. See Section C.*******.4. One or more Items are permitted in this Sequence. If more than one Item is present, each Item represents the same information but encoded using a different Coding Scheme (rather than post-coordinated modifiers). |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Strain Additional Information | (0010,0218) | StrainAdditionalInformation | UT | 1 | str | 3 | Additional information about the strain of the Patient that is not encoded in the formal nomenclature used in Strain Description (0010,0212). See Section C.*******.4. |
| Strain Stock Sequence | (0010,0216) | StrainStockSequence | SQ | 1 | list[Dataset] | 3 | Information identifying a non-human organism within a strain stock. Only a single Item is permitted in this Sequence. |
| &gt;Strain Stock Number | (0010,0214) | StrainStockNumber | LO | 1 | str | 1 | The stock number of the strain of the Patient issued by the organization identified by Strain Source (0010,0217). See Section C.*******.4. |
| &gt;Strain Source | (0010,0217) | StrainSource | LO | 1 | str | 1 | Identification of the organization that is the source of the non-human organism, issued by the registry identified by Strain Source Registry Code Sequence (0010,0215). See Section C.*******.4. |
| &gt;Strain Source Registry Code Sequence | (0010,0215) | StrainSourceRegistryCodeSequence | SQ | 1 | list[Dataset] | 1 | Identification of the organization that is the registry of sources of non-human organisms. See Section C.*******.4. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7490 Research Animal Source Registry](part16.html#sect_CID_7490). |
| Genetic Modifications Sequence | (0010,0221) | GeneticModificationsSequence | SQ | 1 | list[Dataset] | 3 | The genetic modifications of the Patient. One or more Items are permitted in this Sequence. See Section C.*******.4. |
| &gt;Genetic Modifications Description | (0010,0222) | GeneticModificationsDescription | UC | 1 | str | 1 | The genetic modifications of the Patient described using a specific nomenclature. |
| &gt;Genetic Modifications Nomenclature | (0010,0223) | GeneticModificationsNomenclature | LO | 1 | str | 1 | The nomenclature used for Genetic Modifications Description (0010,0222). |
| &gt;Genetic Modifications Code Sequence | (0010,0229) | GeneticModificationsCodeSequence | SQ | 1 | list[Dataset] | 3 | A coded identification of the genetic modifications of the Patient. One or more Items are permitted in this Sequence. If more than one Item is present, each Item represents the same information but encoded using a different Coding Scheme (rather than post-coordinated modifiers). |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Responsible Person | (0010,2297) | ResponsiblePerson | PN | 1 | str, PersonName | 2C | Name of person with medical or welfare decision making authority for the Patient. Required if the Patient is a non-human organism. May be present otherwise. |
| Responsible Person Role | (0010,2298) | ResponsiblePersonRole | CS | 1 | str | 1C | Relationship of Responsible Person to the Patient. See Section C.*******.2 for Defined Terms. Required if Responsible Person is present and has a Value. |
| Responsible Organization | (0010,2299) | ResponsibleOrganization | LO | 1 | str | 2C | Name of organization with medical or welfare decision making authority for the Patient. Required if Patient is a non-human organism. May be present otherwise. |
| Patient Identity Removed | (0012,0062) | PatientIdentityRemoved | CS | 1 | str | 3 | The true identity of the Patient has been removed from the Attributes and the Pixel Data **Enumerated Values:** YES NO |
| De-identification Method | (0012,0063) | DeidentificationMethod | LO | 1-n | str | 1C | A description or label of the mechanism or method use to remove the Patient's identity. May be multi-valued if successive de-identification steps have been performed. ### Note - This may be used to describe the extent or thoroughness of the de-identification, for example whether or not the de-identification is for a "Limited Data Set" (as per HIPAA Privacy Rule). - The characteristics of the de-identifying equipment and/or the responsible operator of that equipment may be recorded as an additional Item of the Contributing Equipment Sequence (0018,A001) in the SOP Common Module. De-identifying equipment may use a Purpose of Reference of [(109104, DCM, "De-identifying Equipment")](part16.html#DCM_109104). Required if Patient Identity Removed (0012,0062) is present and has a Value of YES and De-identification Method Code Sequence (0012,0064) is not present. May be present otherwise. |
| De-identification Method Code Sequence | (0012,0064) | DeidentificationMethodCodeSequence | SQ | 1 | list[Dataset] | 1C | A code describing the mechanism or method use to remove the Patient's identity. One or more Items shall be included in this Sequence. Multiple Items are used if successive de-identification steps have been performed or to describe options of a defined profile. Required if Patient Identity Removed (0012,0062) is present and has a Value of YES and De-identification Method (0012,0063) is not present. May be present otherwise. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7050 De-identification Method](part16.html#sect_CID_7050). |
| Include Table C.7.1.4-1 Patient Group Macro Attributes |  |  |  |  |  |  |  |
### Note
- Previously, Other Patient IDs (0010,1000) was included in this table. This Attribute has been retired. See [PS3.3-2017a](http://dicom.nema.org/MEDICAL/Dicom/2017a/output/pdf/part03.pdf).
- The Value of Patient's Sex (0010,0040) reflects the documentation policies of the local administration for the sex Attributes of the patient. It is often populated based on the PID-8 field in an HL7v2 message, and thus follow the HL7v2 rules that defer the definition to the local administration.
- The DICOM Information Model (see Section 7.3.1.1 ) has a single Patient entity that is used for all Studies and Series for that Patient. As a result, it doesnt support the Value of Patients Sex (0010,0040) being different for different Studies, Series, or Instances for that Patient. This poses issues: when the patient sex changes.
- when patient records are transferred between different systems with different administrative rules, since the specification of the meaning of "M", "F", and "O" has been deferred to the local administration by HL7, and DICOM implementations have usually used the values provided in HL7 orders as the basis for Attribute Values in the DICOM Instances.
- when clinical trials need to define sex for clinical trial purposes.
- when different organizations that have different definitions share one image archive.
To better handle the above issues, there are other sex and gender related Attributes that are in the Patient Study Module (see Section C.7.2.2 ) for which the single Value constraint does not apply because they are permitted to be different in different Studies and the definitions allow reference to terminology standards. These Attributes can convey the history of patient sex and gender. Although these Attributes are optional, a DICOM implementation could use them instead of Patients Sex (0010,0040) if this is compatible with local administration choices for other systems. Another possibility is to make the Patients Sex (0010,0040) empty as an indication that the Attributes in the Patient Study should be used for that Study.
##### C.******* Patient Module Attribute Descriptions
###### C.*******.1 Patient Breed Description and Code Sequence
The breed of a non-human organism, if known, shall be encoded in either Patient Breed Description (0010,2292) or Patient Breed Code Sequence (0010,2293) or both.
In the case of a mixed breed, it shall be either:
- described in plain text in Patient Breed Description (0010,2292), e.g., "Border Collie American Bulldog mix", or just "Mixed", or
- coded as multiple specific breeds by composing the mix as multiple Items of Patient Breed Code Sequence (0010,2293), e.g., [(132561000, SCT, "Border Collie dog breed")](http://snomed.info/id/132561000) followed by [(132534000, SCT, "American Bulldog breed")](http://snomed.info/id/132534000), or
- encoded non-specifically with a code that means "mixed breed" of the appropriate species, as defined in [CID 7486 Mixed Breed](part16.html#sect_CID_7486), which is included in [CID 7480 Breed](part16.html#sect_CID_7480), e.g., [(*********, SCT, "Mixed breed dog")](http://snomed.info/id/*********).
### Note
The absence of a Value for both Patient Breed Description (0010,2292) and Patient Breed Code Sequence (0010,2293) implies that the breed is unknown, not that it is mixed.
###### C.*******.2 Responsible Person Role
**Defined Terms:**
OWNER
PARENT
CHILD
SPOUSE
SIBLING
RELATIVE
GUARDIAN
CUSTODIAN
AGENT
INVESTIGATOR
VETERINARIAN
###### C.*******.3 Patient Species (Taxonomic Rank Value)
If the species is not known then the Patient Species Description (0010,2201) or Patient Species Code Sequence (0010,2202) may describe a more general taxonomic rank value, such as a genus or subgenus, family or subfamily.
### Note
E.g., "Mus" rather than "Mus musculus".
If the subspecies is known then the Patient Species Description (0010,2201) or Patient Species Code Sequence (0010,2202) may describe the subspecies.
### Note
E.g., "Canis lupus familiaris" rather than "Canis lupus".
###### C.*******.4 Patient Strain and Genetic Modifications
The strain of a non-human organism (group of non-human organisms that is genetically uniform), if known, may be encoded in Strain Description (0010,0212). The nomenclature used may be encoded in Strain Nomenclature (0010,0213). A precoordinated code identifying the strain may be encoded in Strain Code Sequence (0010,0219).
**Defined Terms for Strain Nomenclature (0010,0213) and Genetic Modifications Nomenclature (0010,0223):**
MGI_2013
International Committee on Standardized Genetic Nomenclature for Mice, Rat Genome and Nomenclature Committee. MGI-Guidelines for Nomenclature of Mouse and Rat Strains. 2013/10. Available from: [http://www.informatics.jax.org/mgihome/nomen/strains.shtml](http://www.informatics.jax.org/mgihome/nomen/strains.shtml)
### Note
- A pair of text and nomenclature Attributes are used, since standard nomenclatures typically define values that are constructed from multiple components, and do not distinguish between value and meaning. These are distinct from the precoordinated codes used in Strain Code Sequence (0010,0219).
- Some strain and genetic modification nomenclatures make use of superscripts. To encode these superscripts consistently in an unformatted string, the convention of enclosing the superscript text in "&lt;" and "&gt;" pairs may be used. E.g., "D2.B6-Ahr b-1 /J" would be encoded as "D2.B6-Ahr&lt;b-1&gt;/J".
- Relevant information that is not encoded in the formal description of the strain (i.e., not defined in the nomenclature used), such as the number of transgenes, may be encoded as plain text in Strain Additional Information (0010,0218).
The strain of a non-human organism may be more specifically identified by the Attributes within Strain Stock Sequence (0010,0216).
### Note
- The MGI-Guidelines for Nomenclature of Mouse and Rat Strains recommends the use of the laboratory codes assigned by the Institute of Laboratory Animal Research (ILAR). See the International Laboratory Code Registry (ILCR) [http://dels.nas.edu/global/ilar/lab-codes](http://dels.nas.edu/global/ilar/lab-codes).
- Because allele names are closely tied to gene names/symbols it is necessary to have a unique and permanent code for any allele that is part of a genotype of interest. For mice, MGI is the authoritative source of the nomenclature for genes and alleles and maintains unique, permanent codes for these entities. The MGI provides a report of all precoordinated MGI codes that are assigned to specific strains at [http://www.informatics.jax.org/downloads/reports/MGI_Strain.rpt](http://www.informatics.jax.org/downloads/reports/MGI_Strain.rpt). These may be used in Strain Code Sequence (0010,0219) and Genetic Modifications Code Sequence (0010,0229) with a Coding Scheme of "MGI".
- Another source of pre-coordinated codes for strains is the NCI Thesaurus, which includes a snapshot of strains from the International Mouse Strain Resource (IMSR), as children of (C14421, NCIt, "Inbred Mouse Strains"). See [http://ncit.nci.nih.gov/ncitbrowser/pages/concept_details.jsf?dictionary=NCI_Thesaurus&amp;code=C14421](http://ncit.nci.nih.gov/ncitbrowser/pages/concept_details.jsf?dictionary=NCI_Thesaurus&amp;code=C14421).
- For example, a C57BL/6J mouse strain from The Jackson Laboratory might be identified as: Strain Description (0010,0212) = "C57BL/6J"
- Strain Nomenclature (0010,0213) = "MGI_2013"
- Strain Code Sequence (0010,0219) &gt;Code Value (0008,0100) = "3028467"
- &gt;Coding Scheme Designator (0008,0102) = "MGI"
- &gt;Code Meaning (0008,0104) = "C57BL/6J"
- Strain Stock Sequence (0010,0216) &gt;Strain Stock Number (0010,0214) = "000664"
- &gt;Strain Source (0010,0217) = "Jrep"
- &gt;Strain Source Registry Code Sequence (0010,0215) &gt;&gt;Code Value (0008,0100) = [126850](part16.html#DCM_126850)
- &gt;&gt;Coding Scheme Designator (0008,0102) = "DCM"
- &gt;&gt;Code Meaning (0008,0104) = "ILCR"
- For example, a FVB/N mouse with a Tg(MMTV-Erbb2*)NDL2-5Mul transgene might be identified as: Strain Description (0010,0212) = "FVB/N-Tg(MMTV-Erbb2*)NDL2-5Mul"
- Strain Nomenclature (0010,0213) = "MGI_2013"
- Genetic Modifications Sequence (0010,0221) &gt;Genetic Modifications Description (0010,0222) = "Tg(MMTV-Erbb2*)NDL2-5Mul"
- &gt;Genetic Modifications Nomenclature (0010,0223) = "MGI_2013"
- &gt;Genetic Modifications Code Sequence (0010,0229) &gt;&gt;Code Value (0008,0100) = "3793949"
- &gt;&gt;Coding Scheme Designator (0008,0102) = "MGI"
- &gt;&gt;Code Meaning (0008,0104) = "Tg(MMTV-Erbb2*)NDL2-5Mul"
In this example, a precoordinated code for the genetic modification is defined in MGI, but not for the mouse strain.