#### C.7.6.6 Multi-frame Module
Table C.7-14 specifies the Attributes of the Multi-frame Module, which describe a Multi-frame pixel data Image.
**Table C.7-14. Multi-frame Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Number of Frames | (0028,0008) | NumberOfFrames | IS | 1 | str, int | 1 | Number of Frames in a Multi-frame Image. See Section C.*******.1 for further explanation. |
| Frame Increment Pointer | (0028,0009) | FrameIncrementPointer | AT | 1-n | int, tuple | 1 | Contains the Data Element Tag of the Attribute that is used as the Frame increment in Multi-frame pixel data. See Section C.*******.2 for further explanation. |
| Stereo Pairs Present | (0022,0028) | StereoPairsPresent | CS | 1 | str | 3 | The multi-frame pixel data consists of left and right stereoscopic pairs. See Section C.*******.3 for further explanation. **Enumerated Values:** YES NO |
| Encapsulated Pixel Data Value Total Length | (7FE0,0003) | EncapsulatedPixelDataValueTotalLength | UV | 1 |  | 3 | The length of the pixel data bit stream encapsulated in Pixel Data (7FE0,0010), in bytes, when all the fragments have been combined, not including any trailing padding to even length in the last Fragment added for encapsulation. ### Note This Value will depend on the Transfer Syntax in which the Pixel Data (7FE0,0010) is encoded, and may need to be updated depending on the Transfer Syntax negotiated and selected for a particular transfer. See [PS3.5 Section 8.2 Native or Encapsulated Format Encoding](part05.html#sect_8.2). |
##### C.******* Multi-frame Module Attribute Descriptions
###### C.*******.1 Number of Frames
A Multi-frame Image is defined as a Image whose pixel data consists of a sequential set of individual Image Pixel Frames. A Multi-frame Image is transmitted as a single contiguous stream of pixels. Frame headers do not exist within the data stream.
Each individual Frame shall be defined (and thus can be identified) by the Attributes in the Image Pixel Module (see Section C.7.6.3 ). All Image IE Attributes shall be related to the first Frame in the Multi-frame Image.
The total number of Frames contained within a Multi-frame Image is conveyed in the Number of Frames (0028,0008). Number of Frames (0028,0008) shall have a Value greater than zero.
###### C.*******.2 Frame Increment Pointer
The Frames within a Multi-frame Image shall be conveyed as a logical sequence. The information that determines the sequential order of the Frames shall be identified by the Data Element Tag or Tags conveyed by the Frame Increment Pointer (0028,0009). Each specific Image IOD that supports the Multi-frame Module specializes the Frame Increment Pointer (0028,0009) to identify the Attributes that may be used as sequences.
Even if only a single Frame is present, Frame Increment Pointer (0028,0009) is still required to be present and have at least one Value, each of which shall point to an Attribute that is also present in the Data Set and has a Value.
### Note
For example, in single-frame Instance of an IOD that is required to or may contain the Cine Module, it may be appropriate for Frame Time (0018,1063) to be present with a Value of 0, and be the only target of Frame Increment Pointer (0028,0009).
When the IOD permits the use of Multi-frame Functional Groups as a Standard or Standard Extended SOP Class, Frame Increment Pointer may contain the single Value of Per-Frame Functional Groups Sequence (5200,9230) to indicate that the Functional Groups contain the descriptors of the Frames.
### Note
For example, the Multi-frame Grayscale Word Secondary Capture Image IOD requires the Multi-frame Module but also permits the Multi-frame Functional Groups, for example, to describe the plane position of each Frame.
###### C.*******.3 Stereoscopic Pairs Present
Stereo Pairs Present (0022,0028) shall have the Value of YES when Frames within a Multi-frame Image are encoded as left and right stereoscopic pairs.
When Stereoscopic Pairs are present, and the pixel data is uncompressed, or compressed with a Transfer Syntax that does not explicitly convey the semantics of stereo pairs, the first and subsequent odd Frames (Frames numbered from 1) are the left Frame of each pair, and the second and subsequent even Frames are the right Frame of each pair.
If the pixel data is compressed with a Transfer Syntax that does explicitly convey the semantics of stereo pairs, then the identification of the left and right Frames in the compressed pixel data will be as defined in the compressed bit stream.
### Note
- For example, the MPEG-4 AVC/H.264 Supplemental Enhancement Information (SEI) Frame Packing Arrangement (FPA) field defines various methods of encoding stereo pairs. See [PS3.5 Section 8.2.8 MPEG-4 AVC/H.264 High Profile / Level 4.2 Video Compression](part05.html#sect_8.2.8). Videos encoded with this Transfer Syntax are used for what is colloquially referred to as "3D Television" applications. [PS3.5 Section 8.2.9 MPEG-4 AVC/H.264 Stereo High Profile / Level 4.2 Video Compression](part05.html#sect_8.2.9) defines a method of encoding stereo pairs without Frame packing and with 2D backwards compatibility.
- The presence of Stereo Pairs Present (0022,0028) is independent of the use of Instances of the Stereometric Relationship IOD. In particular, no further description of the method of acquisition of the stereoscopic pairs is required, such as might be present in Attributes of the Stereo Pairs Sequence (0022,0020) of the Stereometric Relationship IOD. The definition of the references to left and right pairs in that IOD prohibit the encoding of the left and right pairs in the same Instance, as distinct for the usage here.
- Not all multi-frame IODs are sufficiently generic in their description to permit the presence of stereoscopic pairs. E.g., the Video Endoscopic Image IOD, Video Microscopic IOD and Video Photographic IODs are, since they do not specify any conflicting constraints on the meaning of the Frames.