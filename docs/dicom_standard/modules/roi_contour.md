#### C.8.8.6 ROI Contour Module
In general, a ROI can be defined by either a sequence of overlays or a sequence of contours. This Module, if present, is used to define the ROI as a set of contours. Each ROI contains a sequence of one or more contours, where a contour is either a single point (for a point ROI) or more than one point (representing an open or closed polygon).
**Table C.8-42. ROI Contour Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| ROI Contour Sequence | (3006,0039) | ROIContourSequence | SQ | 1 | list[Dataset] | 1 | Sequence of Contour Sequences defining ROIs. One or more Items are permitted in this Sequence. |
| &gt;Referenced ROI Number | (3006,0084) | ReferencedROINumber | IS | 1 | str, int | 1 | 1 | Uniquely identifies the referenced ROI described in the Structure Set ROI Sequence (3006,0020). |
| &gt;ROI Display Color | (3006,002A) | ROIDisplayColor | IS | 3 | str, int | 1 | 3 | RGB triplet color representation for ROI, specified using the range 0-255. |
| &gt;Recommended Display Grayscale Value | (0062,000C) | RecommendedDisplayGrayscaleValue | US | 1 | int | 3 | A default single gray unsigned value in which it is recommended that the contour be rendered on a monochrome display. The units are specified in P-Values from a minimum of 0000H (black) up to a maximum of FFFFH (white). ### Note The maximum P-Value for this Attribute may be different from the maximum P-Value from the output of the Presentation LUT, which may be less than 16 bits in depth. |
| &gt;Recommended Display CIELab Value | (0062,000D) | RecommendedDisplayCIELabValue | US | 3 | int | 3 | A default triplet value in which it is recommended that the contour be rendered on a color display. The units are specified in PCS-Values, and the value is encoded as CIELab. See Section C.********. |
| &gt;Source Pixel Planes Characteristics Sequence | (3006,004A) | SourcePixelPlanesCharacteristicsSequence | SQ | 1 | list[Dataset] | 3 | The characteristics of the pixel planes from which the grid-based representation of the Contours was derived. Only a single Item is permitted in this Sequence. See Section C.*******. ### Note This is not useful if Contour Geometric Type (3006,0042) equals POINT, OPEN_PLANAR or OPEN_NONPLANAR |
| &gt;&gt;Pixel Spacing | (0028,0030) | PixelSpacing | DS | 2 | str, float, int | 1 | Physical distance in the patient between the center of each pixel, specified by a numeric pair - adjacent row spacing (delimiter) adjacent column spacing in mm. See Section ******** for further explanation. |
| &gt;&gt;Spacing Between Slices | (0018,0088) | SpacingBetweenSlices | DS | 1 | str, float, int | 1 | Spacing between adjacent slices, in mm. The spacing is measured from the center-to-center of each slice, and shall not be negative. |
| &gt;&gt;Image Orientation (Patient) | (0020,0037) | ImageOrientationPatient | DS | 6 | str, float, int | 1 | The direction cosines of the first row and the first column with respect to the patient. See Section C.*******.1. |
| &gt;&gt;Image Position (Patient) | (0020,0032) | ImagePositionPatient | DS | 3 | str, float, int | 1 | The x, y and z coordinates in mm of the upper left hand corner of the pixel matrix in the Patient-Based Coordinate System described in Section C.*******.1. |
| &gt;&gt;Number of Frames | (0028,0008) | NumberOfFrames | IS | 1 | str, int | 1 | Number of source pixel planes. |
| &gt;&gt;Rows | (0028,0010) | Rows | US | 1 | int | 1 | Number of rows in the source pixel planes. |
| &gt;&gt;Columns | (0028,0011) | Columns | US | 1 | int | 1 | Number of columns in the source pixel planes. |
| &gt;Source Series Sequence | (3006,004B) | SourceSeriesSequence | SQ | 1 | list[Dataset] | 3 | Identifies the Image Series on which the ROI was defined. One or more Items are permitted in this Sequence. ### Note - The referenced Series may or may not have the same Frame of Reference UID (0020,0052) as this Instance, and there may be more than one referenced Series within the same Frame of Reference UID (0020,0052). - The referenced Series may or may not contain the images referenced in the Contour Image Sequence (3006,0016). |
| &gt;&gt;Series Instance UID | (0020,000E) | SeriesInstanceUID | UI | 1 | str | 1 | Unique identifier of the Series containing the referenced Instances. |
| &gt;Contour Sequence | (3006,0040) | ContourSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Contours defining ROI. One or more Items are permitted in this Sequence. |
| &gt;&gt;Contour Number | (3006,0048) | ContourNumber | IS | 1 | str, int | 3 | Identification number of the contour. The Value of Contour Number (3006,0048) shall be unique within the Contour Sequence (3006,0040) in which it is defined. No semantics or ordering shall be inferred from this Attribute. |
| &gt;&gt;Contour Image Sequence | (3006,0016) | ContourImageSequence | SQ | 1 | list[Dataset] | 3 | Sequence of images containing the contour. One or more Items are permitted in this Sequence. See Section C.*******. |
| &gt;&gt;&gt;Include Table 10-3 Image SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;&gt;Contour Geometric Type | (3006,0042) | ContourGeometricType | CS | 1 | str | 1 | Geometric type of contour. See Section C.*******. **Enumerated Values:** POINT single point OPEN_PLANAR open contour containing coplanar points OPEN_NONPLANAR open contour containing non-coplanar points CLOSED_PLANAR closed contour (polygon) containing coplanar points CLOSEDPLANAR_XOR closed contour (polygon) containing coplanar points of an inner or outer contour combined using an XOR operator |
| &gt;&gt;Number of Contour Points | (3006,0046) | NumberOfContourPoints | IS | 1 | str, int | 1 | Number of points (triplets) in Contour Data (3006,0050). |
| &gt;&gt;Contour Data | (3006,0050) | ContourData | DS | 3-3n | str, float, int | 1 | Sequence of (x,y,z) triplets defining a contour in the Patient-Based Coordinate System described in Section C.*******.1 (mm). See Section C.******* and Section C.*******. See Section C.*******. ### Note If the Value Length of this Data Element exceeds 65534 bytes and an Explicit VR Transfer Syntax is used, then the Value Representation UN can be used for this Data Element. See [PS3.5 Section 6.2.2](part05.html#sect_6.2.2). |
##### C.******* Contour Geometric Type
A contour can be one of the following geometric types:
- A Contour Geometric Type (3006,0042) of POINT indicates that the contour is a single point, defining a specific location of significance.
- A Contour Geometric Type (3006,0042) of OPEN_PLANAR indicates that the last vertex shall not be connected to the first point, and that all points in Contour Data (3006,0050) shall be coplanar.
- A Contour Geometric Type (3006,0042) of OPEN_NONPLANAR indicates that the last vertex shall not be connected to the first point, and that the points in Contour Data (3006,0050) may be non-coplanar. Contours having a Geometric Type (3006,0042) of OPEN_NONPLANAR can be used to represent objects best described by a single, possibly non-coplanar curve, such as a brachytherapy applicator.
- A Contour Geometric Type (3006,0042) of CLOSED_PLANAR indicates that the last point shall be connected to the first point, where the first point is not repeated in Contour Data (3006,0050). All points in Contour Data (3006,0050) shall be coplanar.
- A Contour Geometric Type (3006,0042) of CLOSEDPLANAR_XOR indicates that the last point shall be connected to the first point, where the first is not repeated in Contour Data (3006,0050). All points in Contour Data (3006,0050) shall be coplanar. More than one Contour is used to describe an ROI and these Contours are combined by geometric exclusive disjunction, see Section C.*******. If any of the Contours within an ROI is of Contour Geometric Type (3006,0042) CLOSEDPLANAR_XOR, all Contours of that ROI shall be of the same type.
##### C.8.8.6.2 Contour Slab Thickness (Retired)
Retired. See [PS3.3-2020e](http://dicom.nema.org/medical/dicom/2020e/output/pdf/part03.pdf).
##### C.******* Representing Inner and Outer Contours
Inner and Outer Contours can be represented by two different techniques:
Using the "keyhole" technique, an ROI with an excluded inner part is represented with a single planar Contour. In this method, an arbitrarily narrow channel is used to connect the outer contour to the inner contour, so that it is drawn as a single contour. An example of such a structure is shown in Figure C.8.8.6-1 with the channel at roughly the 12 o'clock position.
Points in space lying along the path defined by the contour are considered to be part of the ROI.
**Figure C.8.8.6-1. Example of ROI with excluded inner volume**
Using the "XOR" technique, an ROI with an excluded inner part is represented by two planar Contours that are combined by a geometric exclusive disjunction, thus extracting the inner from the outer Contour, see Figure C.8.8.6-2. The contours have the Contour Geometric Type (3006,0042) CLOSEDPLANAR_XOR.
**Figure C.8.8.6-2. Example of ROI with contours exclusively added**
Using this technique, it is also possible to create an ROI that includes disjoint parts of the ROI within an interior void. When two or more Contours are present, two Contours are combined using a geometric exclusive disjunction ("XOR"). Then this result is combined by an XOR operation with a third Contour, and so on for all other Contours of this ROI. The order of combination does not matter. An example of the result of an XOR operation of three Contours is visualized in Figure C.8.8.6-3.
**Figure C.8.8.6-3. Example of ROI with disjoint parts**
##### C.******* Source Pixel Planes Characteristics
The Source Pixel Planes Characteristics Sequence (3006,004A) defines a stack of Source Pixel Planes on the originating system from which the Contour data of an ROI was derived. This stack of Source Pixel Planes does not need to correspond to actual Image Storage SOP Instances.
If a receiving system also utilizes a pixel-based representation of Contours, the information in this Sequence may be utilized to define the same pixel grid as the originating system to reduce the magnitude of errors caused by different sampling rates.
If Source Pixel Planes Characteristics Sequence (3006,004A) is present for an ROI in the ROI Contour Sequence (3006,0039) the following apply:
- Contours are specified on Source Pixel Planes defined by the characteristics in the Source Pixel Planes Characteristics Sequence (3006,004A).
- A Source Pixel Plane is not required to coincide with or be parallel to an actual image plane (i.e., Contour Image Sequence (3006,0016) is not required to be present, and if it is present, the referenced images need to not correspond to the characteristics of Source Pixel Planes).
- The x, y, z triplets of Contour Data (3006,0050) shall be defined on the Source Pixel Planes defined by Source Pixel Planes Characteristics Sequence (3006,004A).
- Contour Data (3006,0050) may have a different (e.g., higher) sampling than the Pixel Spacing (0028,0030).
- Source Pixel Planes for all Contours of the ROI will be parallel (since only one Image Orientation (Patient) is specified).
- Source Pixel Planes will be equidistantly spaced (since only one Spacing Between Slices is specified).
- Contours of an ROI shall be specified on every Source Pixel Plane where the ROI is present.
- If no Contour Data is specified for a given Source Pixel Plane of an ROI, the ROI is defined to be absent on that Source Pixel Plane (i.e., a Source Pixel Plane without corresponding Contour Data defines a "gap").