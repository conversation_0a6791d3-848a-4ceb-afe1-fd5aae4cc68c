#### C.8.8.2 RT Image Module
Table C.8-38 specifies the Attributes of the RT Image Module, which describe RT-specific characteristics of a projection image. The image described by these Attributes must be a radiotherapy image acquired or calculated using a conical imaging geometry.
**Table C.8-38. RT Image Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Samples per Pixel | (0028,0002) | SamplesPerPixel | US | 1 | int | 1 | Number of samples (planes) in this image. See Section C.*******.1 for specialization. |
| Photometric Interpretation | (0028,0004) | PhotometricInterpretation | CS | 1 | str | 1 | Specifies the intended interpretation of the pixel data. See Section C.*******.2 for specialization. |
| Bits Allocated | (0028,0100) | BitsAllocated | US | 1 | int | 1 | Number of bits allocated for each pixel sample. Each sample shall have the same number of bits allocated. See Section C.*******.3 for specialization. |
| Bits Stored | (0028,0101) | BitsStored | US | 1 | int | 1 | Number of bits stored for each pixel sample. Each sample shall have the same number of bits stored. See Section C.*******.4 for specialization. |
| High Bit | (0028,0102) | HighBit | US | 1 | int | 1 | Most significant bit for each pixel sample. Each sample shall have the same high bit. See Section C.*******.5 for specialization. |
| Pixel Representation | (0028,0103) | PixelRepresentation | US | 1 | int | 1 | Data representation of the pixel samples. Each sample shall have the same pixel representation. See Section C.*******.6 for specialization. |
| Pixel Intensity Relationship | (0028,1040) | PixelIntensityRelationship | CS | 1 | str | 3 | The relationship between the pixel sample values and the X-Ray beam intensity. See Section C.********.1.1. |
| Pixel Intensity Relationship Sign | (0028,1041) | PixelIntensityRelationshipSign | SS | 1 | int | 1C | The sign of the relationship between the pixel sample values stored in Pixel Data (7FE0,0010) and the X-Ray beam intensity. Required if Pixel Intensity Relationship (0028,1040) is present. **Enumerated Values:** +1 Lower pixel values correspond to less X-Ray beam intensity -1 Higher pixel values correspond to less X-Ray beam intensity See Section C.********.2 for further explanation. |
| RT Image Label | (3002,0002) | RTImageLabel | SH | 1 | str | 1 | User-defined label for RT Image. |
| RT Image Name | (3002,0003) | RTImageName | LO | 1 | str | 3 | User-defined name for RT Image. |
| RT Image Description | (3002,0004) | RTImageDescription | ST | 1 | str | 3 | User-defined description of RT Image. |
| Image Type | (0008,0008) | ImageType | CS | 2-n | str | 1 | Image identification characteristics (see Section C.*******.2 ). **Defined Terms for Value 3:** DRR digitally reconstructed radiograph PORTAL digital portal image or portal film image SIMULATOR conventional simulator image RADIOGRAPH radiographic image BLANK image pixels set to background value FLUENCE fluence map |
| Conversion Type | (0008,0064) | ConversionType | CS | 1 | str | 2 | Describes the kind of image conversion. **Defined Terms:** DV Digitized Video DI Digital Interface DF Digitized Film WSD Workstation |
| Reported Values Origin | (3002,000A) | ReportedValuesOrigin | CS | 1 | str | 2C | Describes the origin of the parameter values reported in the image. Required if Value 3 of Image Type (0008,0008) is SIMULATOR or PORTAL. **Enumerated Values:** OPERATOR manually entered by operator PLAN planned parameter values ACTUAL electronically recorded |
| RT Image Plane | (3002,000C) | RTImagePlane | CS | 1 | str | 1 | Describes whether or not image plane is normal to beam axis. **Enumerated Values:** NORMAL image plane normal to beam axis NON_NORMAL image plane non-normal to beam axis |
| X-Ray Image Receptor Translation | (3002,000D) | XRayImageReceptorTranslation | DS | 3 | str, float, int | 3 | Position in (x,y,z) coordinates of origin of IEC X-RAY IMAGE RECEPTOR System in the IEC GANTRY coordinate system (mm). See Note 2. |
| X-Ray Image Receptor Angle | (3002,000E) | XRayImageReceptorAngle | DS | 1 | str, float, int | 2 | X-Ray Image Receptor Angle i.e., orientation of IEC X-RAY IMAGE RECEPTOR coordinate system with respect to IEC GANTRY coordinate system (degrees). See Section C.*******. |
| RT Image Orientation | (3002,0010) | RTImageOrientation | DS | 6 | str, float, int | 2C | The direction cosines of the first row and the first column with respect to the IEC X-RAY IMAGE RECEPTOR coordinate system. Required if RT Image Plane (3002,000C) is NON_NORMAL. May be present otherwise. |
| Image Plane Pixel Spacing | (3002,0011) | ImagePlanePixelSpacing | DS | 2 | str, float, int | 2 | Physical distance (in mm) between the center of each image pixel, specified by a numeric pair - adjacent row spacing (delimiter) adjacent column spacing. See Section C.******* and Section 10.7.1.3 for further explanation. |
| RT Image Position | (3002,0012) | RTImagePosition | DS | 2 | str, float, int | 2 | The x and y coordinates (in mm) of the upper left hand corner of the image, in the IEC X-RAY IMAGE RECEPTOR coordinate system. This is the center of the first pixel transmitted. See Section C.8.8.2.7. |
| Radiation Machine Name | (3002,0020) | RadiationMachineName | SH | 1 | str | 2 | User-defined name identifying radiation machine used in acquiring or computing image (i.e., name of conventional simulator, electron accelerator, X-Ray device, or machine modeled when calculating DRR). |
| Primary Dosimeter Unit | (300A,00B3) | PrimaryDosimeterUnit | CS | 1 | str | 2 | Measurement unit of machine dosimeter. **Enumerated Values:** MU Monitor Unit MINUTE minute |
| Radiation Machine SAD | (3002,0022) | RadiationMachineSAD | DS | 1 | str, float, int | 2 | Radiation source to Gantry rotation axis distance of radiation machine used in acquiring or computing image (mm). |
| Radiation Machine SSD | (3002,0024) | RadiationMachineSSD | DS | 1 | str, float, int | 3 | Source to Patient surface distance (in mm) of radiation machine used in acquiring or computing image. |
| RT Image SID | (3002,0026) | RTImageSID | DS | 1 | str, float, int | 2 | Distance from radiation machine source to image plane (in mm) along radiation beam axis. See Section C.*******. |
| Enhanced RT Beam Limiting Device Definition Flag | (3008,00A3) | EnhancedRTBeamLimitingDeviceDefinitionFlag | CS | 1 | str | 3 | Whether the RT Beam Limiting Devices are specified by the Enhanced RT Beam Limiting Device Sequence (3008,00A1). **Enumerated Values:** YES NO |
| Enhanced RT Beam Limiting Device Sequence | (3008,00A1) | EnhancedRTBeamLimitingDeviceSequence | SQ | 1 | list[Dataset] | 1C | Enhanced RT Beam Limiting Device Descriptions. Required if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is present and has the Value YES. One or more Items shall be included in this Sequence. |
| &gt;Include Table C.*********-1 RT Beam Limiting Device Definition Macro Attributes. |  |  |  |  |  |  | Device Type Code Sequence (3010,002E) within RT Accessory Device Identification Macro D [CID 9540 Movable Beam Limiting Device Type](part16.html#sect_CID_9540). See Section C.*********. |
| Source to Reference Object Distance | (3002,0028) | SourceToReferenceObjectDistance | DS | 1 | str, float, int | 3 | Source to reference object distance (in mm), as used for magnification calculation of RADIOGRAPH and SIMULATOR images. |
| Referenced RT Plan Sequence | (300C,0002) | ReferencedRTPlanSequence | SQ | 1 | list[Dataset] | 3 | Sequence of one Class/Instance pair describing RT Plan associated with image. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| Referenced Beam Number | (300C,0006) | ReferencedBeamNumber | IS | 1 | str, int | 3 | Uniquely identifies the corresponding N-segment treatment beam specified by Beam Number (300A,00C0) within Beam Sequence (300A,00B0) in RT Beams Module within the RT Plan referenced in Referenced RT Plan Sequence (300C,0002) or the Ion Beam Sequence (300A,03A2) in the RT Ion Beams Module within the RT Ion Plan referenced in Referenced RT Plan Sequence (300C,0002). |
| Referenced Fraction Group Number | (300C,0022) | ReferencedFractionGroupNumber | IS | 1 | str, int | 3 | Identifier of Fraction Group within RT Plan referenced in Referenced RT Plan Sequence (300C,0002). |
| Fraction Number | (3002,0029) | FractionNumber | IS | 1 | str, int | 3 | Fraction Number of fraction during which image was acquired, within Fraction Group referenced by Referenced Fraction Group Number (300C,0022) within RT Plan referenced in Referenced RT Plan Sequence (300C,0002). See Section C.8.8.2.9. |
| Clinical Fraction Number | (300A,0705) | ClinicalFractionNumber | US | 1 | int | 3 | Identifier of the RT Treatment Fraction of the referenced RT Beam during which image was acquired. See Section C.8.8.21.4. |
| Start Cumulative Meterset Weight | (300C,0008) | StartCumulativeMetersetWeight | DS | 1 | str, float, int | 3 | Cumulative Meterset Weight within Beam referenced by Referenced Beam Number (300C,0006) at which image acquisition starts. |
| End Cumulative Meterset Weight | (300C,0009) | EndCumulativeMetersetWeight | DS | 1 | str, float, int | 3 | Cumulative Meterset Weight within Beam referenced by Referenced Beam Number (300C,0006) at which image acquisition ends. |
| Exposure Sequence | (3002,0030) | ExposureSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Exposure parameter sets, corresponding to exposures used in generating the image. One or more Items are permitted in this Sequence. See Section C.*******. |
| &gt;Referenced Frame Number | (0008,1160) | ReferencedFrameNumber | IS | 1-n | str, int | 1C | Identifies corresponding image Frame in Multi-frame Image. Required if there is more than one Item in Exposure Sequence (3002,0030), and image is a Multi-frame Image. |
| &gt;KVP | (0018,0060) | KVP | DS | 1 | str, float, int | 2C | Peak kilo voltage output (kV) of X-Ray generator used to acquire image. Required if Value 3 of Image Type (0008,0008) is PORTAL, SIMULATOR or RADIOGRAPH. |
| &gt;Primary Fluence Mode Sequence | (3002,0050) | PrimaryFluenceModeSequence | SQ | 1 | list[Dataset] | 3 | Sequence defining whether the primary fluence of the treatment beam uses a non-standard fluence-shaping. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Fluence Mode | (3002,0051) | FluenceMode | CS | 1 | str | 1 | Describes whether the fluence shaping is the standard mode for the beam or an alternate. **Enumerated Values:** STANDARD Uses standard fluence-shaping NON_STANDARD Uses a non-standard fluence-shaping mode |
| &gt;&gt;Fluence Mode ID | (3002,0052) | FluenceModeID | SH | 1 | str | 1C | Identifier for the specific fluence-shaping mode. Required if Fluence Mode (3002,0051) has the Value NON_STANDARD. |
| &gt;X-Ray Tube Current | (0018,1151) | XRayTubeCurrent | IS | 1 | str, int | 2C | Imaging device X-Ray Tube Current (mA). Required if Value 3 of Image Type (0008,0008) is SIMULATOR or RADIOGRAPH. May be present otherwise. |
| &gt;X-Ray Tube Current in mA | (0018,9330) | XRayTubeCurrentInmA | FD | 1 | float | 3 | X-Ray Tube Current in mA. An average in the case of fluoroscopy (continuous radiation mode). |
| &gt;Exposure Time | (0018,1150) | ExposureTime | IS | 1 | str, int | 2C | Time of X-Ray exposure (msec). Required if Value 3 of Image Type (0008,0008) is SIMULATOR or RADIOGRAPH. May be present otherwise. See Section C.*******. |
| &gt;Exposure Time in ms | (0018,9328) | ExposureTimeInms | FD | 1 | float | 3 | Duration of X-Ray exposure in msec. See Section C.*******. |
| &gt;Meterset Exposure | (3002,0032) | MetersetExposure | DS | 1 | str, float, int | 2C | Treatment machine Meterset duration over which image has been acquired, specified in Monitor units (MU) or minutes as defined by Primary Dosimeter Unit (300A,00B3). Required if Value 3 of Image Type (0008,0008) is PORTAL. See Section C.*******. |
| &gt;Diaphragm Position | (3002,0034) | DiaphragmPosition | DS | 4 | str, float, int | 3 | Positions of diaphragm jaw pairs (in mm) in IEC BEAM LIMITING DEVICE coordinate axis in the IEC order X1, X2, Y1, Y2. |
| &gt;Beam Limiting Device Sequence | (300A,00B6) | BeamLimitingDeviceSequence | SQ | 1 | list[Dataset] | 3 | Sequence of beam limiting device (collimator) jaw or leaf (element) positions for given exposure. Shall not be present if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is present and has the Value YES. One or more Items are permitted in this Sequence. |
| &gt;&gt;RT Beam Limiting Device Type | (300A,00B8) | RTBeamLimitingDeviceType | CS | 1 | str | 1 | Type of beam limiting device (collimator). **Enumerated Values:** X symmetric jaw pair in IEC X direction Y symmetric jaw pair in IEC Y direction ASYMX asymmetric jaw pair in IEC X direction ASYMY asymmetric jaw pair in IEC Y direction MLCX single layer multileaf collimator in IEC X direction MLCY single layer multileaf collimator in IEC Y direction |
| &gt;&gt;Source to Beam Limiting Device Distance | (300A,00BA) | SourceToBeamLimitingDeviceDistance | DS | 1 | str, float, int | 3 | Radiation source to beam limiting device (collimator) distance (mm). |
| &gt;&gt;Number of Leaf/Jaw Pairs | (300A,00BC) | NumberOfLeafJawPairs | IS | 1 | str, int | 1 | Number of leaf (element) or jaw pairs (equal to 1 for standard beam limiting device jaws). |
| &gt;&gt;Leaf Position Boundaries | (300A,00BE) | LeafPositionBoundaries | DS | 3-n | str, float, int | 2C | Boundaries (in mm) of beam limiting device (collimator) leaves (elements) in IEC BEAM LIMITING DEVICE coordinate axis appropriate to RT Beam Limiting Device Type (300A,00B8), i.e., X-axis for MLCY, Y-axis for MLCX. Contains N+1 Values, where N is the Number of Leaf/Jaw Pairs (300A,00BC), starting from Leaf (Element) Pair 1. Required if RT Beam Limiting Device Type (300A,00B8) is MLCX or MLCY. May be present otherwise. |
| &gt;&gt;Leaf/Jaw Positions | (300A,011C) | LeafJawPositions | DS | 2-2n | str, float, int | 1C | Positions of beam limiting device (collimator) leaf or jaw (element) pairs (in mm) in IEC BEAM LIMITING DEVICE coordinate axis appropriate to RT Beam Limiting Device Type (300A,00B8), e.g., X-axis for MLCX, Y-axis for MLCY). Contains 2N Values, where N is the Number of Leaf/Jaw Pairs (300A,00BC), in IEC leaf (element) subscript order 101, 102, 1N, 201, 202, 2N. Required if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is absent, or is present and has the Value NO. |
| &gt;Enhanced RT Beam Limiting Opening Sequence | (3008,00A2) | EnhancedRTBeamLimitingOpeningSequence | SQ | 1 | list[Dataset] | 2C | Sequence of beam limiting device (collimator) jaw or leaf (element) positions. Required if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is present and has the Value YES. Zero or more Items shall be included in this Sequence. |
| &gt;&gt;Include Table C.*********-1 RT Beam Limiting Device Opening Definition Macro Attributes |  |  |  |  |  |  | See Section C.********* Enhanced RT Beam Limiting Device Sequence and Enhanced RT Beam Limiting Opening Sequence. |
| &gt;Gantry Angle | (300A,011E) | GantryAngle | DS | 1 | str, float, int | 3 | Treatment machine or imaging device gantry angle, i.e., orientation of IEC GANTRY coordinate system with respect to IEC FIXED REFERENCE coordinate system (degrees). |
| &gt;Gantry Pitch Angle | (300A,014A) | GantryPitchAngle | FL | 1 | float | 3 | Gantry Pitch Angle. i.e., the rotation of the IEC GANTRY coordinate system about the X-axis of the IEC GANTRY coordinate system (degrees). See Section C.8.8.25.6.5. |
| &gt;Beam Limiting Device Angle | (300A,0120) | BeamLimitingDeviceAngle | DS | 1 | str, float, int | 3 | Treatment machine beam limiting device (collimator) angle, i.e., orientation of IEC BEAM LIMITING DEVICE coordinate system with respect to IEC GANTRY coordinate system (degrees). |
| &gt;Patient Support Angle | (300A,0122) | PatientSupportAngle | DS | 1 | str, float, int | 3 | Patient Support angle, i.e., orientation of IEC PATIENT SUPPORT coordinate system with respect to IEC FIXED REFERENCE coordinate system (degrees). |
| &gt;Table Top Pitch Angle | (300A,0140) | TableTopPitchAngle | FL | 1 | float | 3 | Table Top Pitch Angle, i.e., the rotation of the IEC TABLE TOP coordinate system about the X-axis of the IEC TABLE TOP coordinate system (degrees). See Section C.8.8.25.6.2. |
| &gt;Table Top Roll Angle | (300A,0144) | TableTopRollAngle | FL | 1 | float | 3 | Table Top Roll Angle, i.e., the rotation of the IEC TABLE TOP coordinate system about the Y-axis of the IEC TABLE TOP coordinate system (degrees). See Section C.8.8.25.6.2. |
| &gt;Table Top Vertical Position | (300A,0128) | TableTopVerticalPosition | DS | 1 | str, float, int | 3 | Table Top Vertical position in IEC TABLE TOP coordinate system (mm). |
| &gt;Table Top Longitudinal Position | (300A,0129) | TableTopLongitudinalPosition | DS | 1 | str, float, int | 3 | Table Top Longitudinal position in IEC TABLE TOP coordinate system (mm). |
| &gt;Table Top Lateral Position | (300A,012A) | TableTopLateralPosition | DS | 1 | str, float, int | 3 | Table Top Lateral position in IEC TABLE TOP coordinate system (mm). |
| &gt;Applicator Sequence | (300A,0107) | ApplicatorSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Applicators associated with Beam. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Applicator ID | (300A,0108) | ApplicatorID | SH | 1 | str | 1 | User or machine supplied identifier for Applicator. |
| &gt;&gt;Accessory Code | (300A,00F9) | AccessoryCode | LO | 1 | str | 3 | An identifier for the accessory intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Applicator Type | (300A,0109) | ApplicatorType | CS | 1 | str | 1 | Type of Applicator. **Defined Terms:** ELECTRON_SQUARE square electron applicator ELECTRON_RECT rectangular electron applicator ELECTRON_CIRC circular electron applicator ELECTRON_SHORT short electron applicator ELECTRON_OPEN open (dummy) electron applicator PHOTON_SQUARE square photon applicator PHOTON_RECT rectangular photon applicator PHOTON_CIRC circular photon applicator INTRAOPERATIVE intraoperative (custom) applicator STEREOTACTIC stereotactic applicator (deprecated) |
| &gt;&gt;Applicator Geometry Sequence | (300A,0431) | ApplicatorGeometrySequence | SQ | 1 | list[Dataset] | 3 | Describes the applicator aperture geometry. Only a single Item is permitted in this Sequence. |
| &gt;&gt;&gt;Applicator Aperture Shape | (300A,0432) | ApplicatorApertureShape | CS | 1 | str | 1 | Aperture shape of the applicator. **Defined Terms:** SYM_SQUARE A square-shaped aperture symmetrical to the central axis. SYM_RECTANGLE A rectangular-shaped aperture symmetrical to the central axis. SYM_CIRCULAR A circular-shaped aperture symmetrical to the central axis. |
| &gt;&gt;&gt;Applicator Opening | (300A,0433) | ApplicatorOpening | FL | 1 | float | 1C | Opening (in mm) of the applicator's aperture in IEC BEAM LIMITING DEVICE coordinate system. In case of square-shaped applicator contains the length of the sides of the square. In case of circular-shaped applicators, contains the diameter of the circular aperture. Required if Applicator Aperture Shape (300A,0432) is SYM_SQUARE or SYM_CIRCULAR. |
| &gt;&gt;&gt;Applicator Opening X | (300A,0434) | ApplicatorOpeningX | FL | 1 | float | 1C | Opening (in mm) of the applicator's aperture in IEC BEAM LIMITING DEVICE coordinate system in X-Direction. Required if Applicator Aperture Shape (300A,0432) is SYM_RECTANGLE. |
| &gt;&gt;&gt;Applicator Opening Y | (300A,0435) | ApplicatorOpeningY | FL | 1 | float | 1C | Opening (in mm) of the applicator's aperture in IEC BEAM LIMITING DEVICE coordinate system in Y-Direction. Required if Applicator Aperture Shape (300A,0432) is SYM_RECTANGLE. |
| &gt;&gt; Source to Applicator Mounting Position Distance | (300A,0436) | SourceToApplicatorMountingPositionDistance | FL | 1 | float | 3 | Radiation source to applicator mounting position distance (in mm) for current applicator. |
| &gt;&gt;Applicator Description | (300A,010A) | ApplicatorDescription | LO | 1 | str | 3 | User-defined description for Applicator. |
| &gt;General Accessory Sequence | (300A,0420) | GeneralAccessorySequence | SQ | 1 | list[Dataset] | 3 | Sequence of General Accessories associated with the beam producing this image. One or more Items are permitted in this Sequence. |
| &gt;&gt;General Accessory Number | (300A,0424) | GeneralAccessoryNumber | IS | 1 | str, int | 1 | Identification Number of the General Accessory. The Value shall be unique within the Sequence. |
| &gt;&gt;General Accessory ID | (300A,0421) | GeneralAccessoryID | SH | 1 | str | 1 | User or machine supplied identifier for General Accessory. |
| &gt;&gt;General Accessory Description | (300A,0422) | GeneralAccessoryDescription | ST | 1 | str | 3 | User supplied description of General Accessory. |
| &gt;&gt;General Accessory Type | (300A,0423) | GeneralAccessoryType | CS | 1 | str | 3 | Specifies the type of accessory. **Defined Terms:** GRATICULE Accessory tray with a radio-opaque grid IMAGE_DETECTOR Image acquisition device positioned in the beam line RETICLE Accessory tray with radio-transparent markers or grid |
| &gt;&gt;Accessory Code | (300A,00F9) | AccessoryCode | LO | 1 | str | 3 | Machine-readable identifier for this accessory. |
| &gt;&gt;Source to General Accessory Distance | (300A,0425) | SourceToGeneralAccessoryDistance | FL | 1 | float | 3 | Radiation source to general accessory distance (in mm) for current accessory. |
| &gt;Number of Blocks | (300A,00F0) | NumberOfBlocks | IS | 1 | str, int | 1 | Number of shielding blocks associated with Beam. |
| &gt;Block Sequence | (300A,00F4) | BlockSequence | SQ | 1 | list[Dataset] | 2C | Sequence of blocks associated with Beam. Required if Number of Blocks (300A,00F0) is non-zero. Zero or more Items shall be included in this Sequence. |
| &gt;&gt;Block Tray ID | (300A,00F5) | BlockTrayID | SH | 1 | str | 3 | User-supplied identifier for block tray. |
| &gt;&gt;Tray Accessory Code | (300A,0355) | TrayAccessoryCode | LO | 1 | str | 3 | An identifier for the Tray intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Accessory Code | (300A,00F9) | AccessoryCode | LO | 1 | str | 3 | An identifier for the Block intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Source to Block Tray Distance | (300A,00F6) | SourceToBlockTrayDistance | DS | 1 | str, float, int | 2 | Radiation Source to attachment edge of block tray assembly (mm). |
| &gt;&gt;Block Type | (300A,00F8) | BlockType | CS | 1 | str | 1 | Type of block. **Enumerated Values:** SHIELDING blocking material is inside contour APERTURE blocking material is outside contour |
| &gt;&gt;Block Divergence | (300A,00FA) | BlockDivergence | CS | 1 | str | 2 | Indicates presence or otherwise of geometrical divergence **Enumerated Values:** PRESENT block edges are shaped for beam divergence ABSENT block edges are not shaped for beam divergence |
| &gt;&gt;Block Mounting Position | (300A,00FB) | BlockMountingPosition | CS | 1 | str | 3 | Indicates on which side of the Block Tray the block is mounted. **Enumerated Values:** PATIENT_SIDE the block is mounted on the side of the Block Tray that is towards the patient. SOURCE_SIDE the block is mounted on the side of the Block Tray that is towards the radiation source. |
| &gt;&gt;Block Number | (300A,00FC) | BlockNumber | IS | 1 | str, int | 1 | Identification Number of the Block. The Value of Block Number (300A,00FC) shall be unique within the Beam in which it is created. |
| &gt;&gt;Block Name | (300A,00FE) | BlockName | LO | 1 | str | 3 | User-defined name for block. |
| &gt;&gt;Material ID | (300A,00E1) | MaterialID | SH | 1 | str | 2 | User-supplied identifier for material used to manufacture Block. |
| &gt;&gt;Block Thickness | (300A,0100) | BlockThickness | DS | 1 | str, float, int | 3 | Physical thickness of block (in mm) parallel to radiation beam axis. |
| &gt;&gt;Block Number of Points | (300A,0104) | BlockNumberOfPoints | IS | 1 | str, int | 2 | Number of (x,y) pairs defining the block edge. |
| &gt;&gt;Block Data | (300A,0106) | BlockData | DS | 2-2n | str, float, int | 2 | A data stream of (x,y) pairs that comprise the block edge. The number of pairs shall be equal to Block Number of Points (300A,0104), and the vertices shall be interpreted as a closed polygon. Coordinates are projected onto the machine isocentric plane in the IEC BEAM LIMITING DEVICE coordinate system (mm). |
| Fluence Map Sequence | (3002,0040) | FluenceMapSequence | SQ | 1 | list[Dataset] | 1C | A Sequence of data describing the fluence map Attributes for a radiotherapy beam. Only a single Item shall be included in this Sequence. Required if The third Value of Image Type (0008,0008) is FLUENCE. |
| &gt;Fluence Data Source | (3002,0041) | FluenceDataSource | CS | 1 | str | 1 | Source of fluence data. **Enumerated Values:** CALCULATED Calculated by a workstation MEASURED Measured by exposure to a film or detector. |
| &gt;Fluence Data Scale | (3002,0042) | FluenceDataScale | DS | 1 | str, float, int | 3 | The Meterset corresponding with a fluence map cell value of 1.0 expressed in units specified by Primary Dosimeter Units (300A,00B3). This is the Meterset value used for treatment, not the Meterset used to expose the film as defined by Meterset Exposure (3002,0032). |
| Gantry Angle | (300A,011E) | GantryAngle | DS | 1 | str, float, int | 3 | Treatment machine or imaging device gantry angle, i.e., orientation of IEC GANTRY coordinate system with respect to IEC FIXED REFERENCE coordinate system (degrees). |
| Gantry Pitch Angle | (300A,014A) | GantryPitchAngle | FL | 1 | float | 3 | Gantry Pitch Angle. i.e., the rotation of the IEC GANTRY coordinate system about the X-axis of the IEC GANTRY coordinate system (degrees). See Section C.8.8.25.6.5. |
| Beam Limiting Device Angle | (300A,0120) | BeamLimitingDeviceAngle | DS | 1 | str, float, int | 3 | Treatment machine beam limiting device (collimator) angle, i.e., orientation of IEC BEAM LIMITING DEVICE coordinate system with respect to IEC GANTRY coordinate system (degrees). |
| Patient Support Angle | (300A,0122) | PatientSupportAngle | DS | 1 | str, float, int | 3 | Patient Support angle, i.e., orientation of IEC PATIENT SUPPORT coordinate system with respect to IEC FIXED REFERENCE coordinate system (degrees). |
| Table Top Eccentric Axis Distance | (300A,0124) | TableTopEccentricAxisDistance | DS | 1 | str, float, int | 3 | Distance (positive) from the IEC PATIENT SUPPORT vertical axis to the IEC TABLE TOP ECCENTRIC vertical axis (mm). |
| Table Top Eccentric Angle | (300A,0125) | TableTopEccentricAngle | DS | 1 | str, float, int | 3 | Table Top (non-isocentric) angle, i.e., orientation of IEC TABLE TOP ECCENTRIC coordinate system with respect to IEC PATIENT SUPPORT system (degrees). |
| Table Top Pitch Angle | (300A,0140) | TableTopPitchAngle | FL | 1 | float | 3 | Table Top Pitch Angle, i.e., the rotation of the IEC TABLE TOP coordinate system about the X-axis of the IEC TABLE TOP coordinate system (degrees). See Section C.8.8.25.6.2. |
| Table Top Roll Angle | (300A,0144) | TableTopRollAngle | FL | 1 | float | 3 | Table Top Roll Angle, i.e., the rotation of the IEC TABLE TOP coordinate system about the Y-axis of the IEC TABLE TOP coordinate system (degrees). See Section C.8.8.25.6.2. |
| Table Top Vertical Position | (300A,0128) | TableTopVerticalPosition | DS | 1 | str, float, int | 3 | Table Top Vertical position in IEC TABLE TOP coordinate system (mm). |
| Table Top Longitudinal Position | (300A,0129) | TableTopLongitudinalPosition | DS | 1 | str, float, int | 3 | Table Top Longitudinal position in IEC TABLE TOP coordinate system (mm). |
| Table Top Lateral Position | (300A,012A) | TableTopLateralPosition | DS | 1 | str, float, int | 3 | Table Top Lateral position in IEC TABLE TOP coordinate system (mm). |
| Isocenter Position | (300A,012C) | IsocenterPosition | DS | 3 | str, float, int | 3 | Isocenter coordinates (x,y,z), in mm. Specifies the location of the machine isocenter in the Patient-Based Coordinate System associated with the Frame of Reference. It allows transformation from the equipment-based IEC coordinate system to the Patient-Based Coordinate System. |
| Patient Position | (0018,5100) | PatientPosition | CS | 1 | str | 1C | Patient position descriptor relative to the patient support device. Required if Isocenter Position (300A,012C) is present. May be present otherwise. See Section C.********.2 for Defined Terms and further explanation. ### Note The orientation of the patient relative to the patient support device is denoted in the same manner as in the RT Patient Setup Module. It defines the relation of the DICOM Patient-Based Coordinate System identified by the Frame of Reference Module of the RT Image to the IEC coordinate system and together with Isocenter Position (300A,012C) allows the RT Image to be placed into the Patient Frame of Reference. It also allows a system using an RT Image to verify that the patient is setup in a similar position relative to the patient support device. |
| Exposure Time | (0018,1150) | ExposureTime | IS | 1 | str, int | 3 | Time of X-Ray exposure (msec). Required if Value 3 of Image Type (0008,0008) is SIMULATOR or RADIOGRAPH. May be present otherwise. See Section C.*******. |
| Exposure Time in ms | (0018,9328) | ExposureTimeInms | FD | 1 | float | 3 | Duration of X-Ray exposure in msec. See Section C.*******. |
| Meterset Exposure | (3002,0032) | MetersetExposure | DS | 1 | str, float, int | 3 | Treatment machine Meterset duration over which image has been acquired, specified in Monitor units (MU) or minutes as defined by Primary Dosimeter Unit (300A,00B3). See Section C.*******. |
### Note
- The numeric beam data parameters recorded with the RT Image correspond to the parameters as they were known at the time the image was created or taken. The parameters may or may not correspond to an actual RT Plan Instance that is created for a patient. If the Reported Values Origin (3002,000A) has an Enumerated Value of OPERATOR or ACTUAL and there is an RT Plan reference present, the numeric beam data parameters may or may not be the same in the two objects.
- The Z coordinate of the X-Ray Image Receptor Translation (3002,000D) will be equal to the Radiation Machine SAD (3002,0022) minus the RT Image SID (3002,0026). If the image receptor is further from the beam source than the machine isocenter, the Z coordinate will be negative (see [ IEC 61217 ] ).
##### C.******* Multi-frame Image Data
In either multiple exposure Multi-frame Images or cine images, only the Attributes inside Exposure Sequence (3002,0030) shall differ between Frames. For example, Attributes such as beam limiting device (collimator) leaf (element) positions and block information may change, whereas Attributes such as gantry and beam limiting device (collimator) angle shall not change.
##### C.******* X-Ray Image Receptor Angle
The X-Ray Image Receptor Angle (3002,000E) specifies the rotation of the image receptor device in the IEC X-RAY IMAGE RECEPTOR PLANE. A positive angle corresponds to a counter-clockwise rotation of the X-Ray Image Receptor as viewed from the radiation source in the IEC GANTRY coordinate system. The normal (non-rotated) Value for this parameter is zero degrees.
##### C.******* Image Plane Pixel Spacing and RT Image SID
The Image Plane Pixel Spacing (3002,0011) shall always be defined on the image plane, i.e., at the radiation machine source to image plane distance specified by RT Image SID (3002,0026). For images where the source-image distance is undefined or unknown (e.g., DRR images), RT Image SID (3002,0026) shall equal Radiation Machine SAD (3002,0022) and Image Plane Pixel Spacing (3002,0011) shall be defined on this common plane.
##### C.******* Exposure Sequence
Exposure Sequence (3002,0030) allows specification of imaging parameters and aperture definitions for single exposure images (single Item Sequence), integrated images (multiple Item Sequence) or multiple exposures (multiple Item Sequence). An integrated image is a Single-frame Image (no Cine Module or Multi-frame Module present) with multiple Items in the Exposure Sequence. A Referenced Frame Number (0008,1160) is not provided. A multiple exposure image can be expressed as a Multi-frame Image containing either a single Frame, or more than one Frame. Referenced Frame Number (0008,1160) shall be specified for each Exposure Sequence (3002,0030) Item for multiple exposure images expressed using more than one Frame.
Many of the Attributes referring to beam parameters can be present both inside Exposure Sequence (3002,0030) and outside the Sequence. When a specific Attribute is present inside the Sequence, that Attribute shall supersede the Attribute found outside Exposure Sequence (3002,0030). For any Item in the Sequence, if the Attribute is not present, the Value of the Attribute (if specified) outside the Sequence shall be used.
##### C.******* Single-Frame and Multi-frame Images
If the Multi-frame Module is present and the Cine Module is not present then the Frame Increment Pointer (0028,0009) shall have the Enumerated Value of 00200013 (Instance Number). If the Multi-frame Module and Cine Module are both present then the Frame Increment Pointer (0028,0009) shall have an Enumerated Value of either 00181063 (Frame Time) or 00181065 (Frame Time Vector).
##### C.******* Image Pixel Attributes
###### C.*******.1 Samples per Pixel
**Enumerated Values:**
1
###### C.*******.2 Photometric Interpretation
**Enumerated Values:**
MONOCHROME2
###### C.*******.3 Bits Allocated
**Enumerated Values:**
8
16
###### C.*******.4 Bits Stored
**Enumerated Values when Bits Allocated (0028,0100) is 8:**
8
**Enumerated Values when Bits Allocated (0028,0100) is 16:**
12
13
14
15
16
###### C.*******.5 High Bit
For RT Images, High Bit (0028,0102) shall be one less than the Value of Bits Stored (0028,0101).
###### C.*******.6 Pixel Representation
**Enumerated Values when Bits Allocated (0028,0100) is 8:**
0000H
unsigned integer
##### C.8.8.2.7 RT Image Plane, Position and Orientation
When RT Image Plane (3002,000C) is NORMAL and RT Image Orientation (3002,0010) is not provided, the orientation is defined as follows: The image viewing direction shall be from the radiation source to the image (i.e., in the sense of a beam's eye view, or along the negative Zr direction of the IEC X-RAY IMAGE RECEPTOR coordinate system).
If RT Image Plane is NON_NORMAL, any rotation defined by RT Image Orientation is performed about the origin of the IEC X-RAY IMAGE RECEPTOR coordinate system. The definitions of the x and y coordinates of the RT Image Position (3002,0012) are defined before any rotation of the image plane is taken into account, see Figure C.8.8.2.7-1.
The direction of rows shall be along the positive Xr direction and the direction of the columns shall be along the negative Yr direction of the IEC X-RAY IMAGE RECEPTOR coordinate system.
**Figure C.8.8.2.7-1. Non-normal Image Plane**
##### C.******* Exposure Time and Meterset Exposure
Multi-frame RT Images may encode a continuous acquisition. In this case, the Exposure Sequence (3002,0030) may not reference all Frames.
The Attributes Exposure Time (0018,1150), Exposure Time in ms (0018,9328) and Meterset Exposure (3002,0032) in the Exposure Sequence (3002,0030), if present with Values, contain the exposure values encompassing the X-Ray exposure during the acquisition of the single Frame referenced by the Referenced Frame Number (0008,1160). Note that not all Frames may be referenced in this Sequence and therefore the sum of these values may not equal the total exposure value.
The Attributes Exposure Time (0018,1150), Exposure Time in ms (0018,9328) and Meterset Exposure (3002,0032) outside the Exposure Sequence (3002,0030) may be used to record the total exposure values.
##### C.8.8.2.9 Fraction Number
The Attribute Fraction Number (3002,0029) in this Module, is equivalent to the Current Fraction Number (3008,0022). See Section C.8.8.21.4.