#### C.8.8.8 RT ROI Observations Module
The RT ROI Observations Module specifies the identification and interpretation of an ROI specified in the Structure Set Module and ROI Contour Module.
**Table C.8-44. RT ROI Observations Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| RT ROI Observations Sequence | (3006,0080) | RTROIObservationsSequence | SQ | 1 | list[Dataset] | 3 | Sequence of observations related to ROIs defined in the Structure Set ROI Sequence (3006,0020) of the Structure Set Module. One or more Items are permitted in this Sequence. |
| &gt;Observation Number | (3006,0082) | ObservationNumber | IS | 1 | str, int | 1 | Identification number of the Observation. The Value of Observation Number (3006,0082) shall be unique within the RT ROI Observations Sequence (3006,0080). |
| &gt;Referenced ROI Number | (3006,0084) | ReferencedROINumber | IS | 1 | str, int | 1 | Uniquely identifies the referenced ROI described in the Structure Set ROI Sequence (3006,0020). |
| &gt;ROI Observation DateTime | (3006,002E) | ROIObservationDateTime | DT | 1 | str | 3 | DateTime this ROI Observation was last modified. |
| &gt;ROI Observation Context Code Sequence | (3006,004F) | ROIObservationContextCodeSequence | SQ | 1 | list[Dataset] | 3 | The contexts in which the ROI was defined. One or more Items are permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 9272 RT ROI Image Acquisition Context](part16.html#sect_CID_9272). |
| &gt;RT Related ROI Sequence | (3006,0030) | RTRelatedROISequence | SQ | 1 | list[Dataset] | 3 | Sequence of significantly related ROIs, e.g., CTVs contained within a PTV. One or more Items are permitted in this Sequence. |
| &gt;&gt;Referenced ROI Number | (3006,0084) | ReferencedROINumber | IS | 1 | str, int | 1 | Uniquely identifies the related ROI described in the Structure Set ROI Sequence (3006,0020). |
| &gt;&gt;RT ROI Relationship | (3006,0033) | RTROIRelationship | CS | 1 | str | 3 | Relationship of referenced ROI with respect to referencing ROI. **Defined Terms:** SAME ROIs represent the same entity ENCLOSED referenced ROI completely encloses referencing ROI ENCLOSING referencing ROI completely encloses referenced ROI |
| &gt;Include Table 10-7b Multiple Site General Anatomy Optional Macro Attributes |  |  |  |  |  |  | May not be necessary if the anatomy is implicit in the RT ROI Identification Code Sequence (3006,0086). More than one Item in Anatomic Region Sequence (0008,2218) may be used when a region of interest spans multiple anatomical locations and there is not a single pre-coordinated code describing the combination of locations. There is no requirement that the multiple locations be contiguous. |
| &gt;Segmented Property Category Code Sequence | (0062,0003) | SegmentedPropertyCategoryCodeSequence | SQ | 1 | list[Dataset] | 3 | Sequence defining the general category of the property this ROI represents. Only a single Item is permitted in this Sequence. See Note 1. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 9580 RT Segmentation Property Category](part16.html#sect_CID_9580). |
| &gt;RT ROI Identification Code Sequence | (3006,0086) | RTROIIdentificationCodeSequence | SQ | 1 | list[Dataset] | 3 | Sequence containing Code used to identify ROI. Only a single Item is permitted in this Sequence. See Note 2. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | See Section C.******* RT ROI Identification Code Sequence and Therapeutic Role Type Code Sequence. |
| &gt;&gt;Segmented Property Type Modifier Code Sequence | (0062,0011) | SegmentedPropertyTypeModifierCodeSequence | SQ | 1 | list[Dataset] | 3 | Sequence defining the modifier of the property type of this segment. One or more Items are permitted in this Sequence. |
| &gt;&gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 244 Laterality](part16.html#sect_CID_244) for paired anatomic parts, or D [CID 212 Generic Anatomic Location Modifier](part16.html#sect_CID_212) as appropriate. ### Note For Retinal Segmentation Surfaces, laterality is not typically specified. |
| &gt;Therapeutic Role Category Code Sequence | (3010,0064) | TherapeuticRoleCategoryCodeSequence | SQ | 1 | list[Dataset] | 3 | The general category of the therapeutic role of this ROI. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 9503 Radiotherapy Therapeutic Role Category](part16.html#sect_CID_9503). |
| &gt;Therapeutic Role Type Code Sequence | (3010,0065) | TherapeuticRoleTypeCodeSequence | SQ | 1 | list[Dataset] | 3 | The specific property type of the therapeutic role of this ROI. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | See Section C.******* RT ROI Identification Code Sequence and Therapeutic Role Type Code Sequence. |
| &gt;Related RT ROI Observations Sequence | (3006,00A0) | RelatedRTROIObservationsSequence | SQ | 1 | list[Dataset] | 3 | Sequence of related ROI Observations. One or more Items are permitted in this Sequence. |
| &gt;&gt;Observation Number | (3006,0082) | ObservationNumber | IS | 1 | str, int | 1 | Uniquely identifies a related ROI Observation. |
| &gt;RT ROI Interpreted Type | (3006,00A4) | RTROIInterpretedType | CS | 1 | str | 2 | Type of ROI. See Section C.*******. **Defined Terms:** EXTERNAL external patient contour PTV Planning Target Volume (as defined in [ ICRU Report 50 ] ) CTV Clinical Target Volume (as defined in [ ICRU Report 50 ] ) GTV Gross Tumor Volume (as defined in [ ICRU Report 50 ] ) TREATED_VOLUME Treated Volume (as defined in [ ICRU Report 50 ] ) IRRAD_VOLUME Irradiated Volume (as defined in [ ICRU Report 50 ] ) OAR Organ at Risk (as defined in [ ICRU Report 50 ] ) BOLUS patient bolus to be used for external beam therapy AVOIDANCE region in which dose is to be minimized ORGAN patient organ MARKER patient marker or marker on a localizer REGISTRATION registration ROI ISOCENTER treatment isocenter to be used for external beam therapy CONTRAST_AGENT volume into which a contrast agent has been injected CAVITY patient anatomical cavity BRACHY_CHANNEL brachytherapy channel BRACHY_ACCESSORY brachytherapy accessory device BRACHY_SRC_APP brachytherapy source applicator BRACHY_CHNL_SHLD brachytherapy channel shield SUPPORT external patient support device FIXATION external patient fixation or immobilization device DOSE_REGION ROI to be used as a dose reference CONTROL ROI to be used in control of dose optimization and calculation DOSE_MEASUREMENT ROI representing a dose measurement device, such as a chamber or TLD DEVICE device not addressed by another Defined Term |
| &gt;ROI Interpreter | (3006,00A6) | ROIInterpreter | PN | 1 | str, PersonName | 2 | Name of person performing the interpretation. |
| &gt;ROI Interpreter Sequence | (3006,004E) | ROIInterpreterSequence | SQ | 1 | list[Dataset] | 1C | Person or device performing the interpretation. Required if ROI Creator Sequence (3006,004D) is present in the Structure Set Module, and the person or device performing the interpretation differs from ROI Creator Sequence (3006,004D). May be present otherwise. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table C.17-3b Identified Person or Device Macro Attributes |  |  |  |  |  |  | Organizational Role Code Sequence (0044,010A) D [CID 9555 Radiotherapy Treatment Planning Person Role](part16.html#sect_CID_9555). |
| &gt;Material ID | (300A,00E1) | MaterialID | SH | 1 | str | 3 | User-supplied identifier for ROI material. |
| &gt;ROI Physical Properties Sequence | (3006,00B0) | ROIPhysicalPropertiesSequence | SQ | 1 | list[Dataset] | 3 | Sequence describing physical properties associated with current ROI interpretation. One or more Items are permitted in this Sequence. |
| &gt;&gt;ROI Physical Property | (3006,00B2) | ROIPhysicalProperty | CS | 1 | str | 1 | Physical property specified by ROI Physical Property Value (3006,00B4). **Defined Terms:** REL_MASS_DENSITY mass density relative to water REL_ELEC_DENSITY electron density relative to water EFFECTIVE_Z effective atomic number EFF_Z_PER_A ratio of effective atomic number to mass (AMU -1 ) REL_STOP_RATIO ratio of linear stopping power of material relative to linear stopping power of water ELEM_FRACTION elemental composition of the material MEAN_EXCI_ENERGY Mean Excitation Energy of the material (eV) |
| &gt;&gt;ROI Elemental Composition Sequence | (3006,00B6) | ROIElementalCompositionSequence | SQ | 1 | list[Dataset] | 1C | The elemental composition of the ROI and the atomic mass fraction of the elements in the ROI. Required if ROI Physical Property (3006,00B2) equals ELEM_FRACTION. One or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;ROI Elemental Composition Atomic Number | (3006,00B7) | ROIElementalCompositionAtomicNumber | US | 1 | int | 1 | The atomic number of the element for which the ROI Elemental Composition Sequence (3006,00B6) is present. |
| &gt;&gt;&gt;ROI Elemental Composition Atomic Mass Fraction | (3006,00B8) | ROIElementalCompositionAtomicMassFraction | FL | 1 | float | 1 | The fractional weight of the element for which the ROI Elemental Composition Sequence (3006,00B6) is present. The sum of all ROI Elemental Composition Atomic Mass Fractions (3006,00B8) within the ROI Elemental Composition Sequence (3006,00B6) shall equal 1.0 within acceptable limits of floating point precision. |
| &gt;&gt;ROI Physical Property Value | (3006,00B4) | ROIPhysicalPropertyValue | DS | 1 | str, float, int | 1 | User-assigned value for physical property. ### Note The value has no meaning when ROI Physical Property (3006,00B2) has the Value ELEM_FRACTION. Therefore this Attribute may contain any value and receivers may ignore that value. |
### Note
- This Attribute allows preserving information by copying the content of Segmented Property Category Code Sequence (0062,0003) in case a Segmentation object is re-encoded as an RT Structure Set or vice-versa.
- In case of re-encoding a Segmentation object as an RT Structure Set or vice-versa it is suggested, that the Segmented Property Type Code Sequence (0062,000F) is mapped to RT ROI Identification Code Sequence (3006,0086).
##### C.******* RT ROI Interpreted Type
RT ROI Interpreted Type (3006,00A4) shall describe the class of ROI (e.g., CTV, PTV).
In addition to providing a Defined Term in RT ROI Interpreted Type (3006,00A4), it is recommended that coded information be included; see Section C.******* RT ROI Identification Code Sequence and Therapeutic Role Type Code Sequence.
##### C.8.8.8.2 Additional RT ROI Identification Code Sequence (Retired)
Retired. See [PS3.3-2016c](http://dicom.nema.org/MEDICAL/Dicom/2016c/output/pdf/part03.pdf).
##### C.******* RT ROI Identification Code Sequence and Therapeutic Role Type Code Sequence
The CID for RT ROI Identification Code Sequence (3006,0086) shall correspond to the CID defined in [CID 9580 RT Segmentation Property Category](part16.html#sect_CID_9580) for each value in Segmented Property Category Code Sequence (0062,0003).
The CID for Therapeutic Role Type Code Sequence (3010,0065) is defined in Section C.36.6.1.1 Therapeutic Role Type Code Sequence.
###### C.*******.1 Mapping of RT ROI Interpreted Type
Table C.*******.1-1 defines a mapping between the geometric concepts represented in RT ROI Interpreted Type (3006,00A4) and coded values in Segmented Property Category Code Sequence (0062,0003) in combination with RT ROI Identification Code Sequence (3006,0086).
**Table C.*******.1-1. RT ROI Interpreted Type Code Mapping**
| RT ROI Interpreted Type (3006,00A4) | Segmented Property Category Code Sequence (0062,0003) | SegmentedPropertyCategoryCodeSequence | SQ | 1 | list[Dataset] | RT ROI Identification Code Sequence (3006,0086) |
| --- | --- | --- | --- | --- | --- | --- |
| EXTERNAL | [(130047, DCM, "External Body Model")](part16.html#DCM_130047) |  |  |  |  | Any value from D [CID 9507 External Body Model](part16.html#sect_CID_9507) |
| BOLUS | [(130405, DCM, "Patient-Attached Dose Control Object)"](part16.html#DCM_130405) |  |  |  |  | [(228736002, SCT, "Surface Bolus")](http://snomed.info/id/228736002) |
| MARKER | [(130666, DCM, "Radiotherapy Fiducial")](part16.html#DCM_130666) |  |  |  |  | Any value from B [CID 7112 Radiotherapy Fiducial](part16.html#sect_CID_7112) |
| ISOCENTER | [(130043, DCM, "RT Geometric Information")](part16.html#DCM_130043) |  |  |  |  | [(130073, DCM, "Isocentric Treatment Location")](part16.html#DCM_130073) |
| CONTRAST_AGENT | [(105590001, SCT, "Substance")](http://snomed.info/id/105590001) |  |  |  |  | [(7140000, SCT, "Contrast agent")](http://snomed.info/id/7140000) |
| CAVITY | [(91723000, SCT, "Anatomical Structure")](http://snomed.info/id/91723000) |  |  |  |  | [(91806002, SCT, "Body Cavity")](http://snomed.info/id/91806002) |
| BRACHY_CHANNEL | [(130045, DCM, "Brachytherapy Device")](part16.html#DCM_130045) |  |  |  |  | [(130080, DCM, "Brachytherapy channel")](part16.html#DCM_130080) |
| BRACHY_ACCESSORY | [(130045, DCM, "Brachytherapy Device")](part16.html#DCM_130045) |  |  |  |  | No Baseline CID defined. |
| BRACHY_SRC_APP | [(130045, DCM, "Brachytherapy Device")](part16.html#DCM_130045) |  |  |  |  | [(130078, DCM, "Brachytherapy source applicator")](part16.html#DCM_130078) |
| BRACHY_CHNL_SHLD | [(130045, DCM, "Brachytherapy Device")](part16.html#DCM_130045) |  |  |  |  | [(130079, DCM, "Brachytherapy channel shield")](part16.html#DCM_130079) |
| SUPPORT | [(130044, DCM, "Fixation or Positioning Device")](part16.html#DCM_130044) |  |  |  |  | Any value from D [CID 9515 RT Patient Support Device](part16.html#sect_CID_9515) |
| FIXATION | [(130044, DCM, "Fixation or Positioning Device")](part16.html#DCM_130044) |  |  |  |  | Any value from D [CID 9513 Fixation Device](part16.html#sect_CID_9513) |
| DOSE_REGION | [(130748, DCM, "Radiotherapy Dose Region")](part16.html#DCM_130748) |  |  |  |  | [(130747, DCM, "Isodose Volume")](part16.html#DCM_130747) |
| DOSE_MEASUREMENT | [(260787004, SCT, "Physical object")](http://snomed.info/id/260787004) |  |  |  |  | Any value from D [CID 7027 Segmented Radiotherapeutic Dose Measurement Device](part16.html#sect_CID_7027) |
| DEVICE | [(260787004, SCT, "Physical object")](http://snomed.info/id/260787004) |  |  |  |  | Any value from D [CID 7157 Device Segmentation Type](part16.html#sect_CID_7157) or D [CID 6401 Non-lesion Object Type - Physical Object](part16.html#sect_CID_6401) |
Table C.*******.1-2 defines a mapping between therapeutic roles represented in RT ROI Interpreted Type (3006,00A4) and coded values in Therapeutic Role Category Code Sequence (3010,0064) in combination with Therapeutic Role Type Code Sequence (3010,0065).
**Table C.*******.1-2. Therapeutic Role Code Mapping**
| RT ROI Interpreted Type (3006,00A4) | Therapeutic Role Category Code Sequence (3010,0064) | TherapeuticRoleCategoryCodeSequence | SQ | 1 | list[Dataset] | Therapeutic Role Type Code Sequence (3010,0065) |
| --- | --- | --- | --- | --- | --- | --- |
| PTV | [(130041, DCM, "RT Target")](part16.html#DCM_130041) |  |  |  |  | [(228793007, SCT, "PTV")](http://snomed.info/id/228793007) |
| CTV | [(130041, DCM, "RT Target")](part16.html#DCM_130041) |  |  |  |  | [(228792002, SCT, "CTV")](http://snomed.info/id/228792002) |
| GTV | [(130041, DCM, "RT Target")](part16.html#DCM_130041) |  |  |  |  | [(228791009, SCT, "GTV")](http://snomed.info/id/228791009) |
| OAR | [(130042, DCM, "RT Dose Calculation Structure")](part16.html#DCM_130042) |  |  |  |  | [(130060, DCM, "Organ At Risk")](part16.html#DCM_130060) |
| TREATED_VOLUME | [(130041, DCM, "RT Target")](part16.html#DCM_130041) |  |  |  |  | [(130059, DCM, "Treated Volume")](part16.html#DCM_130059) |
| IRRAD_VOLUME | [(130041, DCM, "RT Target")](part16.html#DCM_130041) |  |  |  |  | [(228790005, SCT, "Irradiated Volume")](http://snomed.info/id/228790005) |
| AVOIDANCE | [(130042, DCM, "RT Dose Calculation Structure")](part16.html#DCM_130042) |  |  |  |  | [(130058, DCM, "Avoidance Volume")](part16.html#DCM_130058) |
| REGISTRATION | [(130746, DCM, "RT Registration Mark")](part16.html#DCM_130746) |  |  |  |  | [(112171, DCM, "Fiducial mark")](part16.html#DCM_112171) |
| CONTROL | [(130042, DCM, "RT Dose Calculation Structure")](part16.html#DCM_130042) |  |  |  |  | [(130061, DCM, "Radiation Dose Shaping Volume")](part16.html#DCM_130061) or [(130062, DCM, "Conformality Shell")](part16.html#DCM_130062) |
For examples of how the mapping is applied see [Section XXXX.1.1 Coding RT ROI Interpreted Type Information in PS3.17](part17.html#sect_XXXX.1.1).