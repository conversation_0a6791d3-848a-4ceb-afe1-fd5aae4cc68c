#### C.7.6.22 Specimen Module
Table C.7.6.22-1 specifies the Attributes of the Specimen Module, which identify one or more Specimens being imaged.
**Table C.7.6.22-1. Specimen Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Include Table C.7.6.22-2 Specimen Macro Attributes |  |  |  |  |  |  |  |
**Table C.7.6.22-2. Specimen Macro Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Container Identifier | (0040,0512) | ContainerIdentifier | LO | 1 | str | 1 | The identifier for the container that contains the specimen(s) being imaged. See Section C.********.1. |
| Issuer of the Container Identifier Sequence | (0040,0513) | IssuerOfTheContainerIdentifierSequence | SQ | 1 | list[Dataset] | 2 | Organization that assigned the Container Identifier. Zero or one Item shall be included in this Sequence. |
| &gt;Include Table 10-17 HL7v2 Hierarchic Designator Macro Attributes |  |  |  |  |  |  |  |
| Alternate Container Identifier Sequence | (0040,0515) | AlternateContainerIdentifierSequence | SQ | 1 | list[Dataset] | 3 | Sequence of alternate identifiers for the container that contains the specimen(s) being imaged. These may have been assigned, e.g., by the manufacturer, or by another institution that collected the specimen. One or more Items are permitted in this Sequence. |
| &gt;Container Identifier | (0040,0512) | ContainerIdentifier | LO | 1 | str | 1 | The identifier for the container that contains the specimen(s) being imaged. |
| &gt;Issuer of the Container Identifier Sequence | (0040,0513) | IssuerOfTheContainerIdentifierSequence | SQ | 1 | list[Dataset] | 2 | Organization that assigned the Container Identifier. Zero or one Item shall be included in this Sequence. |
| &gt;&gt;Include Table 10-17 HL7v2 Hierarchic Designator Macro Attributes |  |  |  |  |  |  |  |
| Container Type Code Sequence | (0040,0518) | ContainerTypeCodeSequence | SQ | 1 | list[Dataset] | 2 | Type of container that contains the specimen(s) being imaged. Zero or one Item shall be included in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 8101 Container Type](part16.html#sect_CID_8101). |
| Container Description | (0040,051A) | ContainerDescription | LO | 1 | str | 3 | Description of the container. |
| Container Component Sequence | (0040,0520) | ContainerComponentSequence | SQ | 1 | list[Dataset] | 3 | Description of one or more components of the container (e.g., description of the slide and of the coverslip). One or more Items are permitted in this Sequence. |
| &gt;Container Component Type Code Sequence | (0050,0012) | ContainerComponentTypeCodeSequence | SQ | 1 | list[Dataset] | 1 | Type of container component. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 8102 Container Component Type](part16.html#sect_CID_8102). |
| &gt;Manufacturer | (0008,0070) | Manufacturer | LO | 1 | str | 3 | Manufacturer of the container component. |
| &gt;Manufacturer's Model Name | (0008,1090) | ManufacturerModelName | LO | 1 | str | 3 | Manufacturer's model name of the container component. |
| &gt;Container Component ID | (0050,001B) | ContainerComponentID | LO | 1 | str | 3 | Manufacturer's identifier of the container component, e.g., Lot Number and/or Version. |
| &gt;Container Component Length | (0050,001C) | ContainerComponentLength | FD | 1 | float | 3 | Length in mm of container component. |
| &gt;Container Component Width | (0050,0015) | ContainerComponentWidth | FD | 1 | float | 3 | Width in mm of container component. |
| &gt;Container Component Diameter | (0050,001D) | ContainerComponentDiameter | FD | 1 | float | 3 | Diameter in mm of container component for cylindrical or circular components. |
| &gt;Container Component Thickness | (0050,0013) | ContainerComponentThickness | FD | 1 | float | 3 | Thickness in mm of container component. |
| &gt;Container Component Material | (0050,001A) | ContainerComponentMaterial | CS | 1 | str | 3 | Material of container component. **Defined Terms:** GLASS PLASTIC METAL |
| &gt;Container Component Description | (0050,001E) | ContainerComponentDescription | LO | 1 | str | 3 | Container component text description. |
| Specimen Description Sequence | (0040,0560) | SpecimenDescriptionSequence | SQ | 1 | list[Dataset] | 1 | Sequence of identifiers and detailed description of the specimen(s) being imaged. One or more Items shall be included in this Sequence. Each specimen imaged in the Pixel Data shall be identified by an Item in this Sequence. Other specimens in/on the container, but not imaged in the Pixel Data, may also be identified by Items in this Sequence. |
| &gt;Specimen Identifier | (0040,0551) | SpecimenIdentifier | LO | 1 | str | 1 | A departmental information system identifier for the Specimen. See Section C.********.1 and Section C.********.2. If a single specimen is present in a container, the Value of the Specimen Identifier and the Value of the Container Identifier are typically the same. |
| &gt;Issuer of the Specimen Identifier Sequence | (0040,0562) | IssuerOfTheSpecimenIdentifierSequence | SQ | 1 | list[Dataset] | 2 | The name or code for the institution that has assigned the Specimen Identifier. Zero or one Item shall be included in this Sequence. |
| &gt;&gt;Include Table 10-17 HL7v2 Hierarchic Designator Macro Attributes |  |  |  |  |  |  |  |
| &gt;Specimen UID | (0040,0554) | SpecimenUID | UI | 1 | str | 1 | Unique Identifier for Specimen. See Section C.********.2. |
| &gt;Specimen Type Code Sequence | (0040,059A) | SpecimenTypeCodeSequence | SQ | 1 | list[Dataset] | 3 | Specimen Type. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 8103 Anatomic Pathology Specimen Type](part16.html#sect_CID_8103). |
| &gt;Specimen Short Description | (0040,0600) | SpecimenShortDescription | LO | 1 | str | 3 | Short textual specimen description (may include ancestor specimen descriptions). |
| &gt;Specimen Detailed Description | (0040,0602) | SpecimenDetailedDescription | UT | 1 | str | 3 | Detailed textual specimen description (may include ancestor specimen descriptions). |
| &gt;Specimen Preparation Sequence | (0040,0610) | SpecimenPreparationSequence | SQ | 1 | list[Dataset] | 2 | Sequence of Items identifying the process steps used to prepare the specimen for image acquisition. This includes description of all processing necessary to interpret the image. Zero or more Items shall be included in this Sequence. This Sequence includes description of the specimen sampling step from an ancestor specimen, potentially back to the original part collection. See Section C.********.3. |
| &gt;&gt;Specimen Preparation Step Content Item Sequence | (0040,0612) | SpecimenPreparationStepContentItemSequence | SQ | 1 | list[Dataset] | 1 | Sequence of Content Items identifying the processes used in one preparation step to prepare the specimen for image acquisition. One or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;Include Table 10-2 Content Item Macro Attributes |  |  |  |  |  |  | B [TID 8001 Specimen Preparation](part16.html#sect_TID_8001) |
| &gt;Include Table 10-8 Primary Anatomic Structure Macro Attributes |  |  |  |  |  |  | Original anatomic location in patient of specimen. This location may be identical to that of the parent specimen, may be further refined by modifiers depending on the sampling procedure for this specimen, or may be a distinct concept. B [CID 8134 Anatomic Structure](part16.html#sect_CID_8134) |
| &gt;Specimen Localization Content Item Sequence | (0040,0620) | SpecimenLocalizationContentItemSequence | SQ | 1 | list[Dataset] | 1C | Sequence of Content Items identifying the location of the specimen in the container and/or in the image. See Section C.********.4. One or more Items shall be included in this Sequence. Required if multiple specimens present in the image. May be present otherwise. |
| &gt;&gt;Include Table 10-2 Content Item Macro Attributes |  |  |  |  |  |  | D [TID 8004 Specimen Localization](part16.html#sect_TID_8004). |
##### C.******** Specimen Module Attribute Descriptions
###### C.********.1 Container Identifier and Specimen Identifier
"Specimen" is the role played by a discrete physical object (or a collection of objects that are considered as a unit) that is the subject of pathology examination.
A specimen is a physical object (or a collection of objects) when the laboratory considers it a single discrete, uniquely identified unit that is the subject of one or more steps in the laboratory (diagnostic) workflow. This includes objects at all levels of processing, including fresh tissue, dissected organs, tissue embedded in paraffin, sections made from embedded tissue, and liquid preparations.
Specimens are physically managed by being placed in or on a container. The concept of container includes buckets, cassettes, vials, and slides. While there is usually one specimen per container, it is possible, in some laboratory workflows, for multiple specimens to be in/on a container.
Both specimens and specimen containers have logical identifiers for workflow management. The logical identifier of a container is usually conveyed on a label on the container. The specimen itself will typically not be physically labeled with its identifier. For the usual case of a single specimen in/on a container, the logical identifiers may be identical. However, when there are multiple specimens in/on a container, each specimen receives a distinct logical identifier. These identifiers are encoded in the SOP Instance using Attributes Container Identifier (0040,0512) and Specimen Identifier (0040,0551).
### Note
- This definition of "specimen" extends the common definition beyond the part or parts that were submitted for examination (e.g., from surgery) to include any derivative piece that may be separately analyzed or examined, such as a block or slide preparation.
- Although many Pathology Information Systems use a hierarchical system for identifying parts, blocks and slides, there should be no assumption made that this will be the case and in particular, there should be no attempt to parse a given Specimen Identifier to retrieve an accession number or other higher level identifier.
###### C.********.2 Specimen Identifier and Specimen UID
Specimen Identifier (0040,0551) must be unique at least within the Study; the actual scope of uniqueness is determined by the departmental information system that assigns the IDs. Each specimen shall also be assigned a globally unique Specimen UID (0040,0554) that allows referencing beyond the scope of a Study. This UID may be used, for instance, if a specimen is delivered to another institution for further analysis.
###### C.********.3 Specimen Preparation Sequence and Specimen Preparation Step Content Item Sequence
Interpretation of specimen images requires information about the source of the specimen and its preparation (e.g., sampling, fixation, staining). The processing steps used to prepare a specimen are recorded in the Specimen Preparation Sequence (0040,0610). This Sequence may include one Item for each processing step (as defined in the laboratory workflow) in the history of the specimen, and those Items are composed of a set of Content Items in the Specimen Preparation Step Content Item Sequence (0040,0612).
The Specimen Preparation Sequence may include description of the original part collected from the patient, the processing of that part, the sampling of tissue from the part and the preparation of that sample, and the further sub-sampling and processing of the tissue. In other words, the description of a specific specimen may include descriptions of the specimen's ancestors.
The Specimen Preparation Sequence Items shall be in ascending chronological order.
###### C.********.4 Specimen Localization Content Item Sequence
When there are multiple specimens in/on a container, the Specimen Localization Content Item Sequence (0040,0620) is used to identify the location of the specimen in the container, as there is no physical label with the Specimen Identifier. This Content Item Sequence, in accordance with [TID 8004 Specimen Localization](part16.html#sect_TID_8004), allows the specimen to be localized by a distance in one to three dimensions from a reference point on the container, by an identified physical description such as a colored ink, or by its location as shown in a referenced image of the container. The referenced image may use an overlay, burned-in annotation, or an associated Presentation State SOP Instance to specify the location of the specimen.