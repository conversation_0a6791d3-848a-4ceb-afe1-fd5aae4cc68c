#### C.8.8.4 RT DVH Module
The RT DVH Module provides for the inclusion of differential or cumulative dose volume histogram data. The data contained within this Module may supplement dose data in the RT Dose Module, or it may be present in the absence of other dose data.
**Table C.8-40. RT DVH Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Referenced Structure Set Sequence | (300C,0060) | ReferencedStructureSetSequence | SQ | 1 | list[Dataset] | 1 | Sequence of one Class/Instance pair describing Structure Set containing structures that are used to calculate Dose-Volume Histograms (DVHs). Only a single Item shall be included in this Sequence. See Section C.*******. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| DVH Normalization Point | (3004,0040) | DVHNormalizationPoint | DS | 3 | str, float, int | 3 | Coordinates (x, y, z) of common DVH normalization point in the Patient-Based Coordinate System described in Section C.*******.1 (mm). |
| DVH Normalization Dose Value | (3004,0042) | DVHNormalizationDoseValue | DS | 1 | str, float, int | 3 | Dose Value at DVH Normalization Point (3004,0040) used as reference for individual DVHs when Dose Units (3004,0002) is RELATIVE. |
| DVH Sequence | (3004,0050) | DVHSequence | SQ | 1 | list[Dataset] | 1 | Sequence of DVHs. One or more Items shall be included in this Sequence. |
| &gt;DVH Referenced ROI Sequence | (3004,0060) | DVHReferencedROISequence | SQ | 1 | list[Dataset] | 1 | Sequence of referenced ROIs used to calculate DVH. One or more Items shall be included in this Sequence. |
| &gt;&gt;Referenced ROI Number | (3006,0084) | ReferencedROINumber | IS | 1 | str, int | 1 | Uniquely identifies ROI used to calculate DVH specified by ROI Number (3006,0022) in Structure Set ROI Sequence (3006,0020) in Structure Set Module within RT Structure Set referenced by referenced RT Plan in Referenced RT Plan Sequence (300C,0002) in RT Dose Module. |
| &gt;&gt;DVH ROI Contribution Type | (3004,0062) | DVHROIContributionType | CS | 1 | str | 1 | Specifies whether volume within ROI is included or excluded in DVH. See Section C.8.8.4.2. **Enumerated Values:** INCLUDED EXCLUDED |
| &gt;DVH Type | (3004,0001) | DVHType | CS | 1 | str | 1 | Type of DVH. **Enumerated Values:** DIFFERENTIAL differential dose-volume histogram CUMULATIVE cumulative dose-volume histogram NATURAL natural dose volume histogram |
| &gt;Dose Units | (3004,0002) | DoseUnits | CS | 1 | str | 1 | Dose axis units. **Enumerated Values:** GY Gray RELATIVE dose relative to reference value specified in DVH Normalization Dose Value (3004,0042) |
| &gt;Dose Type | (3004,0004) | DoseType | CS | 1 | str | 1 | Type of dose. **Defined Terms:** PHYSICAL physical dose EFFECTIVE physical dose after correction for biological effect using user-defined modeling technique ERROR difference between desired and planned dose |
| &gt;DVH Dose Scaling | (3004,0052) | DVHDoseScaling | DS | 1 | str, float, int | 1 | Scaling factor that when multiplied by the dose bin widths found in DVH Data (3004,0058), yields dose bin widths in the dose units as specified by Dose Units (3004,0002). |
| &gt;DVH Volume Units | (3004,0054) | DVHVolumeUnits | CS | 1 | str | 1 | Volume axis units. **Defined Terms:** CM3 cubic centimeters PERCENT percent PER_U volume per u with u(dose)=dose -3/2 . See Section C.*******. |
| &gt;DVH Number of Bins | (3004,0056) | DVHNumberOfBins | IS | 1 | str, int | 1 | Number of bins n used to store DVH Data (3004,0058). |
| &gt;DVH Data | (3004,0058) | DVHData | DS | 2-2n | str, float, int | 1 | A data stream describing the dose bin widths Dn and associated volumes Vn in DVH Volume Units (3004,0054) in the order D1V1, D2V2, DnVn. ### Note If the Value Length of this Data Element exceeds 65534 bytes and an Explicit VR Transfer Syntax is used, then the Value Representation UN can be used for this Data Element. See [PS3.5 Section 6.2.2](part05.html#sect_6.2.2). |
| &gt;DVH Minimum Dose | (3004,0070) | DVHMinimumDose | DS | 1 | str, float, int | 3 | Minimum calculated dose to ROI(s) described by DVH Referenced ROI Sequence (3004,0060) in units specified by Dose Units (3004,0002) of the current Sequence Item. |
| &gt;DVH Maximum Dose | (3004,0072) | DVHMaximumDose | DS | 1 | str, float, int | 3 | Maximum calculated dose to ROI(s) described by DVH Referenced ROI Sequence (3004,0060) in units specified by Dose Units (3004,0002) of the current Sequence Item. |
| &gt;DVH Mean Dose | (3004,0074) | DVHMeanDose | DS | 1 | str, float, int | 3 | Mean calculated dose to ROI(s) described by DVH Referenced ROI Sequence (3004,0060) in units specified by Dose Units (3004,0002) of the current Sequence Item. |
##### C.******* Referenced Structure Set Sequence
The Referenced Structure Set Sequence (300C,0060) is required for direct cross-reference of the dose bin data with the corresponding ROI(s) from which they were derived. ROIs referenced by the DVH Referenced ROI Sequence (3004,0050) shall only contain contours with a Contour Geometric Type (3006,0042) of POINT or CLOSED_PLANAR.
##### C.8.8.4.2 DVH ROI Contribution Type
The volume used to calculate the DVH shall be the geometric union of ROIs where DVH ROI Contribution Type (3004,0062) is INCLUDED, minus the geometric union of ROIs where DVH ROI Contribution Type (3004,0062) is EXCLUDED.
##### C.******* DVH Volume Units
The unit PER_U is defined in: Anderson, LL: "A "natural" volume-dose histogram for brachytherapy", Medical Physics 13(6) pp 898-903, 1986.