#### C.8.8.1 RT Series Module
There exist significant differences in the manner in which RT objects as compared to diagnostic objects. An RT object can be one of several types, and a series of a given object type may be created over a temporal span of several weeks. The RT Series Module has been created to satisfy the requirements of the standard DICOM Query/Retrieve model while including only those Attributes relevant to the identification and selection of radiotherapy objects.
**Table C.8-37. RT Series Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Modality | (0008,0060) | Modality | CS | 1 | str | 1 | Type of device, process or method that originally acquired the data used to create the Instances in this Series. **Enumerated Values:** RTIMAGE RT Image RTDOSE RT Dose RTSTRUCT RT Structure Set RTPLAN RT Plan RTRECORD RT Treatment Record See Section C.*******. |
| Series Instance UID | (0020,000E) | SeriesInstanceUID | UI | 1 | str | 1 | Unique identifier of the Series. |
| Series Number | (0020,0011) | SeriesNumber | IS | 1 | str, int | 2 | A number that identifies this Series. |
| Series Date | (0008,0021) | SeriesDate | DA | 1 | str | 3 | Date the Series started. |
| Series Time | (0008,0031) | SeriesTime | TM | 1 | str | 3 | Time the Series started. |
| Series Description | (0008,103E) | SeriesDescription | LO | 1 | str | 3 | Description of the Series. |
| Series Description Code Sequence | (0008,103F) | SeriesDescriptionCodeSequence | SQ | 1 | list[Dataset] | 3 | A coded description of the Series. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Operators' Name | (0008,1070) | OperatorsName | PN | 1-n | str, PersonName | 2 | Name(s) of the operator(s) supporting the Series. |
| Operator Identification Sequence | (0008,1072) | OperatorIdentificationSequence | SQ | 1 | list[Dataset] | 3 | Identification of the operator(s) supporting the Series. One or more Items are permitted in this Sequence. If more than one Item, the number and order shall correspond to the Value of Operators' Name (0008,1070), if present. |
| &gt;Include Table 10-1 Person Identification Macro Attributes |  |  |  |  |  |  |  |
| Referenced Performed Procedure Step Sequence | (0008,1111) | ReferencedPerformedProcedureStepSequence | SQ | 1 | list[Dataset] | 3 | Uniquely identifies the Performed Procedure Step SOP Instance to which the Series is related. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| Request Attributes Sequence | (0040,0275) | RequestAttributesSequence | SQ | 1 | list[Dataset] | 3 | Sequence that contains Attributes from the Imaging Service Request. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10-9 Request Attributes Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Include Table 10-16 Performed Procedure Step Summary Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Treatment Session UID | (300A,0700) | TreatmentSessionUID | UI | 1 | str | 3 | A unique identifier of the RT Treatment Session to which Instances in this Series belong. |
##### C.******* Modality
The Enumerated Value for Modality (0008,0060) shall determined by the IOD.
**Enumerated Values if RT Image IOD :**
RTIMAGE
**Enumerated Values if RT Dose IOD :**
RTDOSE
**Enumerated Values if RT Structure Set IOD :**
RTSTRUCT
**Enumerated Values if RT Plan IOD or RT Ion Plan IOD :**
RTPLAN
**Enumerated Values if RT Beams Treatment Record IOD, RT Ion Beams Treatment Record IOD, RT Brachy Treatment Record IOD, or RT Treatment Summary Record IOD :**
RTRECORD
### Note
DICOM specifies that a given Series shall contain objects of only one Modality, and shall be created by a single device (described in the General Equipment Module ). However, in general there may be many Series defined for a given modality/device pair. Note that a radiotherapy Series is generally created over an extended time interval (unlike in radiology, where all images in an image Series are generally created together).