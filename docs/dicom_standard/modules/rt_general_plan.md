#### C.8.8.9 RT General Plan Module
**Table C.8-45. RT General Plan Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| RT Plan Label | (300A,0002) | RTPlanLabel | SH | 1 | str | 1 | User-defined label for treatment plan. |
| RT Plan Name | (300A,0003) | RTPlanName | LO | 1 | str | 3 | User-defined name for treatment plan. |
| RT Plan Description | (300A,0004) | RTPlanDescription | ST | 1 | str | 3 | User-defined description of treatment plan. |
| Instance Number | (0020,0013) | InstanceNumber | IS | 1 | str, int | 3 | A number that identifies this object Instance. |
| RT Plan Date | (300A,0006) | RTPlanDate | DA | 1 | str | 2 | Date treatment plan was last modified. |
| RT Plan Time | (300A,0007) | RTPlanTime | TM | 1 | str | 2 | Time treatment plan was last modified. |
| RT Assertions Sequence | (0044,0110) |  |  |  |  | 3 | Assertions made for this instance. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10.30-1 Assertion Macro Attributes |  |  |  |  |  |  | To make assertions about Approval Status (300E,0002), use Assertion Code Sequence (0044,0101) B [CID 9584 RT Plan Approval Assertion](part16.html#sect_CID_9584). The Approver is recorded in the Asserter Identification Sequence inside the Assertion Macro. |
| Treatment Protocols | (300A,0009) | TreatmentProtocols | LO | 1-n | str | 3 | Planned treatment protocols. |
| Plan Intent | (300A,000A) | PlanIntent | CS | 1 | str | 3 | Intent of this plan. **Defined Terms:** CURATIVE Curative therapy on patient PALLIATIVE Palliative therapy on patient PROPHYLACTIC Preventative therapy on patient VERIFICATION Verification of patient plan using phantom MACHINE_QA Quality assurance of the delivery machine (independently of a specific patient) RESEARCH Research project SERVICE Machine repair or maintenance operation |
| Treatment Site | (3010,0077) | TreatmentSite | LO | 1 | str | 3 | A free-text label describing the anatomical treatment site. |
| Treatment Site Code Sequence | (3010,0078) | TreatmentSiteCodeSequence | SQ | 1 | list[Dataset] | 3 | Coded description of the treatment site. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 4 Anatomic Region](part16.html#sect_CID_4). |
| &gt;Treatment Site Modifier Code Sequence | (3010,0089) | TreatmentSiteModifierCodeSequence | SQ | 1 | list[Dataset] | 3 | Coded description of the laterality of the treatment site. Only a single Item is permitted in this Sequencee. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 244 Laterality](part16.html#sect_CID_244). |
| RT Plan Geometry | (300A,000C) | RTPlanGeometry | CS | 1 | str | 1 | Describes whether RT Plan is based on patient or treatment device geometry. See Section C.*******. **Defined Terms:** PATIENT RT Structure Set exists TREATMENT_DEVICE RT Structure Set does not exist |
| Referenced Structure Set Sequence | (300C,0060) | ReferencedStructureSetSequence | SQ | 1 | list[Dataset] | 1C | The RT Structure Set on which the RT Plan is based. Only a single Item shall be included in this Sequence. Required if RT Plan Geometry (300A,000C) is PATIENT. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| Referenced Dose Sequence | (300C,0080) | ReferencedDoseSequence | SQ | 1 | list[Dataset] | 3 | Related Instances of RT Dose (for grids and named/unnamed point doses). One or more Items are permitted in this Sequence. See Note 1. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| Referenced RT Plan Sequence | (300C,0002) | ReferencedRTPlanSequence | SQ | 1 | list[Dataset] | 3 | Related Instances of RT Plan. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;RT Plan Relationship | (300A,0055) | RTPlanRelationship | CS | 1 | str | 1 | Relationship of referenced plan with respect to current plan. **Defined Terms:** PRIOR plan delivered prior to current treatment ALTERNATIVE alternative plan prepared for current treatment PREDECESSOR plan used in derivation of current plan VERIFIED_PLAN plan that is verified using the current plan. This Value shall only be used if Plan Intent (300A,000A) is present and has a Value of VERIFICATION CONCURRENT plan that forms part of a set of two or more RT Plan Instances representing a single conceptual 'plan', applied in parallel in one treatment phase |
| Frame of Reference to Displayed Coordinate System Transformation Matrix | (0070,030B) | FrameOfReferenceToDisplayedCoordinateSystemTransformationMatrix | FD | 16 | float | 3 | A 4x4 transformation matrix that transforms a coordinate of the Frame of Reference to a displayed coordinate system. Only rigid transformation matrices are permitted (see definition in Section C.******** ). Matrix elements shall be listed in row major order. |
### Note
- An RT Dose IOD referenced within the Referenced Dose Sequence (300C,0080) can be used for storing grid-based (pixel) data, individual dose points (with optional dose point names), isodose curves, and DVHs.
##### C.******* Referenced Structure Set Sequence
An RT Plan Geometry (300A,000C) of PATIENT shall signify that an RT Structure Set has been defined upon which the plan geometry is based, and this RT Structure Set shall be specified in the Referenced Structure Set Sequence (300C,0060). An RT Plan Geometry (300A,000C) of TREATMENT_DEVICE shall indicate that no patient geometry is available, and that the RT Plan is being defined with respect to the IEC FIXED Coordinate System.