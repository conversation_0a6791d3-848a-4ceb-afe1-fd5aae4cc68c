### C.12.3 Frame Extraction Module
Table C.12-9 defines the Attributes that describe the Frames extracted if the SOP Instance was created in response to a Frame-Level retrieve request.
**Table C.12-9. Frame Extraction Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Frame Extraction Sequence | (0008,1164) | FrameExtractionSequence | SQ | 1 | list[Dataset] | 1 | Sequence containing details of how this SOP Instance was extracted from a source multi-frame SOP Instance. If this Instance was created from an Instance that contains a Frame Extraction Sequence, then this Sequence shall contain all of the Items from the parent's Frame Extraction Sequence and a new Item that describes this extraction. One or more Items shall be included in this Sequence. |
| &gt;Multi-frame Source SOP Instance UID | (0008,1167) | MultiFrameSourceSOPInstanceUID | UI | 1 | str | 1 | SOP Instance from which the Frames of this Instance are extracted. |
| &gt;Simple Frame List | (0008,1161) | SimpleFrameList | UL | 1-n | int | 1C | A list of Frames that were extracted in the form of a simple list. Required if object extraction is based on a Frame Level Retrieve using the Simple Frame List (0008,1161). See [PS3.4](part04.html#PS3.4) "Instance and Frame Level Retrieve SOP Classes". |
| &gt;Calculated Frame List | (0008,1162) | CalculatedFrameList | UL | 3-3n | int | 1C | A list of Frames that were extracted in the form of one or more triplets Required if object extraction is based on a Frame Level Retrieve using the Calculated Frame List (0008,1162). See [PS3.4](part04.html#PS3.4) "Instance and Frame Level Retrieve SOP Classes". |
| &gt;Time Range | (0008,1163) | TimeRange | FD | 2 | float | 1C | The start and end times of the Frames that were extracted. Required if object extraction is based on a Frame Level Retrieve using Time Range (0008,1163). See [PS3.4](part04.html#PS3.4) "Instance and Frame Level Retrieve SOP Classes". |