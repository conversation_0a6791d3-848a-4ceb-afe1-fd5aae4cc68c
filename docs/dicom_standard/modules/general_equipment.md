#### C.7.5.1 General Equipment Module
Table C.7-8 specifies the Attributes of the General Equipment Module, which identify and describe the piece of equipment that produced Composite Instances.
**Table C.7-8. General Equipment Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Manufacturer | (0008,0070) | Manufacturer | LO | 1 | str | 2 | Manufacturer of the equipment that produced the Composite Instances. |
| Institution Name | (0008,0080) | InstitutionName | LO | 1 | str | 3 | Institution where the equipment that produced the Composite Instances is located. ### Note This Attribute represents the organizational context only for the Equipment IE, and should not be construed to be a substitute for Issuer of Patient ID (0010,0021) or Issuer of Accession Number (0008,0051). |
| Institution Address | (0008,0081) | InstitutionAddress | ST | 1 | str | 3 | Mailing address of the institution where the equipment that produced the Composite Instances is located. |
| Station Name | (0008,1010) | StationName | SH | 1 | str | 3 | User defined name identifying the machine that produced the Composite Instances. |
| Institutional Department Name | (0008,1040) | InstitutionalDepartmentName | LO | 1 | str | 3 | Department in the institution where the equipment that produced the Composite Instances is located. |
| Institutional Department Type Code Sequence | (0008,1041) | InstitutionalDepartmentTypeCodeSequence | SQ | 1 | list[Dataset] | 3 | A coded description of the type of Department or Service within the healthcare facility. ### Note This might be obtained from a corresponding HL7v2 message containing PV1:10 Hospital Service. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 7030 Institutional Department/Unit/Service](part16.html#sect_CID_7030). |
| Manufacturer's Model Name | (0008,1090) | ManufacturerModelName | LO | 1 | str | 3 | Manufacturer's model name of the equipment that produced the Composite Instances. |
| Manufacturer's Device Class UID | (0018,100B) | ManufacturerDeviceClassUID | UI | 1-n | str | 3 | Manufacturer's Unique Identifier (UID) for the class of the device. A class is a manufacturer-specific grouping concept with no DICOM-defined scope or criteria. A class is independent from a marketing-defined make, model or version. A class allows grouping of devices with a similar set of capabilities. This Attribute may be multi-valued if this device is a member of more than one class. |
| Device Serial Number | (0018,1000) | DeviceSerialNumber | LO | 1 | str | 3 | Manufacturer's serial number of the equipment that produced the Composite Instances. ### Note This identifier corresponds to the device that actually created the images, such as a CR plate reader or a CT console, and may not be sufficient to identify all of the equipment in the imaging chain, such as the generator or gantry or plate. |
| Software Versions | (0018,1020) | SoftwareVersions | LO | 1-n | str | 3 | Manufacturer's designation of software version of the equipment that produced the Composite Instances. See Section C.*******.3. |
| Gantry ID | (0018,1008) | GantryID | LO | 1 | str | 3 | Identifier of the gantry or positioner. |
| UDI Sequence | (0018,100A) | UDISequence | SQ | 1 | list[Dataset] | 3 | Unique Device Identifier (UDI) of the entire equipment. For example, the entire CT Scanner. ### Note - Multiple Items may be present if the entire equipment has UDIs issued by different Issuing Authorities. - Multiple Items may be present if multiple pieces of equipment were involved in the creation of this Instance, e.g., the DR plate and the DR reader. - This is not intended to contain the UDIs of the components of the equipment, such as the X-Ray tube of the CT scanner. Such information is stored elsewhere and accessible using the UDI of the entire equipment and a date. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10.29-1 UDI Macro Attributes |  |  |  |  |  |  |  |
| Device UID | (0018,1002) | DeviceUID | UI | 1 | str | 3 | Unique identifier of the equipment that produced the Composite Instances. ### Note - If present in an SR object, the value is expected to be the same as the [(121012, DCM, "Device Observer UID")](part16.html#DCM_121012) in [TID 1004 Device Observer Identifying Attributes](part16.html#sect_TID_1004). - There is no requirement that the Device UID (0018,1002) be the same as the Instance Creator UID (0008,0014) in the SOP Common Module, though they may be. |
| Spatial Resolution | (0018,1050) | SpatialResolution | DS | 1 | str, float, int | 3 | The inherent limiting resolution in mm of the acquisition equipment for high contrast objects for the data gathering and reconstruction technique chosen. If variable across the images of the Series, the value at the image center. |
| Date of Manufacture | (0018,1204) | DateOfManufacture | DA | 1 | str | 3 | The date the equipment that produced the Composite Instances was originally manufactured or re-manufactured (as opposed to refurbished). |
| Date of Installation | (0018,1205) | DateOfInstallation | DA | 1 | str | 3 | The date the equipment that produced the Composite Instances was installed in its current location. The equipment may or may not have been used prior to installation in its current location. |
| Date of Last Calibration | (0018,1200) | DateOfLastCalibration | DA | 1-n | str | 3 | Date when the image acquisition device calibration was last changed in any way. Multiple entries may be used for additional calibrations at other times. See Section C.*******.1 for further explanation. |
| Time of Last Calibration | (0018,1201) | TimeOfLastCalibration | TM | 1-n | str | 3 | Time when the image acquisition device calibration was last changed in any way. Multiple entries may be used. See Section C.*******.1 for further explanation. |
| Pixel Padding Value | (0028,0120) | PixelPaddingValue | US or SS | 1 |  | 1C | Single pixel value or one limit (inclusive) of a range of pixel values used in an image to pad to rectangular format or to signal background that may be suppressed or that may be rendered "transparently" when superimposing images. See Section C.*******.2 for further explanation. Required if Pixel Padding Range Limit (0028,0121) is present and either Pixel Data (7FE0,0010) or Pixel Data Provider URL (0028,7FE0) is present. May be present otherwise only if Pixel Data (7FE0,0010) or Pixel Data Provider URL (0028,7FE0) is present. ### Note - The Value Representation of this Attribute is determined by the Value of Pixel Representation (0028,0103). - This Attribute is not used in Presentation State Instances; there is no means in a Presentation State to "override" any Pixel Padding Value (0028,0120) specified in the referenced images. - This Attribute does apply to RT Dose and Segmentation Instances, since they include Pixel Data. - This Attribute does not apply when Float Pixel Data (7FE0,0008) or Double Float Pixel Data (7FE0,0009) are used instead of Pixel Data (7FE0,0010); Float Pixel Padding Value (0028,0122) or Double Float Pixel Padding Value (0028,0123), respectively, are used instead, and defined at the Image, not the Equipment, level. - Only a single Value is allowed for this Attribute, so it only applies to images with Samples per Pixel (0028,0002) of 1, i.e., images with a Photometric Interpretation (0028,0004) of MONOCHROME1, MONOCHROME2 or PALETTE COLOR. See Section C.*******.2 for details. |
##### C.******* General Equipment Module Attribute Descriptions
### Note
The Attributes Manufacturer (0008,0070), Manufacturer's Model Name (0008,1090) and Device Serial Number (0018,1000) are intended to be a primary identification of the system that produces the data (e.g., modality or workstation application providing the content of the SOP Instance) and not the identification of the component that encodes the SOP Instance (e.g., a commonly used DICOM encoding toolkit).
###### C.*******.1 Date of Last Calibration, Time of Last Calibration
Date of Last Calibration (0018,1200) and Time of Last Calibration (0018,1201) are used to convey the date and time of calibration. The Attribute Date of Last Calibration (0018,1200) may be supported alone, however, Time of Last Calibration (0018,1201) has no meaning unless Attribute Date of Last Calibration (0018,1200) is also supported. The order for each Attribute shall be from the oldest date/time to the most recent date/time. When the Attributes are both supported they shall be provided as pairs.
###### C.*******.2 Pixel Padding Value and Pixel Padding Range Limit
Pixel Padding Value (0028,0120) is typically used to pad grayscale images (those with a Photometric Interpretation (0028,0004) of MONOCHROME1 or MONOCHROME2), or color images with a Photometric interpretation (0028,0004) of PALETTE COLOR, to rectangular format. The native format of some images is not rectangular. It is common for devices with this format to pad the images, to the rectangular format required by the DICOM Standard, with a specific pixel value that is not contained in the native image. Further, when resampling, such as after spatial registration, padding may need to be used to fill previously non-existent pixels.
Pixel Padding Value (0028,0120) and Pixel Padding Range Limit (0028,0121) are also used to identify pixels to be excluded from the rendering pipeline for other reasons, such as suppression of background air. Pixel Padding Range Limit (0028,0121) is defined in the Image Pixel Module.
### Note
- The "native image" is that which is being padded to the required rectangular format, e.g., the area within the circular reconstruction perimeter of a CT image, or the subset of the rectangular area that contains useful image information, i.e., which is not to be suppressed, or e.g., is that part of a pseudo-colored image that might be superimposed on top of another image. For other mechanisms, see [Section N.2.6 Advanced Blending Transformations in PS3.4](part04.html#sect_N.2.6).
- The Pixel Padding Value is explicitly described in order to prevent display applications from taking it into account when determining the dynamic range of an image, since the Pixel Padding Value will be outside the range between the minimum and maximum values of the pixels in the native image
- No pixels in the native image will have a value equal to Pixel Padding Value (0028,0120).
Pixel Padding Value (0028,0120) specifies either a single Value of this padding, or when combined with Pixel Padding Range Limit (0028,0121), a range of values (inclusive) that are padding.
The Values of Pixel Padding Value (0028,0120) and Pixel Padding Range Limit (0028,0121) shall be valid values within the constraints defined by Bits Allocated (0028,0100), Bits Stored (0028,0101), and High Bit (0028,0102).
The Pixel Padding Value shall correspond to a value in the original stored pixel data, before the Modality LUT Transformation or any other transformations are applied.
Pixel Padding Value (0028,0120) and Pixel Padding Range Limit (0028,0121) shall not be present when padding is performed but the pixel value used for padding does occur in the native image.
If Photometric Interpretation (0028,0004) is MONOCHROME2 or PALETTE COLOR, Pixel Padding Value (0028,0120) shall be less than (closer to or equal to the minimum possible pixel value) or equal to Pixel Padding Range Limit (0028,0121). If Photometric Interpretation (0028,0004) is MONOCHROME1, Pixel Padding Value (0028,0120) shall be greater than (closer to or equal to the maximum possible pixel value) or equal to Pixel Padding Range Limit (0028,0121).
### Note
- When the relationship between pixel value and X-Ray Intensity is unknown, it is recommended that the following Values be used to pad with black when the image is unsigned: 0 if Photometric Interpretation (0028,0004) is MONOCHROME2.
- 2 Bits Stored - 1 if Photometric Interpretation (0028,0004) is MONOCHROME1.
and when the image is signed:
- -2 Bits Stored-1 if Photometric Interpretation (0028,0004) is MONOCHROME2.
- 2 Bits Stored-1 - 1 if Photometric Interpretation (0028,0004) is MONOCHROME1.
- For projection radiography, when the relationship between pixel value and X-Ray Intensity is known (for example as defined by Pixel Intensity Relationship (0028,1040) and Pixel Intensity Relationship Sign (0028,1041)), it is recommended that a pixel value equivalent to, or rendered similarly to, air (least X-Ray absorbance) be used for padding. However, if such a value may occur in the native image, the Pixel Padding Value (0028,0120) itself should not be present. E.g., for an XRF image obtained with an image intensifier, if air is black then a padded perimeter, if any, should also appear black. Typically though, if unpadded, this area would be collimated with a circular collimator, in which case the pixels would appear natively as white (greatest X-Ray absorbance) and a circular shutter would be necessary to neutralize them as black. Whether collimated areas are detected and treated as padded, or neutralized with shutters is at the discretion of the application. See also the Display Shutter Module Section C.7.6.11.
- The conditional requirement for the Pixel Padding Range Limit (0028,0121) in the Image Pixel Module means that it shall not be present unless Pixel Padding Value (0028,0120) is also present.
- The range of values to be suppressed between Pixel Padding Value (0028,0120) and Pixel Padding Range Limit (0028,0121) is specified as being inclusive, that is the values themselves as well as all values between are suppressed.
- When Pixel Padding Range Limit (0028,0121) is present, but not supported by a rendering application, the constraint that Pixel Padding Value (0028,0120) is closest to the "blackest" value, which is typically the most frequently occurring background pixel, will most often result in an acceptable display, permitting "backward compatibility" in the majority of cases.
- For Segmentations with a Segmentation Type (0062,0001) of LABELMAP, Pixel Padding Value (0028,0120) may be used to indicate that a particular pixel value is to be treated as background. Only a single pixel value is so indicated, and Pixel Padding Range Limit (0028,0121) is not used; see Section A.51.4. Typically the Pixel Padding Value (0028,0120) will be 0, but it can be another value, or may not be specified at all.
When modifying equipment changes the Pixel Padding Value in the image, it shall change the Values of Pixel Padding Value (0028,0120) and Pixel Padding Range Limit (0028,0121), if present. If modifying equipment changes the Pixel Padding Values in the image to values present in the native image, the Attribute Pixel Padding Value (0028,0120) and Pixel Padding Range Limit (0028,0121) shall be removed.
### Note
- For example, if a CT image containing signed values from -1024 to 3191 and a Pixel Padding Value (0028,0120) of -2000 and a Rescale Intercept (0028,1052) of 0 is converted to an unsigned image with a Rescale Intercept (0028,1052) of -1024 by adding 1024 to all pixels and clipping all more negative pixels to 0, then the padding pixels will be indistinguishable from some of the modified native image pixels, and hence Pixel Padding Value (0028,0120) needs to be removed.
- If the modification involves lossy compression, which may result in changes to the pixel values, then the application of Pixel Padding Value (0028,0120) and Pixel Padding Range Limit (0028,0121) may result in a different appearance, and hence these Attributes may need different values also.
###### C.*******.3 Software Versions
Software Versions (0018,1020) is a multi-valued Attribute. For equipment that is composed of several components, it may be used to identify the name and version for each of those components. This may also include the identifier and version of libraries or configuration files that significantly affect the production of the SOP Instance.