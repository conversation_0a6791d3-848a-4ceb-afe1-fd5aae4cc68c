#### C.7.6.2 Image Plane Module
Table C.7-10 specifies the Attributes of the Image Plane Module, which define the transmitted pixel array of a two dimensional image plane in a three dimensional space.
### Note
In previous versions of the Standard (  [  ACR-NEMA 300-1985  ] ,  [  ACR-NEMA 300-1988  ]  ), image position and image orientation were specified relative to a specific equipment coordinate system. This equipment coordinate system was not fully defined and a number of ambiguities existed. The Equipment-Based Coordinate System has been retired and replaced by the Patient-Based Coordinate System defined in this Module.
**Table C.7-10. Image Plane Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Pixel Spacing | (0028,0030) | PixelSpacing | DS | 2 | str, float, int | 1 | Physical distance in the patient between the center of each pixel, specified by a numeric pair - adjacent row spacing (delimiter) adjacent column spacing in mm. See Section ******** for further explanation. |
| Image Orientation (Patient) | (0020,0037) | ImageOrientationPatient | DS | 6 | str, float, int | 1 | The direction cosines of the first row and the first column with respect to the patient. See Section C.*******.1 for further explanation. |
| Image Position (Patient) | (0020,0032) | ImagePositionPatient | DS | 3 | str, float, int | 1 | The x, y, and z coordinates of the upper left hand corner (center of the first voxel transmitted) of the image, in mm. See Section C.*******.1 for further explanation. |
| Slice Thickness | (0018,0050) | SliceThickness | DS | 1 | str, float, int | 2 | Nominal slice thickness, in mm. |
| Spacing Between Slices | (0018,0088) | SpacingBetweenSlices | DS | 1 | str, float, int | 3 | Spacing between adjacent slices, in mm. The spacing is measured from the center-to-center of each slice. If present, shall not be negative, unless specialized to define the meaning of the sign in a specialized IOD, e.g., as in the Section C.8.4.15. |
| Slice Location | (0020,1041) | SliceLocation | DS | 1 | str, float, int | 3 | Relative position of the image plane expressed in mm. See Section C.*******.2 for further explanation. |
##### C.******* Image Plane Module Attribute Descriptions
###### C.*******.1 Image Position and Image Orientation
Image Position (Patient) (0020,0032) specifies the x, y, and z coordinates of the upper left hand corner of the image; it is the center of the first voxel transmitted. Image Orientation (Patient) (0020,0037) specifies the direction cosines of the first row and the first column with respect to the patient. These Attributes shall be provide as a pair. Row value for the x, y, and z axes respectively followed by the Column value for the x, y, and z axes respectively.
The direction of the axes is defined fully by the patient's orientation.
If Anatomical Orientation Type (0010,2210) is absent or has a Value of BIPED, the x-axis is increasing to the left hand side of the patient. The y-axis is increasing to the posterior side of the patient. The z-axis is increasing toward the head of the patient.
If Anatomical Orientation Type (0010,2210) has a Value of QUADRUPED, the
- x-axis is increasing to the left (as opposed to right) side of the patient
- the y-axis is increasing towards the dorsal (as opposed to ventral) side of the patient for the neck, trunk and tail,
- the dorsal (as opposed to ventral) side of the patient for the head,
- the dorsal (as opposed to plantar or palmar) side of the distal limbs,
- the cranial (as opposed caudal) side of the proximal limbs, and
- the z-axis is increasing towards the cranial (as opposed to caudal) end of the patient for the neck, trunk and tail,
- the rostral (as opposed to caudal) end of the patient for the head, and
- the proximal (as opposed to distal) end of the limbs
### Note
- The axes for quadrupeds are those defined and illustrated in Smallwood et al for proper anatomic directional terms as they apply to various parts of the body.
- It should be anticipated that when quadrupeds are imaged on human equipment, and particularly when they are position in a manner different from the traditional human prone and supine head or feet first longitudinal position, then the equipment may well not indicate the correct orientation, though it will remain an orthogonal Cartesian right-handed system that could be corrected subsequently.
The Patient-Based Coordinate System is a right handed system, i.e., the vector cross product of a unit vector along the positive x-axis and a unit vector along the positive y-axis is equal to a unit vector along the positive z-axis.
### Note
If a patient is positioned parallel to the ground, in dorsal recumbency (i.e., for humans, face-up on the table), with the caudo-cranial (i.e., for humans, feet-to-head) direction the same as the front-to-back direction of the imaging equipment, the direction of the axes of this Patient-Based Coordinate System and the Equipment-Based Coordinate System in previous versions of this Standard will coincide.
The Image Plane Attributes, in conjunction with the Pixel Spacing Attribute, describe the position and orientation of the image slices relative to the Patient-Based Coordinate System. In each image Frame Image Position (Patient) (0020,0032) specifies the origin of the image with respect to the Patient-Based Coordinate System. RCS and Image Orientation (Patient) (0020,0037) Values specify the orientation of the image Frame rows and columns. The mapping of an integer (entire) pixel location (i,j) to the RCS is calculated as follows:
**Equation C.*******-1.**
Where:
- P xyz The coordinates of the voxel (i,j) in the Frame's image plane in units of mm.
- S xyz The three Values of Image Position (Patient) (0020,0032). It is the location in mm from the origin of the RCS.
- X xyz The Values from the row (X) direction cosine of Image Orientation (Patient) (0020,0037).
- Y xyz The Values from the column (Y) direction cosine of Image Orientation (Patient) (0020,0037).
- i Column integer index to the image plane. The first (entire) column is index zero.
- i Column pixel resolution of Pixel Spacing (0028,0030) in units of mm.
- j Row integer index to the image plane. The first (entire) row index is zero.
- j Row pixel resolution of Pixel Spacing (0028,0030) in units of mm.
### Note
The integer entire row and column indices (i,j) that are the input to this equation start from zero, which is a common mathematical convention. Many DICOM Attributes define such indices as starting from one, e.g., those affected by Bounding Box Annotation Units (0070,0003) for PIXEL and MATRIX in Section C.10.5 Graphic Annotation Module. This needs to be accounted for when applying this equation literally.
The mapping of a sub-pixel resolution image or total pixel matrix relative location (c,r), such as used in Spatial Coordinates Macro, to the RCS is calculated as follows
**Equation C.*******-2.**
Where:
- P xyz The coordinates of the voxel (c,r) in the Frame's image plane in units of mm.
- S xyz The three Values of Image Position (Patient) (0020,0032). It is the location in mm from the origin of the RCS.
- X xyz The Values from the row (X) direction cosine of Image Orientation (Patient) (0020,0037).
- Y xyz The Values from the column (Y) direction cosine of Image Orientation (Patient) (0020,0037).
- c Column sub-pixel resolution index to the image plane. The left pixel edge of the first column of the Frame or total pixel matrix is index zero.
- c Column pixel resolution of Pixel Spacing (0028,0030) in units of mm.
- r Row sub-pixel resolution index to the image plane. The top pixel edge of the first row of the Frame or total pixel matrix index is zero.
- r Row pixel resolution of Pixel Spacing (0028,0030) in units of mm.
Additional constraints apply:
- The row and column direction cosine vectors shall be orthogonal, i.e., their dot product shall be zero.
- The row and column direction cosine vectors shall be normal, i.e., the dot product of each direction cosine vector with itself shall be unity.
###### C.*******.2 Slice Location
Slice Location (0020,1041) is defined as the relative position of the image plane expressed in mm. This information is relative to an unspecified implementation specific reference point.