#### C.8.2.2 Multi-energy CT Image Module
Table C.8.2.2-1 specifies the Attributes of the Multi-energy CT Image Module, which describe a Multi-energy CT image.
**Table C.8.2.2-1. Multi-energy CT Image Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Multi-energy CT Acquisition Sequence | (0018,9362) | MultienergyCTAcquisitionSequence | SQ | 1 | list[Dataset] | 1 | The Attributes of a Multi-energy CT Image acquisition. One Item shall be included in this Sequence. |
| &gt;Multi-energy Acquisition Description | (0018,937B) | MultienergyAcquisitionDescription | UT | 1 | str | 3 | Human readable summary of the Multi-Energy technique applied during the acquisition. |
| &gt;Include Table C.8.2.2-2 Multi-energy CT X-Ray Source Macro Attributes |  |  |  |  |  |  |  |
| &gt;Include Table C.8.2.2-3 Multi-energy CT X-Ray Detector Macro Attributes |  |  |  |  |  |  |  |
| &gt;Include Table C.8.2.2-4 Multi-energy CT Path Macro Attributes |  |  |  |  |  |  |  |
| &gt;Include Table C.8-124 CT Exposure Macro Attributes |  |  |  |  |  |  |  |
| &gt;Include Table C.8-125 CT X-Ray Details Macro Attributes |  |  |  |  |  |  |  |
| &gt;Include Table C.8-119 CT Acquisition Details Macro Attributes |  |  |  |  |  |  |  |
| &gt;Include Table C.8-122 CT Geometry Macro Attributes |  |  |  |  |  |  |  |
| Include Table C.*********-1 Multi-energy CT Processing Macro Attributes |  |  |  |  |  |  |  |
| Include Table C.*********-1 Multi-energy CT Characteristics Macro Attributes |  |  |  |  |  |  |  |
##### C.******* Multi-energy CT X-Ray Source Macro
Table C.8.2.2-2 specifies the Attributes of the Multi-energy CT X-Ray Source Macro, which describe the CT X-Ray Source(s).
**Table C.8.2.2-2. Multi-energy CT X-Ray Source Macro Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Multi-energy CT X-Ray Source Sequence | (0018,9365) | MultienergyCTXRaySourceSequence | SQ | 1 | list[Dataset] | 1 | X-Ray Source (see Section C.*******.1 ) information. One or more Items shall be included in this Sequence. |
| &gt;X-Ray Source Index | (0018,9366) | XRaySourceIndex | US | 1 | int | 1 | Identification number of this Item in the Multi-energy CT X-Ray Source Sequence. The number shall be 1 for the first Item and increase by 1 for each subsequent Item. |
| &gt;X-Ray Source ID | (0018,9367) | XRaySourceID | UC | 1 | str | 1 | Identifier of the physical X-Ray source. This might be the serial number. The X-Ray Source ID (0018,9367) will have the same value for different values of X-Ray Source Index (0018,9366) if a single source generates different nominal energies. |
| &gt;Multi-energy Source Technique | (0018,9368) | MultienergySourceTechnique | CS | 1 | str | 1 | Technique used to acquire Multi-energy data. **Defined Terms:** SWITCHING_SOURCE a physical X-Ray source (tube) uses beam mode switching CONSTANT_SOURCE a physical X-Ray source (tube) uses a beam with constant characteristics |
| &gt;Source Start DateTime | (0018,9369) | SourceStartDateTime | DT | 1 | str | 1 | The date and time this X-Ray source (see Section C.*******.1 ) was first used in this Multi-energy acquisition. |
| &gt;Source End DateTime | (0018,936A) | SourceEndDateTime | DT | 1 | str | 1 | The date and time this X-Ray source (see Section C.*******.1 ) was last used in this Multi-energy acquisition. |
| &gt;Switching Phase Number | (0018,936B) | SwitchingPhaseNumber | US | 1 | int | 1C | A number, unique within the Sequence, to identify the switching phase. Required if Multi-energy Source Technique (0018,9368) is "SWITCHING_SOURCE". |
| &gt;Switching Phase Nominal Duration | (0018,936C) | SwitchingPhaseNominalDuration | DS | 1 | str, float, int | 3 | Duration, in microseconds, that the energy is nominally in the target KV for this switching phase. I.e., the Switching Phase Nominal Duration does not include the Switching Phase Transition Duration (0018,936D). The target KV is the Value of KVP (0018,0060) for the Item in the CT X-Ray Details Sequence (0018,9325) that identifies the Multi-energy CT Path Index (0018,937A) that corresponds to this X-Ray Source. ### Note Applicable if Multi-energy Source Technique (0018,9368) is "SWITCHING_SOURCE". |
| &gt;Switching Phase Transition Duration | (0018,936D) | SwitchingPhaseTransitionDuration | DS | 1 | str, float, int | 3 | Duration, in microseconds, that the energy has left the target KV for this switching phase, but has not yet reached the target KV for the next phase. The target KV is the Value of KVP (0018,0060) for the Item in the CT X-Ray Details Sequence (0018,9325) that identifies the Multi-energy CT Path Index (0018,937A) that corresponds to this X-Ray Source. ### Note Applicable if Multi-energy Source Technique (0018,9368) is "SWITCHING_SOURCE". |
| &gt;Generator Power | (0018,1170) | GeneratorPower | IS | 1 | str, int | 3 | Power in kW going into the X-Ray generator. |
###### C.*******.1 Multi-energy X-Ray Source Description
Each Item in the Multi-energy CT X-Ray Source Sequence (0018,9365) might describe either a constant source, a constant source with energy selective filter or one output corresponding to a specific energy of a KV switching source. The Attributes will refer to a source, meaning one Item in the Multi-energy CT X-Ray Source Sequence (0018,9365). The Attributes use the phrase "physical X-Ray Source" when it is necessary to refer to the actual device (tube) rather than the current Item.
##### C.******* Multi-energy CT X-Ray Detector Macro
Table C.8.2.2-3 specifies the Attributes of the Multi-energy CT X-Ray Detector Macro, which describe the CT X-Ray Detector(s).
**Table C.8.2.2-3. Multi-energy CT X-Ray Detector Macro Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Multi-energy CT X-Ray Detector Sequence | (0018,936F) | MultienergyCTXRayDetectorSequence | SQ | 1 | list[Dataset] | 1 | X-Ray Detector (see Section C.*******.1 ) information. One or more Items shall be included in this Sequence. |
| &gt;X-Ray Detector Index | (0018,9370) | XRayDetectorIndex | US | 1 | int | 1 | Identification number of this Item in the Multi-energy CT X-Ray Detector Sequence. The number shall be 1 for the first Item and increase by 1 for each subsequent Item. |
| &gt;X-Ray Detector ID | (0018,9371) | XRayDetectorID | UC | 1 | str | 1 | Identifier of the physical X-Ray detector. This might be the serial number. When a single detector discriminates different energies, the X-Ray Detector ID (0018,9371) will have the same value in different Items of Multi-energy CT X-Ray Detector Sequence (0018,936F). |
| &gt;Multi-energy Detector Type | (0018,9372) | MultienergyDetectorType | CS | 1 | str | 1 | Technology used to detect multiple energies. **Defined Terms:** INTEGRATING physical detector integrates the full X-Ray spectrum. MULTILAYER physical detector layers absorb different parts of the X-Ray spectrum PHOTON_COUNTING physical detector counts photons with energy discrimination capability |
| &gt;X-Ray Detector Label | (0018,9373) | XRayDetectorLabel | ST | 1 | str | 3 | Label of this Item in the Multi-energy CT X-Ray Detector Sequence. ### Note The label might be High, Low or some nominal bin energy. |
| &gt;Nominal Max Energy | (0018,9374) | NominalMaxEnergy | DS | 1 | str, float, int | 1C | Nominal maximum energy in keV of photons that are integrated/counted by the detector (see Section C.*******.1 ). Due to energy resolution limits of the detector, some photons above the nominal maximum may be counted. Required if Multi-energy Detector Type (0018,9372) is PHOTON_COUNTING. May be present otherwise. |
| &gt;Nominal Min Energy | (0018,9375) | NominalMinEnergy | DS | 1 | str, float, int | 1C | Nominal minimum energy in keV of photons that are integrated/counted by the detector (see Section C.*******.1 ). Due to energy resolution limits of the detector, some photons below the nominal minimum may be counted. Required if Multi-energy Detector Type (0018,9372) is PHOTON_COUNTING. May be present otherwise. |
| &gt;Effective Bin Energy | (0018,936E) | EffectiveBinEnergy | DS | 1 | str, float, int | 3 | Energy of the heterogeneous (polychromatic) photon beam represented by this detector (see Section C.*******.1 ) calculated as if it were monochromatic. ### Note E.g., this can be calculated based on the beam spectrum or derived from the attenuation of phantom measurement. |
###### C.*******.1 Multi-energy X-Ray Detector Sequence
Each Item in the Multi-energy CT X-Ray Detector Sequence (0018,936F) might describe either an integrating detector or one output corresponding to a specific spectrum of a physical detector like:
- One layer of a multi-layer detector.
- One energy bin of a photon counting detector.
- One energy threshold of a photon counting detector.
The Sequence Attribute descriptions will refer to a detector, meaning one Item in the Multi-energy CT X-Ray Detector Sequence (0018,936F). The Attributes use the phrase "physical detector" when it is necessary to refer to the actual device rather than the current Item.
##### C.******* Multi-energy CT Path Macro
Table C.8.2.2-4 specifies the Attributes of the Multi-energy CT Path Macro, which describe a Multi-energy CT Path. A Multi-energy CT Path is an X-Ray source paired with a corresponding X-Ray detector for a particular energy level.
**Table C.8.2.2-4. Multi-energy CT Path Macro Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Multi-energy CT Path Sequence | (0018,9379) | MultienergyCTPathSequence | SQ | 1 | list[Dataset] | 1 | Describes the paths corresponding to each energy level of a Multi-energy acquisition. Each path consists of a source (see Section C.*******.1 ) and a detector (see Section C.*******.1 ). Two or more Items shall be included in this Sequence. See also Section C.******* and Section C.*******. |
| &gt;Multi-energy CT Path Index | (0018,937A) | MultienergyCTPathIndex | US | 1 | int | 1 | Identification number of the element in the Multi-energy CT Path Sequence. The number shall be 1 for the first Item and increase by 1 for each subsequent Item. |
| &gt;Referenced X-Ray Source Index | (0018,9377) | ReferencedXRaySourceIndex | US | 1-n | int | 1 | References the X-Ray Source Index (0018,9366) in the Multi-energy CT X-Ray Source Sequence (0018,9365) in this path. |
| &gt;Referenced X-Ray Detector Index | (0018,9376) | ReferencedXRayDetectorIndex | US | 1-n | int | 1 | References the X-Ray Detector Index (0018,9370) in the Multi-energy CT X-Ray Detector Sequence (0018,936F) in this path. |