### C.12.1 SOP Common Module
Table C.12-1 specifies the Attributes of the SOP Common Module, which are required for proper functioning and identification of the associated SOP Instances. They do not specify any semantics about the Real-World Object represented by the IOD.
**Table C.12-1. SOP Common Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| SOP Class UID | (0008,0016) | SOPClassUID | UI | 1 | str | 1 | Uniquely identifies the SOP Class. See Section C.******** for further explanation. See also [PS3.4](part04.html#PS3.4). |
| SOP Instance UID | (0008,0018) | SOPInstanceUID | UI | 1 | str | 1 | Uniquely identifies the SOP Instance. See Section C.******** for further explanation. See also [PS3.4](part04.html#PS3.4). |
| Specific Character Set | (0008,0005) | SpecificCharacterSet | CS | 1-n | str | 1C | Character Set that expands or replaces the Basic Graphic Set. Required if an expanded or replacement character set is used. See Section C.******** for Defined Terms. |
| Instance Creation Date | (0008,0012) | InstanceCreationDate | DA | 1 | str | 3 | Date the SOP Instance was created. This is the date that the SOP Instance UID was assigned, and does not change during subsequent coercion of the Instance. |
| Instance Creation Time | (0008,0013) | InstanceCreationTime | TM | 1 | str | 3 | Time the SOP Instance was created. This is the time that the SOP Instance UID was assigned, and does not change during subsequent coercion of the Instance. |
| Instance Coercion DateTime | (0008,0015) | InstanceCoercionDateTime | DT | 1 | str | 3 | Date and time that the SOP Instance was last coerced, corrected or converted by a Storage SCP (see [Section B.4.1.3 Coercion of Attributes in PS3.4](part04.html#sect_B.4.1.3) ). |
| Instance Creator UID | (0008,0014) | InstanceCreatorUID | UI | 1 | str | 3 | Uniquely identifies device that created the SOP Instance. ### Note There is no requirement that the Instance Creator UID (0008,0014) be the same as the Device UID (0018,1002) in the General Equipment Module, though they may be. |
| Related General SOP Class UID | (0008,001A) | RelatedGeneralSOPClassUID | UI | 1-n | str | 3 | Uniquely identifies a Related General SOP Class for the SOP Class of this Instance. See [PS3.4](part04.html#PS3.4). |
| Original Specialized SOP Class UID | (0008,001B) | OriginalSpecializedSOPClassUID | UI | 1 | str | 3 | The SOP Class in which the Instance was originally encoded that has been replaced during a fall-back conversion to the current Related General SOP Class. See [PS3.4](part04.html#PS3.4). |
| Synthetic Data | (0008,001C) | SyntheticData | CS | 1 | str | 3 | Whether or not some or all of the content of this instance was made artificially rather than being a faithful representation of acquired data. ### Note - Since synthetic data may be intended for use in training or testing, the data may be otherwise indistinguishable from acquired patient data. For example, the Value of Manufacturer's Model Name (0008,1090) in the Equipment Module may reflect the model, whose output the instance is simulating, even though such a model did not create this instance. Similarly, the Value of KVP (0018,0060) may reflect the technique being simulated even though no x-rays were involved. - The equipment that synthesized the data may be recorded as additional Item(s) of the Contributing Equipment Sequence (0018,A001) in the SOP Common Module. The Purpose of Reference code value of [(109100, DCM, "Synthesizing Equipment")](part16.html#DCM_109100) can be used. - The use of this Attribute to indicate synthetic data is not restricted to images, since any type of SOP Instance may be created artificially **Enumerated Values:** YES NO If data with a Synthetic Data (0008,001C) Value of YES is used to derive other content then it is expected that this derived content will also have a Synthetic Data (0008,001C) Value of YES. |
| Coding Scheme Identification Sequence | (0008,0110) | CodingSchemeIdentificationSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Items that map Values of Coding Scheme Designator (0008,0102) to an external coding system registration, or to a private or local Coding Scheme. One or more Items are permitted in this Sequence. |
| &gt;Coding Scheme Designator | (0008,0102) | CodingSchemeDesignator | SH | 1 | str | 1 | The value of a Coding Scheme Designator, used in this SOP Instance, which is being mapped. |
| &gt;Coding Scheme Registry | (0008,0112) | CodingSchemeRegistry | LO | 1 | str | 1C | The name of the external registry where further definition of the identified Coding Scheme may be obtained. Required if Coding Scheme is registered. **Defined Terms:** HL7 |
| &gt;Coding Scheme UID | (0008,010C) | CodingSchemeUID | UI | 1 | str | 1C | The Coding Scheme UID identifier. Required if Coding Scheme is identified by an ISO 8824 object identifier compatible with the UI VR. |
| &gt;Coding Scheme External ID | (0008,0114) | CodingSchemeExternalID | ST | 1 | str | 2C | The Coding Scheme identifier as defined in an external registry. Required if Coding Scheme is registered and Coding Scheme UID (0008,010C) is not present. |
| &gt;Coding Scheme Name | (0008,0115) | CodingSchemeName | ST | 1 | str | 3 | The Coding Scheme full common name. |
| &gt;Coding Scheme Version | (0008,0103) | CodingSchemeVersion | SH | 1 | str | 3 | The Coding Scheme version associated with the Coding Scheme Designator (0008,0102). |
| &gt;Coding Scheme Responsible Organization | (0008,0116) | CodingSchemeResponsibleOrganization | ST | 1 | str | 3 | Name of the organization responsible for the Coding Scheme. May include organizational contact information. |
| &gt;Coding Scheme Resources Sequence | (0008,0109) | CodingSchemeResourcesSequence | SQ | 1 | list[Dataset] | 3 | Resources related to the Coding Scheme. One or more Items are permitted in this Sequence. |
| &gt;&gt;Coding Scheme URL Type | (0008,010A) | CodingSchemeURLType | CS | 1 | str | 1 | The type of the resource related to the Coding Scheme at the Coding Scheme URL (0008,010E). **Defined Terms:** DOC The resource is human-readable information describing the Coding Scheme. OWL The resource contains an OWL file that contains a representation of the Coding Scheme. CSV The resource contains a comma separated value text file that contains a representation of the Coding Scheme. FHIR The resource is a FHIR CodingScheme, e.g., as would be referred to in the Coding.system element of a FHIR Coding resource. |
| &gt;&gt;Coding Scheme URL | (0008,010E) | CodingSchemeURL | UR | 1 | str | 1 | A resource related to the Coding Scheme. |
| Context Group Identification Sequence | (0008,0123) | ContextGroupIdentificationSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Items that map Values of Context Identifier (0008,010F) to an external, private or local Context Group. One or more Items are permitted in this Sequence. |
| &gt;Context Identifier | (0008,010F) | ContextIdentifier | CS | 1 | str | 1 | The identifier of the Context Group. See Section 8.6. |
| &gt;Context UID | (0008,0117) | ContextUID | UI | 1 | str | 3 | The unique identifier of the Context Group. See Section 8.6. |
| &gt;Mapping Resource | (0008,0105) | MappingResource | CS | 1 | str | 1 | The identifier of the Mapping Resource that defines the Context Group. See Section 8.4. |
| &gt;Context Group Version | (0008,0106) | ContextGroupVersion | DT | 1 | str | 1 | The identifier of the version of the Context Group. See Section 8.5. |
| Mapping Resource Identification Sequence | (0008,0124) | MappingResourceIdentificationSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Items that map Values of Mapping Resource (0008,0105) to an external, private or local Mapping Resource. One or more Items are permitted in this Sequence. |
| &gt;Mapping Resource | (0008,0105) | MappingResource | CS | 1 | str | 1 | The identifier of the Mapping Resource. See Section 8.4. |
| &gt;Mapping Resource UID | (0008,0118) | MappingResourceUID | UI | 1 | str | 3 | The unique identifier of the Mapping Resource. |
| &gt;Mapping Resource Name | (0008,0122) | MappingResourceName | LO | 1 | str | 3 | The name of the Mapping Resource. See Section 8.4. |
| Timezone Offset From UTC | (0008,0201) | TimezoneOffsetFromUTC | SH | 1 | str | 3 | Contains the offset from UTC to the timezone for all DA and TM Attributes present in this SOP Instance, and for all DT Attributes present in this SOP Instance that do not contain an explicitly encoded timezone offset. See Section C.******** The local timezone offset is undefined if this Attribute is absent. |
| Contributing Equipment Sequence | (0018,A001) | ContributingEquipmentSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Items containing descriptive Attributes of related equipment that has contributed to the acquisition, creation or modification of the Composite Instance. One or more Items are permitted in this Sequence. See Section C.******** for further explanation. |
| &gt;Purpose of Reference Code Sequence | (0040,A170) | PurposeOfReferenceCodeSequence | SQ | 1 | list[Dataset] | 1 | Describes the purpose for which the related equipment is being referenced. Only a single Item shall be included in this Sequence. See Section C.******** for further explanation. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | D [CID 7005 Contributing Equipment Purpose of Reference](part16.html#sect_CID_7005). |
| &gt;Manufacturer | (0008,0070) | Manufacturer | LO | 1 | str | 1 | Manufacturer of the equipment that contributed to the Composite Instance. |
| &gt;Institution Name | (0008,0080) | InstitutionName | LO | 1 | str | 3 | Institution where the equipment that contributed to the Composite Instance is located. |
| &gt;Institution Address | (0008,0081) | InstitutionAddress | ST | 1 | str | 3 | Address of the institution where the equipment that contributed to the Composite Instance is located. |
| &gt;Station Name | (0008,1010) | StationName | SH | 1 | str | 3 | User defined name identifying the machine that contributed to the Composite Instance. |
| &gt;Institutional Department Name | (0008,1040) | InstitutionalDepartmentName | LO | 1 | str | 3 | Department in the institution where the equipment that contributed to the Composite Instance is located. |
| &gt;Institutional Department Type Code Sequence | (0008,1041) | InstitutionalDepartmentTypeCodeSequence | SQ | 1 | list[Dataset] | 3 | A coded description of the type of Department or Service within the healthcare facility. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 7030 Institutional Department/Unit/Service](part16.html#sect_CID_7030). |
| &gt;Operators' Name | (0008,1070) | OperatorsName | PN | 1-n | str, PersonName | 3 | Name(s) of the operator(s) of the contributing equipment. |
| &gt;Operator Identification Sequence | (0008,1072) | OperatorIdentificationSequence | SQ | 1 | list[Dataset] | 3 | Identification of the operator(s) of the contributing equipment. One or more Items are permitted in this Sequence. The number and order of Items shall correspond to the number and order of values of Operators' Name (0008,1070), if present. |
| &gt;&gt;Include Table 10-1 Person Identification Macro Attributes |  |  |  |  |  |  |  |
| &gt;Manufacturer's Model Name | (0008,1090) | ManufacturerModelName | LO | 1 | str | 3 | Manufacturer's model name of the equipment that contributed to the Composite Instance. |
| &gt;Device Serial Number | (0018,1000) | DeviceSerialNumber | LO | 1 | str | 3 | Manufacturer's serial number of the equipment that contributed to the Composite Instance. |
| &gt;Software Versions | (0018,1020) | SoftwareVersions | LO | 1-n | str | 3 | Manufacturer's designation of the software version of the equipment that contributed to the Composite Instance. See Section C.*******.3. |
| &gt;Date of Manufacture | (0018,1204) | DateOfManufacture | DA | 1 | str | 3 | The date the equipment that contributed to the Composite Instance was originally manufactured or re-manufactured (as opposed to refurbished). |
| &gt;Date of Installation | (0018,1205) | DateOfInstallation | DA | 1 | str | 3 | The date the equipment that contributed to the Composite Instance was installed in its current location. The equipment may or may not have been used prior to installation in its current location. |
| &gt;Device UID | (0018,1002) | DeviceUID | UI | 1 | str | 3 | Unique identifier of the contributing equipment. ### Note There is no requirement that this Device UID (0018,1002) be the same as the Instance Creator UID (0008,0014) in the SOP Common Module, though it may be. |
| &gt;UDI Sequence | (0018,100A) | UDISequence | SQ | 1 | list[Dataset] | 3 | Unique Device Identifier (UDI) of the contributing equipment. One or more Items are permitted in this Sequence. ### Note - This is the UDI that corresponds to the entire contributing equipment as described by the other Attributes in the Contributing Equipment Sequence Item. This is not intended to contain the UDIs of sub-components of the contributing equipment. - Multiple Items may be present if the contributing equipment has UDIs issued by different Issuing Authorities. |
| &gt;&gt;Include Table 10.29-1 UDI Macro Attributes |  |  |  |  |  |  |  |
| &gt;Spatial Resolution | (0018,1050) | SpatialResolution | DS | 1 | str, float, int | 3 | The inherent limiting resolution in mm of the acquisition equipment for high contrast objects for the data gathering and reconstruction technique chosen. If variable across the images of the Series, the value at the image center. |
| &gt;Date of Last Calibration | (0018,1200) | DateOfLastCalibration | DA | 1-n | str | 3 | Date when the image acquisition device calibration was last changed in any way. Multiple entries may be used for additional calibrations at other times. See Section C.*******.1 for further explanation. |
| &gt;Time of Last Calibration | (0018,1201) | TimeOfLastCalibration | TM | 1-n | str | 3 | Time when the image acquisition device calibration was last changed in any way. Multiple entries may be used. See Section C.*******.1 for further explanation. |
| &gt;Contribution DateTime | (0018,A002) | ContributionDateTime | DT | 1 | str | 3 | The Date &amp; Time when the equipment contributed to the Composite Instance. |
| &gt;Contribution Description | (0018,A003) | ContributionDescription | ST | 1 | str | 3 | Description of the contribution the equipment made to the Composite Instance. |
| Instance Number | (0020,0013) | InstanceNumber | IS | 1 | str, int | 3 | A number that identifies this Composite Instance. |
| SOP Instance Status | (0100,0410) | SOPInstanceStatus | CS | 1 | str | 3 | A flag that indicates the storage status of the SOP Instance. **Enumerated Values:** NS Not Specified; implies that this SOP Instance has no special storage status, and hence no special actions need be taken OR Original; implies that this is the primary SOP Instance for the purpose of storage, but that it has not yet been authorized for diagnostic use AO Authorized Original; implies that this is the primary SOP Instance for the purpose of storage, which has been authorized for diagnostic use AC Authorized Copy; implies that this is a copy of an Authorized Original SOP Instance; any copies of an Authorized Original should be given the status of Authorized Copy ### Note Proper use of these flags is specified in Security Profiles. Implementations that do not conform to such Security Profiles may not necessarily handle these flags properly. |
| SOP Authorization DateTime | (0100,0420) | SOPAuthorizationDateTime | DT | 1 | str | 3 | The date and time when the SOP Instance Status (0100,0410) was set to AO. |
| SOP Authorization Comment | (0100,0424) | SOPAuthorizationComment | LT | 1 | str | 3 | Any comments associated with the setting of the SOP Instance Status (0100,0410) to AO. |
| Authorization Equipment Certification Number | (0100,0426) | AuthorizationEquipmentCertificationNumber | LO | 1 | str | 3 | The certification number issued to the Application Entity that set the SOP Instance Status (0100,0410) to AO. |
| Include Table C.12-6 Digital Signatures Macro Attributes |  |  |  |  |  |  |  |
| Encrypted Attributes Sequence | (0400,0500) | EncryptedAttributesSequence | SQ | 1 | list[Dataset] | 1C | Sequence of Items containing encrypted DICOM data. One or more Items shall be included in this Sequence. Required if application level confidentiality is needed and certain recipients are allowed to decrypt all or portions of the Encrypted Attributes Data Set. See Section C.********.1. |
| &gt;Encrypted Content Transfer Syntax UID | (0400,0510) | EncryptedContentTransferSyntaxUID | UI | 1 | str | 1 | Transfer Syntax used to encode the encrypted content. Only Transfer Syntaxes that explicitly include the VR and use Little Endian encoding shall be used. |
| &gt;Encrypted Content | (0400,0520) | EncryptedContent | OB | 1 | bytes | 1 | Encrypted data. See Section C.********.2. |
| Include Table C.********-1 Original Attributes Macro Attributes |  |  |  |  |  |  |  |
| HL7 Structured Document Reference Sequence | (0040,A390) | HL7StructuredDocumentReferenceSequence | SQ | 1 | list[Dataset] | 1C | Sequence of Items defining mapping between HL7 Instance Identifiers of unencapsulated HL7 Structured Documents referenced from the current SOP Instance as if they were DICOM Composite SOP Instances defined by SOP Class and Instance UID pairs. May also define a means of accessing the Documents. One or more Items shall be included in this Sequence. See Section C.********. Required if unencapsulated HL7 Structured Documents are referenced within the Instance. Every such document so referenced is required to have a corresponding Item in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;HL7 Instance Identifier | (0040,E001) | HL7InstanceIdentifier | ST | 1 | str | 1 | Instance Identifier of the referenced HL7 Structured Document, encoded as a UID (OID or UUID), concatenated with a caret ("^") and Extension value (if Extension is present in Instance Identifier). |
| &gt;Retrieve URI | (0040,E010) | RetrieveURI | UR | 1 | str | 3 | Retrieval access path to HL7 Structured Document. Includes fully specified scheme, authority, path, and query in accordance with [ RFC3986 ] . ### Note The VR of this Data Element has changed from UT to UR. |
| Longitudinal Temporal Information Modified | (0028,0303) | LongitudinalTemporalInformationModified | CS | 1 | str | 3 | Indicates whether or not the date and time Attributes in the Instance have been modified during de-identification. **Enumerated Values:** UNMODIFIED MODIFIED REMOVED See Section E.2 Basic Application Level Confidentiality Profile in PS3.15 and Section E.3.6 Retain Longitudinal Temporal Information Options in PS3.15 . |
| Query/Retrieve View | (0008,0053) | QueryRetrieveView | CS | 1 | str | 1C | The view requested during the C-MOVE operation that resulted in the transfer of this Instance. **Enumerated Values:** CLASSIC ENHANCED Required if the Instance has ever been converted from its source form as the result of a C-MOVE operation with a specific view. |
| Conversion Source Attributes Sequence | (0020,9172) | ConversionSourceAttributesSequence | SQ | 1 | list[Dataset] | 1C | The set of images or other composite SOP Instances that were converted to this Instance. If this Instance was converted from a specific Frame in the source Instance, the reference shall include the Frame Number. One or more Items shall be included in this Sequence. Required if this Instance was created by conversion from a DICOM source, and Conversion Source Attributes Sequence (0020,9172) is not present in an Item of Shared Functional Groups Sequence (5200,9229) or Per-Frame Functional Groups Sequence (5200,9230). |
| &gt;Include Table 10-3 Image SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| Content Qualification | (0018,9004) | ContentQualification | CS | 1 | str | 3 | Content Qualification Indicator **Enumerated Values:** PRODUCT RESEARCH SERVICE See Section C.8.13.2.1.1 for further explanation. |
| Private Data Element Characteristics Sequence | (0008,0300) | PrivateDataElementCharacteristicsSequence | SQ | 1 | list[Dataset] | 3 | Characteristics of Private Data Elements within or referenced in the current SOP Instance. See Section C.********. One or more Items are permitted in this Sequence. |
| &gt;Private Group Reference | (0008,0301) | PrivateGroupReference | US | 1 | int | 1 | Odd group number within which the Private Data Element block is reserved. |
| &gt;Private Creator Reference | (0008,0302) | PrivateCreatorReference | LO | 1 | str | 1 | The value of the Private Creator Data Element value used to reserve the block of Private Data Elements whose characteristics are described in this Item. ### Note Private blocks are identified by their Private Creator Data Element value, rather than their numeric block number, since instances may be modified and numeric block numbers reassigned but the Private Creator Data Element value, which is required to be unique within a Group of Private Data Elements, will be preserved. |
| &gt;Private Data Element Definition Sequence | (0008,0310) | PrivateDataElementDefinitionSequence | SQ | 1 | list[Dataset] | 3 | Description of individual Private Data Elements. One or more Items are permitted in this Sequence. |
| &gt;&gt;Private Data Element | (0008,0308) | PrivateDataElement | US | 1 | int | 1 | Element Number used to identify the Data Element within the reserved block. The Value of this Attribute represents the last two digits of the Data Element Tag; i.e., the Value of xx in (gggg,00xx) where gggg is the Private Group Reference (0008,0301). |
| &gt;&gt;Private Data Element Value Multiplicity | (0008,0309) | PrivateDataElementValueMultiplicity | UL | 1-3 | int | 1 | Value Multiplicity (VM) of the Data Element. See Section C.********.1. |
| &gt;&gt;Private Data Element Value Representation | (0008,030A) | PrivateDataElementValueRepresentation | CS | 1 | str | 1 | Value Representation (VR) of the Data Element. |
| &gt;&gt;Private Data Element Number of Items | (0008,030B) | PrivateDataElementNumberOfItems | UL | 1-2 | int | 1C | Number of Items allowed in a Sequence Data Element. Required if the Value of Private Data Element Value Representation (0008,030A) is SQ. See Section C.********.2. |
| &gt;&gt;Private Data Element Keyword | (0008,030D) | PrivateDataElementKeyword | UC | 1 | str | 1 | Keyword for the Data Element (in the sense of the keywords provided in [PS3.6](part06.html#PS3.6) ). |
| &gt;&gt;Private Data Element Name | (0008,030C) | PrivateDataElementName | UC | 1 | str | 1 | Name for referring to the Data Element. |
| &gt;&gt;Private Data Element Description | (0008,030E) | PrivateDataElementDescription | UT | 1 | str | 3 | Description of the purpose and/or proper usage of the Data Element. |
| &gt;&gt;Private Data Element Encoding | (0008,030F) | PrivateDataElementEncoding | UT | 1 | str | 3 | Description of how the Data Element value contents are encoded. |
| &gt;&gt;Retrieve URI | (0040,E010) | RetrieveURI | UR | 1 | str | 3 | Retrieval access path to associated documentation. Includes fully specified scheme, authority, path, and query in accordance with [ RFC3986 ] . |
| &gt;Block Identifying Information Status | (0008,0303) | BlockIdentifyingInformationStatus | CS | 1 | str | 1 | Specifies whether some or all of the Private Data Elements in the block are safe from identity leakage as defined by [PS3.15 Section E.3.10 Retain Safe Private Option](part15.html#sect_E.3.10). **Enumerated Values:** SAFE no Data Elements within the block contain identifying information UNSAFE all Data Elements within the block may contain identifying information MIXED some Data Elements within the block may contain identifying information |
| &gt;Nonidentifying Private Elements | (0008,0304) | NonidentifyingPrivateElements | US | 1-n | int | 1C | List of Private Data Elements in block that do not contain identifying information (are safe from identity leakage). Elements are identified by the lowest 8-bits of the Date Element Tag (i.e., with a value from 0000H to 00FFH) within the block, stored as an unsigned short integer. Multiple values shall be in increasing order and a given Value shall be listed at most once. Required if Block Identifying Information Status (0008,0303) equals MIXED. |
| &gt;Deidentification Action Sequence | (0008,0305) | DeidentificationActionSequence | SQ | 1 | list[Dataset] | 3 | Actions to be performed on element within the block that are not safe from identify leakage. One or more Items are permitted in this Sequence. |
| &gt;&gt;Identifying Private Elements | (0008,0306) | IdentifyingPrivateElements | US | 1-n | int | 1 | List of Private Data Elements in block that may contain identifying information (are unsafe from identity leakage). Elements are identified by the lowest 8-bits of the Data Element Tag (i.e., with a value from 0000H to 00FFH) within the block, stored as an unsigned short integer. Multiple values shall be in increasing order and a given Value shall be listed at most once. |
| &gt;&gt;Deidentification Action | (0008,0307) | DeidentificationAction | CS | 1 | str | 1 | Recommended action to be performed during de-identification on elements listed in Identifying Private Elements (0008,0306) within this Item. ### Note A specific type of action is suggested in order to minimize the impact of de-identification on the behavior of recipients that use the Private Data Elements. **Enumerated Values:** D replace with a non-zero length value that may be a dummy value and consistent with the VR Z replace with a zero length value, or a non-zero length value that may be a dummy value and consistent with the VR X remove U replace with a non-zero length UID that is internally consistent within a set of Instance ### Note - No C (clean) action is specified, since replacement with values of similar meaning known not to contain identifying information and consistent with the VR requires an understanding of the meaning of the value of the element. Whether or not to clean rather than remove or replace values is at the discretion of the implementer. - No suggested dummy value is provided, since the encoding of the value would depend on the VR of the Data Element. - Further explanation of these actions can be found in [PS3.15 Section E.3.1 Clean Pixel Data Option](part15.html#sect_E.3.1). |
| Instance Origin Status | (0400,0600) | InstanceOriginStatus | CS | 1 | str | 3 | Categorizes the locality of the entity from whence the Instance originated. See Section C.********0. |
| Barcode Value | (2200,0005) | BarcodeValue | LT | 1 | str | 3 | Barcode interpreted from a scanned label. ### Note - In the case of a scanned patient label, this may be the same as Patient ID (0010,0020), but it is included in an Instance level Module rather than a Patient level Module since barcodes may also be used to identify lower level entities. This might be obtained by scanning the patient's wrist band, request form, or extracting a burned-in label from the image pixel data, for example. - In the case of a scanned slide label, this may be the same as Container Identifier (0040,0512) in the Specimen Module. |
| Include Table 10.41-1 General Procedure Protocol Reference Macro Attributes |  |  |  |  |  |  |  |
#### C.12.1.1 SOP Common Module Attribute Descriptions
##### C.******** SOP Class UID, SOP Instance UID
The SOP Class UID and SOP Instance UID Attributes are defined for all DICOM IODs. However, they are only encoded in Composite IODs with the Type equal to 1. See Section C.1.2.3. When encoded they shall be equal to their respective Attributes in the DIMSE Services and the File Meta Information header (see [PS3.10 Media Storage](part10.html#PS3.10) ).
##### C.******** Specific Character Set
Specific Character Set (0008,0005) identifies the Character Set that expands or replaces the Basic Graphic Set (ISO 646) for values of Data Elements that have Value Representation of SH, LO, ST, PN, LT, UC or UT. See [PS3.5](part05.html#PS3.5).
If the Attribute Specific Character Set (0008,0005) is not present or has only a single Value, Code Extension techniques are not used. Defined Terms for the Attribute Specific Character Set (0008,0005), when single Valued, are derived from the International Registration Number as per ISO 2375 (e.g., ISO_IR 100 for Latin alphabet No. 1). See Table C.12-2.
### Note
- The Specific Character Set value does not indicate the character set version in use at the time of SOP Instance creation. Updates to character sets designated by a Specific Character Set value are expected to be backward compatible.
- This Standard does not specify the language associated with a specific character set. Language and character set selection are defined by local and regulatory requirements.
**Table C.12-2. Defined Terms for Single-Byte Character Sets Without Code Extensions**
| Character Set Description | Defined Term |  |  |  |  | ISO Registration Number | Number of Characters | Code Element | Character Set |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Default repertoire | none |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 1 | ISO_IR 100 |  |  |  |  | ISO-IR 100 | 96 | G1 | [ ISO IR 100 ] [ ISO/IEC 8859-1 ] |
| Latin alphabet No. 1 | ISO_IR 100 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 2 | ISO_IR 101 |  |  |  |  | ISO-IR 101 | 96 | G1 | [ ISO IR 101 ] [ ISO/IEC 8859-2 ] |
| Latin alphabet No. 2 | ISO_IR 101 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 3 | ISO_IR 109 |  |  |  |  | ISO-IR 109 | 96 | G1 | [ ISO IR 109 ] [ ISO/IEC 8859-3 ] |
| Latin alphabet No. 3 | ISO_IR 109 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 4 | ISO_IR 110 |  |  |  |  | ISO-IR 110 | 96 | G1 | [ ISO IR 110 ] [ ISO/IEC 8859-4 ] |
| Latin alphabet No. 4 | ISO_IR 110 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Cyrillic | ISO_IR 144 |  |  |  |  | ISO-IR 144 | 96 | G1 | [ ISO IR 144 ] [ ISO/IEC 8859-5 ] |
| Cyrillic | ISO_IR 144 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Arabic | ISO_IR 127 |  |  |  |  | ISO-IR 127 | 96 | G1 | [ ISO IR 127 ] [ ISO/IEC 8859-6 ] |
| Arabic | ISO_IR 127 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Greek | ISO_IR 126 |  |  |  |  | ISO-IR 126 | 96 | G1 | [ ISO IR 126 ] [ ISO/IEC 8859-7 ] |
| Greek | ISO_IR 126 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Hebrew | ISO_IR 138 |  |  |  |  | ISO-IR 138 | 96 | G1 | [ ISO IR 138 ] [ ISO/IEC 8859-8 ] |
| Hebrew | ISO_IR 138 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 5 | ISO_IR 148 |  |  |  |  | ISO-IR 148 | 96 | G1 | [ ISO IR 148 ] [ ISO/IEC 8859-9 ] |
| Latin alphabet No. 5 | ISO_IR 148 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 9 | ISO_IR 203 |  |  |  |  | ISO-IR 203 | 96 | G1 | [ ISO IR 203 ] [ ISO/IEC 8859-15 ] |
| Latin alphabet No. 9 | ISO_IR 203 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Japanese | ISO_IR 13 |  |  |  |  | ISO-IR 13 | 94 | G1 | [ ISO IR 13 ] [ JIS X 0201 ] : Katakana |
| Japanese | ISO_IR 13 |  |  |  |  | ISO-IR 14 | 94 | G0 | [ ISO IR 14 ] [ JIS X 0201 ] : Romaji |
| Thai | ISO_IR 166 |  |  |  |  | ISO-IR 166 | 88 | G1 | [ ISO IR 166 ] [ TIS 620-2533 ] |
| Thai | ISO_IR 166 |  |  |  |  | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
### Note
To use the single-byte code table of JIS X0201, the Value of Attribute Specific Character Set (0008,0005), Value 1 should be ISO_IR 13. This means that ISO-IR 13 is designated as the G1 code element, which is invoked in the GR area. It should be understood that, in addition, ISO-IR 14 is designated as the G0 code element and this is invoked in the GL area.
If the Attribute Specific Character Set (0008,0005) has more than one value, Code Extension techniques are used and Escape Sequences may be encountered in all character sets. Requirements for the use of Code Extension techniques are specified in [PS3.5](part05.html#PS3.5). In order to indicate the presence of Code Extension, the Defined Terms for the repertoires have the prefix "ISO 2022", e.g., ISO 2022 IR 100 for the Latin Alphabet No. 1. See Table C.12-3 and Table C.12-4. Table C.12-3 describes single-byte character sets for Value 1 to value n of the Attribute Specific Character Set (0008,0005), and Table C.12-4 describes multi-byte character sets for Value 2 to value n of the Attribute Specific Character Set (0008,0005).
### Note
A prefix other than "ISO 2022" may be needed in the future if other Code Extension techniques are adopted.
The same character set shall not be used more than once in Specific Character Set (0008,0005).
### Note
For example, the values "ISO 2022 IR 100\ISO 2022 IR 100" or "ISO_IR 100\ISO 2022 IR 100" are redundant and not permitted.
**Table C.12-3. Defined Terms for Single-Byte Character Sets with Code Extensions**
| Character Set Description | Defined Term |  |  |  |  | Standard for Code Extension | ESC Sequence | ISO Registration Number | Number of Characters | Code Element | Character Set |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Default repertoire | ISO 2022 IR 6 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 1 | ISO 2022 IR 100 |  |  |  |  | ISO 2022 | ESC 02/13 04/01 | ISO-IR 100 | 96 | G1 | [ ISO IR 100 ] [ ISO/IEC 8859-1 ] |
| Latin alphabet No. 1 | ISO 2022 IR 100 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 2 | ISO 2022 IR 101 |  |  |  |  | ISO 2022 | ESC 02/13 04/02 | ISO-IR 101 | 96 | G1 | [ ISO IR 101 ] [ ISO/IEC 8859-2 ] |
| Latin alphabet No. 2 | ISO 2022 IR 101 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 3 | ISO 2022 IR 109 |  |  |  |  | ISO 2022 | ESC 02/13 04/03 | ISO-IR 109 | 96 | G1 | [ ISO IR 109 ] [ ISO/IEC 8859-3 ] |
| Latin alphabet No. 3 | ISO 2022 IR 109 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 4 | ISO 2022 IR 110 |  |  |  |  | ISO 2022 | ESC 02/13 04/04 | ISO-IR 110 | 96 | G1 | [ ISO IR 110 ] [ ISO/IEC 8859-4 ] |
| Latin alphabet No. 4 | ISO 2022 IR 110 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Cyrillic | ISO 2022 IR 144 |  |  |  |  | ISO 2022 | ESC 02/13 04/12 | ISO-IR 144 | 96 | G1 | [ ISO IR 144 ] [ ISO/IEC 8859-5 ] |
| Cyrillic | ISO 2022 IR 144 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Arabic | ISO 2022 IR 127 |  |  |  |  | ISO 2022 | ESC 02/13 04/07 | ISO-IR 127 | 96 | G1 | [ ISO IR 127 ] [ ISO/IEC 8859-6 ] |
| Arabic | ISO 2022 IR 127 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Greek | ISO 2022 IR 126 |  |  |  |  | ISO 2022 | ESC 02/13 04/06 | ISO-IR 126 | 96 | G1 | [ ISO IR 126 ] [ ISO/IEC 8859-7 ] |
| Greek | ISO 2022 IR 126 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Hebrew | ISO 2022 IR 138 |  |  |  |  | ISO 2022 | ESC 02/13 04/08 | ISO-IR 138 | 96 | G1 | [ ISO IR 138 ] [ ISO/IEC 8859-8 ] |
| Hebrew | ISO 2022 IR 138 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 5 | ISO 2022 IR 148 |  |  |  |  | ISO 2022 | ESC 02/13 04/13 | ISO-IR 148 | 96 | G1 | [ ISO IR 148 ] [ ISO/IEC 8859-9 ] |
| Latin alphabet No. 5 | ISO 2022 IR 148 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Latin alphabet No. 9 | ISO 2022 IR 203 |  |  |  |  | ISO 2022 | ESC 02/13 06/02 | ISO-IR 203 | 96 | G1 | [ ISO IR 203 ] [ ISO/IEC 8859-15 ] |
| Latin alphabet No. 9 | ISO 2022 IR 203 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
| Japanese | ISO 2022 IR 13 |  |  |  |  | ISO 2022 | ESC 02/09 04/09 | ISO-IR 13 | 94 | G1 | [ ISO IR 13 ] [ JIS X 0201 ] : Katakana |
| Japanese | ISO 2022 IR 13 |  |  |  |  | ISO 2022 | ESC 02/08 04/10 | ISO-IR 14 | 94 | G0 | [ ISO IR 14 ] [ JIS X 0201 ] : Romaji |
| Thai | ISO 2022 IR 166 |  |  |  |  | ISO 2022 | ESC 02/13 05/04 | ISO-IR 166 | 88 | G1 | [ ISO IR 166 ] [ TIS 620-2533 ] |
| Thai | ISO 2022 IR 166 |  |  |  |  | ISO 2022 | ESC 02/08 04/02 | ISO-IR 6 | 94 | G0 | [ ISO 646 ] |
### Note
If the Attribute Specific Character Set (0008,0005) has more than one value and Value 1 is empty, it is assumed that Value 1 is ISO 2022 IR 6.
**Table C.12-4. Defined Terms for Multi-Byte Character Sets with Code Extensions**
| Character Set Description | Defined Term |  |  |  |  | Standard for Code Extension | ESC Sequence | ISO Registration Number | Number of Characters | Code Element | Character Set |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Japanese | ISO 2022 IR 87 |  |  |  |  | ISO 2022 | ESC 02/04 04/02 | ISO-IR 87 | 942 | G0 | [ JIS X 0208 ] : Kanji |
| Japanese | ISO 2022 IR 159 |  |  |  |  | ISO 2022 | ESC 02/04 02/08 04/04 | ISO-IR 159 | 942 | G0 | [ JIS X 0212 ] : Supplementary Kanji set |
| Korean | ISO 2022 IR 149 |  |  |  |  | ISO 2022 | ESC 02/04 02/09 04/03 | ISO-IR 149 | 942 | G1 | [ KS X 1001 ] : Hangul and Hanja |
| Simplified Chinese | ISO 2022 IR 58 |  |  |  |  | ISO 2022 | ESC 02/04 02/09 04/01 | ISO-IR 58 | 6,763 | G1 | [ GB 2312 ] |
There are multi-byte character sets that prohibit the use of Code Extension Techniques. The following multi-byte character sets prohibit the use of Code Extension Techniques:
- The Unicode character set used in [ ISO/IEC 10646 ] , when encoded in UTF
- The [ GB 18030 ] character set, when encoded per the rules of [ GB 18030 ]
- The [ GBK ] character set encoded per the rules of [ GBK ]
These character sets may only be specified as Value 1 in the Specific Character Set (0008,0005) and there shall only be one value. The minimal length UTF-8 encoding shall always be used for  [  ISO/IEC 10646  ] .
### Note
- [ ISO/IEC 10646 ] now prohibits the use of anything but the minimum length encoding for UTF-8. UTF-8 permits multiple different encodings, but when used to encode Unicode characters in accordance with ISO 10646-1 and 10646-2 (with extensions) only the minimal encodings are legal.
- The representation for the characters in the DICOM Default Character Repertoire is the same single byte value for the Default Character Repertoire, [ ISO/IEC 10646 ] in UTF-8, [ GB 18030 ] and [ GBK ] . It is also the 7-bit US-ASCII encoding.
- The [ GBK ] character set is a subset of the [ GB 18030 ] character set, which is restricted in its one- and two-byte code points. In this subset, the [ GBK ] character set follows the exactly same encoding rules of [ GB 18030 ] .
**Table C.12-5. Defined Terms for Multi-Byte Character Sets Without Code Extensions**
| Character Set Description | Defined Term |  |  |  |  | Character Set |
| --- | --- | --- | --- | --- | --- | --- |
| Unicode in UTF-8 | ISO_IR 192 |  |  |  |  | [ ISO IR 192 ] |
| GB18030 | GB18030 |  |  |  |  | [ GB 18030 ] |
| GBK | GBK |  |  |  |  | [ GBK ] |
##### C.******** Digital Signatures Macro
This Macro allows Digital Signatures to be included in a DICOM Data Set for the purpose of insuring the integrity of the Data Set, and to authenticate the sources of the Data Set. Table C.12-6 defines the Attributes needed to embed a Digital Signature in a Data Set. This Macro may appear in individual Sequence Items as well as in the top level Data Set of the SOP Instance.
### Note
- Each Item of a Sequence of Items is a Data Set. Thus, individual Sequence Items may incorporate their own Digital Signatures in addition to any Digital Signatures added to the Data Set in which the Sequence appears.
- The inclusion of this Macro in Sequence Items, other than as specified in this Part of the Standard, may be specified in an application-defined Standard Extended SOP Class or Private SOP Class (see [PS3.2](part02.html#PS3.2) ).
**Table C.12-6. Digital Signatures Macro Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| MAC Parameters Sequence | (4FFE,0001) | MACParametersSequence | SQ | 1 | list[Dataset] | 3 | A Sequence of Items that describe the parameters used to calculate a MAC for use in Digital Signatures. One or more Items shall be included in this Sequence. |
| &gt;MAC ID Number | (0400,0005) | MACIDNumber | US | 1 | int | 1 | A number, unique within this SOP Instance, used to identify this MAC Parameters Sequence (4FFE,0001) Item from an Item of the Digital Signatures Sequence (FFFA,FFFA). |
| &gt;MAC Calculation Transfer Syntax UID | (0400,0010) | MACCalculationTransferSyntaxUID | UI | 1 | str | 1 | The Transfer Syntax UID used to encode the values of the Data Elements included in the MAC calculation. Only Transfer Syntaxes that explicitly include the VR and use Little Endian encoding shall be used. ### Note Certain Transfer Syntaxes, particularly those that are used with compressed data, allow the fragmentation of the pixel data to change. If such fragmentation changes, Digital Signatures generated with such Transfer Syntaxes could become invalid. |
| &gt;MAC Algorithm | (0400,0015) | MACAlgorithm | CS | 1 | str | 1 | The algorithm used in generating the MAC to be encrypted to form the Digital Signature. For Defined Terms, see Table C.********.1.2-1, Defined Terms for MAC Algorithm (0400,0015). |
| &gt;Data Elements Signed | (0400,0020) | DataElementsSigned | AT | 1-n | int, tuple | 1 | A list of Data Element Tags in the order they appear in the Data Set that identify the Data Elements used in creating the MAC for the Digital Signature. See Section C.********.1.1. |
| Digital Signatures Sequence | (FFFA,FFFA) | DigitalSignaturesSequence | SQ | 1 | list[Dataset] | 3 | Sequence holding Digital Signatures. One or more Items are permitted in this Sequence. |
| &gt;MAC ID Number | (0400,0005) | MACIDNumber | US | 1 | int | 1 | A number used to identify which MAC Parameters Sequence Item was used in the calculation of this Digital Signature. |
| &gt;Digital Signature UID | (0400,0100) | DigitalSignatureUID | UI | 1 | str | 1 | A UID that can be used to uniquely reference this signature. |
| &gt;Digital Signature DateTime | (0400,0105) | DigitalSignatureDateTime | DT | 1 | str | 1 | The date and time the Digital Signature was created. The time shall include an offset (i.e., time zone indication) from Coordinated Universal Time. ### Note This is not a certified timestamp, and hence is not completely verifiable. An application can compare this date and time with those of other signatures and the validity date of the certificate to gain confidence in the veracity of this date and time. |
| &gt;Certificate Type | (0400,0110) | CertificateType | CS | 1 | str | 1 | The type of certificate used in (0400,0115). **Defined Terms:** X509_1993_SIG ### Note Digital Signature Security Profiles (see [PS3.15](part15.html#PS3.15) ) may require the use of a restricted subset of these terms. |
| &gt;Certificate of Signer | (0400,0115) | CertificateOfSigner | OB | 1 | bytes | 1 | A certificate that holds the identity of the entity producing this Digital Signature, that entity's public key or key identifier, and the algorithm and associated parameters with which that public key is to be used. Algorithms allowed are specified in Digital Signature Security Profiles (see [PS3.15](part15.html#PS3.15) ). ### Note - As technology advances, additional encryption algorithms may be allowed in future releases. Implementations should take this possibility into account. - When symmetric encryption is used, the certificate merely identifies which key was used by which entity, but not the actual key itself. Some other means (e.g., a trusted third party) must be used to obtain the key. |
| &gt;Signature | (0400,0120) | Signature | OB | 1 | bytes | 1 | The MAC generated as described in Section C.********.1.1 and encrypted using the algorithm, parameters, and private key associated with the Certificate of the Signer (0400,0115). See Section C.********.1.2. |
| &gt;Certified Timestamp Type | (0400,0305) | CertifiedTimestampType | CS | 1 | str | 1C | The type of certified timestamp used in Certified Timestamp (0400,0310). Required if Certified Timestamp (0400,0310) is present. **Defined Terms:** CMS_TSP Internet X.509 Public Key Infrastructure Time Stamp Protocol ### Note Digital Signature Security Profiles (see [PS3.15](part15.html#PS3.15) ) may require the use of a restricted subset of these terms. |
| &gt;Certified Timestamp | (0400,0310) | CertifiedTimestamp | OB | 1 | bytes | 3 | A certified timestamp of the Digital Signature (0400,0120) Value, which shall be obtained when the Digital Signature is created. See Section C.********.1.3. |
| &gt;Digital Signature Purpose Code Sequence | (0400,0401) | DigitalSignaturePurposeCodeSequence | SQ | 1 | list[Dataset] | 3 | The purpose of this Digital Signature. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 7007 Signature Purpose](part16.html#sect_CID_7007). |
###### C.********.1 Digital Signatures Macro Attribute Descriptions
###### C.********.1.1 Data Elements Signed
The Data Elements Signed Attribute shall list the Tags of the Data Elements that are included in the MAC calculation. The Tags listed shall reference Data Elements at the same level as the Mac Parameters Sequence (4FFE,0001) Data Element in which the Data Elements Signed Attribute appears. Tags included in Data Elements Signed shall be listed in the order in which they appear within the Data Set.
The following Data Elements shall not be included either implicitly or explicitly in the list of Tags in Data Elements Signed, nor included as part of the MAC calculation:
- The Length to End (0008,0001) or any Tag with an element number of 0000 (i.e., no Data Set or group lengths may be included in MAC calculations)
- Tags with a group number less than 0008
- Tags associated with Data Elements whose VR is UN
- Tags of Data Elements whose VR is SQ, where any Data Element within that Sequence of Items has a VR of UN recursively
- Tags with a group number of FFFA (e.g., the Digital Signatures Sequence)
- MAC Parameters Sequence (4FFE,0001)
- Data Set Trailing Padding (FFFC,FFFC)
- Item Delimitation Item (FFFE,E00D)
### Note
- The Length to End and group lengths can change if non-signed Data Elements change, so it is not appropriate to include them in the MAC calculation.
- Since the Data Element Tags that identify a Sequence and the start of each Item are included in the MAC calculation, there is no need to include the Item Delimitation Item Tags.
If any of the Data Element Tags in the list refer to a Sequence of Items, then the Tags of all Data Elements within all Items of that Sequence shall be implicitly included in the list of Data Elements Signed, except those disallowed above. This implicit list shall also include the Item Tag (FFFE,E000) Data Elements that separate the Sequence Items and the Sequence Delimitation Item (FFFE,E0DD).
### Note
It is possible to sign individual Items within a Sequence by including the Digital Signatures Macro in that Sequence Item. In fact, this is a highly desirable feature, particular when used in the context of reports. The Digital Signatures Macro is applied at the Data Set level, and Sequences of Items are merely Data Sets embedded within a larger Data Set. Essentially, the Digital Signatures Macro may be applied recursively.
An example of nesting Digital Signatures within Data Elements is illustrated in Figure C.12-1.
**Figure C.12-1. Example of Nesting Digital Signatures (Informative)**
In this example, there is main signature covering the pixel data and a few other Data Elements, plus two individually signed Items within a Sequence.
For Data Elements with a VR OB (e. g. pixel data) that have an undefined length (i.e., the data is encapsulated as described in [PS3.5](part05.html#PS3.5) ), the Item Data Element Tags that separate the fragments shall implicitly be included in the list of Data Elements Signed (i.e., a Data Element with a VR of OB is encoded in the same fashion as a Sequence of Items).
###### C.********.1.2 Signature
To generate the MAC, Data Elements referenced either explicitly or implicitly by the Tags in the Data Elements Signed (0400,0020) list shall be encoded using the Transfer Syntax identified by the MAC Calculation Transfer Syntax UID (0400,0010) of the MAC Parameters Sequence (0400,0010) Item where the Data Elements Signed (0400,0020) appears. Data shall be formed into a byte stream and presented to the algorithm specified by MAC Algorithm (0400,0015) for computation of the MAC according to the following rules:
For all Data Elements except those with a VR of SQ or with a VR of OB with an undefined length, all Data Element fields, including the Tag, the VR, the reserved field (if any), the Value Length, and the Value, shall be placed into the byte stream in the order encountered.
For Data Elements with a VR of SQ or with a VR of OB with an undefined length, the Tag, the VR, and the reserved field are placed into the byte stream. The Value Length shall not be included. This is followed by each Item Tag in the order encountered, without including the Value Length, followed by the contents of the Value for that Item. In the case of an Item within a Data Element whose VR is SQ, these rules are applied recursively to all of the Data Elements within the Value of that Item. After all the Items have been incorporate into the byte stream, a Sequence Delimitation Item Tag (FFFE,E0DD) shall be added to the byte stream presented to the MAC Algorithm, regardless of whether or not it was originally present.
### Note
Since the Value Length of Data Elements with a VR of SQ can be either explicit or undefined, the Value Lengths of such Data Elements are left out of the MAC calculation. Similarly, the Value Length of Data Elements with a VR of OB with an undefined length are also left out so that they are handled consistently. If such Data Elements do come with undefined lengths, including the Item Tags that separate the Items or fragments insures that Data Elements cannot be moved between Items or Fragments without compromising the Digital Signature. For those Data Elements with explicit lengths, if the length of an Item changes, the added or removed portions would also impact the MAC calculation, so it is not necessary to include explicit lengths in the MAC calculation. It is possible that including the Value Lengths could make cryptanalysis easier.
After the fields of all the Data Elements in the Data Elements Signed list have been placed into the byte stream presented to the MAC Algorithm according to the above rules, all of the Data Elements within the Digital Signatures Sequence Item except the Certificate of Signer (0400,0115), Signature (0400,0120), Certified Timestamp Type (0400,0305), and Certified Timestamp (0400,0310) shall also be encoded according to the above rules, and presented to the MAC algorithm (i.e., the Attributes of the Digital Signature Sequence Item for this particular Digital Signature are also implicitly included in the list of Data Elements Signed, except as noted above).
The resulting MAC code after processing this byte stream by the MAC Algorithm is then encrypted as specified in the Certificate of Signer and placed in the Value of the Signature Data Element.
### Note
- The Transfer Syntax used in the MAC calculation may differ from the Transfer Syntax used to exchange the Data Set.
- Digital Signatures require explicit VR in order to calculate the MAC. An Application Entity that receives a Data Set with an implicit VR Transfer Syntax may not be able to verify Digital Signatures that include Private Data Elements or Data Elements unknown to that Application Entity. This also true of any Data Elements whose VR is UN. Without knowledge of the Value Representation, the receiving Application Entity would be unable to perform proper byte swapping or be able to properly parse Sequences in order to generate a MAC.
- If more than one entity signs, each Digital Signature would appear in its own Digital Signatures Sequence Item. The Digital Signatures may or may not share the same MAC Parameters Sequence Item.
- The notion of a notary public (i.e., someone who verifies the identity of the signer) for Digital Signatures is partially filled by the authority that issued the Certificate of Signer.
Table C.********.1.2-1 lists the Defined Terms for MAC Algorithm (0400,0015).
**Table C.********.1.2-1. Defined Terms for MAC Algorithm (0400,0015)**
| Defined Term | Reference |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| RIPEMD160 | [ ISO/IEC 10118-3 ] |  |  |  |  |
| MD5 | [ RFC1321 ] ### Note See also security considerations in [ RFC6151 ] . The use of MD5 is no longer recommended. |  |  |  |  |
| SHA1 | [ FIPS PUB 180-4 ] |  |  |  |  |
| SHA224 | [ FIPS PUB 180-4 ] |  |  |  |  |
| SHA256 | [ FIPS PUB 180-4 ] |  |  |  |  |
| SHA384 | [ FIPS PUB 180-4 ] |  |  |  |  |
| SHA512 | [ FIPS PUB 180-4 ] |  |  |  |  |
| SHA512_224 | [ FIPS PUB 180-4 ] |  |  |  |  |
| SHA512_256 | [ FIPS PUB 180-4 ] |  |  |  |  |
| SHA3_224 | [ FIPS PUB 202 ] |  |  |  |  |
| SHA3_256 | [ FIPS PUB 202 ] |  |  |  |  |
| SHA3_384 | [ FIPS PUB 202 ] |  |  |  |  |
| SHA3_512 | [ FIPS PUB 202 ] |  |  |  |  |
### Note
Security Profiles (see [PS3.15](part15.html#PS3.15) ) may restrict or extend the list of MAC algorithms that are permitted or required by a specific profile.
###### C.********.1.3 Certified Timestamp
To generate a certified timestamp, the Value of the Signature (0400,0120) is transmitted to a third party, as specified by the protocol referred to by the Certified Timestamp Type (0400,0305). The third party then generates and returns a certified timestamp in the form specified by that protocol. The certified timestamp returned by the third party is encoded as a stream of bytes in the Certified Timestamp Attribute.
### Note
The timestamp protocol may be specified by a Profile in [PS3.15](part15.html#PS3.15).
##### C.******** Encrypted Attributes
###### C.********.1 Encrypted Attributes Sequence
Each Item of the Encrypted Attributes Sequence (0400,0500) contains an encrypted DICOM Data Set containing a single instance of the Encrypted Attributes Data Set ( Table C.12-7 ). It also contains encrypted content-encryption keys for one or more recipients. The encoding is based on the Enveloped-data Content Type of the Cryptographic Message Syntax defined in IETF STD 70  [  RFC5652  ] . It allows to encrypt the embedded Data Set for an arbitrary number of recipients using any of the three key management techniques supported by IETF STD 70  [  RFC5652  ]  :
- Key Transport: the content-encryption key is encrypted in the recipient's public key;
- Key Agreement: the recipient's public key and the sender's private key are used to generate a pairwise symmetric key, then the content-encryption key is encrypted in the pairwise symmetric key; and
- Symmetric key-encryption Keys: the content-encryption key is encrypted in a previously distributed symmetric key-encryption key.
A recipient decodes the embedded Encrypted Attributes Data Set by decrypting one of the encrypted content-encryption keys, decrypting the encrypted Data Set with the recovered content-encryption key, and then decoding the DICOM Data Set using the Transfer Syntax specified in Encrypted Content Transfer Syntax UID (0400,0510).
Multiple Items may be present in the Encrypted Attributes Sequence. The different Items may contain Encrypted Attributes Data Sets with the same or different sets of Attributes and may contain encrypted content-encryption keys for the same or different sets of recipients. However, if the same Attribute is contained in more than one embedded Encrypted Attributes Data Set, the value of the Attribute must be identical in all embedded Encrypted Attributes Data Sets in which the Attribute is contained.
### Note
If the Encrypted Attributes Sequence contains more than one Item, and a recipient holds the key for more than one of the Items, the recipient may either decode any single one or more of the embedded Data Sets at its own discretion. Since the same Attribute is required to have the same value in all embedded Encrypted Attributes Data Sets, it is safe to "overlay" multiple embedded Encrypted Attributes Data Sets in an arbitrary order upon decoding.
###### C.********.2 Encrypted Content
Encrypted Content (0400,0520) contains an Enveloped-data content type of the cryptographic message syntax defined in IETF STD 70  [  RFC5652  ] . The encrypted content of the Enveloped-data content type is an instance of the Encrypted Attributes Data Set as shown in Table C.12-7 (i.e., it is a Sequence with a single Item), encoded with the Transfer Syntax specified by the Encrypted Content Transfer Syntax UID (0400,0510). Figure C.12-2 shows an example of how the Encrypted Content is encoded. The exact use of this Data Set is defined in the Attribute Confidentiality Profiles in [PS3.15](part15.html#PS3.15).
Since the de-identified SOP Instance is a significantly altered version of the original Data Set, it is a new SOP Instance, with a SOP Instance UID that differs from the original Data Set.
### Note
- Content encryption may require that the content (the DICOM Data Set) be padded to a multiple of some block size. This shall be performed according to the Content-encryption Process defined in IETF STD 70 [ RFC5652 ] .
- Any Standard or Private Transfer Syntax may be specified in Encrypted Content Transfer Syntax UID (0400,0510) unless encoding is performed in accordance with an Attribute Confidentiality Profile that specifies additional restrictions. In general, an application entity decoding the Encrypted Attributes Sequence may not assume any particular Transfer Syntax or set of Transfer Syntaxes to be used with Encrypted Content Transfer Syntax UID (0400,0510).
- For certain applications it might be necessary to "blacken" (remove) identifying information that is burned in to the image pixel data. The Encrypted Attributes Data Set does not specify a means of restoring the original image information without the complete image pixel data being encoded inside the Modified Attributes Sequence (0400,0550). If access to the original, unmodified pixel data is required and the image pixel data cannot be replicated inside the Modified Attributes Sequence (0400,0550) due to resource considerations, the SOP Instance UID may be used to locate the original SOP Instance from which the de-identified version was derived.
- There is no guarantee that the original SOP Instance can be reconstructed from the data in Encrypted Content. If access to the original data is required, the (de-encrypted) UIDs may be used to locate the original SOP Instance from which the de-identified version was derived.
**Table C.12-7. Encrypted Attributes Data Set Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Modified Attributes Sequence | (0400,0550) | ModifiedAttributesSequence | SQ | 1 | list[Dataset] | 1 | Sequence of Items containing all Attributes that were removed or replaced by "dummy values" in the top level Data Set during de-identification of the SOP Instance. Upon reversal of the de-identification process, the Attributes are copied back into the top level Data Set, replacing any dummy values that might have been created. Only a single Item shall be included in this Sequence. |
| &gt;Any Attribute from the top level Data Set that was modified or removed during the de-identification process. |  |  |  |  |  | 3 |  |
**Figure C.12-2. Example of Encoding of Encrypted Attributes Data Set (Informative)**
##### C.******** Contributing Equipment Sequence
Contributing Equipment Sequence (0018,A001) allows equipment that has contributed towards the creation of the Composite Instance to be described. Equipment encompasses both hardware (such as an acquisition device or a film digitizer or a workstation) and software (such as a de-identification tool or an AI model). The general class of contribution is denoted via a coded entry within the Purpose of Reference Code Sequence (0040,A170).
### Note
- For example, a post-processing application creating DERIVED images from ORIGINAL images would place its own identification within the General Equipment Module and identify the original acquisition equipment as an Item within the Contributing Equipment Sequence (0018,A001). Here, the Value of Purpose of Reference Code Sequence (0040,A170) within the Item would be [(109101, DCM, "Acquisition Equipment")](part16.html#DCM_109101). Image display applications wishing to annotate images with information related to the acquisition environment would prefer to extract such details from the Contributing Equipment Sequence rather than the General Equipment Module.
- For example, an image fusion application would place its own identification within the General Equipment Module and identify each of the original acquisition equipment as separate Items within the Contributing Equipment Sequence (0018,A001). Here, the Value of Purpose of Reference Code Sequence (0040,A170) within each Item would be [(109101, DCM, "Acquisition Equipment")](part16.html#DCM_109101).
- For example, a post-processing application creating DERIVED images from other DERIVED images would place its own identification within the General Equipment Module and add the source equipment as an additional Item within the Contributing Equipment Sequence (0018,A001). Here, the Value of Purpose of Reference Code Sequence (0040,A170) within the Item would be [(109102, DCM, "Processing Equipment")](part16.html#DCM_109102).
- For example, a gateway device that coerces Attributes of existing Composite Instances (without creating new Composite Instances) would retain information about the creating equipment within the General Equipment Module and provide its own identification as an Item within the Contributing Equipment Sequence (0018,A001). Here, the Value of Purpose of Reference Code Sequence (0040,A170) within the Item would be [(109103, DCM, "Modifying Equipment")](part16.html#DCM_109103).
- For example, equipment that has been used for de-identifying could retain information about the creating equipment within the General Equipment Module and provide its own identification, and that of its operator, as an Item within Contributing Equipment Sequence (0018,A001). Here, the Value of Purpose of Reference Code Sequence (0040,A170) within the Item would be [(109104, DCM, "De-identifying Equipment")](part16.html#DCM_109104).
- In the case of processing equipment, further information about the algorithm(s) used may be found in invocations of the Table 10-19 Algorithm Identification Macro Attributes, or in SR instances, [TID 4019 Algorithm Identification](part16.html#sect_TID_4019).
##### C.******** HL7 Structured Document Reference Sequence
The HL7 Structured Document Reference Sequence (0040,A390) identifies instances of Structured Documents defined under an HL7 standard. The HL7 standards that define such documents include the Clinical Document Architecture (CDA) and Structured Product Labeling (SPL) standards.
References to unencapsulated HL7 Structured Documents from within DICOM SOP Instances shall be encoded with a SOP Class UID and SOP Instance UID pair. The Abstract Syntax of an HL7 Structured Document is defined by its Hierarchical Message Description; the Object Identifier of the Hierarchical Message Description shall be used as the SOP Class UID for the Structured Document reference.
### Note
- The Hierarchical Message Description Object Identifiers are specified in the HL7 OID Registry ( [http://hl7.org/oid](http://hl7.org/oid) ). The HL7 OIDs for these types of documents are: CDA Release 1 2.16.840.1.113883.1.7.1 CDA Release 2 2.16.840.1.113883.1.7.2 SPL Release 1 2.16.840.1.113883.1.7.3
- The Hierarchical Message Description Object Identifiers do not imply a network or media storage service, as do SOP Class UIDs. However, they do identify the Abstract Syntax, similar to SOP Class UIDs.
The HL7 Structured Document instances are natively identified by an Attribute using the Instance Identifier (II) Data Type, as defined in HL7 v3 Data Types - Abstract Specification. A UID as defined by the DICOM UI Value Representation is a valid identifier under the II Data Type; however, an II value is not always encodable as a UID. Therefore a UID shall be constructed for use within the DICOM Data Set that can be mapped to the native instance identifier encoded as an HL7 II Data Type. This mapping is performed through the combination of the local Referenced SOP Instance UID (0008,1155) and the HL7 Instance Identifier (0040,E001) in the HL7 Structured Document Reference Sequence (0040,A390).
### Note
- An HL7 II is not encodable as a UID if it exceeds 64 characters, or if it includes an extension. See HL7 v3 DT R1.
- Even though an II may contain just a UID, applications should take care to use the II specified in HL7 Instance Identifier (0040,E001) to access the Structured Document. If the instance identifier used natively within the referenced document is encodable using the UI VR, i.e., it is an ISO 8824 OID up to 64 characters without an extension, it is recommended to be used as the Referenced SOP Instance UID within the current Instance.
- The Referenced SOP Instance UID used to reference a particular HL7 Structured Document is not necessarily the same in all DICOM Instances. For example, two SR Documents may internally use different SOP Instance UIDs to reference the same HL7 Structured Document, but they will each contain a mapping to the same HL7 Instance Identifier as the external identifier.
- The HL7 Instance Identifier is encoded in Attribute HL7 Instance Identifier (0040,E001) as a serialization of the UID and Extension (if any) separated by a caret character. This is the same format adopted in the IHE Cross-Enterprise Document Sharing (XDS) profile [ IHE RAD TF-1 ] .
- See Figure C.12-3. **Figure C.12-3. HL7 Structured Document References**
##### C.******** Private Data Element Characteristics
The creator of the Private Data Elements (identified by the Value of Private Creator Reference (0008,0302)) is responsible for managing the Private Data Element Tags associated with them and ensuring that the Private Data Element (0008,0308) and the Private Data Element Keyword (0008,030D) are a unique pair, and that the other associated details are consistent.
Implementers are encouraged to describe all Private Data Elements in the Private Data Element Characteristics Sequence (0008,0300).
### Note
The Private Data Element Characteristics Sequence (0008,0300) may describe Data Elements that are referenced in the current SOP Instance (for example they may be identified as a Selector Attribute), but do not exist as actual Data Elements in the current SOP Instance.
###### C.********.1 Private Data Element Value Multiplicity
For Data Elements with a fixed multiplicity, this Attribute shall contain a single integer value, e.g., 3.
For Data Elements with a variable multiplicity, this Attribute contains either two or three values. The first Value is the minimum multiplicity, the second Value is the maximum multiplicity. If the maximum multiplicity is open-ended, 0 is used. The third Value, if present, is the "stride", i.e., the increment between valid multiplicity values. A stride is used when values are added in sets, such as an x/y/z set of coordinate values that is recorded in triplets. If the stride is 1, The third Value may be omitted. The stride is not permitted to be 0.
Examples:
- VM of 1-3 is expressed as 1,3 or 1,3,1 meaning the multiplicity is permitted to be 1, 2 or 3
- VM of 1-n is expressed as 1,0 or 1,0,1
- VM of 0-n is expressed as 0,0 or 0,0,1
- VM of 3-3n is expressed as 3,0,3
For a Private Data Element Value Representation (0008,030A) of SQ, the multiplicity shall be 1 and the allowed number of Items in a Sequence is recorded in Private Data Element Number of Items(0008,030B).
###### C.********.2 Private Data Element Number of Items
For Sequences that permit a fixed number of Items, this Attribute shall contain a single integer value, e.g., 3.
For Sequences with a variable number of Items, this Attribute contains two values. The first Value is the minimum number of Items, the second Value is the maximum number of Items. If the maximum number of Items is open-ended, 0 is used.
##### C.******** Timezone Offset From UTC
Encoded as an ASCII string in the format "&amp;ZZXX". The components of this string, from left to right, are &amp; = "+" or "-", and ZZ = Hours and XX = Minutes of offset. Leading space characters shall not be present.
The offset for UTC shall be +0000; -0000 shall not be used.
### Note
- This encoding is the same as described in [PS3.5](part05.html#PS3.5) for the offset component of the DT Value Representation.
- This Attribute does not apply to values with a DT Value Representation, that contains an explicitly encoded timezone offset.
- The corrected time may cross a 24 hour boundary. For example, if Local Time = 1.00 a.m. and Offset = +0200, then UTC = 11.00 p.m. (23.00) the day before.
- The "+" sign may not be omitted.
Time earlier than UTC is expressed as a negative offset.
### Note
For example:
UTC = 5.00 a.m.
Local Time = 3.00 a.m.
Offset = -0200
##### C.******** Original Attributes Sequence and Instance Coercion DateTime
Every transfer of a SOP Instance may result in Attribute coercion (see [Section B.4.1.3 Coercion of Attributes in PS3.4](part04.html#sect_B.4.1.3) ) by the receiving application. The receiving application may also detect and correct errors in SOP Instances to bring them into conformance with the SOP Class definition without changing the SOP Instance UID or creating a derived Instance (see status Warning in [Section 9.1.1.1.9 Status in PS3.7](part07.html#sect_9.1.1.1.9) and [Section B.2.3 Statuses in PS3.4](part04.html#sect_B.2.3).
When performing coercion, correction or conversion, the application may set Instance Coercion DateTime (0008,0015) to the current datetime. When performing such actions, the application may add an Item to the Original Attributes Sequence (0400,0561) describing the change and the prior values of replaced or removed Attributes. Any existing Items in the Original Attributes Sequence shall be preserved.
### Note
- Attributes may also be coerced, corrected or converted outside the context of transfer (e.g., while being managed in a storage system). For example, see the IHE Patient Information Reconciliation Integration Profile [ IHE RAD TF-1 ] . Such updates may also be recorded in the Instance Coercion DateTime (0008,0015) and Original Attributes Sequence (0400,0561).
- If Patient ID (0010,0020) is included in the Modified Attributes Sequence (0400,0550), inclusion of Issuer of Patient ID (0010,0021), even if unchanged, or absent in the original, can more precisely identify the context of the replaced value.
Table C.********-1 defines the Attributes of the Original Attributes Sequence (0400,0561).
**Table C.********-1. Original Attributes Macro Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Original Attributes Sequence | (0400,0561) | OriginalAttributesSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Items containing all Attributes that were added, removed or replaced by other values in the top level Data Set. See Section C.********. One or more Items are permitted in this Sequence. |
| &gt;Source of Previous Values | (0400,0564) | SourceOfPreviousValues | LO | 1 | str | 2 | The source that provided the SOP Instance prior to the removal or replacement of the values. For example, this might be the Institution from which imported SOP Instances were received. |
| &gt;Attribute Modification DateTime | (0400,0562) | AttributeModificationDateTime | DT | 1 | str | 1 | Date and time the Attributes were replaced, added or removed. |
| &gt;Modifying System | (0400,0563) | ModifyingSystem | LO | 1 | str | 1 | Identification of the system that replaced, added or removed the Attributes. |
| &gt;Reason for the Attribute Modification | (0400,0565) | ReasonForTheAttributeModification | CS | 1 | str | 1 | Reason for the Attribute modification. **Defined Terms:** COERCE Replace, add or remove values of Attributes such as Patient Name, ID, Accession Number, for example, during import of media from an external institution, or reconciliation against a master patient index. CORRECT Replace or remove incorrect values, or add correct values, such as Patient Name or ID, for example, when incorrect worklist item was chosen or operator input error. CONVERT Replace, add, or remove values of Attributes during a conversion, for example, of private DICOM objects to a standard SOP Class. |
| &gt;Modified Attributes Sequence | (0400,0550) | ModifiedAttributesSequence | SQ | 1 | list[Dataset] | 1 | Sequence that contains all the Attributes, with their previous values, that were modified or removed from the top level Data Set. See Section C.********.1. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Any Attribute from the top level Data Set that was modified or removed. |  |  |  |  |  | 2 | May include Sequence Attributes and their Items. |
| &gt;Nonconforming Modified Attributes Sequence | (0400,0551) | NonconformingModifiedAttributesSequence | SQ | 1 | list[Dataset] | 3 | Attributes that were replaced or removed from the Data Set because the values were not conformant to the Attribute's Value Representation or Value Multiplicity. See Section C.********.2. One or more Items are permitted in this Sequence, one Item for each nonconforming Attribute. |
| &gt;&gt;Include Table 10-20 Selector Attribute Macro Attributes |  |  |  |  |  |  | Pointer to Attribute in Modified Attributes Sequence (0400,0550) that had a nonconforming value. |
| &gt;&gt;Nonconforming Data Element Value | (0400,0552) | NonconformingDataElementValue | OB | 1 | bytes | 1 | The original Value of the nonconforming Attribute. |
###### C.********.1 Modified Attributes Sequence
Attributes that were replaced, added or removed shall be placed in the Modified Attributes Sequence (0400,0550) with their prior values. If an Attribute within a Sequence was replaced, added or removed, the entire prior Value of the Sequence shall be placed in the Modified Attributes Sequence (0400,0550); this applies recursively up to the enclosing Sequence Attribute in the top level Data Set.
Attributes that were empty or absent and for which values have been added may be present in the Modified Attributes Sequence (0400,0550) with a zero length value.
If an Attribute was replaced, added or removed because its value was nonconforming to its Value Representation or Value Multiplicity, it shall be included in the Modified Attributes Sequence (0400,0550) with a zero length value.
Any Private Data Elements present in the Item shall be accompanied by their respective Private Data Element Creator Attribute.
###### C.********.2 Nonconforming Modified Attributes Sequence
If an Attribute Value was replaced or removed because its value was nonconforming to its Value Representation or Value Multiplicity, the original value (which was replaced by a zero length value in the Modified Attributes Sequence) may be recorded in the Nonconforming Modified Attributes Sequence (0400,0551).
The nonconforming Attribute is identified by the Attributes of the Selector Attribute Macro. Because a single Attribute is being identified, Selector Attribute (0072,0026) shall be present.
The Data Set to which the Selector Attribute Macro applies is the single Item of the Modified Attributes Sequence (0400,0550) within the same Item of the Original Attributes Sequence (0400,0561). Therefore, the Modified Attributes Sequence (0400,0550) is not identified in the Selector Sequence Pointer (0072,0052).
### Note
- This is effectively the same as a pointer to the equivalent Attribute in the original top level Data Set.
- Characters in text Attributes non-conformant to the identified Specific Character Set (0008,0005) may be considered non-conformant to the VR.
- For example, if Body Part Examined (0018,0015) had a nonconforming value, the Nonconforming Modified Attributes Sequence (0400,0551) Item would have the Attributes: (0072,0026) 00180015 Selector Attribute (0072,0028) 1 Selector Value Number (0400,0552) ABDOMEN&amp;PELVIS Nonconforming Data Element Value
- The Nonconforming Data Element Value (0400,0552) has Value Representation OB, which allows an arbitrary byte string to be encoded.
##### C.********0 Instance Origin Status
**Enumerated Values:**
LOCAL
Acquired or created in the local entity.
IMPORTED
Imported from an external entity.
### Note
- The interpretation of the meaning of local and imported are user-specific. The purpose of this Attribute is to provide a means of communicating a user-specific decision, not to attempt to achieve uniformity with respect to any particular organizational or geographical boundary around any particular organizational or geographical entity, be it an entity that is a device, system, facility, office, department, site, enterprise, region, nation, etc.
- A typical pattern would be for an archive to set a Value of LOCAL when receiving Instances on the network from a modality within the hospital but to set a Value of IMPORTED when receiving Instances from an interchange media reader or an external network Instance sharing gateway. Displaying the Value of this Attribute in a viewer then makes it apparent to a user whether or not an Instance is "one of their own". How a receiver determines whether or not the sender is local is not specified, but could, for example, be determined from the sender's AE Title.
- When Instances are transported from one entity to another and then imported, it is expected that this Attribute be set to an appropriate value for the new context, overwriting any previous value. E.g., a Value of LOCAL used within an archive at the site where the Instances were acquired would be coerced to a Value of IMPORTED when those Instances were received by another site to which the patient had been transferred. Whether or not previous Values of this Attribute are copied into Original Attributes Sequence (0400,0561) after coercion is not specified.
- It is not expected (but nor is it prohibited) that this Attribute will be removed by an exporting entity, since it may be useful to a recipient to know whether or not the Instance was local to the previous entity or not. Similarly, a modality may populate this Attribute with a Value of LOCAL after creating the images, but is not required to.
- The round-trip case, e.g., when an image is acquired locally, exported, locally deleted and then re-imported may be challenging unless a local record is maintained. I.e., after acquisition, in the local archive it would be expected to be LOCAL. After re-importation without any local state, it may be hard to determine that it once was LOCAL. Other Attributes such as Institution Name may not be sufficient to reliably detect this.
- A new SOP Instance UID (0008,0018) is not required when adding or coercing this Attribute, since a derived Instance is not being created. See Section C.******** Derivation Description and Section B.4.1.3 Coercion of Attributes in PS3.4 .
- A more detailed history of the handling of an Instance may be recorded in Contributing Equipment Sequence (0018,A001).
- This Attribute may need to be removed during de-identification. See [Annex E Attribute Confidentiality Profiles (Normative) in PS3.15](part15.html#chapter_E).