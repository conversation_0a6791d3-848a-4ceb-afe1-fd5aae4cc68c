#### C.8.8.13 RT Fraction Scheme Module
The RT Fraction Scheme Module contains Attributes that describe a single or multiple scheme of dose descriptions. Each Sequence Item contains dose specification information, fractionation patterns, and either beam or brachytherapy application setup specifications. The design of the RT Fraction Scheme Module allows a beam or brachytherapy application setup to be used in multiple fraction schemes.
**Table C.8-49. RT Fraction Scheme Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Fraction Group Sequence | (300A,0070) | FractionGroupSequence | SQ | 1 | list[Dataset] | 1 | Sequence of Fraction Groups in current Fraction Scheme. One or more Items shall be included in this Sequence. |
| &gt;Fraction Group Number | (300A,0071) | FractionGroupNumber | IS | 1 | str, int | 1 | Identification number of the Fraction Group. The Value of Fraction Group Number (300A,0071) shall be unique within the RT Plan in which it is created. |
| &gt;Fraction Group Description | (300A,0072) | FractionGroupDescription | LO | 1 | str | 3 | The user defined description for the fraction group. |
| &gt;Definition Source Sequence | (0008,1156) | DefinitionSourceSequence | SQ | 1 | list[Dataset] | 3 | Instances containing the source of the Fraction Group information. Only a single Item is permitted in this Sequence. Referenced SOP Class UID (0008,1150) shall be "1.2.840.10008.*******.1.481.12" (RT Radiation Set Storage). See Section C.********. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes. |  |  |  |  |  |  |  |
| &gt;Referenced Dose Sequence | (300C,0080) | ReferencedDoseSequence | SQ | 1 | list[Dataset] | 3 | Related Instances of RT Dose (for grids, isodose curves and named/unnamed point doses). One or more Items are permitted in this Sequence. See Note 1. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;Referenced Dose Reference Sequence | (300C,0050) | ReferencedDoseReferenceSequence | SQ | 1 | list[Dataset] | 3 | Sequence of Dose References for the current Fraction Group. One or more Items are permitted in this Sequence. |
| &gt;&gt;Referenced Dose Reference Number | (300C,0051) | ReferencedDoseReferenceNumber | IS | 1 | str, int | 1 | Uniquely identifies Dose Reference specified by Dose Reference Number (300A,0012) within Dose Reference Sequence (300A,0010) in RT Prescription Module. |
| &gt;&gt;Constraint Weight | (300A,0021) | ConstraintWeight | DS | 1 | str, float, int | 3 | Relative importance of satisfying constraint, where high values represent more important constraints. |
| &gt;&gt;Delivery Warning Dose | (300A,0022) | DeliveryWarningDose | DS | 1 | str, float, int | 3 | The dose (in Gy) that when reached or exceeded should cause some action to be taken. |
| &gt;&gt;Delivery Maximum Dose | (300A,0023) | DeliveryMaximumDose | DS | 1 | str, float, int | 3 | The maximum dose (in Gy) that can be delivered to the dose reference. |
| &gt;&gt;Target Minimum Dose | (300A,0025) | TargetMinimumDose | DS | 1 | str, float, int | 3 | Minimum permitted dose (in Gy) to Dose Reference if Dose Reference Type (300A,0020) of referenced Dose Reference is TARGET. |
| &gt;&gt;Target Prescription Dose | (300A,0026) | TargetPrescriptionDose | DS | 1 | str, float, int | 3 | Prescribed dose (in Gy) to Dose Reference if Dose Reference Type (300A,0020) of referenced Dose Reference is TARGET. |
| &gt;&gt;Target Maximum Dose | (300A,0027) | TargetMaximumDose | DS | 1 | str, float, int | 3 | Maximum permitted dose (in Gy) to Dose Reference if Dose Reference Type (300A,0020) of referenced Dose Reference is TARGET. |
| &gt;&gt;Target Underdose Volume Fraction | (300A,0028) | TargetUnderdoseVolumeFraction | DS | 1 | str, float, int | 3 | Maximum permitted fraction (in percent) of Target to receive less than the Target Prescription Dose (300A,0027) if Dose Reference Type (300A,0020) of referenced Dose Reference is TARGET and Dose Reference Structure Type (300A,0014) of referenced Dose Reference is VOLUME. |
| &gt;&gt;Organ at Risk Full-volume Dose | (300A,002A) | OrganAtRiskFullVolumeDose | DS | 1 | str, float, int | 3 | Maximum dose (in Gy) to entire Dose Reference if Dose Reference Type (300A,0020) of referenced Dose Reference is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) of referenced Dose Reference is VOLUME. |
| &gt;&gt;Organ at Risk Limit Dose | (300A,002B) | OrganAtRiskLimitDose | DS | 1 | str, float, int | 3 | Maximum permitted dose (in Gy) to any part of Dose Reference if Dose Reference Type (300A,0020) of referenced Dose Reference is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) of referenced Dose Reference is VOLUME. |
| &gt;&gt;Organ at Risk Maximum Dose | (300A,002C) | OrganAtRiskMaximumDose | DS | 1 | str, float, int | 3 | Maximum dose (in Gy) to non-overdosed part of Dose Reference if Dose Reference Type (300A,0020) of referenced Dose Reference is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) of referenced Dose Reference is VOLUME. |
| &gt;&gt;Organ at Risk Overdose Volume Fraction | (300A,002D) | OrganAtRiskOverdoseVolumeFraction | DS | 1 | str, float, int | 3 | Maximum permitted fraction (in percent) of Organ at Risk to receive more than the Organ at Risk Maximum Dose if Dose Reference Type (300A,0020) of referenced Dose Reference is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) of referenced Dose Reference is VOLUME. |
| &gt;Number of Fractions Planned | (300A,0078) | NumberOfFractionsPlanned | IS | 1 | str, int | 2 | Total number of treatments (Fractions) prescribed for current Fraction Group. |
| &gt;Number of Fraction Pattern Digits Per Day | (300A,0079) | NumberOfFractionPatternDigitsPerDay | IS | 1 | str, int | 3 | Number of digits in Fraction Pattern (300A,007B) used to represent one day. See Note 2. |
| &gt;Repeat Fraction Cycle Length | (300A,007A) | RepeatFractionCycleLength | IS | 1 | str, int | 3 | Number of weeks needed to describe treatment pattern. See Note 2. |
| &gt;Fraction Pattern | (300A,007B) | FractionPattern | LT | 1 | str | 3 | String of 0's (no treatment) and 1's (treatment) describing treatment pattern. Length of string is 7 x Number of Fraction Pattern Digits Per Day x Repeat Fraction Cycle Length. Pattern shall start on a Monday. See Note 2. |
| &gt;Beam Dose Meaning | (300A,008B) | BeamDoseMeaning | CS | 1 | str | 3 | Indicates the meaning of Beam Dose (300A,0084). **Enumerated Values:** BEAM_LEVEL Beam Dose value is individually calculated for this Beam FRACTION_LEVEL Dose is calculated on the Fraction level, and the Value of Beam Dose (300A,0084) is assigned to the Beam to carry a nominally distributed dose only. |
| &gt;Number of Beams | (300A,0080) | NumberOfBeams | IS | 1 | str, int | 1 | Number of Beams in current Fraction Group. If Number of Beams is greater then zero, Number of Brachy Application Setups (300A,00A0) shall equal zero. |
| &gt;Referenced Beam Sequence | (300C,0004) | ReferencedBeamSequence | SQ | 1 | list[Dataset] | 1C | Sequence of treatment beams in current Fraction Group. One or more Items shall be included in this Sequence. Required if Number of Beams (300A,0080) is greater than zero. |
| &gt;&gt;Referenced Beam Number | (300C,0006) | ReferencedBeamNumber | IS | 1 | str, int | 1 | Uniquely identifies Beam specified by Beam Number (300A,00C0) within Beam Sequence (300A,00B0) in RT Beams Module or within Ion Beam Sequence (300A,03A2) in RT Ion Beams Module. |
| &gt;&gt;Referenced Dose Reference UID | (300A,0083) | ReferencedDoseReferenceUID | UI | 1 | str | 3 | Identifies the Dose Reference specified by Dose Reference UID (300A,0013) in the Dose Reference Sequence (300A,0010) in the RT Prescription Module, which specifies the primary target for the current Beam. If present shall have a Value that is present in the Dose Reference Sequence. |
| &gt;&gt;Beam Dose | (300A,0084) | BeamDose | DS | 1 | str, float, int | 3 | Dose (in Gy) due to current Beam for one treatment fraction. See Note 9. |
| &gt;&gt;Beam Dose Type | (300A,0090) | BeamDoseType | CS | 1 | str | 1C | Type of Dose of the Beam Dose (300A,0084). **Enumerated Values:** PHYSICAL EFFECTIVE Shall not have the same Value as Alternate Beam Dose Type (300A,0092). Required if Alternate Beam Dose (300A,0091) is present. May be present otherwise. |
| &gt;&gt;Alternate Beam Dose | (300A,0091) | AlternateBeamDose | DS | 1 | str, float, int | 3 | Alternate Dose (in Gy) according to the Alternate Beam Dose Type (300A,0092). See Note 9. |
| &gt;&gt;Alternate Beam Dose Type | (300A,0092) | AlternateBeamDoseType | CS | 1 | str | 1C | Type of Dose of the Alternate Beam Dose (300A,0091). **Enumerated Values:** PHYSICAL EFFECTIVE Shall not have the same Value as Beam Dose Type (300A,0090). Required if Alternate Beam Dose (300A,0091) is present. |
| &gt;&gt;Beam Meterset | (300A,0086) | BeamMeterset | DS | 1 | str, float, int | 3 | Machine setting to be delivered for current Beam, specified in Monitor Units (MU) or minutes as defined by Primary Dosimeter Unit (300A,00B3) (in RT Beams Module ) for referenced Beam. See Note 4. |
| &gt;&gt;Beam Delivery Duration Limit | (300A,00C5) | BeamDeliveryDurationLimit | FD | 1 | float | 3 | The expected maximum delivery time in sec. See Note 7. |
| &gt;&gt;Dose Calibration Conditions Verified Flag | (300C,0123) | DoseCalibrationConditionsVerifiedFlag | CS | 1 | str | 3 | Indicates whether verifiable calibration conditions of the delivery device were used during treatment planning. **Enumerated Values:** YES NO |
| &gt;&gt;Dose Calibration Conditions Sequence | (300C,0120) | DoseCalibrationConditionsSequence | SQ | 1 | list[Dataset] | 1C | Dose calibration conditions for the referenced beam. Required if Dose Calibration Conditions Verified Flag (300C,0123) is present and equals YES and Radiation Device Configuration and Commissioning Key Sequence (300A,065A) is absent. May be present if Radiation Device Configuration and Commissioning Key Sequence (300A,065A) is present. Only a single Item shall be present in this Sequence. |
| &gt;&gt;&gt;Absorbed Dose to Meterset Ratio | (300C,0121) | AbsorbedDoseToMetersetRatio | FD | 1 | float | 1 | Ratio of absorbed dose in Gy to Meterset as defined by Primary Dosimeter Unit (300A,00B3) in the reference conditions. |
| &gt;&gt;&gt;Delineated Radiation Field Size | (300C,0122) | DelineatedRadiationFieldSize | FD | 2 | float | 1 | Field size in mm in X and Y directions in the IEC BEAM LIMITING DEVICE coordinate system, specified by a numeric pair, X Value then Y Value. |
| &gt;&gt;&gt;Calibration Reference Point Depth | (300C,0124) | CalibrationReferencePointDepth | FD | 1 | float | 1 | Calibration reference point depth in mm from the phantom surface. |
| &gt;&gt;&gt;Source to Surface Distance | (300A,0130) | SourceToSurfaceDistance | DS | 1 | str, float, int | 1 | Distance in mm from the radiation source to the phantom surface during calibration. |
| &gt;&gt;&gt;Calibration DateTime | (0018,1203) | CalibrationDateTime | DT | 1 | str | 2 | Date and time the calibration was performed. |
| &gt;&gt;Radiation Device Configuration and Commissioning Key Sequence | (300A,065A) | RadiationDeviceConfigurationAndCommissioningKeySequence | SQ | 1 | list[Dataset] | 1C | Keys identifying the configuration and commissioning data used as input for treatment planning of this Instance. Value Type (0040,A040) is constrained to Value UIDREF. Required if Dose Calibration Conditions Verified Flag (300C,0123) is present and equals YES and Dose Calibration Conditions Sequence (300C,0120) is absent. May be present if Dose Calibration Conditions Sequence (300C,0120) is present. One or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;Include Table 10-2 Content Item Macro Attributes. |  |  |  |  |  |  |  |
| &gt;Number of Brachy Application Setups | (300A,00A0) | NumberOfBrachyApplicationSetups | IS | 1 | str, int | 1 | Number of Brachy Application Setups in current Fraction Group. If Number of Brachy Application Setups is greater then zero, Number of Beams (300A,0080) shall equal zero. |
| &gt;Referenced Brachy Application Setup Sequence | (300C,000A) | ReferencedBrachyApplicationSetupSequence | SQ | 1 | list[Dataset] | 1C | Sequence of treatment Brachy Application Setups in current Fraction Group. Required if Number of Brachy Application Setups (300A,00A0) is greater than zero. One or more Items shall be included in this Sequence. |
| &gt;&gt;Referenced Brachy Application Setup Number | (300C,000C) | ReferencedBrachyApplicationSetupNumber | IS | 1 | str, int | 1 | Uniquely identifies Brachy Application Setup specified by Brachy Application Setup Number (300A,0234) within Brachy Application Setup Sequence (300A,0230) in RT Brachy Application Setups Module. |
| &gt;&gt;Brachy Application Setup Dose Specification Point | (300A,00A2) | BrachyApplicationSetupDoseSpecificationPoint | DS | 3 | str, float, int | 3 | Coordinates (x,y,z) of point in the Patient-Based Coordinate System described in Section C.7.6.2.1.1 at which Brachy Application Setup Dose (300A,00A4) is specified (mm). |
| &gt;&gt;Brachy Application Setup Dose | (300A,00A4) | BrachyApplicationSetupDose | DS | 1 | str, float, int | 3 | Dose (in Gy) at Brachy Application Setup Dose Specification Point (300A,00A2) due to current Brachy Application Setup. |
| &gt;&gt;Referenced Dose Reference UID | (300A,0083) | ReferencedDoseReferenceUID | UI | 1 | str | 3 | Identifies the Dose Reference specified by Dose Reference UID (300A,0013) in the Dose Reference Sequence (300A,0010) in the RT Prescription Module that specifies the primary target for the current Brachy Application Setup. If present, shall have a Value that is present in the Dose Reference Sequence (300A,0010). |
### Note
- An RT Dose IOD referenced within the Referenced Dose Sequence (300C,0080) can be used for storing grid-based (pixel) data, isodose curves, and/or individual dose points (with optional dose point names) for the current Fraction Group.
- The fractionation pattern does not indicate the actual start of treatment, or the order or timing of fraction delivery. If treatment does not commence as outlined in the pattern, it is the application's responsibility to make any necessary adjustments. Examples of Fractionation Pattern Schemes: 1 fraction group, 1 fraction per day (Monday to Friday): Number of Fraction Pattern Digits per Day = 1, Repeat Fraction Cycle Length = 1, Fraction Pattern = 1111100
- 2 fraction groups, 1 fraction per day, first fraction group Monday, Wednesday, and Friday, second fraction group Tuesday and Thursday: Fraction Group 1: Number of Fraction Pattern Digits Per Day = 1, Repeat Fraction Cycle Length = 1, Fraction Pattern = 1010100
- Fraction Group 2: Number of Fraction Pattern Digits Per Day = 1, Repeat Fraction Cycle Length = 1, Fraction Pattern = 0101000
- 2 fraction groups, 1 fraction per day, alternating fraction groups every day of treatment (Monday to Friday): Fraction Group 1: Number of Fraction Pattern Digits Per Day = 1, Repeat Fraction Cycle Length = 2, Fraction Pattern = 10101000101000
- Fraction Group 2: Number of Fraction Pattern Digits Per Day = 1, Repeat Fraction Cycle Length = 2, Fraction Pattern = 01010001010100
- 1 fraction group, 2 fractions per day (Monday to Friday): Fraction Group 1: Number of Fraction Pattern Digits Per Day = 2, Repeat Fraction Cycle Length = 1, Fraction Pattern = 11111111110000
- 2 fraction groups, 2 fractions per day, alternating fraction groups every treatment (Monday to Friday): Fraction Group 1: Number of Fraction Pattern Digits Per Day = 1, Repeat Fraction Cycle Length = 1, Fraction Pattern = 1111100
- Fraction Group 2: Number of Fraction Pattern Digits Per Day = 2, Repeat Fraction Cycle Length = 1, Fraction Pattern = 11111111110000
- The Brachy Application Setup Dose Specification Point (300A,00A2) contains the coordinates of the single point used for dose normalization. This point is distinct from the Brachy Referenced Dose Reference Sequence (300C,0055) in the RT Brachy Application Setups Module, which are used for plan evaluation and dose tracking.
- The Meterset at a given Control Point (see RT Beams Module ) is equal to Beam Meterset (300A,0086) multiplied by the Cumulative Meterset Weight (300A,0134) for the Control Point, divided by the Final Cumulative Meterset Weight (300A,010E).
- Attribute Referenced Patient Setup Number (300C,006A) was previously defined. Its use in this Module is now retired (See [PS3.3-2004](http://dicom.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf) ).
- Attributes Beam Dose Point Depth, Beam Dose Point Equivalent Depth and Beam Dose Point SSD were previously included in this Module as optional Attributes but have been retired. See [PS3.3-2011](http://medical.nema.org/MEDICAL/Dicom/2011/11_03pu.pdf).
- The Beam Delivery Duration Limit (300A,00C5) is the maximum time span allowed to deliver a single fraction of a beam to prevent significant over-treatments. Treatment is expected to be terminated upon reaching the Beam Delivery Duration Limit independent of the Meterset. This limit represents the expected time span including some increase by a factor greater than 1 to accommodate normal variations in delivery.
- The Beam Dose Verification Control Point Sequence was previously included and has been retired. See [PS3.3-2017c](http://dicom.nema.org/medical/dicom/2017c/output/pdf/part03.pdf). The information is now described in the Referenced Dose Reference Sequence (300C,0050) in the Section C.8.8.14 RT Beams Module.
- The Beam Dose Specification Point (300A,0082) was previously included in this module as a means to specify a single point at which dose contributions of different beams could be specified. This Attribute has been retired as it no longer reflects clinical practice. Along with this, the semantics of the Beam Dose (300A,0084) and Alternate Beam Dose (300A,0091) have been adapted to reflect the absence of the Beam Dose Specification Point. In order to refer to an Item in the Dose Reference Sequence (300A,0010) it is recommended to utilize the Referenced Dose Reference UID (300A,0083) and the Dose Reference UID (300A,0013) respectively.
##### C.8.8.13.1 Beam Dose Verification Parameters (Retired)
Retired. See [PS3.3-2017c](http://dicom.nema.org/MEDICAL/Dicom/2017c/output/pdf/part03.pdf).
##### C.******** Definition Source Sequence
The Definition Source Sequence (0008,1156) references SOP Instances of First or Second Generation Radiotherapy IODs as the source of the information which has been transcoded to the current SOP Instance up to the capability of the current SOP Class. The Definition Source Sequence shall not be used when the current SOP Instance represents a derivation or successor of the source Instance. The source Instance shall not contain a reference to the current Instance.
Typical use cases are: A device (e.g. a treatment planning system or treatment delivery system) is creating Second Generation SOP Instances and additionally encoding them in First Generation SOP Instances for other receivers supporting First Generation RT IODs only. Another use case is that an application receives Second Generation SOP Instances and transcodes them to First Generation SOP Instances to make the content available to receivers supporting First Generation IODs only.
The same applies for the reverse use cases when the source Instance is a First Generation SOP Instance and the current SOP Instance is a transcoded Second Generation SOP Instance.