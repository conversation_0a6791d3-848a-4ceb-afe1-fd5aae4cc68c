#### C.8.8.5 Structure Set Module
A structure set defines a set of areas of significance. Each area can be associated with a Frame of Reference and zero or more images. Information that can be transferred with each region of interest (ROI) includes geometrical and display parameters, and generation technique.
**Table C.8-41. Structure Set Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Structure Set Label | (3006,0002) | StructureSetLabel | SH | 1 | str | 1 | User-defined label for Structure Set. |
| Structure Set Name | (3006,0004) | StructureSetName | LO | 1 | str | 3 | User-defined name for Structure Set. |
| Structure Set Description | (3006,0006) | StructureSetDescription | ST | 1 | str | 3 | User-defined description for Structure Set. |
| Instance Number | (0020,0013) | InstanceNumber | IS | 1 | str, int | 3 | A number that identifies this object Instance. |
| Structure Set Date | (3006,0008) | StructureSetDate | DA | 1 | str | 2 | Date at which the content of the Structure Set was last modified. |
| Structure Set Time | (3006,0009) | StructureSetTime | TM | 1 | str | 2 | Time at which the content of the Structure Set was last modified. |
| Referenced Frame of Reference Sequence | (3006,0010) | ReferencedFrameOfReferenceSequence | SQ | 1 | list[Dataset] | 3 | Sequence describing Frames of Reference in which the ROIs are defined. One or more Items are permitted in this Sequence. See Section C.*******. |
| &gt;Frame of Reference UID | (0020,0052) | FrameOfReferenceUID | UI | 1 | str | 1 | Uniquely identifies Frame of Reference within Structure Set. |
| &gt;RT Referenced Study Sequence | (3006,0012) | RTReferencedStudySequence | SQ | 1 | list[Dataset] | 3 | Sequence of Studies containing Series to be referenced. One or more Items are permitted in this Sequence. See Section C.*******. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| &gt;&gt;RT Referenced Series Sequence | (3006,0014) | RTReferencedSeriesSequence | SQ | 1 | list[Dataset] | 1 | Sequence describing Series of images within the referenced Study that are used in defining the Structure Set. One or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;Series Instance UID | (0020,000E) | SeriesInstanceUID | UI | 1 | str | 1 | Unique identifier for the Series containing the images. |
| &gt;&gt;&gt;Contour Image Sequence | (3006,0016) | ContourImageSequence | SQ | 1 | list[Dataset] | 1 | Sequence of Items describing images in a given Series used in defining the Structure Set (typically CT or MR images). One or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;&gt;Include Table 10-3 Image SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
| Source Series Information Sequence | (3006,004C) | SourceSeriesInformationSequence | SQ | 1 | list[Dataset] | 3 | Information about Image Series that are sources of ROIs in this Instance. This also includes Series present in the RT Referenced Series Sequence (3006,0014). The Image Series may have been the source of the definition, but the ROI has been resampled to the Image Series referenced in the Referenced Frame of Reference Sequence (3006,0010). One or more Items are permitted in this Sequence. |
| &gt;Modality | (0008,0060) | Modality | CS | 1 | str | 1 | Type of device, process or method that originally acquired or produced the data used to create the Instances in this Series. |
| &gt;Series Date | (0008,0021) | SeriesDate | DA | 1 | str | 1 | Date the Series started. |
| &gt;Series Time | (0008,0031) | SeriesTime | TM | 1 | str | 1 | Time the Series started. |
| &gt;Series Description | (0008,103E) | SeriesDescription | LO | 1 | str | 1 | Description of the Series. |
| &gt;Series Instance UID | (0020,000E) | SeriesInstanceUID | UI | 1 | str | 1 | Unique identifier of the Series. |
| &gt;Series Number | (0020,0011) | SeriesNumber | IS | 1 | str, int | 1 | A number that identifies this Series. |
| Structure Set ROI Sequence | (3006,0020) | StructureSetROISequence | SQ | 1 | list[Dataset] | 3 | ROIs for current Structure Set. One or more Items are permitted in this Sequence. |
| &gt;ROI Number | (3006,0022) | ROINumber | IS | 1 | str, int | 1 | Identification number of the ROI. The Value of ROI Number (3006,0022) shall be unique within the Structure Set in which it is created. |
| &gt;Conceptual Volume Identification Sequence | (3010,00A0) | ConceptualVolumeIdentificationSequence | SQ | 1 | list[Dataset] | 3 | Identifies the Conceptual Volume which is represented by this ROI. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 10.33-1 Conceptual Volume Macro Attributes. |  |  |  |  |  |  |  |
| &gt;Referenced Frame of Reference UID | (3006,0024) | ReferencedFrameOfReferenceUID | UI | 1 | str | 1 | Uniquely identifies Frame of Reference in which ROI is defined, specified by Frame of Reference UID (0020,0052) in Referenced Frame of Reference Sequence (3006,0010). |
| &gt;ROI Name | (3006,0026) | ROIName | LO | 1 | str | 2 | User-defined name for ROI. |
| &gt;ROI Description | (3006,0028) | ROIDescription | ST | 1 | str | 3 | User-defined description for ROI. |
| &gt;ROI Volume | (3006,002C) | ROIVolume | DS | 1 | str, float, int | 3 | Volume of ROI (cubic centimeters). |
| &gt;ROI DateTime | (3006,002D) | ROIDateTime | DT | 1 | str | 3 | DateTime when this ROI was last modified. |
| &gt;ROI Generation Algorithm | (3006,0036) | ROIGenerationAlgorithm | CS | 1 | str | 2 | Type of algorithm used to generate ROI. **Defined Terms:** AUTOMATIC calculated ROI SEMIAUTOMATIC ROI calculated with user assistance MANUAL user-entered ROI |
| &gt;ROI Generation Description | (3006,0038) | ROIGenerationDescription | LO | 1 | str | 3 | User-defined description of technique used to generate ROI. |
| &gt;RT Protocol Code Sequence | (3010,005B) | RTProtocolCodeSequence | SQ | 1 | list[Dataset] | 3 | The protocol(s) selected by the RT Physician. One or more Items are permitted in this Sequence. See Section C.********. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| &gt;ROI Creator Sequence | (3006,004D) | ROICreatorSequence | SQ | 1 | list[Dataset] | 3 | The person or device that last modified this ROI. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table C.17-3b Identified Person or Device Macro Attributes |  |  |  |  |  |  | Organizational Role Code Sequence (0044,010A) D [CID 9555 Radiotherapy Treatment Planning Person Role](part16.html#sect_CID_9555). |
| &gt;ROI Derivation Algorithm Identification Sequence | (3006,0037) | ROIDerivationAlgorithmIdentificationSequence | SQ | 1 | list[Dataset] | 3 | Software algorithm that derived the ROI. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 10-19 Algorithm Identification Macro Attributes. |  |  |  |  |  |  |  |
| &gt;Derivation Code Sequence | (0008,9215) | DerivationCodeSequence | SQ | 1 | list[Dataset] | 3 | A coded description of how this ROI was derived. One or more Items are permitted in this Sequence. See Section C.******* for further explanation. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes. |  |  |  |  |  |  | Enumerated Value [(113085, DCM, "Spatial resampling")](part16.html#DCM_113085). |
| &gt;Definition Source Sequence | (0008,1156) | DefinitionSourceSequence | SQ | 1 | list[Dataset] | 3 | Instances containing the source of the ROI Contour information. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes. |  |  |  |  |  |  |  |
| &gt;&gt;Referenced Segment Number | (0062,000B) | ReferencedSegmentNumber | US | 1-n | int | 1C | The Value of Segment Number (0062,0004) in the referenced SOP Instance that identifies the segment that is the source of the ROI Contour information. Required if Referenced SOP Class UID (0008,1150) is "1.2.840.10008.*******.1.66.4" (Segmentation Storage). |
| &gt;&gt;Referenced Fiducial UID | (0070,031B) | ReferencedFiducialUID | UI | 1 | str | 1C | The Value of Fiducials UID (0070,031A) in the referenced SOP Instance that identifies the fiducial that is the source of the ROI Contour information. Required if Referenced SOP Class UID (0008,1150) is "1.2.840.10008.*******.1.66.2" (Spatial Fiducials Storage). |
| Predecessor Structure Set Sequence | (3006,0018) | PredecessorStructureSetSequence | SQ | 1 | list[Dataset] | 3 | The Structure Set that has been used to derive the current Structure Set. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |  |  |  |  |  |  |  |
##### C.******* Frames of Reference
The Referenced Frame of Reference Sequence (3006,0010) describes a set of Frames of Reference in which some or all of the ROIs are expressed. Since the Referenced Frame of Reference UID (3006,0024) is required for each ROI, each Frame of Reference used to express the coordinates of an ROI shall be listed in the Referenced Frame of Reference Sequence (3006,0010) once and only once.
### Note
As an example, a set of ROIs defined using a single image Series would list the image Series in a single Referenced Frame of Reference Sequence (3006,0010) Item, providing the UID for this referenced Frame of Reference (obtained from the source images), and listing all pertinent images in the Contour Image Sequence (3006,0016).
##### C.******* Frame of Reference Relationship Sequence and Transformation Matrix (Retired)
Retired. See [PS3.3-2011](http://medical.nema.org/MEDICAL/Dicom/2011/11_03pu.pdf).
### Note
The concept of definition of registered Frame of References using the Frame of Reference Relationship Sequence (3006,00C0) formerly present in the Standard is retired. The use of Registration IODs is advised since the introduction of Spatial Registration IOD, which is a much stronger and more general concept, and independent from the specifics of RT Structure Sets. Additionally it is of importance that registrations are decoupled from image and segmentation objects.
##### C.******* ROI Derivation Sequence
If an ROI is created by re-sampling an existing ROI that is either (a) present in a different Frame of Reference (FoR) and has been resampled to align with the image Series referenced by the current structure set, or (b) present in the same FoR, but its contours are referenced to a different image Series, it is useful to be able to identify that the ROI is resampled. The Derivation Code Sequence (0008,9215) shall be used with code [(113085, DCM, "Spatial resampling")](part16.html#DCM_113085) to indicate the resampled ROI.
##### C.******* SOP Class UID in RT Referenced Study Sequence
Since RT Referenced Study Sequence (3006,0012) is Type 3, the Attribute may be omitted.
If RT Referenced Study Sequence (3006,0012) is present with an Item, for each Item, the SOP Class UID (0008,0016) is that of the Study itself, not of the Instances within the Study. The Detached Study Management SOP Class (Retired) ("1.2.840.10008.3.1.2.3.1") may be used in Referenced SOP Class UID (0008,1150) in lieu of there being any other private or standard Study SOP Class defined.
### Note
The SOP Class of an Instance (e.g., of one of the images within the referenced Study) is not used, even if all of the Instances within the Study are of the same Instance SOP Class.