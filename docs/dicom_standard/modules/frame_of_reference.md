#### C.7.4.1 Frame of Reference Module
Table C.7-6 specifies the Attributes of the Frame of Reference Module, which are necessary to uniquely identify a Frame of Reference that ensures the spatial relationship of Images within a Series. It also allows Images across multiple Series to share the same Frame of Reference. This Frame of Reference (or coordinate system) shall be constant for all Images related to a specific Frame of Reference.
When a Frame of Reference is identified, it is not important how the imaging target (patient, specimen, or phantom) is positioned relative to the imaging equipment or where the origin of the Frame of Reference is located. It is important that the position of the imaging target and the origin are constant in relationship to a specific Frame of Reference.
### Note
Since the criteria used to group images into a Series is application specific, it is possible for imaging applications to define multiple Series within a Study that share the same imaging space. Previous releases of the DICOM Standard specified that all images within the Series must be spatially related. However, insufficient information was available to determine if multiple Series within a Study were spatially related.
**Table C.7-6. Frame of Reference Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Frame of Reference UID | (0020,0052) | FrameOfReferenceUID | UI | 1 | str | 1 | Uniquely identifies the Frame of Reference for a Series. See Section C.*******.1 for further explanation. |
| Position Reference Indicator | (0020,1040) | PositionReferenceIndicator | LO | 1 | str | 2 | Part of the imaging target used as a reference. See Section C.*******.2 for further explanation. |
##### C.******* Frame of Reference Module Attribute Descriptions
###### C.*******.1 Frame of Reference UID
The Frame of Reference UID (0020,0052) shall be used to uniquely identify a Frame of Reference for a Series. Each Series shall have a single Frame of Reference UID. However, multiple Series within a Study may share a Frame of Reference UID. All images in a Series that share the same Frame of Reference UID shall be spatially related to each other.
### Note
- Previous versions of this Standard defined a Data Element "Location", which has been retired. Frame of Reference UID provides a completely unambiguous identification of the image location reference used to indicate position.
- A common Frame of Reference UID may be used to spatially relate localizer images with a set of transverse images. However, in some cases (e.g., multiple localizer images being related to a single set of transverse images) a common Frame of Reference UID may not be sufficient. The Referenced Image Sequence (0008,1140) provides an unambiguous method for relating localizer images.
###### C.*******.2 Position Reference Indicator
The Position Reference Indicator (0020,1040) specifies the part of the imaging target that was used as a reference point associated with a specific Frame of Reference UID. The Position Reference Indicator may or may not coincide with the origin of the fixed Frame of Reference related to the Frame of Reference UID.
For a Patient-related Frame of Reference, this is an anatomical reference point such as the iliac crest, orbital-medial, sternal notch, symphysis pubis, xiphoid, lower costal margin, or external auditory meatus, or a fiducial marker placed on the patient. The Patient-Based Coordinate System is described in Section C.*******.1.
For a slide-related Frame of Reference, this is the slide corner as specified in Section C.******** and shall be identified in this Attribute with the Value "SLIDE_CORNER". The slide-based coordinate system is described in Section C.********.
For a Corneal Coordinate System, the Frame of Reference is based upon the corneal vertex. The corneal vertex is determined by the measuring instrument and shall be identified in this Attribute with the Value CORNEAL_VERTEX_R (for the right eye) or CORNEAL_VERTEX_L (for the left eye). The Corneal Coordinate System is described in Section C.********.4.
The Position Reference Indicator shall be used only for annotation purposes and is not intended to be used as a mathematical spatial reference.
### Note
The Position Reference Indicator may be encoded as zero length when it has no meaning, for example, when the Frame of Reference Module is required to relate mammographic images of the breast acquired without releasing breast compression, but where there is no meaningful anatomical reference point as such.