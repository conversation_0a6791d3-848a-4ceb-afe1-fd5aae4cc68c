#### C.7.2.2 Patient Study Module
Table C.7-4a specifies the Attributes of the Patient Study Module, which provide information about the Patient at the time the Study started.
### Note
In the case of imaging a group of small non-human organisms simultaneously, the Attributes in this Module can only have Values that apply to the entire group, otherwise they are absent (e.g., <PERSON><PERSON>'s Weight (0010,1030)) or empty (e.g., <PERSON><PERSON>'s Sex Neutered (0010,2203).
**Table C.7-4a. Patient Study Module Attributes**
| Attribute Name | Tag | Keyword | VR | VM | Pydicom Types | Type | Attribute Description |
| --- | --- | --- | --- | --- | --- | --- | --- |
| Admitting Diagnoses Description | (0008,1080) | AdmittingDiagnosesDescription | LO | 1-n | str | 3 | Description of the reason(s) the patient sought care. ### Note This may be present even if the patient is not actually admitted. |
| Admitting Diagnoses Code Sequence | (0008,1084) | AdmittingDiagnosesCodeSequence | SQ | 1 | list[Dataset] | 3 | The reason(s) the patient sought care. ### Note This may be present even if the patient is not actually admitted. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Principal Diagnosis Code Sequence | (0008,1301) |  |  |  |  | 3 | Condition that, after investigation, was chiefly responsible for the visit. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Primary Diagnosis Code Sequence | (0008,1302) |  |  |  |  | 3 | Most serious and/or resource-intensive condition during the visit. Only a single Item is permitted in this Sequence. ### Note - The primary diagnosis may or may not be the same as the principal diagnosis. - The use of the term "primary" in this context is not related to its different meaning in the context of cancer, i.e., it is not necessarily a condition that is a primary tumor, which might be encoded as the principal diagnosis, if appropriate. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Secondary Diagnoses Code Sequence | (0008,1303) |  |  |  |  | 3 | Conditions that coexist with the primary diagnosis, and that affect care during the visit. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Histological Diagnoses Code Sequence | (0008,1304) |  |  |  |  | 3 | Diagnoses that have been confirmed by anatomical histopathology of specimens removed from the subject. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Patient's Age | (0010,1010) | PatientAge | AS | 1 | str | 3 | Age of the Patient. |
| Patient's Size | (0010,1020) | PatientSize | DS | 1 | str, float, int | 3 | Length or size of the Patient, in meters. |
| Patient's Weight | (0010,1030) | PatientWeight | DS | 1 | str, float, int | 3 | Weight of the Patient, in kilograms. |
| Patient's Body Mass Index | (0010,1022) | PatientBodyMassIndex | DS | 1 | str, float, int | 3 | Body Mass Index of the Patient in kg/m2. |
| Measured AP Dimension | (0010,1023) | MeasuredAPDimension | DS | 1 | str, float, int | 3 | The thickness in mm of the body part being scanned, in the antero-posterior dimension (per AAPM Report 204). ### Note These values are normally derived from a scanned image, but might also be obtained using physical calipers, e.g., for children. |
| Measured Lateral Dimension | (0010,1024) | MeasuredLateralDimension | DS | 1 | str, float, int | 3 | The side-to-side (left to right) dimension in mm of the body part being scanned (per AAPM Report 204). ### Note These values are normally derived from a scanned image, but might also be obtained using physical calipers, e.g., for children. |
| Patient's Size Code Sequence | (0010,1021) | PatientSizeCodeSequence | SQ | 1 | list[Dataset] | 3 | Patient's size category code One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 7039 Pediatric Size Category](part16.html#sect_CID_7039) for pediatric patients B [CID 7041 Calcium Scoring Patient Size Category](part16.html#sect_CID_7041) for CT calcium scoring |
| Medical Alerts | (0010,2000) | MedicalAlerts | LO | 1-n | str | 3 | Conditions to which medical staff should be alerted (e.g., contagious condition, drug allergies, etc.). |
| Allergies | (0010,2110) | Allergies | LO | 1-n | str | 3 | Description of prior reaction to contrast agents, or other patient allergies or adverse reactions. |
| Smoking Status | (0010,21A0) | SmokingStatus | CS | 1 | str | 3 | Indicates whether Patient smokes. **Enumerated Values:** YES NO UNKNOWN |
| Pregnancy Status | (0010,21C0) | PregnancyStatus | US | 1 | int | 3 | Describes pregnancy state of Patient. **Enumerated Values:** 0001H not pregnant 0002H possibly pregnant 0003H definitely pregnant 0004H unknown |
| Last Menstrual Date | (0010,21D0) | LastMenstrualDate | DA | 1 | str | 3 | Date of onset of last menstrual period. |
| Patient State | (0038,0500) | PatientState | LO | 1 | str | 3 | Description of Patient state (comatose, disoriented, vision impaired, etc.). |
| Occupation | (0010,2180) | Occupation | SH | 1 | str | 3 | Occupation of the Patient. |
| Additional Patient History | (0010,21B0) | AdditionalPatientHistory | LT | 1 | str | 3 | Additional information about the Patient's medical history. |
| Admission ID | (0038,0010) | AdmissionID | LO | 1 | str | 3 | Identifier of the Visit as assigned by the healthcare provider ### Note Visit and Admission are used interchangeably here. In the broader sense, an admission is a type of visit at an institution where there is an admission process for patients. |
| Issuer of Admission ID Sequence | (0038,0014) | IssuerOfAdmissionIDSequence | SQ | 1 | list[Dataset] | 3 | Identifier of the Assigning Authority that issued Admission ID (0038,0010). Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-17 HL7v2 Hierarchic Designator Macro Attributes |  |  |  |  |  |  |  |
| Reason for Visit | (0032,1066) | ReasonForVisit | UT | 1 | str | 3 | Reason(s) for this visit by the patient to the facility or provider. ### Note This might or might not be the same Value as Admitting Diagnoses Description (0008,1080). |
| Reason for Visit Code Sequence | (0032,1067) | ReasonForVisitCodeSequence | SQ | 1 | list[Dataset] | 3 | Coded reason(s) for this visit by the patient to the facility or provider. ### Note This might or might not be the same Value as Admitting Diagnoses Description (0008,1080). One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | No Baseline CID is defined. |
| Service Episode ID | (0038,0060) | ServiceEpisodeID | LO | 1 | str | 3 | Identifier of the Service Episode as assigned by the healthcare provider. |
| Issuer of Service Episode ID Sequence | (0038,0064) | IssuerOfServiceEpisodeIDSequence | SQ | 1 | list[Dataset] | 3 | Identifier of the Assigning Authority that issued the Service Episode ID (0038,0060). Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-17 HL7v2 Hierarchic Designator Macro Attributes |  |  |  |  |  |  |  |
| Service Episode Description | (0038,0062) | ServiceEpisodeDescription | LO | 1 | str | 3 | Description of the type of service episode. |
| Patient's Sex Neutered | (0010,2203) | PatientSexNeutered | CS | 1 | str | 2C | Whether or not a procedure has been performed in an effort to render the Patient sterile. **Enumerated Values:** ALTERED Altered/Neutered UNALTERED Unaltered/intact ### Note If this Attribute is present but has no Value then the status is unknown. Required if Patient is a non-human organism. May be present otherwise. |
| Gender Identity Sequence | (0010,0041) |  |  |  |  | 3 | An individual's personal sense of being a man, woman, boy, girl, nonbinary, or something else, ascertained by asking them what their identity is. One or more Items are permitted in this Sequence. See Section C.*******.5. |
| &gt;Gender Identity Code Sequence | (0010,0044) |  |  |  |  | 1 | A coded gender identity. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 7458 Person Gender Identity](part16.html#sect_CID_7458). |
| &gt;Effective Start DateTime | (0040,A034) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item begins to be applicable. See Section C.*******.5. |
| &gt;Effective Stop DateTime | (0040,A035) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item ceases to be applicable. See Section C.*******.5. |
| &gt;Gender Identity Comment | (0010,0045) |  |  |  |  | 3 | Comments on this gender identity, such as the context in which it should be used. |
| Sex Parameters for Clinical Use Category Sequence | (0010,0043) |  |  |  |  | 3 | Guidance on how to apply settings or reference ranges that are derived from observable information such as an organ inventory, recent hormone lab tests, genetic testing, menstrual status, obstetric history, etc. One or more Items are permitted in this Sequence. See Section C.*******.2. |
| &gt;Sex Parameters for Clinical Use Category Code Sequence | (0010,0046) |  |  |  |  | 1 | The category of this Sex Parameter for Clinical Use (SPCU). Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 7459 Category of Sex Parameters for Clinical Use](part16.html#sect_CID_7459). |
| &gt;Effective Start DateTime | (0040,A034) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item begins to be applicable. See Section C.*******.5. |
| &gt;Effective Stop DateTime | (0040,A035) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item ceases to be applicable. See Section C.*******.5. |
| &gt;Sex Parameters for Clinical Use Category Comment | (0010,0042) |  |  |  |  | 2C | Further description of clinical implications and reasons for the selected code. Required if Sex Parameters for Clinical Use Category Code Sequence (0010,0046) is [(131232, DCM, "Specified")](part16.html#DCM_131232). May be present otherwise. |
| &gt;Sex Parameters for Clinical Use Category Reference | (0010,0047) |  |  |  |  | 2C | Reference to a resource that explains or extends the Sex Parameters for Clinical Use Category Code. Required if Sex Parameters for Clinical Use Category Code Sequence (0010,0046) is [(131232, DCM, "Specified")](part16.html#DCM_131232). May be present otherwise. |
| Person Names to Use Sequence | (0010,0011) |  |  |  |  | 3 | The name(s) that should be used when addressing or referencing the person. One or more Items are permitted in this Sequence. |
| &gt;Name to Use | (0010,0012) |  |  |  |  | 1 | A name that should be used when addressing or referencing the person. This need not be an official name nor comply with any particular name structure. See Section C.*******.3. |
| &gt;Effective Start DateTime | (0040,A034) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item begins to be applicable. See Section C.*******.5. |
| &gt;Effective Stop DateTime | (0040,A035) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item ceases to be applicable. See Section C.*******.5. |
| &gt;Name to Use Comment | (0010,0013) |  |  |  |  | 3 | Further explanation of appropriate name usage. |
| Third Person Pronouns Sequence | (0010,0014) |  |  |  |  | 3 | Pronoun(s) specified by the patient to use when referring to the patient in speech, in clinical notes, and in written instructions to caregivers. One or more Items are permitted in this Sequence. See Section C.*******.4. |
| &gt;Pronoun Code Sequence | (0010,0015) |  |  |  |  | 1 | A single code that specifies the set of third person pronouns to be used in reference to this patient. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |  |  |  |  |  |  | B [CID 7448 Third Person Pronoun Set](part16.html#sect_CID_7448). |
| &gt;Effective Start DateTime | (0040,A034) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item begins to be applicable. See Section C.*******.5. |
| &gt;Effective Stop DateTime | (0040,A035) |  |  |  |  | 3 | The date and time at which the content of this Sequence Item ceases to be applicable. See Section C.*******.5. |
| &gt;Pronoun Comment | (0010,0016) |  |  |  |  | 3 | Further explanation of pronoun usage. |
##### C.******* Patient Study Module Attribute Descriptions
This DICOM specification follows the logical model described in HL7  [  HL7 Gender Harmony Model  ] . Refer to the HL7 document for more detailed descriptions of the concepts and codes and guidance on the corresponding encodings in HL7v2  [  HL7 V2.9.1  ] , CDA  [  HL7 CDA R2.0 Gender Harmony IG  ] , and FHIR  [  HL7 FHIR 5 Patient Gender  ] .
### Note
The details captured in the following Sequences may or may not reflect the complete corresponding content of the medical record for the patient. It is typical for the items here to be limited to information considered relevant to the performance or interpretation of this Study.
###### C.*******.1 Gender Identity Sequence
Gender Identity Sequence (0010,0041) describes the identity of the patient. This is important in many social and cultural contexts. The meaning, criteria, and implications of specific gender identities is defined by the local culture and society. The terms used to capture gender identity are expected to reflect the variations found in different cultures and location, so local terminology is expected to extend this Value Set.
If the patient (such as a fetus, infant, or uncommunicative new patient) is unable to express a gender identity it may be missing. The Sequence may be absent in cases where the patient does not want to specify a value. Gender identity can be congruent or incongruent with ones Sex Parameters for Clinical Use Category Sequence (0010,0043). Patients may identify using different terms at different times for various reasons or use multiple identities in different contexts during the same time interval.
Given that the gender identity element supports representing multiple gender identities, individuals who identify as having both male and female gender identities (or any other combination) at the same time in different contexts, each gender identity can be modeled with the same effective period.
###### C.*******.2 Sex Parameters for Clinical Use Category Sequence
The Sex Parameters for Clinical Use Category Sequence (0010,0043) is used in orders, observations, and other clinical situations. The Sex Parameters for Clinical Use Category Sequence (0010,0043) allows specific considerations to be provided for potential automated uses and records. These may be reference ranges, procedure setup, diagnostic algorithm parameters, etc. For example, the computation of glomerular filtration rate (GFR) based on blood chemistry may use formulas that are different for "male" and "female".
There are many other situations involving tumors, resections, congenital conditions (e.g., ovotestes), and transgender patients where Sex Parameters for Clinical Use Category Sequence (0010,0043) can be used to provide information that is needed to perform a procedure properly.
###### C.*******.3 Person Names to Use Sequence
The Person Names to Use Sequence (0010,0011) enables names that are chosen by the patient to be used by care providers in patient-centered healthcare conversations. This information is usually provided by the patient and may be different from their legal name. Some cultures have very long names and expect those to be used only for mandatory legal situations. Also, rules and processes for legal name changes vary. There is often a mismatch that can be prolonged in difficult situations, and Person Names to Use Sequence (0010,0011) may be an expedient solution for the care environment.
If different names are to be used in different contexts, that can be explained in the Name to Use Comment (0010,0013).
### Note
The Value Representation of this Name to Use (0010,0012) is a long text string (LT) rather than a person name (PN) to avoid any constraints on the structure of the name. The Name to Use (0010,0012) need not be an official name of any sort, nor does it need to comply with any standard naming structure.
###### C.*******.4 Third Person Pronouns Sequence
Personal pronouns are words used instead of a noun or a noun phrase used to refer to people. Understanding which pronoun(s) to use when referring to someone is important for providing gender affirming healthcare. Avoiding incorrect pronoun use and patient misgendering is crucial in transgender and gender-diverse care. It is important to reliably exchange personal pronouns that the individual has specifically reported they want to use. Local policy will determine how pronouns are chosen for infants and other similar situations. Policy and local customs will determine what to use when this Attribute is not present, or when multiple sets are present.
Different pronouns may be used in one care setting than another care setting. The pronouns used in the work environment may be different than those in the care setting.
###### C.*******.5 Effective Start DateTime and Effective Stop DateTime
Each Sequence Item may have an Effective Start DateTime (0040,A034) and Effective Stop DateTime (0040,A035) specifying the time interval during which the content of the Item applies. These Attributes are optional.They are included when they are expected to be relevant
- If Effective Start DateTime (0040,A034) is missing, then the Item content applies for all times before Effective Stop DateTime (0040,A035).
- If Effective Stop DateTime (0040,A035) is missing, then the Item content applies for all times after Effective Start DateTime (0040,A034).
- If both are missing, the Item content applies for all times past and future.
These Attributes can be particularly useful when there are multiple Items in the Sequence. For example, a male at birth has a subsequent orchiectomy for testicular cancer. This could be represented as an Sex Parameters for Clinical Use Category Sequence (0010,0043) Item of Male-typical parameters with an Effective Start DateTime (0040,A034) at birth date and an Effective Stop DateTime (0040,A035) at about the date of orchiectomy, and a second Item of Neither male typical nor female typical parameters with an Effective Start DateTime (0040,A034) at about the date of orchiectomy and Effective Stop DateTime (0040,A035) is missing.