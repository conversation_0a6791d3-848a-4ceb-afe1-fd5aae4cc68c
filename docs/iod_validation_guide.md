# DICOM IOD Validation Guide

## Overview

This guide provides comprehensive documentation for implementing and maintaining DICOM Information Object Definition (IOD) validation in the PyRT-DICOM library. IOD validation ensures that complete DICOM objects, composed of multiple modules, comply with DICOM PS3.3 specifications and meet the requirements for specific clinical workflows.

**Scope**: This guide focuses exclusively on **IOD-level validation**. For module-level validation patterns, see the [Module Validation Guide](./module_validation_guide.md).

## Architecture Overview

### IOD Validation vs Module Validation

IOD validation differs from module validation in several key aspects:

| Aspect | Module Validation | IOD Validation |
|--------|-------------------|----------------|
| **Scope** | Single module requirements | Complete IOD with multiple modules |
| **Dependencies** | Module-internal only | Cross-module dependencies and consistency |
| **Requirements** | Type 1, 1C, 2, 2C, 3 elements | Required (M), Conditional (C), User Optional (U) modules |
| **Validation Focus** | Individual element validation | Module presence, orchestration, and IOD-specific constraints |
| **Return Type** | ValidationResult | ValidationResult with module-level aggregation |

### IOD Validation Infrastructure

The IOD validation system consists of four main components:

1. **BaseValidator** (`src/pyrt_dicom/validators/modules/base_validator.py`)
   - Inherited utility methods for common validation patterns
   - ValidationResult creation and management
   - Consistent error message formatting

2. **ValidationConfig** (`src/pyrt_dicom/validators/modules/base_validator.py`)
   - Configures validation behavior across all validation levels
   - Controls module-level, cross-module, and IOD-specific validation

3. **IOD-Specific Validators** (e.g., `RTDoseIODValidator`)
   - Implement DICOM PS3.3 IOD requirements for specific object types
   - Coordinate module validators and validate IOD-level constraints
   - Return structured ValidationResult objects

4. **ValidationResult** (`src/pyrt_dicom/validators/validation_result.py`)
   - Structured result objects with rich properties and methods
   - Support for merging results from multiple validators with prefixes
   - Backward compatibility with dict-based validation patterns

### Design Principles

- **Comprehensive Module Orchestration**: IOD validators coordinate multiple module validators
- **Cross-Module Validation**: Validates dependencies and consistency between modules
- **IOD-Specific Constraints**: Implements requirements unique to each IOD type
- **External Dataset Support**: Works with any pydicom Dataset, not just PyRT-DICOM created ones
- **Structured Results**: All validators return ValidationResult objects with rich properties
- **Configurable Validation**: Use ValidationConfig to enable/disable validation categories
- **Hierarchical Error Reporting**: Clear attribution of errors to specific modules or IOD-level issues

## Current Implementation Status

### ✅ Implemented IOD Validators

| IOD | Validator | DICOM Reference | Status | Notes |
|-----|-----------|-----------------|--------|-------|
| RT Dose IOD | RTDoseIODValidator | PS3.3 A.18.3 | Complete | Production-ready with comprehensive module orchestration |

### ❌ Missing IOD Validators (Roadmap)

#### Phase 1 - Core RT IODs
| IOD | DICOM Reference | Priority | Notes |
|-----|-----------------|----------|-------|
| RT Plan IOD | PS3.3 A.18.1 | Critical | Treatment planning workflow support |
| RT Structure Set IOD | PS3.3 A.18.2 | Critical | ROI and contour management |
| RT Image IOD | PS3.3 A.18.4 | High | Portal and verification images |

#### Phase 2 - Advanced RT IODs  
| IOD | DICOM Reference | Priority | Notes |
|-----|-----------------|----------|-------|
| RT Ion Plan IOD | PS3.3 A.18.7 | Medium | Ion beam therapy support |
| RT Brachy Plan IOD | PS3.3 A.18.8 | Medium | Brachytherapy planning |
| RT Treatment Record IOD | PS3.3 A.18.5 | Low | Treatment delivery records |

#### Phase 3 - General IODs
| IOD | DICOM Reference | Priority | Notes |
|-----|-----------------|----------|-------|
| CT Image IOD | PS3.3 A.3.3 | Medium | CT image validation |
| MR Image IOD | PS3.3 A.4.3 | Medium | MR image validation |
| Secondary Capture IOD | PS3.3 A.8.1 | Low | Screen captures and derived images |

## IOD Validation Patterns and Best Practices

### Basic IOD Validator Structure

All IOD validators should follow this comprehensive pattern:

```python
"""IOD Name DICOM validation - PS3.3 Reference"""

from pydicom import Dataset
from pydicom import uid
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class IODNameIODValidator(BaseValidator):
    """Validator for DICOM IOD Name (PS3.3 Reference).
    
    This validator implements comprehensive validation for IOD Name IODs
    based on the DICOM PS3.3 specification. It validates:
    - Required modules presence and compliance
    - Conditional modules based on data characteristics
    - Optional modules when present
    - Cross-module dependencies and consistency
    - IOD-specific requirements and constraints
    """
    
    # IOD-specific SOP Class UID constant
    IOD_SOP_CLASS_UID = "1.2.840.10008.*******.1.x.x"
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate IOD Name requirements.
        
        Args:
            dataset: pydicom Dataset to validate against IOD requirements
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Step 1: Validate base IOD requirements (SOP Class/Instance UIDs)
        IODNameIODValidator._validate_base_iod_requirements(dataset, result)
        
        # Step 2: Validate required modules are present and valid
        IODNameIODValidator._validate_required_modules(dataset, result, config)
        
        # Step 3: Validate conditional modules if applicable
        IODNameIODValidator._validate_conditional_modules(dataset, result, config)
        
        # Step 4: Validate optional modules if present
        if config.validate_optional_modules:
            IODNameIODValidator._validate_optional_modules(dataset, result, config)
        
        # Step 5: Validate cross-module dependencies
        if config.validate_cross_module_dependencies:
            IODNameIODValidator._validate_cross_module_dependencies(dataset, result, config)
        
        # Step 6: Validate IOD-specific requirements
        if config.validate_conditional_requirements:
            IODNameIODValidator._validate_iod_specific_requirements(dataset, result)
        
        return result
```

### IOD Validation Categories

#### 1. Base IOD Requirements Validation

All IOD validators must validate fundamental DICOM object requirements:

```python
@staticmethod
def _validate_base_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
    """Validate base IOD requirements (SOP Class and Instance UIDs)."""
    # Validate SOP Class UID
    sop_class_uid = getattr(dataset, 'SOPClassUID', None)
    if not sop_class_uid:
        result.add_error("SOP Class UID (0008,0016) is required for IOD")
    elif sop_class_uid != IODNameIODValidator.IOD_SOP_CLASS_UID:
        result.add_error(
            f"SOP Class UID must be '{IODNameIODValidator.IOD_SOP_CLASS_UID}' for IOD Name IOD, "
            f"got '{sop_class_uid}'"
        )
    
    # Validate SOP Instance UID
    sop_instance_uid = getattr(dataset, 'SOPInstanceUID', None)
    if not sop_instance_uid:
        result.add_error("SOP Instance UID (0008,0018) is required for IOD")
    
    # Optional: Validate UID format and uniqueness
    if sop_instance_uid and not IODNameIODValidator._is_valid_uid(sop_instance_uid):
        result.add_warning(f"SOP Instance UID format may be invalid: {sop_instance_uid}")

@staticmethod
def _is_valid_uid(uid_value: str) -> bool:
    """Validate UID format per DICOM PS3.5."""
    import re
    # UID pattern: numbers and dots, max 64 chars, no leading/trailing dots
    pattern = r'^[0-9]+(\.[0-9]+)*$'
    return bool(re.match(pattern, uid_value)) and len(uid_value) <= 64
```

#### 2. Required Modules Validation

IOD validators must orchestrate validation of all required modules:

```python
@staticmethod
def _validate_required_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
    """Validate that all required modules are present and valid.
    
    Required modules per DICOM PS3.3 Reference:
    - Module A (C.x.x.x) - M
    - Module B (C.y.y.y) - M
    - Module C (C.z.z.z) - M
    """
    # Import module validators here to avoid circular imports
    from ..modules.module_a_validator import ModuleAValidator
    from ..modules.module_b_validator import ModuleBValidator
    from ..modules.module_c_validator import ModuleCValidator
    
    # Required modules mapping: (validator, module_name, key_elements)
    required_modules = [
        (ModuleAValidator, 'Module A', ['KeyElementA1', 'KeyElementA2']),
        (ModuleBValidator, 'Module B', ['KeyElementB1']),
        (ModuleCValidator, 'Module C', ['KeyElementC1', 'KeyElementC2', 'KeyElementC3'])
    ]
    
    for validator, module_name, key_elements in required_modules:
        # Check if module is present (any key element exists)
        module_present = any(hasattr(dataset, element) for element in key_elements)
        
        if not module_present:
            result.add_error(f"{module_name} is required for IOD Name IOD but appears to be missing")
        else:
            # Module is present, validate it and merge results with prefix
            module_result = validator.validate(dataset, config)
            result.merge(module_result, prefix=f"{module_name}")
```

#### 3. Conditional Modules Validation

Handle modules that are conditionally required based on data characteristics:

```python
@staticmethod
def _validate_conditional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
    """Validate conditional modules if present.
    
    Conditional modules per DICOM PS3.3 Reference:
    - Module D (C.a.a.a) - C (Required if condition X is met)
    - Module E (C.b.b.b) - C (Required if condition Y is met)
    """
    from ..modules.module_d_validator import ModuleDValidator
    from ..modules.module_e_validator import ModuleEValidator
    
    # Define conditional modules with their conditions
    conditional_modules = [
        # Module D required when condition X is met
        (ModuleDValidator, 'Module D', 
         IODNameIODValidator._check_condition_x(dataset),
         ['ModuleDKeyElement'], 'condition X is met'),
         
        # Module E required when condition Y is met
        (ModuleEValidator, 'Module E',
         IODNameIODValidator._check_condition_y(dataset),
         ['ModuleEKeyElement'], 'condition Y is met')
    ]
    
    for validator, module_name, condition, key_elements, condition_desc in conditional_modules:
        module_present = any(hasattr(dataset, element) for element in key_elements)
        
        if condition and not module_present:
            result.add_error(f"{module_name} is required when {condition_desc}")
        elif module_present:
            # Module is present, validate it regardless of condition
            module_result = validator.validate(dataset, config)
            result.merge(module_result, prefix=f"{module_name}")

@staticmethod
def _check_condition_x(dataset: Dataset) -> bool:
    """Check if condition X is met for conditional module requirement."""
    # Implementation-specific condition logic
    return hasattr(dataset, 'ConditionalTriggerElement')

@staticmethod  
def _check_condition_y(dataset: Dataset) -> bool:
    """Check if condition Y is met for conditional module requirement."""
    # Implementation-specific condition logic
    return getattr(dataset, 'NumericElement', 0) > 1
```

#### 4. Optional Modules Validation

Validate optional modules when present:

```python
@staticmethod
def _validate_optional_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
    """Validate optional modules if present.
    
    Optional modules per DICOM PS3.3 Reference:
    - Module F (C.x.x.x) - U
    - Module G (C.y.y.y) - U
    """
    from ..modules.module_f_validator import ModuleFValidator
    from ..modules.module_g_validator import ModuleGValidator
    
    # Define optional modules
    optional_modules = [
        (ModuleFValidator, 'Module F', ['ModuleFKeyElement1', 'ModuleFKeyElement2']),
        (ModuleGValidator, 'Module G', ['ModuleGKeyElement'])
    ]
    
    for validator, module_name, key_elements in optional_modules:
        module_present = any(hasattr(dataset, element) for element in key_elements)
        
        if module_present:
            # Optional module is present, validate it
            module_result = validator.validate(dataset, config)
            result.merge(module_result, prefix=f"{module_name}")
```

#### 5. Cross-Module Dependencies Validation

Validate consistency and dependencies across modules:

```python
@staticmethod
def _validate_cross_module_dependencies(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
    """Validate cross-module dependencies and consistency."""
    # Import cross-module validators
    from ..cross_module.uid_consistency_validator import UIDConsistencyValidator
    from ..cross_module.spatial_consistency_validator import SpatialConsistencyValidator
    from ..cross_module.temporal_consistency_validator import TemporalConsistencyValidator
    
    # Validate UID consistency across modules
    uid_result = UIDConsistencyValidator.validate(dataset, config)
    result.merge(uid_result, prefix="UID Consistency")
    
    # Validate spatial consistency if applicable
    if IODNameIODValidator._has_spatial_data(dataset):
        spatial_result = SpatialConsistencyValidator.validate(dataset, config)
        result.merge(spatial_result, prefix="Spatial Consistency")
    
    # Validate temporal consistency if applicable
    if IODNameIODValidator._has_temporal_data(dataset):
        temporal_result = TemporalConsistencyValidator.validate(dataset, config)
        result.merge(temporal_result, prefix="Temporal Consistency")

@staticmethod
def _has_spatial_data(dataset: Dataset) -> bool:
    """Check if dataset contains spatial information requiring validation."""
    spatial_elements = ['ImagePositionPatient', 'ImageOrientationPatient', 
                       'PixelSpacing', 'FrameOfReferenceUID']
    return any(hasattr(dataset, element) for element in spatial_elements)

@staticmethod
def _has_temporal_data(dataset: Dataset) -> bool:
    """Check if dataset contains temporal information requiring validation."""
    temporal_elements = ['StudyDate', 'StudyTime', 'SeriesDate', 'SeriesTime', 
                        'AcquisitionDate', 'AcquisitionTime']
    return any(hasattr(dataset, element) for element in temporal_elements)
```

#### 6. IOD-Specific Requirements Validation

Validate constraints and business rules specific to the IOD:

```python
@staticmethod
def _validate_iod_specific_requirements(dataset: Dataset, result: ValidationResult) -> None:
    """Validate IOD Name-specific requirements."""
    # Validate IOD-specific modality requirements
    modality = getattr(dataset, 'Modality', None)
    if modality and modality != 'EXPECTED_MODALITY':
        result.add_error(f"Modality must be 'EXPECTED_MODALITY' for IOD Name IOD, got '{modality}'")
    
    # Validate IOD-specific parameter constraints
    IODNameIODValidator._validate_iod_parameters(dataset, result)
    
    # Validate IOD-specific business rules
    IODNameIODValidator._validate_business_rules(dataset, result)

@staticmethod
def _validate_iod_parameters(dataset: Dataset, result: ValidationResult) -> None:
    """Validate IOD-specific parameters."""
    # Example: Validate parameter ranges or combinations
    param_value = getattr(dataset, 'SpecificParameter', None)
    if param_value is not None:
        if not 0 <= param_value <= 100:
            result.add_error(f"Specific Parameter must be between 0 and 100, got {param_value}")

@staticmethod
def _validate_business_rules(dataset: Dataset, result: ValidationResult) -> None:
    """Validate IOD-specific business rules."""
    # Example: Validate clinical workflow constraints
    workflow_status = getattr(dataset, 'WorkflowStatus', '')
    approval_status = getattr(dataset, 'ApprovalStatus', '')
    
    if workflow_status == 'COMPLETED' and approval_status != 'APPROVED':
        result.add_warning("Completed workflows should typically be approved")
```

### Error Message Guidelines for IODs

- **Module Attribution**: Prefix errors with module names for clarity
- **IOD Context**: Include IOD type in error messages
- **DICOM References**: Reference PS3.3 sections for IOD requirements
- **Cross-Module Issues**: Clearly identify when errors span multiple modules

Example error messages:
- `"Patient Module: Patient Name (0010,0010) is required (Type 1)"`
- `"RT Dose IOD: General Image Module is required when dose data contains grid-based doses"`
- `"Spatial Consistency: Frame of Reference UID mismatch between modules"`

## Implementation Templates

### Simple IOD Validator Template

For IODs with minimal complexity and few conditional requirements:

```python
"""Simple IOD DICOM validation - PS3.3 Reference"""

from pydicom import Dataset
from pydicom import uid
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class SimpleIODValidator(BaseValidator):
    """Validator for DICOM Simple IOD (PS3.3 Reference)."""
    
    SIMPLE_IOD_SOP_CLASS_UID = "1.2.840.10008.*******.1.x.x"
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Simple IOD requirements."""
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate base IOD requirements
        SimpleIODValidator._validate_base_iod_requirements(dataset, result)
        
        # Validate required modules
        SimpleIODValidator._validate_required_modules(dataset, result, config)
        
        # Validate IOD-specific requirements
        if config.validate_conditional_requirements:
            SimpleIODValidator._validate_simple_iod_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_base_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate base IOD requirements."""
        sop_class_uid = getattr(dataset, 'SOPClassUID', None)
        if not sop_class_uid:
            result.add_error("SOP Class UID (0008,0016) is required")
        elif sop_class_uid != SimpleIODValidator.SIMPLE_IOD_SOP_CLASS_UID:
            result.add_error(f"Invalid SOP Class UID for Simple IOD: {sop_class_uid}")
    
    @staticmethod
    def _validate_required_modules(dataset: Dataset, result: ValidationResult, config: ValidationConfig) -> None:
        """Validate required modules."""
        # Import and validate required modules
        from ..modules.patient_validator import PatientValidator
        from ..modules.general_study_validator import GeneralStudyValidator
        
        required_modules = [
            (PatientValidator, 'Patient Module', ['PatientName', 'PatientID']),
            (GeneralStudyValidator, 'General Study Module', ['StudyInstanceUID'])
        ]
        
        for validator, module_name, key_elements in required_modules:
            module_present = any(hasattr(dataset, element) for element in key_elements)
            
            if not module_present:
                result.add_error(f"{module_name} is required but missing")
            else:
                module_result = validator.validate(dataset, config)
                result.merge(module_result, prefix=module_name)
    
    @staticmethod
    def _validate_simple_iod_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Simple IOD-specific requirements."""
        # IOD-specific validation logic
        pass
```

### Complex IOD Validator Template

For IODs with extensive module orchestration and conditional logic:

```python
"""Complex IOD DICOM validation - PS3.3 Reference"""

from pydicom import Dataset
from pydicom import uid
from ..modules.base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class ComplexIODValidator(BaseValidator):
    """Validator for DICOM Complex IOD (PS3.3 Reference).
    
    This validator handles complex IODs with:
    - Multiple required modules
    - Complex conditional module logic  
    - Extensive cross-module dependencies
    - IOD-specific business rules
    """
    
    COMPLEX_IOD_SOP_CLASS_UID = "1.2.840.10008.*******.1.x.x"
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Complex IOD requirements."""
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Step 1: Validate base IOD requirements
        ComplexIODValidator._validate_base_iod_requirements(dataset, result)
        
        # Step 2: Validate required modules
        ComplexIODValidator._validate_required_modules(dataset, result, config)
        
        # Step 3: Validate conditional modules
        ComplexIODValidator._validate_conditional_modules(dataset, result, config)
        
        # Step 4: Validate optional modules if enabled
        if config.validate_optional_modules:
            ComplexIODValidator._validate_optional_modules(dataset, result, config)
        
        # Step 5: Validate cross-module dependencies
        if config.validate_cross_module_dependencies:
            ComplexIODValidator._validate_cross_module_dependencies(dataset, result, config)
        
        # Step 6: Validate IOD-specific requirements
        if config.validate_conditional_requirements:
            ComplexIODValidator._validate_complex_iod_requirements(dataset, result)
        
        return result
    
    # Implementation methods following the patterns shown above
    # ... (methods for each validation step)
```

## Real-World Example: RT Dose IOD Validator

The RTDoseIODValidator provides a production example of comprehensive IOD validation:

### Key Features Demonstrated

1. **Comprehensive Module Orchestration**: Validates 7 required modules plus conditional and optional modules
2. **Complex Conditional Logic**: Handles grid-based dose data requirements and multi-frame pixel data
3. **Cross-Module Dependencies**: Validates Frame of Reference consistency and RT-specific references  
4. **IOD-Specific Constraints**: Enforces RT Dose-specific requirements like modality validation
5. **Rich Error Attribution**: Uses module prefixes for clear error source identification

### Validation Flow Example

```python
# RT Dose IOD validation flow
result = RTDoseIODValidator.validate(dataset, config)

# Results include:
# - Base IOD errors: "SOP Class UID (0008,0016) is required for RT Dose IOD"
# - Module errors: "Patient Module: Patient Name (0010,0010) is required (Type 1)"  
# - Conditional errors: "General Image Module is required when dose data contains grid-based doses"
# - Cross-module errors: "UID Consistency: Frame of Reference UID mismatch between modules"
# - IOD-specific errors: "RT Series Module modality must be 'RTDOSE' for RT Dose IOD"
```

## Testing and Quality Assurance

### IOD Testing Strategy

1. **Unit Tests**: Test each IOD validator with known valid and invalid datasets
2. **Module Integration Tests**: Test IOD validators with various module combinations  
3. **Cross-Module Tests**: Test cross-module dependency validation
4. **Real-World Data Tests**: Test with actual DICOM files from clinical systems
5. **Edge Case Tests**: Test boundary conditions and unusual but valid combinations

### Test Data Requirements

- **Complete IODs**: Valid datasets with all required modules present
- **Incomplete IODs**: Datasets missing required modules for error testing
- **Conditional Variations**: Datasets that trigger different conditional module requirements
- **Cross-Module Scenarios**: Datasets with intentional cross-module inconsistencies
- **IOD-Specific Cases**: Datasets that test IOD-specific business rules and constraints

### IOD Validation Test Template

```python
import pytest
from pydicom import Dataset
from pyrt_dicom.validators.iods.iod_name_iod_validator import IODNameIODValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig


class TestIODNameIODValidator:
    def test_valid_complete_iod(self):
        """Test validation of valid complete IOD."""
        ds = self._create_valid_iod_dataset()
        
        result = IODNameIODValidator.validate(ds)
        assert result.is_valid
        assert result.error_count == 0
    
    def test_missing_required_module(self):
        """Test validation fails when required module is missing."""
        ds = self._create_incomplete_iod_dataset()
        
        result = IODNameIODValidator.validate(ds)
        assert not result.is_valid
        assert result.error_count > 0
        assert "Module A is required" in str(result.errors)
    
    def test_conditional_module_requirements(self):
        """Test conditional module validation."""
        ds = self._create_conditional_trigger_dataset()
        
        result = IODNameIODValidator.validate(ds)
        # Should require conditional module when condition is met
        assert "Module D is required when condition X is met" in str(result.errors)
    
    def test_cross_module_consistency(self):
        """Test cross-module dependency validation."""
        ds = self._create_inconsistent_modules_dataset()
        
        config = ValidationConfig(validate_cross_module_dependencies=True)
        result = IODNameIODValidator.validate(ds, config)
        assert "Consistency" in str(result.errors)
    
    def test_iod_specific_requirements(self):
        """Test IOD-specific validation rules."""
        ds = self._create_invalid_iod_specific_dataset()
        
        result = IODNameIODValidator.validate(ds)
        assert "IOD Name IOD" in str(result.errors)
    
    def test_validation_config_options(self):
        """Test validation configuration options."""
        ds = self._create_dataset_with_optional_issues()
        
        # Test with optional module validation disabled
        config = ValidationConfig(validate_optional_modules=False)
        result = IODNameIODValidator.validate(ds, config)
        
        # Should not include optional module errors
        assert "Optional" not in str(result.errors)
    
    # Helper methods to create test datasets
    def _create_valid_iod_dataset(self) -> Dataset:
        """Create a valid complete IOD dataset."""
        ds = Dataset()
        # Add all required elements for valid IOD
        ds.SOPClassUID = IODNameIODValidator.IOD_SOP_CLASS_UID
        ds.SOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        # Add required module elements...
        return ds
    
    def _create_incomplete_iod_dataset(self) -> Dataset:
        """Create IOD dataset missing required modules."""
        ds = Dataset()
        ds.SOPClassUID = IODNameIODValidator.IOD_SOP_CLASS_UID
        ds.SOPInstanceUID = "1.2.3.4.5.6.7.8.9"
        # Intentionally omit required module elements
        return ds
    
    # Additional helper methods...
```

## Integration with PyRT-DICOM IODs

### IOD Class Integration

PyRT-DICOM IOD classes should integrate validation seamlessly:

```python
class ExampleIOD(BaseIOD):
    """Example IOD with integrated validation."""
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this IOD instance."""
        dataset = self.to_dataset()
        return ExampleIODValidator.validate(dataset, config)
    
    def to_validated_dataset(self, config: ValidationConfig | None = None) -> tuple[Dataset, ValidationResult]:
        """Generate validated dataset."""
        dataset = self.to_dataset()
        validation_result = ExampleIODValidator.validate(dataset, config)
        return dataset, validation_result
    
    def is_valid(self, config: ValidationConfig | None = None) -> bool:
        """Check if IOD is valid."""
        return self.validate(config).is_valid
```

### Factory Method Validation

IOD factory methods should validate construction parameters:

```python
@classmethod
def for_clinical_workflow(cls, **kwargs) -> 'ExampleIOD':
    """Factory method with validation."""
    # Create IOD instance
    iod = cls._construct_from_params(**kwargs)
    
    # Validate construction
    result = iod.validate()
    if not result.is_valid:
        raise IODValidationError(f"IOD construction validation failed: {result.errors}")
    
    return iod
```

## Next Steps

1. **Implement Core RT IODs**: Create validators for RT Plan, RT Structure Set, and RT Image IODs
2. **Cross-Module Validator Framework**: Develop reusable cross-module validation components
3. **Advanced Testing Infrastructure**: Create comprehensive test data sets and validation scenarios
4. **Performance Optimization**: Optimize validation performance for large datasets and complex IODs
5. **Documentation Integration**: Create IOD-specific validation documentation as validators are implemented

## Conclusion

This IOD validation guide provides the foundation for implementing comprehensive DICOM IOD validation that builds upon the module-level validation framework. IOD validators orchestrate multiple module validators while adding IOD-specific requirements, cross-module consistency checks, and rich error reporting.

The key differences from module validation are the multi-module orchestration, cross-module dependency validation, and IOD-specific business rule enforcement. By following these patterns and the RTDoseIODValidator example, developers can create robust, maintainable IOD validation that ensures DICOM compliance and clinical workflow requirements.

**Remember**: IOD validation complements and builds upon module validation - both work together to ensure comprehensive DICOM compliance from individual elements to complete clinical objects.