# Module Implementation Plan

This document outlines the implementation strategy for DICOM modules with a focus on user-friendly IntelliSense support and intuitive APIs. All modules inherit from BaseModule and the PatientModule serves as the exemplar for all future module implementations.

## BaseModule Abstract Base Class

**All DICOM modules inherit from BaseModule which provides:**

### 🏗️ Core Infrastructure
- **pydicom.Dataset Inheritance**: Native DICOM data handling through Dataset inheritance
- **Abstract Method Enforcement**: Ensures all modules implement `from_required_elements()`
- **Validation Framework**: Common validation interface with structured error/warning reporting
- **Helper Methods**: Common utilities for date/time formatting, enum handling, and conditional validation

### 🛠️ Helper Methods Available to All Modules
- **`_validate_conditional_requirement(condition, required_values, error_message)`**: Validate Type 1C/2C requirements

### 🔍 Common Properties
- **`module_name`**: Get the module class name
- **`has_data`**: Check if module contains any DICOM data elements
- **`get_element_count()`**: Get number of DICOM data elements
- **`get_element_tags()`**: Get list of DICOM tags present in module

### ⚠️ Important Constraints
- **No Save Methods**: Modules are NOT intended to be saved directly - file operations are handled by IOD classes
- **No File Metadata**: Modules contain only DICOM data elements, not file metadata
- **Abstract Base**: BaseModule cannot be instantiated directly - only concrete module subclasses

## Core User Experience Features

**PatientModule Demonstrates Clean IntelliSense Through Method-Based API:**

### 🎯 Clean IntelliSense Design
- **Method-Only Interface**: No direct DICOM attribute access to avoid IntelliSense clutter
- **Logical Method Grouping**: Clear separation of construction, configuration, and validation methods
- **Type-Safe Parameters**: Full IDE autocomplete with parameter types and enum support
- **Method Chaining**: Fluent API enables `from_required_elements().with_optional_elements()`
- **Module Class Docstring**: Basic usage guide including sequence initialization using `create_*_item()`

### 🏗️ DICOM Type-Based Parameter Design
- **Type 1 as Required Args**: `from_required_elements(type1_param)` - user must provide value
- **Type 2 as Kwargs with Defaults**: `from_required_elements(type2_param="")` - required but can be empty
- **Type 3 as Optional**: `with_optional_elements(type3_param=None)` - truly optional elements
- **Conditional Types**: `with_[...]()` handles Type 1C/2C based on context

### 🧩 Method Categories by DICOM Type
- **`from_required_elements()`**: All Type 1 (args) + Type 2 (kwargs with empty defaults)
- **`with_optional_elements()`**: All Type 3 elements (kwargs with None defaults)  
- **`with_[...]()`**: Type 1C/2C elements with validation logic
- **`create_*_item()`**: Static helpers for sequence item creation - **MUST return pydicom.Dataset, NOT dict**

### 🔍 Logical Properties & Validation
- **State Checks**: `is_human`, `is_non_human`, `is_deidentified`, `has_alternative_calendar_dates`
- **Validation**: `validate()` returns structured error/warning dictionary

## Module Implementation Template

**All future modules must inherit from BaseModule and follow the PatientModule DICOM Type-based design:**

```python
from .base_module import BaseModule

class NewModule(BaseModule):
    """Module description - DICOM PS3.3 reference.

    Usage:
        module = NewModule.from_required_elements(
            type1_required_param,                    # Type 1: User must provide
            type2_param="",                         # Type 2: Required but can be empty
            type2_another="",
            type2_sequence=[NewModule.create_sequence_item(required_item_param)]
        ).with_optional_elements(
            type3_param=None,                       # Type 3: Optional
            type3_another=None,
            type3_sequence=[NewModule.create_sequence_item(required_item_param)]
        ).with_conditional_group(
            type1c_param,                           # Type 1C: Required if condition met
            type2c_param=""                         # Type 2C: Required but empty if condition met
        )
    """
    
    @classmethod
    def from_required_elements(
        cls, 
        # Type 1 elements as positional args (user MUST provide value)
        required_param: str,
        # Type 2 elements as kwargs with empty string defaults (required but can be empty)
        optional_but_present_param: str = "",
        another_type2_param: str = ""
    ) -> 'NewModule':
        """Create module with all required (Type 1 and Type 2) elements.
        
        Args:
            required_param: Description (tag) Type 1
            optional_but_present_param: Description (tag) Type 2
            another_type2_param: Description (tag) Type 2
        """
        instance = cls()
        # Set DICOM attributes using BaseModule helper methods
        instance.RequiredParam = required_param
        instance.OptionalButPresentParam = optional_but_present_param
        instance.AnotherType2Param = another_type2_param
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        optional_param: str | None = None,
        another_optional: str | None = None
    ) -> 'NewModule':
        """Add optional (Type 3) elements."""
        # Use BaseModule helper methods
        self._set_attribute_if_not_none('OptionalParam', optional_param)
        self._set_attribute_if_not_none('AnotherOptional', another_optional)
        return self
    
    def with_conditional_group(
        self,
        # Type 1C/2C elements with validation
        conditional_param: str,
        conditional_optional: str = ""
    ) -> 'NewModule':
        """Add conditional elements with required validation logic."""
        # Use BaseModule validation helper
        self._validate_conditional_requirement(
            some_condition,  # Boolean condition
            [conditional_param],  # Required values when condition is true
            "conditional_param required when condition X is true"
        )

        self.ConditionalParam = conditional_param
        self.ConditionalOptional = conditional_optional
        return self
    
    @staticmethod
    def create_sequence_item(
        required_item_param: str,
        optional_item_param: str | None = None
    ) -> Dataset:
        """Create sequence items for complex structures."""
        from pydicom import Dataset
        item = Dataset()
        item.RequiredItemParam = required_item_param
        if optional_item_param is not None:
            item.OptionalItemParam = optional_item_param
        return item
    
    @property
    def is_configured(self) -> bool:
        """Logical state check based on internal data."""
        return hasattr(self, 'RequiredParam')
    
    @property  
    def has_optional_data(self) -> bool:
        """Check if optional elements are present."""
        return hasattr(self, 'OptionalParam')
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return ModuleValidator.validate(self, config)
```

## Key Design Principles

### 🎯 Module Success Criteria
1. **Clean IntelliSense**: Only methods and properties visible, no raw DICOM attributes
2. **DICOM Type Clarity**: Type 1 (args), Type 2 (kwargs=""), Type 3 (kwargs=None), Conditional (validated)
3. **Method-Based API**: All data setting through explicit named parameter methods
4. **Logical Properties**: `is_*` and `has_*` properties for common state checks
5. **Validation Integration**: Built-in validation with structured error/warning reporting

### 🏗️ Implementation Requirements
- **Inherit from BaseModule**: All modules inherit from BaseModule (which inherits from pydicom.Dataset)
- **Named Parameters**: Every DICOM element visible as explicit method parameter
- **Type Safety**: Full type hints for IDE support and runtime validation
- **Error Handling**: Clear validation errors with DICOM tag references
- **Documentation**: Google-style docstrings with DICOM references
- **Helper Methods**: Use BaseModule helper methods for common patterns (date/time formatting, enum handling, etc.)
- **Sequence Item Creation**: All `create_*_item()` methods must return `pydicom.Dataset` objects, not dictionaries

### ✅ Validation Standards
- **Decoupled Validation**: Each module has a corresponding validator class in validation/ folder
- **Validator Pattern**: `ModuleNameValidator.validate(module, config)` returns `{"errors": [], "warnings": []}`
- **Configurable**: `ValidationConfig` parameter for customization
- **Structured**: Validators handle all validation logic independently of module classes
- **No Module Dependencies**: Modules focus on data structure, validators handle DICOM compliance

## Module Categories

**All modules inherit from BaseModule and follow the PatientModule pattern with method-based APIs:**

### 🏥 Patient Information Modules
- [x] **PatientModule** (C.7.1.1) - ✅ Complete exemplar implementation with BaseModule inheritance
- [x] **ClinicalTrialSubjectModule** (C.7.1.3) - ✅ Complete with conditional validation for subject identification
- [x] **PatientStudyModule** (C.7.2.2) - ✅ Complete with comprehensive patient study attributes

### 📋 Study & Series Modules
- [x] **GeneralStudyModule** (C.7.2.1) - ✅ Complete with comprehensive study identification and physician info
- [x] **ClinicalTrialStudyModule** (C.7.2.3) - ✅ Complete with temporal event and consent handling
- [x] **GeneralSeriesModule** (C.7.3.1) - ✅ Complete with positioning, operator, and pixel value info
- [x] **RTSeriesModule** (C.8.8.1) - ✅ Complete with RT-specific elements and frame references
- [x] **ClinicalTrialSeriesModule** (C.7.3.2) - ✅ Complete with coordinating center and series identification

### 🖼️ Image & Spatial Modules
- [x] **GeneralImageModule** (C.7.6.1) - ✅ Complete with image identification, quality control, and compression info
- [x] **ImagePlaneModule** (C.7.6.2) - ✅ Complete with image geometry, orientation, and spatial positioning
- [x] **ImagePixelModule** (C.7.6.3) - ✅ Complete with pixel data characteristics, color configuration, and palette support
- [x] **MultiFrameModule** (C.7.6.6) - ✅ Complete with multi-frame data and stereo pair support
- [x] **FrameOfReferenceModule** (C.7.4.1) - ✅ Complete with spatial reference frame identification
- [x] **CineModule** (C.7.6.5) - ✅ Complete with cine timing, playback, and audio channel support
- [x] **OverlayPlaneModule** (C.9.2) - ✅ Complete with overlay graphics and ROI data

### ⚡ Radiotherapy Modules
- [x] **RTImageModule** (C.8.8.2) - ✅ Complete
- [x] **RTDoseModule** (C.8.8.3) - ✅ Complete
- [x] **RTDVHModule** (C.8.8.4) - ✅ Complete
- [x] **StructureSetModule** (C.8.8.5) - ✅ Complete
- [x] **ROIContourModule** (C.8.8.6) - ✅ Complete - ROI contour data with geometric types
- [x] **RTROIObservationsModule** (C.8.8.8) - ✅ Complete - RT ROI observations and interpretations
- [x] **RTGeneralPlanModule** (C.8.8.9) - ✅ Complete
- [x] **RTPrescriptionModule** (C.8.8.10) - ✅ Complete - RT prescription with dose references
- [x] **RTToleranceTablesModule** (C.8.8.11) - ✅ Complete - RT tolerance tables
- [x] **RTPatientSetupModule** (C.8.8.12) - ✅ Complete - RT patient setup with fixation devices
- [x] **RTFractionSchemeModule** (C.8.8.13) - ✅ Complete - RT fraction scheme with beam references
- [x] **RTBeamsModule** (C.8.8.14) - ✅ Complete (Simplified) - RT beam information
- [x] **RTBrachyApplicationSetupsModule** (C.8.8.15) - ✅ Complete (Simplified) - Brachytherapy application setups

### 🔧 Equipment & Common Modules
- [x] **GeneralEquipmentModule** (C.7.5.1) - ✅ Complete with equipment identification and pixel padding support
- [x] **SOPCommonModule** (C.12.1) - ✅ Complete with SOP instance identification and complex sequences
- [x] **CommonInstanceReferenceModule** (C.12.2) - ✅ Complete with hierarchical instance references
- [x] **DeviceModule** - ✅ Complete with device information and conditional diameter units
- [x] **GeneralReferenceModule** - ✅ Complete with general reference data and derivation support

### 🔬 Specialized Modules
- [x] **CTImageModule** - ✅ Complete with comprehensive CT image attributes and conditional validation
- [x] **MultiEnergyCTImageModule** - ✅ Complete with complex nested sequences for multi-energy CT
- [x] **ContrastBolusModule** - ✅ Complete with contrast agent information and flow validation
- [x] **GeneralAcquisitionModule** - ✅ Complete with acquisition timing and identification
- [x] **ModalityLutModule** - ✅ Complete with conditional LUT sequence or rescale parameters
- [x] **VoiLutModule** - ✅ Complete with conditional LUT sequence or window parameters
- [x] **ApprovalModule** - ✅ Complete with approval status and conditional review information
- [x] **FrameExtractionModule** - ✅ Complete with frame extraction sequence and conditional frame lists
- [x] **EnhancedPatientOrientationModule** - ✅ Complete with patient orientation sequences for gravity and equipment relationship
- [x] **SynchronizationModule** - ✅ Complete with temporal synchronization and trigger management
- [x] **SpecimenModule** - ✅ Complete with container and specimen identification sequences


## Complete Module Implementation Checklist (44 Total)

**All 44 modules from the DICOM standard documentation:**

1. [x] **approval** - ✅ Complete with approval status and conditional review information
2. [x] **cine** - ✅ Complete with cine timing and audio support
3. [x] **clinical_trial_series** - ✅ Complete with coordinating center info
4. [x] **clinical_trial_study** - ✅ Complete with temporal event handling
5. [x] **clinical_trial_subject** - ✅ Complete with conditional validation
6. [x] **common_instance_reference** - ✅ Complete with hierarchical instance references
7. [x] **contrast_bolus** - ✅ Complete with contrast agent information and flow validation
8. [x] **ct_image** - ✅ Complete with comprehensive CT image attributes and conditional validation
9. [x] **device** - ✅ Complete with device information and conditional diameter units
10. [x] **enhanced_patient_orientation** - ✅ Complete with patient orientation sequences for gravity and equipment relationship
11. [x] **frame_extraction** - ✅ Complete with frame extraction sequence and conditional frame lists
12. [x] **frame_of_reference** - ✅ Complete with spatial reference frame identification
13. [x] **general_acquisition** - ✅ Complete with acquisition timing and identification
14. [x] **general_equipment** - ✅ Complete with equipment identification and pixel padding support
15. [x] **general_image** - ✅ Complete with image identification and quality control
16. [x] **general_reference** - ✅ Complete with general reference data and derivation support
17. [x] **general_series** - ✅ Complete with positioning and operator info
18. [x] **general_study** - ✅ Complete with physician identification
19. [x] **image_pixel** - ✅ Complete with pixel data characteristics and color configuration
20. [x] **image_plane** - ✅ Complete with image geometry and orientation
21. [x] **modality_lut** - ✅ Complete with conditional LUT sequence or rescale parameters
22. [x] **multi-energy_ct_image** - ✅ Complete with complex nested sequences for multi-energy CT
23. [x] **multi-frame** - ✅ Complete with multi-frame data and stereo pair support
24. [x] **overlay_plane** - ✅ Complete with overlay graphics and ROI data
25. [x] **patient** - ✅ Complete exemplar implementation with BaseModule inheritance
26. [x] **patient_study** - ✅ Complete with comprehensive study attributes
27. [x] **roi_contour** - ROI contour data
28. [x] **rt_beams** - RT beam information
29. [x] **rt_brachy_application_setups** - Brachytherapy application setups
30. [x] **rt_dose** - ✅ Complete
31. [x] **rt_dvh** - ✅ Complete
32. [x] **rt_fraction_scheme** - RT fraction scheme
33. [x] **rt_general_plan** - ✅ Complete
34. [x] **rt_image** - ✅ Complete
35. [x] **rt_patient_setup** - RT patient setup
36. [x] **rt_prescription** - RT prescription
37. [x] **rt_roi_observations** - RT ROI observations
38. [x] **rt_series** - ✅ Complete with RT-specific elements
39. [x] **rt_tolerance_tables** - RT tolerance tables
40. [x] **sop_common** - ✅ Complete with SOP instance identification and complex sequences
41. [x] **specimen** - ✅ Complete with container and specimen identification sequences
42. [x] **structure_set** - ✅ Complete
43. [x] **synchronization** - ✅ Complete with temporal synchronization and trigger management
44. [x] **voi_lut** - ✅ Complete with conditional LUT sequence or window parameters

## Module Testing Standards

**Each module must pass comprehensive testing before implementation completion:**

### 🧪 Unit Tests (Required for Each Module)
- [ ] **Factory Method Tests**: `from_required_elements()` with valid/invalid Type 1/2 parameters
- [ ] **Builder Method Tests**: All `with_*()` methods with valid/invalid optional parameters
- [ ] **Static Helper Tests**: `create_*_item()` methods for sequence item construction
- [ ] **Property Tests**: All `is_*` and `has_*` properties return expected boolean values
- [ ] **Validation Tests**: `validate()` method returns proper error/warning structure

### 🎯 IntelliSense Verification (Manual Testing)
- [ ] **Method Visibility**: Only factory, builder, helper, and property methods visible
- [ ] **Parameter Clarity**: All DICOM elements clearly named as method parameters
- [ ] **Type Hints**: Full IDE autocomplete with parameter types and return values
- [ ] **Documentation**: Method docstrings show DICOM tags and element types

### ✅ DICOM Compliance Testing  
- [ ] **Dataset Generation**: Module creates valid pydicom.Dataset with proper DICOM attributes
- [ ] **Tag Validation**: All DICOM tags match PS3.6 data dictionary
- [ ] **VR Compliance**: Value representations match DICOM standard requirements
- [ ] **Type Requirements**: Type 1/2/3/1C/2C elements handled according to DICOM rules

## Implementation Dependencies

**Build modules in logical dependency order:**

### 🥇 Priority 1 - Foundation Modules
1. **PatientModule** - ✅ Complete (exemplar implementation)
2. **GeneralStudyModule** - Study-level identification 
3. **GeneralEquipmentModule** - Equipment/manufacturer information
4. **SOPCommonModule** - SOP instance identification

### 🥈 Priority 2 - Series & Image Modules  
5. **GeneralSeriesModule** - Series-level information
6. **RTSeriesModule** - RT-specific series information
7. **FrameOfReferenceModule** - Spatial coordinate system
8. **GeneralImageModule** - Basic image information

### 🥉 Priority 3 - Specialized Modules
9. **RTDoseModule** - ✅ Complete - Dose distribution data
10. **ImagePixelModule** - Pixel data characteristics
11. **ImagePlaneModule** - Image geometry and positioning
12. Additional modules as needed...

### 🏥 Radiotherapy Modules - Recently Implemented
13. **RTGeneralPlanModule** - ✅ Complete - Basic RT plan information (C.8.8.9)
14. **StructureSetModule** - ✅ Complete - Structure set definitions (C.8.8.5)
15. **ROIContourModule** - ✅ Complete - ROI contour data with geometric types (C.8.8.6)
16. **RTROIObservationsModule** - ✅ Complete - ROI observations and interpretations (C.8.8.8)
17. **RTPrescriptionModule** - ✅ Complete - RT prescription with dose references (C.8.8.10)
18. **RTToleranceTablesModule** - ✅ Complete - RT tolerance tables (C.8.8.11)
19. **RTPatientSetupModule** - ✅ Complete - Patient setup with fixation devices (C.8.8.12)
20. **RTFractionSchemeModule** - ✅ Complete - Fraction scheme with beam references (C.8.8.13)
21. **RTBeamsModule** - ✅ Complete (Simplified) - RT beam information (C.8.8.14)
22. **RTBrachyApplicationSetupsModule** - ✅ Complete (Simplified) - Brachy setups (C.8.8.15)
23. **RTDVHModule** - ✅ Complete - Dose-volume histogram data (C.8.8.4)
24. **RTDoseModule** - ✅ Complete - Dose distribution data (C.8.8.3)
25. **RTImageModule** - ✅ Complete - RT image characteristics (C.8.8.2)

**Note**: Each module is self-contained and can be implemented independently following the PatientModule pattern.