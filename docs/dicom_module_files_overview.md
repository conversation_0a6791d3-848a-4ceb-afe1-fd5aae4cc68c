# DICOM Module Files Overview

## Description

This document provides a comprehensive overview of all DICOM module-related files in the pyrt-dicom project. The project implements DICOM modules according to the DICOM PS3.3 standard, with a structured approach that includes documentation, implementation, validation, and testing components.

## Project Structure

The DICOM module files are organized across six main directories:

### 1. Documentation (`docs/dicom_standard/modules/`)
Contains markdown files with DICOM standard specifications for each module. These files include:
- Module descriptions from DICOM PS3.3 standard
- Attribute tables with tags, types, and descriptions
- Implementation notes and requirements

### 2. Module Implementations (`src/pyrt_dicom/modules/`)
Contains Python module implementations that inherit from `BaseModule`. These files provide:
- DICOM-compliant data structures
- Attribute validation and type checking
- Factory methods for creating module instances
- Integration with pydicom Dataset functionality

### 3. Validators (`src/pyrt_dicom/validators/`)
Contains validation logic for each module, inheriting from `BaseValidator`. These files provide:
- Attribute presence validation
- Value range and format checking
- DICOM compliance verification
- Custom validation rules per module

### 4. Module Unit Tests (`tests/unit/modules/`)
Contains pytest test files for module functionality. These files provide:
- Module creation and instantiation tests
- API method testing and validation
- Edge case and error condition testing
- Integration with pydicom Dataset functionality

### 5. Validator Unit Tests (`tests/unit/validators/`)
Contains pytest test files for validator functionality. **Note: Validator testing framework is planned for future implementation**. These files will provide:
- Validator logic and rule testing
- DICOM compliance verification tests
- Error detection and reporting validation
- Performance and edge case testing

### 6. Semantic Validation Documentation (`docs/dicom_standard/module_validation/`)
Contains comprehensive validation documentation for each module. **Note: Semantic validation documentation is planned for future implementation**. These files will provide:
- Detailed module and validator usage examples
- DICOM standard adherence verification
- End-user guidance for generating compliant module instances
- Cross-component validation across modules, tests, validators, and validator tests

## Master Table of DICOM Module Files

| Module Name | Documentation | Implementation | Validator | Module Tests | Validator Tests | Semantic Check | Status |
|-------------|---------------|----------------|-----------|--------------|-----------------|----------------|--------|
| approval | ✅ approval.md | ✅ approval_module.py | ✅ approval_validator.py | ✅ test_approval_module.py | ❌ test_approval_validator.py | ❌ approval_validation.md | Incomplete |
| cine | ✅ cine.md | ✅ cine_module.py | ✅ cine_validator.py | ✅ test_cine_module.py | ❌ test_cine_validator.py | ❌ cine_validation.md | Incomplete |
| clinical_trial_series | ✅ clinical_trial_series.md | ✅ clinical_trial_series_module.py | ✅ clinical_trial_series_validator.py | ✅ test_clinical_trial_series_module.py | ❌ test_clinical_trial_series_validator.py | ❌ clinical_trial_series_validation.md | Incomplete |
| clinical_trial_study | ✅ clinical_trial_study.md | ✅ clinical_trial_study_module.py | ✅ clinical_trial_study_validator.py | ✅ test_clinical_trial_study_module.py | ❌ test_clinical_trial_study_validator.py | ❌ clinical_trial_study_validation.md | Incomplete |
| clinical_trial_subject | ✅ clinical_trial_subject.md | ✅ clinical_trial_subject_module.py | ✅ clinical_trial_subject_validator.py | ✅ test_clinical_trial_subject_module.py | ❌ test_clinical_trial_subject_validator.py | ❌ clinical_trial_subject_validation.md | Incomplete |
| common_instance_reference | ✅ common_instance_reference.md | ✅ common_instance_reference_module.py | ✅ common_instance_reference_validator.py | ✅ test_common_instance_reference_module.py | ❌ test_common_instance_reference_validator.py | ❌ common_instance_reference_validation.md | Incomplete |
| contrast_bolus | ✅ contrast_bolus.md | ✅ contrast_bolus_module.py | ✅ contrast_bolus_validator.py | ✅ test_contrast_bolus_module.py | ❌ test_contrast_bolus_validator.py | ❌ contrast_bolus_validation.md | Incomplete |
| ct_image | ✅ ct_image.md | ✅ ct_image_module.py | ✅ ct_image_validator.py | ✅ test_ct_image_module.py | ❌ test_ct_image_validator.py | ❌ ct_image_validation.md | Incomplete |
| device | ✅ device.md | ✅ device_module.py | ✅ device_validator.py | ✅ test_device_module.py | ❌ test_device_validator.py | ❌ device_validation.md | Incomplete |
| enhanced_patient_orientation | ✅ enhanced_patient_orientation.md | ✅ enhanced_patient_orientation_module.py | ✅ enhanced_patient_orientation_validator.py | ✅ test_enhanced_patient_orientation_module.py | ❌ test_enhanced_patient_orientation_validator.py | ❌ enhanced_patient_orientation_validation.md | Incomplete |
| frame_extraction | ✅ frame_extraction.md | ✅ frame_extraction_module.py | ✅ frame_extraction_validator.py | ✅ test_frame_extraction_module.py | ❌ test_frame_extraction_validator.py | ❌ frame_extraction_validation.md | Incomplete |
| frame_of_reference | ✅ frame_of_reference.md | ✅ frame_of_reference_module.py | ✅ frame_of_reference_validator.py | ✅ test_frame_of_reference_module.py | ❌ test_frame_of_reference_validator.py | ❌ frame_of_reference_validation.md | Incomplete |
| general_acquisition | ✅ general_acquisition.md | ✅ general_acquisition_module.py | ✅ general_acquisition_validator.py | ✅ test_general_acquisition_module.py | ❌ test_general_acquisition_validator.py | ❌ general_acquisition_validation.md | Incomplete |
| general_equipment | ✅ general_equipment.md | ✅ general_equipment_module.py | ✅ general_equipment_validator.py | ✅ test_general_equipment_module.py | ❌ test_general_equipment_validator.py | ❌ general_equipment_validation.md | Incomplete |
| general_image | ✅ general_image.md | ✅ general_image_module.py | ✅ general_image_validator.py | ✅ test_general_image_module.py | ❌ test_general_image_validator.py | ❌ general_image_validation.md | Incomplete |
| general_reference | ✅ general_reference.md | ✅ general_reference_module.py | ✅ general_reference_validator.py | ✅ test_general_reference_module.py | ❌ test_general_reference_validator.py | ❌ general_reference_validation.md | Incomplete |
| general_series | ✅ general_series.md | ✅ general_series_module.py | ✅ general_series_validator.py | ✅ test_general_series_module.py | ❌ test_general_series_validator.py | ❌ general_series_validation.md | Incomplete |
| general_study | ✅ general_study.md | ✅ general_study_module.py | ✅ general_study_validator.py | ✅ test_general_study_module.py | ❌ test_general_study_validator.py | ❌ general_study_validation.md | Incomplete |
| image_pixel | ✅ image_pixel.md | ✅ image_pixel_module.py | ✅ image_pixel_validator.py | ✅ test_image_pixel_module.py | ❌ test_image_pixel_validator.py | ❌ image_pixel_validation.md | Incomplete |
| image_plane | ✅ image_plane.md | ✅ image_plane_module.py | ✅ image_plane_validator.py | ✅ test_image_plane_module.py | ❌ test_image_plane_validator.py | ❌ image_plane_validation.md | Incomplete |
| modality_lut | ✅ modality_lut.md | ✅ modality_lut_module.py | ✅ modality_lut_validator.py | ✅ test_modality_lut_module.py | ❌ test_modality_lut_validator.py | ❌ modality_lut_validation.md | Incomplete |
| multi-energy_ct_image | ✅ multi-energy_ct_image.md | ✅ multi_energy_ct_image_module.py | ✅ multi_energy_ct_image_validator.py | ✅ test_multi_energy_ct_image_module.py | ❌ test_multi_energy_ct_image_validator.py | ❌ multi_energy_ct_image_validation.md | Incomplete |
| multi-frame | ✅ multi-frame.md | ✅ multi_frame_module.py | ✅ multi_frame_validator.py | ✅ test_multi_frame_module.py | ❌ test_multi_frame_validator.py | ❌ multi_frame_validation.md | Incomplete |
| overlay_plane | ✅ overlay_plane.md | ✅ overlay_plane_module.py | ✅ overlay_plane_validator.py | ✅ test_overlay_plane_module.py | ❌ test_overlay_plane_validator.py | ❌ overlay_plane_validation.md | Incomplete |
| patient | ✅ patient.md | ✅ patient_module.py | ✅ patient_validator.py | ✅ test_patient_module.py | ❌ test_patient_validator.py | ❌ patient_validation.md | Incomplete |
| patient_study | ✅ patient_study.md | ✅ patient_study_module.py | ✅ patient_study_validator.py | ✅ test_patient_study_module.py | ❌ test_patient_study_validator.py | ❌ patient_study_validation.md | Incomplete |
| roi_contour | ✅ roi_contour.md | ✅ roi_contour_module.py | ✅ roi_contour_validator.py | ✅ test_roi_contour_module.py | ❌ test_roi_contour_validator.py | ❌ roi_contour_validation.md | Incomplete |
| rt_beams | ✅ rt_beams.md | ✅ rt_beams_module.py | ✅ rt_beams_validator.py | ✅ test_rt_beams_module.py | ❌ test_rt_beams_validator.py | ❌ rt_beams_validation.md | Incomplete |
| rt_brachy_application_setups | ✅ rt_brachy_application_setups.md | ✅ rt_brachy_application_setups_module.py | ✅ rt_brachy_application_setups_validator.py | ✅ test_rt_brachy_application_setups_module.py | ❌ test_rt_brachy_application_setups_validator.py | ❌ rt_brachy_application_setups_validation.md | Incomplete |
| rt_dose | ✅ rt_dose.md | ✅ rt_dose_module.py | ✅ rt_dose_validator.py | ✅ test_rt_dose_module.py | ❌ test_rt_dose_validator.py | ❌ rt_dose_validation.md | Incomplete |
| rt_dvh | ✅ rt_dvh.md | ✅ rt_dvh_module.py | ✅ rt_dvh_validator.py | ✅ test_rt_dvh_module.py | ❌ test_rt_dvh_validator.py | ❌ rt_dvh_validation.md | Incomplete |
| rt_fraction_scheme | ✅ rt_fraction_scheme.md | ✅ rt_fraction_scheme_module.py | ✅ rt_fraction_scheme_validator.py | ✅ test_rt_fraction_scheme_module.py | ❌ test_rt_fraction_scheme_validator.py | ❌ rt_fraction_scheme_validation.md | Incomplete |
| rt_general_plan | ✅ rt_general_plan.md | ✅ rt_general_plan_module.py | ✅ rt_general_plan_validator.py | ✅ test_rt_general_plan_module.py | ❌ test_rt_general_plan_validator.py | ❌ rt_general_plan_validation.md | Incomplete |
| rt_image | ✅ rt_image.md | ✅ rt_image_module.py | ✅ rt_image_validator.py | ✅ test_rt_image_module.py | ❌ test_rt_image_validator.py | ❌ rt_image_validation.md | Incomplete |
| rt_patient_setup | ✅ rt_patient_setup.md | ✅ rt_patient_setup_module.py | ✅ rt_patient_setup_validator.py | ✅ test_rt_patient_setup_module.py | ❌ test_rt_patient_setup_validator.py | ❌ rt_patient_setup_validation.md | Incomplete |
| rt_prescription | ✅ rt_prescription.md | ✅ rt_prescription_module.py | ✅ rt_prescription_validator.py | ✅ test_rt_prescription_module.py | ❌ test_rt_prescription_validator.py | ❌ rt_prescription_validation.md | Incomplete |
| rt_roi_observations | ✅ rt_roi_observations.md | ✅ rt_roi_observations_module.py | ✅ rt_roi_observations_validator.py | ✅ test_rt_roi_observations_module.py | ❌ test_rt_roi_observations_validator.py | ❌ rt_roi_observations_validation.md | Incomplete |
| rt_series | ✅ rt_series.md | ✅ rt_series_module.py | ✅ rt_series_validator.py | ✅ test_rt_series_module.py | ❌ test_rt_series_validator.py | ❌ rt_series_validation.md | Incomplete |
| rt_tolerance_tables | ✅ rt_tolerance_tables.md | ✅ rt_tolerance_tables_module.py | ✅ rt_tolerance_tables_validator.py | ✅ test_rt_tolerance_tables_module.py | ❌ test_rt_tolerance_tables_validator.py | ❌ rt_tolerance_tables_validation.md | Incomplete |
| sop_common | ✅ sop_common.md | ✅ sop_common_module.py | ✅ sop_common_validator.py | ✅ test_sop_common_module.py | ❌ test_sop_common_validator.py | ❌ sop_common_validation.md | Incomplete |
| specimen | ✅ specimen.md | ✅ specimen_module.py | ✅ specimen_validator.py | ✅ test_specimen_module.py | ❌ test_specimen_validator.py | ❌ specimen_validation.md | Incomplete |
| structure_set | ✅ structure_set.md | ✅ structure_set_module.py | ✅ structure_set_validator.py | ✅ test_structure_set_module.py | ❌ test_structure_set_validator.py | ❌ structure_set_validation.md | Incomplete |
| synchronization | ✅ synchronization.md | ✅ synchronization_module.py | ✅ synchronization_validator.py | ✅ test_synchronization_module.py | ❌ test_synchronization_validator.py | ❌ synchronization_validation.md | Incomplete |
| voi_lut | ✅ voi_lut.md | ✅ voi_lut_module.py | ✅ voi_lut_validator.py | ✅ test_voi_lut_module.py | ❌ test_voi_lut_validator.py | ❌ voi_lut_validation.md | Incomplete |

## Summary Statistics

- **Total Modules**: 44
- **Documentation Files**: 44/44 (100%)
- **Implementation Files**: 44/44 (100%)
- **Validator Files**: 44/44 (100%)
- **Module Test Files**: 44/44 (100%)
- **Validator Test Files**: 0/44 (0%) - *Planned for future implementation*
- **Semantic Validation Documents**: 0/44 (0%) - *Planned for future implementation*
- **Complete Modules** (all 7 components): 0/44 (0%) - *All modules currently incomplete pending validator tests and semantic validation*

## Base Classes

The project includes foundational base classes:

- **`base_module.py`**: Base class for all DICOM module implementations
- **`base_validator.py`**: Base class for all module validators

## Instructions for Updating This Documentation

To keep this documentation current, follow these steps:

### 1. Automated Update Script
Create a script to automatically scan directories and update the table:

```bash
# Navigate to project root
cd /path/to/pyrt-dicom-2

# Run directory listing commands
ls docs/dicom_standard/modules/*.md > /tmp/docs_files.txt
ls src/pyrt_dicom/modules/*_module.py > /tmp/modules_files.txt
ls src/pyrt_dicom/validators/modules/*_validator.py > /tmp/validators_files.txt
ls tests/unit/modules/test_*_module.py > /tmp/module_tests_files.txt
ls tests/unit/validators/test_*_validator.py > /tmp/validator_tests_files.txt 2>/dev/null || echo "No validator tests found"
ls docs/dicom_standard/module_validation/*_validation.md > /tmp/semantic_validation_files.txt 2>/dev/null || echo "No semantic validation docs found"
```

### 2. Manual Update Process
1. **Check for new documentation files**: `ls docs/dicom_standard/modules/*.md`
2. **Check for new module implementations**: `ls src/pyrt_dicom/modules/*_module.py`
3. **Check for new validators**: `ls src/pyrt_dicom/validators/modules/*_validator.py`
4. **Check for new module tests**: `ls tests/unit/modules/test_*_module.py`
5. **Check for new validator tests**: `ls tests/unit/validators/test_*_validator.py`
6. **Check for semantic validation docs**: `ls docs/dicom_standard/module_validation/*_validation.md`
7. **Update the master table** with any new entries
8. **Update summary statistics** based on current counts

### 3. Validation Steps
- Ensure naming consistency across all file types
- Verify that each module has corresponding documentation
- Check that base classes are excluded from the module count
- Confirm module test file naming follows the pattern `test_{module_name}_module.py`
- Confirm validator test file naming follows the pattern `test_{module_name}_validator.py`
- Confirm semantic validation file naming follows the pattern `{module_name}_validation.md`

### 4. Regular Maintenance
- Update this document when new modules are added
- Review and update status when validator tests are implemented
- Review and update status when semantic validation documentation is added
- Verify file paths remain accurate after any restructuring
- Update descriptions if module functionality changes significantly
- Only mark modules as "Complete" when all 7 components exist and are verified

---

*Last updated: 2025-08-19*
*Total modules documented: 44*
*New tracking structure: 7 components per module (Documentation, Implementation, Validator, Module Tests, Validator Tests, Semantic Check, Status)*
