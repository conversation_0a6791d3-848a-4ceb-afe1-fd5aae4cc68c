#%%

import os
import re
import requests
from pathlib import Path
from html_to_markdown import HTMLToMarkdownConverter
from bs4 import BeautifulSoup

if not os.getcwd().endswith("utils"):
    os.chdir("utils")
print(os.getcwd())

#%%

def get_dicom_standard_html(cache_dir='dicom_standard', force_download=False):
    """
    Download or load the DICOM standard HTML page.
    
    Args:
        cache_dir (str): Directory to store the cached HTML file
        force_download (bool): If True, always download a fresh copy
        
    Returns:
        str: The HTML content of the DICOM standard page
    """
    url = "https://dicom.nema.org/medical/dicom/current/output/html/part03.html"
    os.makedirs(cache_dir, exist_ok=True)
    cache_path = Path(cache_dir) / "dicom_standard_part03.html"
    
    # If file exists and we're not forcing a download, load from cache
    if cache_path.exists() and not force_download:
        print(f"Loading from cache: {cache_path}")
        print(f"To download a fresh copy, either delete {cache_path} or set force_download=True")
        return cache_path.read_text(encoding='utf-8')
    
    # Download the file
    print(f"Downloading DICOM standard from {url}...")
    response = requests.get(url)
    response.raise_for_status()
    
    # Save to cache
    cache_path.write_text(response.text, encoding='utf-8')
    print(f"Saved to {cache_path}")
    return response.text

# Configure the DICOM output directory
dicom_output_dir = "dicom_standard"

# Get the HTML content (will download if not cached)
html_content = get_dicom_standard_html(dicom_output_dir)

# To force a fresh download, use:
# html_content = get_dicom_standard_html(force_download=True)

print(f"Retrieved {len(html_content)} characters of HTML")

#%%

soup = BeautifulSoup(html_content, 'html.parser')


#%%


def get_text(html, additional_chars=None):
    """Remove non-printable characters from HTML content.
    
    Args:
        html: BeautifulSoup object or HTML string
        additional_chars: Optional list of additional characters to remove
    """
    # Convert to string if it's a BeautifulSoup object
    text = html.prettify() if hasattr(html, 'prettify') else str(html)

    # Replace the non-printing spacing character with a simple space
    text = re.sub(r"Â ", " ", text)
    text = re.sub(r"Â", " ", text)
    
    # Default problematic characters to remove
    default_chars = "Ââ€â€œâ€\x9d"
    chars_to_remove = set(default_chars + (additional_chars or ''))
    
    # Remove non-printable ASCII and specified Unicode characters
    cleaned = ''.join(
        char for char in text 
        if (32 <= ord(char) <= 126 or char in '\n\r\t') 
        and char not in chars_to_remove
    )

    # Remove spaces before a period or comma
    cleaned = re.sub(r'\s+([.,])', r'\1', cleaned)
    
    return cleaned

iod_output_dir = f"{dicom_output_dir}/iods"
os.makedirs(iod_output_dir, exist_ok=True)

rt_iods = {
    "RT Dose IOD": "",
    "RT Plan IOD": "",
    "RT Image IOD": "",
    "RT Structure Set IOD": "",
    "CT Image IOD": ""
}

section_div = None
for h2 in soup.find_all('h2'):
    for iod, value in list(rt_iods.items()):
        if iod in h2.text and value == "":
            section_div = h2.find_parent('div', class_='section')
            if section_div:
                # Save the section HTML to the dicom_standard folder
                output = get_text(section_div)
                iod_file = iod.replace(" ", "_").lower()
                # with open(f"{iod_output_dir}/{iod_file}.html", "w") as f:
                #     f.write(output)
                converter = HTMLToMarkdownConverter()
                text = converter.convert(output)
                rt_iods[iod] = text
                # with open(Path(f"{iod_output_dir}/{iod_file}.md"), "w") as f:
                #     f.write(text)

#%%

macro_output_dir = f"{dicom_output_dir}/macros"
os.makedirs(macro_output_dir, exist_ok=True)

macros = []
for iod, iod_markdown in rt_iods.items():
    module_table = [line[1:-1].split("|") for line in iod_markdown.split("\n") if "|" in line]
    for module_row in module_table:
        if module_row[0].strip() in ["IE", "---"]:
            continue
        ie, macro, ref, usage = [col.strip() for col in module_row]
        if (macro, ref) in macros:
            continue

        # Find the link with the ID sect_C.7.1.1
        link = soup.find('a', id=f'sect_{ref}')
        if not link:
            print(f"Link not found for {ref}")
            continue

        section_div = link.find_parent('div', class_='section')
        if not section_div:
            print("Section not found")
            continue
        
        converter = HTMLToMarkdownConverter()
        module_markdown = converter.convert(get_text(section_div))
        attribute_table = [line[1:-1].split("|") for line in module_markdown.split("\n") if "|" in line]
        for attr_row in attribute_table:
            pattern = r".*Include Table ([\d.\-a-zA-Z]+)\s+(.*)$"
            match = re.match(pattern, attr_row[0].strip())
            if not match:
                continue

            table_number = match.group(1)
            description = match.group(2)
            if (table_number, description) in macros:
                continue

            print(attr_row[0])
            print("\t", (table_number, description))

            # Find the a link with the table id
            # <a id="table_10-18" shape="rect"></a>
            link = soup.find('a', id=f'table_{table_number}')
            if not link:
                print(f"Link not found for {table_number}")
                continue

            section_div = link.find_parent('div', class_='section')
            if not section_div:
                print("Section not found")
                continue

            # Save the section HTML to the dicom_standard/macros folder
            output = get_text(section_div)
            macro_file = table_number.replace(" ", "_").replace("/", "_").lower()
            with open(f"{macro_output_dir}/{macro_file}.html", "w") as f:
                f.write(output)

            converter = HTMLToMarkdownConverter()
            text = converter.convert(output)

            with open(Path(f"{macro_output_dir}/{macro_file}.md"), "w") as f:
                f.write(text)

            macros.append((table_number, description))
            break

        break

    break
        
print(f"Done. {len(macros)} macros parsed.")