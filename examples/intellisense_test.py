"""IntelliSense Test - PatientModule and RTDose IOD Creation

This script demonstrates how IntelliSense helps create DICOM datasets using
the pyrt-dicom library. The examples show actual working code that demonstrates
the composition-based architecture with explicit module constructors.

Instructions: 
- Copy this file to a new location and open it in your Python IDE
- Experience IntelliSense by typing each section yourself
- Observe auto-completion, type hints, docstrings, and enum suggestions
- Run the script to see the actual output
"""

# STEP 1: Import statements - Experience IntelliSense module suggestions
from pyrt_dicom.modules import (
    PatientModule, 
    RTSeriesModule, 
    GeneralStudyModule,
    FrameOfReferenceModule,
    GeneralEquipmentModule,
    RTDoseModule,
    SOPCommonModule
)
from pyrt_dicom.enums import PatientSex, TypeOfPatientID, Modality, DoseUnits, DoseType, DoseSummationType
from pyrt_dicom.iods.rt_dose_iod import RTDoseIOD
from pyrt_dicom.validators.modules.base_validator import ValidationConfig

print("=== PyRT-DICOM IntelliSense Test ===\n")

# STEP 2: Create PatientModule using class method - Type hints and parameter suggestions
print("Step 2: Creating PatientModule with from_required_elements()")
print("- Notice enum suggestions for patient_sex")
print("- Observe parameter type hints for dates and names\n")

patient = PatientModule.from_required_elements(
    patient_name="Doe^John^Q^Mr^Jr",
    patient_id="RT001",
    patient_birth_date="19900115",
    patient_sex=PatientSex.MALE  # IntelliSense shows enum values: MALE, FEMALE, OTHER
)

print(f"Created patient: {patient['PatientName']}")

# STEP 3: Add optional elements - Experience comprehensive parameter lists
print("\nStep 3: Adding optional elements with with_optional_elements()")
print("- Notice the extensive parameter list with descriptions")
print("- Observe enum suggestions for TypeOfPatientID\n")

patient.with_optional_elements(
    type_of_patient_id=TypeOfPatientID.TEXT,  # IntelliSense: TEXT, RFID, BARCODE
    quality_control_subject="NO",
    patient_comments="Test patient for IntelliSense demo"
)

# STEP 4: Validate the PatientModule - Return type hints
print("Step 4: Validating PatientModule")
print("- Notice ValidationResult return type")
print("- Observe the .errors and .warnings attributes\n")

validation_result = patient.validate()
print(f"Patient validation - Errors: {len(validation_result.errors)}, Warnings: {len(validation_result.warnings)}")

# STEP 5: Create RTSeriesModule - Modality enum suggestions
print("\nStep 5: Creating RTSeriesModule with modality enum")
print("- Notice Modality enum suggestions")
print("- Observe required vs optional parameters\n")

rt_series = RTSeriesModule.from_required_elements(
    modality=Modality.RTDOSE,  # IntelliSense shows: RTDOSE, RTPLAN, RTSTRUCT, etc.
    series_instance_uid="*******.*******.9.10",
    operators_name="Tech^John"
).with_optional_elements(
    series_description="Treatment Planning Dose Distribution",
    series_number=1
)

print(f"Created RT series: {rt_series['Modality']}")

# STEP 6: Create additional required modules - Module composition pattern
print("\nStep 6: Creating additional required modules")
print("- Experience the explicit module constructor pattern")
print("- Notice factory method suggestions\n")

study_module = GeneralStudyModule.from_required_elements(
    study_instance_uid="*******.*******.9.11"
).with_optional_elements(
    study_description="Prostate IMRT Treatment Plan"
)

frame_ref_module = FrameOfReferenceModule.from_required_elements(
    frame_of_reference_uid="*******.*******.9.12"
)

equipment_module = GeneralEquipmentModule.from_required_elements(
    manufacturer="Eclipse Treatment Planning System"
)

dose_module = RTDoseModule.from_required_elements(
    dose_units=DoseUnits.GY,  # IntelliSense: GY, CGY
    dose_type=DoseType.PHYSICAL,  # IntelliSense: PHYSICAL, EFFECTIVE, ERROR
    dose_summation_type=DoseSummationType.PLAN  # IntelliSense: PLAN, BEAM, BRACHY, etc.
)

sop_module = SOPCommonModule.from_required_elements(
    sop_class_uid="1.2.840.10008.*******.1.481.2",  # RT Dose Storage
    sop_instance_uid="*******.*******.9.13"
)

print("Created all required modules")

# STEP 7: Create RTDose IOD from modules - Composition pattern
print("\nStep 7: Creating RTDoseIOD using module composition")
print("- Notice the comprehensive parameter list")
print("- Observe type hints for each module parameter")
print("- Experience the explicit composition approach\n")

rt_dose = RTDoseIOD(
    patient_module=patient,
    general_study_module=study_module,
    rt_series_module=rt_series,
    frame_of_reference_module=frame_ref_module,
    general_equipment_module=equipment_module,
    rt_dose_module=dose_module,
    sop_common_module=sop_module
)

print(f"Created RTDose IOD: {rt_dose}")

# STEP 8: Explore RTDose IOD properties - Property IntelliSense
print("\nStep 8: Exploring RTDose IOD properties")
print("- Notice boolean property return types")
print("- Observe descriptive property names\n")

print(f"Has DVH data: {rt_dose.has_dvh_data}")
print(f"Has image representation: {rt_dose.has_image_representation}")  
print(f"Is 3D dose: {rt_dose.is_3d_dose}")
print(f"Has spatial info: {rt_dose.has_spatial_information}")
print(f"Is multi-frame: {rt_dose.is_multi_frame}")

# STEP 9: Access module data through IOD - Module access patterns
print("\nStep 9: Accessing modules through IOD")
print("- Experience the get_module() method")
print("- Notice the live module references\n")

dose_summary = rt_dose.get_dose_summary()
spatial_info = rt_dose.get_spatial_information()

print("Dose summary:", dose_summary)
print("Spatial info keys:", list(spatial_info.keys()))

# STEP 10: Validate the complete RTDose IOD - ValidationResult handling
print("\nStep 10: Validating complete RTDose IOD")
print("- Notice ValidationResult object structure")
print("- Observe error/warning access patterns\n")

dose_validation = rt_dose.validate()
print(f"RT Dose validation - Errors: {len(dose_validation.errors)}, Warnings: {len(dose_validation.warnings)}")

if dose_validation.errors:
    print("Validation errors found:")
    for error in dose_validation.errors:
        print(f"  - {error}")

# STEP 11: Generate and work with DICOM dataset - Dataset generation
print("\nStep 11: Generating DICOM dataset")
print("- Experience the to_dataset() method")
print("- Notice the on-demand dataset generation\n")

if not dose_validation.errors:
    dataset = rt_dose.to_dataset()
    print(f"Generated dataset with {len(dataset)} elements")
    print(f"Dataset modality: {dataset.Modality}")
    print(f"Dataset SOP Class: {dataset.SOPClassUID}")
    
    # Could save with: dataset.save_as("sample_dose.dcm")
    print("Dataset ready for saving (commented out file save)")
else:
    print("Cannot generate dataset due to validation errors")

# STEP 12: Explore module access through IOD - Direct module manipulation
print("\nStep 12: Direct module access and manipulation")
print("- Experience live module references")
print("- Notice attribute access patterns\n")

patient_from_iod = rt_dose.get_module('patient')
rt_series_from_iod = rt_dose.get_module('rt_series')

if patient_from_iod:
    print(f"Patient name from IOD: {patient_from_iod['PatientName']}")
    print(f"Patient ID: {patient_from_iod['PatientID']}")

if rt_series_from_iod:
    print(f"Series modality: {rt_series_from_iod['Modality']}")
    print(f"Series description: {rt_series_from_iod['SeriesDescription'] if 'SeriesDescription' in rt_series_from_iod else 'Not set'}")

# STEP 13: Factory methods - Convenience constructors
print("\nStep 13: Using RTDoseIOD factory methods")
print("- Experience convenience factory methods")
print("- Notice simplified parameter sets\n")

# Using factory method for treatment planning workflow
rt_dose_factory = RTDoseIOD.for_treatment_planning(
    patient_name="Smith^Jane",
    patient_id="RT002", 
    patient_birth_date="19850301",
    patient_sex=PatientSex.FEMALE,
    study_description="Breast IMRT Treatment Plan",
    manufacturer="Varian Eclipse v16.1"
)

print(f"Factory-created RTDose: {rt_dose_factory}")

# STEP 14: Advanced features - Non-human organisms and clinical trials
print("\nStep 14: Advanced module features")
print("- Experience conditional element validation")
print("- Notice comprehensive parameter validation\n")

# Create patient with non-human organism data
animal_patient = PatientModule.from_required_elements(
    patient_name="Lab^Mouse^001",
    patient_id="MOUSE001",
    patient_birth_date="20240101", 
    patient_sex=PatientSex.OTHER
).with_non_human_organism(
    patient_species_description="Mus musculus",
    patient_breed_description="C57BL/6J",
    responsible_person="Dr. Researcher^Jane"
)

print("Created non-human patient with species data")

# STEP 15: Validation configuration - Configurable validation
print("\nStep 15: Advanced validation configuration")
print("- Experience ValidationConfig usage")
print("- Notice customizable validation settings\n")

config = ValidationConfig(strict_mode=True)
detailed_result = rt_dose.validate(config)

print(f"Strict validation - Errors: {len(detailed_result.errors)}, Warnings: {len(detailed_result.warnings)}")

# STEP 16: Enum exploration - Complete enum system
print("\nStep 16: Exploring the comprehensive enum system")
print("- Notice all available enum values")
print("- Experience type-safe coded values\n")

print("Patient sex options:", [sex.value for sex in PatientSex])
print("Dose units options:", [unit.value for unit in DoseUnits]) 
print("Dose summation types:", [summation.value for summation in DoseSummationType])

print("\n=== IntelliSense Features Demonstrated ===")
print("✓ 1. Method and parameter auto-completion")
print("✓ 2. Type hints in function signatures") 
print("✓ 3. Docstring popup help")
print("✓ 4. Enum value suggestions")
print("✓ 5. Parameter validation feedback")
print("✓ 6. Module composition with type safety")
print("✓ 7. IOD property access patterns")
print("✓ 8. Factory method convenience")
print("✓ 9. ValidationResult object structure")
print("✓ 10. Live module reference access")

print("\n=== Advanced Patterns ===")
print("✓ Composition-based architecture")
print("✓ Builder pattern with method chaining")
print("✓ Factory methods for common workflows") 
print("✓ Structured validation with ValidationResult")
print("✓ On-demand dataset generation")
print("✓ Type-safe enum system")
print("✓ Conditional element validation")

print("\n=== Script completed successfully! ===")
print("Try copying this file and typing the examples yourself to experience")
print("the full IntelliSense benefits in your Python IDE.")