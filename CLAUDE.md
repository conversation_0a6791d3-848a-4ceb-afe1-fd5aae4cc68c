# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

PyRT-DICOM is a production-ready Python library for creating radiotherapy DICOM files with strongly-typed, IntelliSense-friendly interfaces. The project has evolved from POC to **production implementation phase** with advanced composition-based architecture, factory patterns, fluent APIs, and comprehensive validation systems.

## Core Architecture

### Design Philosophy
- **Factory + Fluent API patterns**: IODs provide specialized factory methods with chainable feature enhancement
- **Composition over inheritance**: IODs use module composition instead of complex multiple inheritance
- **Explicit constructors**: Each module has type-safe `from_required_elements()` and `with_optional_elements()` builders  
- **On-demand dataset generation**: IODs generate fresh pydicom datasets from modules when needed
- **Structured validation**: ValidationResult objects provide consistent error/warning handling with rich properties
- **Live module references**: IODs store and manipulate modules directly with memory efficiency

### Key Architectural Decisions
- **BaseIOD with module storage**: IODs store live module references and generate datasets dynamically
- **ValidationResult class**: Replaced dict-based validation with structured ValidationResult class (256 lines)
- **Factory method patterns**: RTDoseIOD provides 4 specialized factory methods for different workflows
- **Fluent API enhancement**: Chainable methods for optional feature addition (`with_*` patterns)
- **Type 1C conditional validation**: Advanced DICOM compliance with conditional requirement handling
- **Module organization**: Modules grouped under `modules/` with corresponding validators under `validators/modules/`

### Project Structure
```
src/pyrt_dicom/
├── __init__.py                           # Package initialization
├── enums/                                # DICOM enumerated values (2 files)
│   ├── __init__.py
│   ├── dose_enums.py                    # Dose-specific coded values
│   ├── clinical_trial_enums.py          # Clinical trial coded values  
│   └── rt_enums.py                      # RT-specific coded values
├── iods/                                 # Information Object Definitions
│   ├── __init__.py
│   ├── base_iod.py                      # Base IOD with composition pattern (198 lines)
│   └── rt_dose_iod.py                   # Production RT Dose IOD (1,210+ lines)
├── modules/                             # DICOM modules (50+ modules implemented)
│   ├── __init__.py
│   ├── base_module.py                   # Abstract base module pattern (147 lines)
│   ├── patient_module.py                # Patient module with Type 1C validation (502 lines)
│   ├── rt_dose_module.py                # RT Dose module implementation
│   ├── rt_dvh_module.py                 # Dose Volume Histogram module
│   ├── rt_general_plan_module.py        # RT General Plan module
│   └── ...                              # 47+ additional modules
└── validators/                           # Validation system
    ├── __init__.py
    ├── validation_result.py              # ValidationResult class (256 lines)
    └── modules/                          # Module-specific validators
        ├── __init__.py
        ├── base_validator.py
        ├── patient_validator.py
        ├── rt_general_plan_validator.py
        └── ...                           # Corresponding validators for all modules
```

## Production RTDoseIOD Implementation

The RTDoseIOD is production-ready with advanced patterns and comprehensive functionality.

### Factory Methods (Workflow-Specific Creation)
```python
from pyrt_dicom.iods.rt_dose_iod import RTDoseIOD

# Treatment planning workflow
dose_iod = RTDoseIOD.for_treatment_planning(
    patient_name="Doe^John",
    patient_id="12345", 
    study_instance_uid="1.2.3.4.5",
    dose_summation_type=DoseSummationType.PLAN,
    dose_type=DoseType.PHYSICAL
)

# Dose verification workflow  
dose_iod = RTDoseIOD.for_dose_verification(
    patient_name="Doe^John",
    patient_id="12345",
    study_instance_uid="1.2.3.4.5",
    reference_plan_uid="1.2.3.4.5.6"
)

# Research study workflow
dose_iod = RTDoseIOD.for_research_study(
    patient_name="Anonymous",
    patient_id="STUDY001",
    study_instance_uid="1.2.3.4.5",
    protocol_name="Phase II Trial",
    institution_name="Research Center"
)

# Quality assurance workflow
dose_iod = RTDoseIOD.for_quality_assurance(
    patient_name="QA^Phantom",
    patient_id="QA001", 
    study_instance_uid="1.2.3.4.5",
    qa_protocol="Monthly Linac QA"
)
```

### Fluent API Enhancement (Chainable Features)
```python
# Chain optional features after factory creation
enhanced_dose = dose_iod \
    .with_dose_volume_histogram(
        dvh_data=dvh_array,
        roi_volume=125.5,
        dose_units=DoseUnits.GY
    ) \
    .with_3d_dose_grid(
        dose_array=dose_matrix,
        voxel_spacing=(2.0, 2.0, 2.5),
        grid_origin=(-100.0, -150.0, -200.0)
    ) \
    .with_clinical_trial_context(
        sponsor_name="RTOG Foundation",
        protocol_id="RTOG-1234", 
        site_id="Site001"
    ) \
    .with_dose_approval(
        reviewing_physician="Dr. Smith",
        approval_status=ApprovalStatus.APPROVED,
        review_date="20240101",
        review_time="120000"
    )
```

### Rich Properties and Advanced Methods
```python
# Spatial and dose information properties
print(f"Dose grid dimensions: {dose_iod.dose_grid_dimensions}")
print(f"Voxel volume: {dose_iod.voxel_volume_cm3} cm³")
print(f"Total dose grid volume: {dose_iod.total_grid_volume_cm3} cm³")

# Statistical properties  
print(f"Maximum dose: {dose_iod.maximum_dose_value} {dose_iod.dose_units}")
print(f"Mean dose: {dose_iod.mean_dose_value} {dose_iod.dose_units}")
print(f"Minimum dose: {dose_iod.minimum_dose_value} {dose_iod.dose_units}")

# Advanced summary methods
dose_summary = dose_iod.get_dose_summary()
grid_info = dose_iod.get_dose_grid_info()
dvh_summary = dose_iod.get_dvh_summary() if dose_iod.has_dvh_data else None
```

## Module Architecture Patterns

### BaseModule Composition Pattern
```python
from pyrt_dicom.modules.base_module import BaseModule
from pyrt_dicom.validators.validation_result import ValidationResult

class ExampleModule(BaseModule):
    """Example module showing composition pattern."""
    
    def __init__(self):
        """Initialize with empty pydicom Dataset."""
        super().__init__()  # Creates self._dataset = pydicom.Dataset()
    
    @classmethod
    def from_required_elements(cls, 
                              required_param: str,
                              another_required: int) -> 'ExampleModule':
        """Create module with Type 1 and Type 2 elements."""
        instance = cls()
        instance._dataset.RequiredElement = required_param
        instance._dataset.AnotherRequired = another_required
        return instance
    
    def with_optional_elements(self, 
                              optional_param: str | None = None) -> 'ExampleModule':
        """Add Type 3 elements using fluent API."""
        if optional_param is not None:
            self._dataset.OptionalElement = optional_param
        return self
    
    def to_dataset(self) -> pydicom.Dataset:
        """Return copy of internal dataset."""
        return self._dataset.copy()
    
    def validate(self, config: dict | None = None) -> ValidationResult:
        """Validate using corresponding validator."""
        from ..validators.modules.example_validator import ExampleValidator
        return ExampleValidator.validate(self, config)
```

### Advanced Type 1C Conditional Validation
```python
# From PatientModule - shows sophisticated conditional logic
def validate(self, config: dict | None = None) -> ValidationResult:
    """Validate with Type 1C conditional requirements."""
    result = ValidationResult()
    
    # Type 1C: Required if other conditions are met
    if hasattr(self, 'SpeciesDescription'):
        if not hasattr(self, 'PatientSpeciesCodeSequence'):
            result.add_error("PatientSpeciesCodeSequence required when SpeciesDescription present")
    
    # Complex conditional validation
    if self.patient_sex == "O":  # Other gender
        if not hasattr(self, 'PatientSexNeutered'):
            result.add_error("PatientSexNeutered required for Other gender")
    
    return result
```

## ValidationResult Class Architecture

The ValidationResult class provides structured validation with rich properties and methods.

### Core ValidationResult Usage
```python
from pyrt_dicom.validators.validation_result import ValidationResult

# Create and populate validation result
result = ValidationResult()
result.add_error("Required element missing")
result.add_warning("Recommended element not provided")

# Rich property access
print(f"Valid: {result.is_valid}")           # bool
print(f"Has errors: {result.has_errors}")    # bool  
print(f"Error count: {result.error_count}")  # int
print(f"Total issues: {result.total_issues}") # int

# Merge results from multiple validators
module_result = patient_module.validate()
dose_result = dose_module.validate()

combined = ValidationResult()
combined.merge(module_result, prefix="Patient")
combined.merge(dose_result, prefix="Dose")

# Backward compatibility
dict_result = result.to_dict()  # Convert to dict format
restored = ValidationResult.from_dict(dict_result)  # Restore from dict
```

### ValidationResult Properties and Methods
```python
class ValidationResult:
    # Properties
    @property
    def errors(self) -> list[str]:              # Copy of error list
    @property  
    def warnings(self) -> list[str]:            # Copy of warning list
    @property
    def has_errors(self) -> bool:               # True if any errors
    @property
    def has_warnings(self) -> bool:             # True if any warnings
    @property
    def has_issues(self) -> bool:               # True if errors OR warnings
    @property
    def is_valid(self) -> bool:                 # True if no errors
    @property
    def error_count(self) -> int:               # Number of errors
    @property
    def warning_count(self) -> int:             # Number of warnings  
    @property
    def total_issues(self) -> int:              # Total errors + warnings
    
    # Methods
    def add_error(self, message: str) -> None:
    def add_warning(self, message: str) -> None:
    def extend_errors(self, messages: list[str]) -> None:
    def extend_warnings(self, messages: list[str]) -> None:
    def merge(self, other: 'ValidationResult', prefix: str = "") -> None:
    def clear(self) -> None:
    def to_dict(self) -> dict[str, list[str]]:
    @classmethod
    def from_dict(cls, result_dict: dict[str, list[str]]) -> 'ValidationResult':
```

## Development Commands

### Environment Setup
```bash
# Install in development mode with test dependencies
pip install -e .[test]

# Alternative: Install base package only
pip install -e .
```

### Testing
```bash
# Run all tests (48+ test files)
pytest

# Run tests with coverage
pytest --cov=src/pyrt_dicom --cov-report=html

# Run specific test categories
pytest tests/unit/modules/ -v                    # Module tests
pytest tests/unit/validators/ -v                 # Validator tests  
pytest tests/unit/iods/ -v                      # IOD tests

# Run specific test file
pytest tests/unit/modules/test_patient_module.py -v
```

### Type Checking
```bash
# Type check with Hatch environment (recommended)
hatch run types:check

# Direct mypy (if installed separately)
mypy src/pyrt_dicom tests
```

### Code Quality
```bash
# Lint with Ruff (included in test dependencies)
ruff check src/ tests/

# Format with Ruff
ruff format src/ tests/
```

## Current Implementation Status

### Completed (Production-Ready Architecture)
- ✅ BaseIOD composition pattern with live module storage and dynamic dataset generation
- ✅ RTDoseIOD production implementation with 4 factory methods and 4 fluent API methods
- ✅ ValidationResult class with rich properties and comprehensive error/warning handling
- ✅ 50+ DICOM modules implemented with fluent builder patterns
- ✅ Advanced Type 1C conditional validation throughout module system
- ✅ Complete module validator system migrated to ValidationResult objects
- ✅ Comprehensive enums for dose, clinical trial, and RT-specific coded values
- ✅ Rich property methods and summary calculations for dose statistics
- ✅ Advanced spatial information and dose grid analysis capabilities

### In Progress (Testing & Integration)
- 🔄 Pytest test suite updates for ValidationResult migration
- 🔄 Integration testing with real DICOM file generation and validation
- 🔄 Performance benchmarking and memory usage optimization

### Future Phases (Additional IODs)
- ⏳ RT Plan IOD with treatment planning module support
- ⏳ RT Structure Set IOD with ROI and contour management
- ⏳ RT Image IOD with portal and verification images
- ⏳ Advanced dose calculation utilities and analysis tools

## Implementation Patterns

### Creating New Modules with Advanced Features
```python
from pyrt_dicom.modules.base_module import BaseModule
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.enums.rt_enums import SomeEnum

class NewAdvancedModule(BaseModule):
    """Advanced module with Type 1C validation and fluent API.
    
    DICOM Reference: PS3.3 Section X.Y.Z
    
    Usage:
        module = NewAdvancedModule.from_required_elements(
            required_param="value",
            numeric_param=42
        ).with_optional_elements(
            optional_param="value"
        ).with_specialized_feature(
            feature_data=some_data
        )
    """
    
    @classmethod
    def from_required_elements(cls,
                              required_param: str,
                              numeric_param: int,
                              enum_param: SomeEnum) -> 'NewAdvancedModule':
        """Create module with all required (Type 1 and Type 2) elements."""
        instance = cls()
        instance._dataset.RequiredElement = required_param
        instance._dataset.NumericElement = numeric_param  
        instance._dataset.EnumElement = enum_param.value
        return instance
    
    def with_optional_elements(self,
                              optional_param: str | None = None,
                              optional_numeric: int | None = None) -> 'NewAdvancedModule':
        """Add optional (Type 3) elements."""
        if optional_param is not None:
            self._dataset.OptionalElement = optional_param
        if optional_numeric is not None:
            self._dataset.OptionalNumeric = optional_numeric
        return self
    
    def with_specialized_feature(self, 
                               feature_data: list[float]) -> 'NewAdvancedModule':
        """Add specialized feature with validation."""
        if len(feature_data) < 3:
            raise ValueError("Feature data requires at least 3 elements")
        self._dataset.FeatureDataSequence = self._create_feature_sequence(feature_data)
        return self
    
    @staticmethod
    def _create_feature_sequence(data: list[float]) -> list[pydicom.Dataset]:
        """Create specialized sequence items."""
        sequence = []
        for i, value in enumerate(data):
            item = pydicom.Dataset()
            item.ItemNumber = i + 1
            item.FeatureValue = value
            sequence.append(item)
        return sequence
    
    # Rich property access
    @property
    def has_specialized_feature(self) -> bool:
        """Check if specialized feature is present."""
        return hasattr(self._dataset, 'FeatureDataSequence')
    
    @property
    def feature_count(self) -> int:
        """Get count of feature items."""
        if not self.has_specialized_feature:
            return 0
        return len(self._dataset.FeatureDataSequence)
    
    def validate(self, config: dict | None = None) -> ValidationResult:
        """Validate with Type 1C conditional requirements."""
        from ..validators.modules.new_advanced_validator import NewAdvancedValidator
        return NewAdvancedValidator.validate(self, config)
```

### Creating New IODs with Factory + Fluent Pattern
```python
from pyrt_dicom.iods.base_iod import BaseIOD
from pyrt_dicom.modules import PatientModule, GeneralStudyModule, NewAdvancedModule
from pyrt_dicom.validators.validation_result import ValidationResult

class NewAdvancedIOD(BaseIOD):
    """Advanced IOD with factory methods and fluent API.
    
    DICOM Reference: PS3.3 Section A.B.C
    """
    
    SOP_CLASS_UID = "1.2.840.10008.5.1.4.1.x.x.x"
    
    def __init__(self,
                 patient: PatientModule,
                 study: GeneralStudyModule, 
                 advanced: NewAdvancedModule):
        """Initialize IOD with required modules."""
        super().__init__()
        self._required_modules = {
            'patient': patient,
            'study': study,
            'advanced': advanced
        }
    
    @classmethod
    def for_clinical_workflow(cls,
                            patient_name: str,
                            patient_id: str,
                            study_instance_uid: str,
                            workflow_type: str) -> 'NewAdvancedIOD':
        """Factory method for clinical workflow."""
        patient = PatientModule.from_required_elements(
            patient_name=patient_name,
            patient_id=patient_id
        )
        
        study = GeneralStudyModule.from_required_elements(
            study_instance_uid=study_instance_uid,
            study_date=datetime.now().strftime("%Y%m%d"),
            study_time=datetime.now().strftime("%H%M%S")
        )
        
        advanced = NewAdvancedModule.from_required_elements(
            required_param=workflow_type,
            numeric_param=1,
            enum_param=SomeEnum.DEFAULT
        )
        
        return cls(patient, study, advanced)
    
    def with_enhanced_features(self,
                             feature_data: list[float],
                             enhancement_level: int = 1) -> 'NewAdvancedIOD':
        """Add enhanced features using fluent API."""
        self._required_modules['advanced'].with_specialized_feature(feature_data)
        
        # Add enhancement-specific module if needed
        if enhancement_level > 1:
            enhancement_module = EnhancementModule.from_required_elements(
                level=enhancement_level
            )
            self._required_modules['enhancement'] = enhancement_module
            
        return self
    
    # Rich property access
    @property
    def workflow_type(self) -> str:
        """Get workflow type from advanced module."""
        return self._required_modules['advanced']._dataset.RequiredElement
    
    @property
    def has_enhancements(self) -> bool:
        """Check if IOD has enhancement features."""
        return 'enhancement' in self._required_modules
    
    def get_feature_summary(self) -> dict[str, Any]:
        """Get summary of IOD features."""
        advanced = self._required_modules['advanced']
        return {
            'workflow_type': self.workflow_type,
            'feature_count': advanced.feature_count,
            'has_specialized_feature': advanced.has_specialized_feature,
            'has_enhancements': self.has_enhancements
        }
    
    def _validate_dependencies(self) -> ValidationResult:
        """Validate IOD-specific requirements."""
        result = ValidationResult()
        
        # IOD-level validation rules
        if self.workflow_type == "SPECIALIZED" and not self.has_enhancements:
            result.add_warning("Specialized workflow recommended with enhancements")
            
        return result
```

## Important Development Guidelines

### Architecture Constraints
- **Factory + Fluent API pattern**: IODs should provide specialized factory methods with chainable enhancement
- **Composition-first**: IODs must use module composition, not inheritance
- **ValidationResult consistency**: All validation methods must return ValidationResult objects
- **Type safety**: Maintain complete type hints and IntelliSense support
- **Module independence**: Modules should be testable and usable independently
- **Rich properties**: Provide convenient property access for computed values

### Testing Requirements
- All modules must have comprehensive pytest tests covering ValidationResult objects
- Test both successful operations and validation failures with proper ValidationResult assertions
- Include DICOM compliance validation tests with Type 1C conditional requirements
- Module tests should verify builder patterns (`from_required_elements`, `with_optional_elements`)
- IOD tests should verify factory methods, fluent API, composition, and dataset generation
- Integration tests should generate actual DICOM files and validate with external tools

### Code Quality Standards
- Google-style docstrings for all classes and methods with Sphinx compatibility
- Complete type hints using modern syntax (`str | None` instead of `Optional[str]`)
- ValidationResult objects instead of dict-based validation results
- Enum usage for all DICOM coded values (no free text strings)
- Module validators must be in `validators/modules/` with corresponding names
- Rich property methods for computed values and convenience access

### DICOM Compliance Requirements
- Follow DICOM PS3.3 specifications exactly with proper section references
- Implement Type 1, 1C, 2, 2C, and 3 requirements correctly with conditional validation
- Use proper DICOM data element tags and VRs as specified
- Generate valid DICOM files that pass external validation tools
- Support proper DICOM file meta information and transfer syntax

## Dependencies

### Core Dependencies
- `pydicom>=2.4.0`: Core DICOM functionality with Dataset and FileDataset support
- `python>=3.11`: Modern Python features including union types and pattern matching  
- `numpy>=1.24.0`: Array operations for dose data and spatial calculations

### Development Dependencies
- `pytest>=7.0.0`: Testing framework with fixtures and parametrization
- `pytest-cov>=4.0.0`: Coverage reporting and analysis
- `ruff>=0.12.0`: Fast linting and formatting with modern rules
- `mypy>=1.0.0`: Static type checking with strict mode support

## Build System

The project uses **hatch** as the build system with comprehensive configuration:

### Key Build Features
- Package metadata and dependencies defined in `pyproject.toml`
- Version management from `src/pyrt_dicom/__about__.py`
- Multiple environments: default (test), types (mypy), docs (sphinx)
- Test environment with pytest, coverage reporting, and parallel execution
- Type checking environment with mypy strict mode and comprehensive checks
- Ruff integration for linting, formatting, and import sorting

### Hatch Environment Commands
```bash
# Run tests in default environment
hatch run pytest

# Run type checking
hatch run types:check

# Run all quality checks
hatch run ruff check src/ tests/
hatch run types:check
hatch run pytest --cov
```

## Current Development Status

### Phase 1 - Production Architecture ✅
1. ✅ Complete BaseIOD composition architecture with live module storage
2. ✅ Advanced RTDoseIOD with 4 factory methods and 4 fluent API methods  
3. ✅ ValidationResult class migration with rich properties and comprehensive methods
4. ✅ 50+ DICOM modules implemented with Type 1C conditional validation

### Phase 2 - Testing & Integration 🔄
1. 🔄 Update pytest suite for ValidationResult migration and advanced patterns
2. 🔄 Achieve >90% test coverage with comprehensive validation and integration tests
3. 🔄 Generate and validate real DICOM files with external validation tools
4. 🔄 Performance benchmarking and memory usage optimization

### Phase 3 - Additional IODs ⏳
1. ⏳ RT Plan IOD with treatment planning workflow support
2. ⏳ RT Structure Set IOD with ROI and contour management capabilities
3. ⏳ RT Image IOD with portal and verification image support
4. ⏳ Advanced dose calculation utilities and spatial analysis tools

## Architecture Benefits

The production architecture provides significant advantages:

**✅ Factory + Fluent API Design**:
- Specialized factory methods for different clinical workflows
- Chainable fluent API for optional feature enhancement  
- Type-safe method chaining with full IntelliSense support
- Clear separation between required construction and optional enhancement

**✅ Advanced Composition Architecture**:
- IODs store live module references for dynamic manipulation
- No complex multiple inheritance chains or fragile base classes
- Clear separation between data (modules) and orchestration (IODs)
- Memory-efficient on-demand dataset generation

**✅ Rich Validation System**:
- ValidationResult objects with comprehensive properties and methods
- Type 1C conditional validation with sophisticated business logic
- Module validators are independently testable and maintainable
- Clear validation flows from modules → IODs → complete datasets

**✅ Production DICOM File Generation**:
- `to_dataset()` creates pydicom Dataset objects for manipulation
- `generate_file_dataset()` creates FileDataset objects for direct file I/O
- Proper DICOM file meta information and transfer syntax handling
- Support for different transfer syntaxes and encoding options

**✅ Enhanced Developer Experience**:
- Factory methods make IOD creation intuitive for different workflows
- Fluent API enables progressive feature enhancement
- Rich property access for dose statistics and spatial information
- Comprehensive type hints enable full IDE support and error detection

**✅ Advanced Module System**:
- 50+ modules implemented with sophisticated validation rules
- Type 1C conditional validation handles complex DICOM requirements
- Static factory methods for specialized sequence item creation
- Rich property methods for computed values and convenience access

**⚠️ CRITICAL DEVELOPMENT GUIDELINES ⚠️**
- Follow factory + fluent API patterns for all new IODs
- Use ValidationResult objects consistently - never return dict-based validation
- Implement Type 1C conditional validation for sophisticated DICOM compliance
- Always add Google-style docstrings with DICOM PS3.3 section references
- Use modern type hints: `str | None` instead of `Optional[str]`, `int | float` instead of `Union[int, float]`, `list` instead of `List`, `dict` instead of `Dict`
- Provide rich property methods for computed values and convenience access
- Only use `Any` (`from typing import Any`) when absolutely necessary for complex generic patterns
- Factory methods should be workflow-specific with clear clinical intent
- Fluent API methods should be chainable and return self for method composition
- Use dot-style notation to set and get DICOM attributes in the DICOM *_moduly.py classes, e.g., module.PatientName = name